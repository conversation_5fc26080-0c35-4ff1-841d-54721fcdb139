# C端接口subplanId字段增强说明

**修改时间**: 2025-07-25  
**目的**: 为C端接口添加subplanId字段，便于前端调用时传递正确的子计划参数

## 修改背景

### 问题描述
原来的C端接口缺少`subplanId`字段，导致：
1. 前端无法知道每次计划对应的具体子计划ID
2. 调用其他接口时无法传递正确的`subplanId`参数
3. 数据关联不够明确，影响业务逻辑的准确性

### 业务需求
- `plan-list`接口应该返回每次计划对应的`subplanId`
- `plan-detail`接口应该包含当前查看计划的`subplanId`
- 其他问卷相关接口已支持`subplanId`参数，需要确保数据一致性

## 修改内容

### 1. plan-list接口增强

**修改位置**: `/emr/followup/customer/plan-list`

**修改内容**:
```java
Map<String, Object> planItem = new HashMap<>();
planItem.put("planId", plan.getId());
planItem.put("subplanId", subplan.getId()); // 新增：子计划ID
planItem.put("date", dateStr);
planItem.put("name", plan.getName());
planItem.put("desc", subplan.getDescription() != null ? subplan.getDescription() : plan.getDescription());
planItem.put("number", "第" + (i + 1) + "次计划");
planItem.put("status", status);
```

**修改前响应**:
```json
{
    "code": 200,
    "data": [
        {
            "date": "07月24日",
            "number": "第1次计划",
            "name": "高血压随访计划",
            "planId": 34,
            "desc": "",
            "status": "0"
        },
        {
            "date": "07月27日",
            "number": "第2次计划",
            "name": "高血压随访计划",
            "planId": 34,
            "desc": "",
            "status": "0"
        }
    ]
}
```

**修改后响应**:
```json
{
    "code": 200,
    "data": [
        {
            "date": "07月24日",
            "number": "第1次计划",
            "name": "高血压随访计划",
            "planId": 34,
            "subplanId": 44,
            "desc": "",
            "status": "0"
        },
        {
            "date": "07月27日",
            "number": "第2次计划",
            "name": "高血压随访计划",
            "planId": 34,
            "subplanId": 45,
            "desc": "",
            "status": "0"
        }
    ]
}
```

### 2. plan-detail接口增强

**修改位置**: `/emr/followup/customer/plan-detail`

**修改内容**:

#### 2.1 特定子计划模式
```java
Map<String, Object> planDetail = new HashMap<>();
planDetail.put("planId", plan.getId());
planDetail.put("subplanId", mainSubplan.getId()); // 新增：子计划ID
planDetail.put("date", calculatePlanDate(mainSubplan, plan, phone));
planDetail.put("name", plan.getName());
// ... 其他字段
```

#### 2.2 汇总模式
```java
// 汇总模式下，添加所有子计划ID列表
List<Long> subplanIds = new ArrayList<>();
for (FollowupSubplan subplan : subplans) {
    subplanIds.add(subplan.getId());
}
planDetail.put("subplanIds", subplanIds);
```

**修改后响应示例**:

**特定子计划模式**（传入number参数）:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "subplanId": 44,
        "name": "高血压随访计划",
        "number": "第1次计划",
        "date": "07月24日",
        "projectItems": [...]
    }
}
```

**汇总模式**（不传number参数）:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "subplanIds": [44, 45],
        "name": "高血压随访计划",
        "number": "全部计划（共2次）",
        "date": "07月24日",
        "projectItems": [...]
    }
}
```

## 接口调用流程优化

### 优化前的调用流程
```javascript
// 1. 获取计划列表
const planList = await getPlanList({phone, state, pageNo, pageSize});

// 2. 获取计划详情（缺少subplanId）
const planDetail = await getPlanDetail({
    planId: plan.planId,
    phone: phone
});

// 3. 获取问卷列表（需要猜测subplanId）
const questionnaires = await getQuestionnaireList({
    phone: phone,
    planId: plan.planId,
    subplanId: ??? // 不知道应该传什么
});
```

### 优化后的调用流程
```javascript
// 1. 获取计划列表（包含subplanId）
const planList = await getPlanList({phone, state, pageNo, pageSize});

// 2. 获取特定计划详情
const planDetail = await getPlanDetail({
    planId: plan.planId,
    phone: phone,
    number: 1 // 或者根据用户选择
});

// 3. 获取问卷列表（使用正确的subplanId）
const questionnaires = await getQuestionnaireList({
    phone: phone,
    planId: plan.planId,
    subplanId: plan.subplanId // 从plan-list获取的subplanId
});

// 4. 提交问卷答案（使用正确的subplanId）
const result = await submitQuestionnaire({
    phone: phone,
    planId: plan.planId,
    subplanId: plan.subplanId, // 确保数据关联正确
    questionnaires: [...]
});
```

## 数据一致性保证

### 1. 子计划ID映射关系
```
第1次计划 → subplanId: 44 → 疼痛相关不适评估
第2次计划 → subplanId: 45 → NRS动态疼痛评估
```

### 2. 接口间数据传递
- `plan-list` → 提供 `subplanId`
- `plan-detail` → 使用 `subplanId` 查询具体信息
- `questionnaire-list` → 使用 `subplanId` 过滤问卷
- `questionnaire-submit` → 使用 `subplanId` 关联答案
- `questionnaire-answers` → 使用 `subplanId` 查询答案

### 3. 数据库关联完整性
```sql
-- 问卷答案表中的关联字段
questionnaire_answer.plan_id = 34
questionnaire_answer.subplan_id = 44 (或45)
questionnaire_answer.questionnaire_id = 1 (或2)
```

## 前端适配建议

### 1. 列表页面
```javascript
// 渲染计划列表时保存subplanId
planList.forEach(plan => {
    const planItem = {
        planId: plan.planId,
        subplanId: plan.subplanId, // 保存子计划ID
        name: plan.name,
        number: plan.number,
        date: plan.date
    };
    // 渲染UI...
});
```

### 2. 详情页面
```javascript
// 点击具体计划时传递subplanId
const onPlanClick = (plan) => {
    // 方式1: 使用subplanId直接查询
    const questionnaires = await getQuestionnaireList({
        phone: userPhone,
        planId: plan.planId,
        subplanId: plan.subplanId
    });
    
    // 方式2: 使用number参数查询详情
    const planDetail = await getPlanDetail({
        phone: userPhone,
        planId: plan.planId,
        number: extractNumber(plan.number) // 从"第1次计划"提取数字
    });
};
```

### 3. 问卷提交
```javascript
// 提交问卷时确保subplanId正确
const submitAnswers = async (answers) => {
    const result = await submitQuestionnaire({
        phone: userPhone,
        planId: currentPlan.planId,
        subplanId: currentPlan.subplanId, // 确保使用正确的子计划ID
        questionnaires: answers
    });
};
```

## 兼容性说明

### 向后兼容
- 新增字段不影响现有功能
- 原有的接口调用方式仍然有效
- 只是提供了更多的数据信息

### 数据完整性
- 所有子计划ID都来自数据库真实数据
- 确保前后端数据一致性
- 避免了参数传递错误的问题

## 测试建议

### 1. 接口测试
- 测试plan-list接口返回的subplanId是否正确
- 测试plan-detail接口在不同模式下的subplanId字段
- 验证subplanId与数据库中的数据一致

### 2. 业务流程测试
- 测试从plan-list到questionnaire-list的完整调用链
- 验证问卷提交时subplanId的正确性
- 测试问卷答案查看时的数据关联

### 3. 数据一致性测试
- 验证同一个subplanId在不同接口中的数据一致性
- 测试边界情况（如不存在的subplanId）

这次增强确保了C端接口的数据完整性和调用便利性，前端现在可以准确地知道每次计划对应的子计划ID，从而正确调用相关接口。

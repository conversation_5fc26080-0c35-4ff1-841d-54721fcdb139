# plan-detail接口修复说明

**修复时间**: 2025-07-25  
**问题**: plan-detail接口只返回第1次计划详情，缺少第2次计划记录

## 问题分析

### 1. plan-list接口逻辑
`plan-list`接口会为每个子计划生成一条计划记录：

```java
for (int i = 0; i < subplans.size(); i++) {
    FollowupSubplan subplan = subplans.get(i);
    // ...
    planItem.put("number", "第" + (i + 1) + "次计划");
    // ...
    planList.add(planItem);
}
```

所以如果有2个子计划，就会返回：
- 第1次计划（对应第1个子计划）
- 第2次计划（对应第2个子计划）

### 2. plan-detail接口问题
原来的`plan-detail`接口存在以下问题：

1. **固定选择第1个子计划**：
   ```java
   FollowupSubplan mainSubplan = subplans.get(0); // 总是取第一个
   ```

2. **硬编码"第1次计划"**：
   ```java
   planDetail.put("number", "第1次计划"); // 固定值
   ```

3. **缺少参数区分**：无法根据请求参数区分要查看第几次计划的详情

## 修复内容

### 1. 新增number参数支持
```java
Object numberObj = request.get("number"); // 新增：第几次计划
int planNumber = 1; // 默认第1次计划

if (numberObj != null) {
    try {
        planNumber = Integer.parseInt(numberObj.toString());
    } catch (NumberFormatException e) {
        // 如果传入的是"第1次计划"这样的格式，提取数字
        String numberStr = numberObj.toString();
        if (numberStr.contains("第") && numberStr.contains("次")) {
            String numStr = numberStr.replaceAll("[^0-9]", "");
            if (!numStr.isEmpty()) {
                planNumber = Integer.parseInt(numStr);
            }
        }
    }
}
```

### 2. 根据number选择对应子计划
```java
// 查询子计划并排序
LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
subplanWrapper.eq(FollowupSubplan::getPlanId, planId);
subplanWrapper.orderByAsc(FollowupSubplan::getSortOrder); // 按排序字段排序
List<FollowupSubplan> subplans = followupSubplanService.list(subplanWrapper);

// 根据planNumber选择对应的子计划
if (planNumber > subplans.size()) {
    result.put("code", 404);
    result.put("message", "第" + planNumber + "次计划不存在");
    return ResponseEntity.ok(result);
}

FollowupSubplan mainSubplan = subplans.get(planNumber - 1); // 数组索引从0开始
```

### 3. 动态设置number字段
```java
planDetail.put("number", "第" + planNumber + "次计划");
```

## 测试用例

### 测试用例1: 查看第1次计划详情
**请求**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 1
}
```
或者
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": "第1次计划"
}
```
或者（默认第1次）
```json
{
    "planId": "34",
    "phone": "13800138000"
}
```

**预期响应**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "name": "高血压随访计划",
        "number": "第1次计划",
        "date": "07月24日",
        "projectItems": [
            {
                "title": "问卷调查",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            }
        ]
    }
}
```

### 测试用例2: 查看第2次计划详情
**请求**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 2
}
```

**预期响应**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "name": "高血压随访计划",
        "number": "第2次计划",
        "date": "07月27日",
        "projectItems": [
            {
                "title": "问卷调查",
                "subQuestions": ["NRS动态疼痛评估（共1题）"]
            }
        ]
    }
}
```

### 测试用例3: 查看不存在的计划
**请求**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 3
}
```

**预期响应**:
```json
{
    "code": 404,
    "message": "第3次计划不存在"
}
```

## 兼容性说明

### 向后兼容
- 如果不传`number`参数，默认返回第1次计划详情
- 保持原有的响应数据结构不变
- 支持多种number参数格式（数字、文本）

### 前端调用建议
前端可以这样调用：

1. **从plan-list获取计划列表**：
   ```javascript
   // 获取计划列表
   const planList = await getPlanList({phone, state, pageNo, pageSize});
   ```

2. **点击具体计划时传入number参数**：
   ```javascript
   // 用户点击"第2次计划"时
   const planDetail = await getPlanDetail({
       planId: plan.planId,
       phone: phone,
       number: 2  // 或者从plan.number中提取数字
   });
   ```

## 数据库结构说明

假设数据库中有以下子计划：
```sql
-- 子计划表
INSERT INTO followup_subplan VALUES (1, 34, '第一阶段评估', 'cycle', 'allTime', NULL, NULL, '09:00:00', 1, ...);
INSERT INTO followup_subplan VALUES (2, 34, '第二阶段评估', 'custom', 'custom', NULL, NULL, '14:00:00', 2, ...);
```

那么：
- `number=1` 对应 `sort_order=1` 的子计划（第一阶段评估）
- `number=2` 对应 `sort_order=2` 的子计划（第二阶段评估）

## 注意事项

### 1. 子计划排序
确保子计划表中的`sort_order`字段正确设置，这决定了第几次计划的顺序。

### 2. 问卷关联
每个子计划可能关联不同的问卷，所以不同次数的计划详情会显示不同的问卷内容。

### 3. 日期计算
不同子计划可能有不同的执行时间配置，所以`date`字段会根据子计划的配置动态计算。

这次修复解决了plan-detail接口只能查看第1次计划的问题，现在可以根据number参数查看任意次数的计划详情了。

/*
 Navicat Premium Dump SQL

 Source Server         : 4th
 Source Server Type    : MySQL
 Source Server Version : 50735 (5.7.35-log)
 Source Host           : *************:3306
 Source Schema         : ylydb

 Target Server Type    : MySQL
 Target Server Version : 50735 (5.7.35-log)
 File Encoding         : 65001

 Date: 25/07/2025 20:20:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for questionnaire_answer
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_answer`;
CREATE TABLE `questionnaire_answer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `question_id` bigint(20) NOT NULL COMMENT '问题ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `answer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案类型：single-单选，multiple-多选，text-文本，number-数字，date-日期，slider-滑动',
  `answer_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案文本内容',
  `answer_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '答案值（选项ID、数字值等）',
  `answer_score` decimal(10, 2) NULL DEFAULT NULL COMMENT '答案得分',
  `answer_time` datetime NOT NULL COMMENT '答题时间',
  `time_spent` int(11) NULL DEFAULT NULL COMMENT '答题耗时（秒）',
  `task_id` bigint(20) NULL DEFAULT NULL COMMENT '任务ID（关联随访任务）',
  `subplan_id` bigint(20) NULL DEFAULT NULL COMMENT '子计划ID',
  `plan_id` bigint(20) NULL DEFAULT NULL COMMENT '计划ID',
  `answer_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'manual' COMMENT '答案来源：manual-手动填写，auto-自动填充，import-导入',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_question_id`(`question_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_answer_time`(`answer_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_patient_questionnaire_time`(`patient_id`, `questionnaire_id`, `answer_time`) USING BTREE,
  INDEX `idx_task_question`(`task_id`, `question_id`) USING BTREE,
  CONSTRAINT `fk_questionnaire_answer_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_question` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_task` FOREIGN KEY (`task_id`) REFERENCES `followup_task` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷答案表（随访计划专用）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of questionnaire_answer
-- ----------------------------
INSERT INTO `questionnaire_answer` VALUES (1, 1, 1, 1, '张三', 'checkbox', '疼痛,麻木,咳嗽', '2,3,5', 0.00, '2025-07-24 21:06:07', NULL, NULL, NULL, 34, 'manual', '2025-07-24 21:06:07', '2025-07-24 21:06:07');

SET FOREIGN_KEY_CHECKS = 1;

/*
 Navicat Premium Dump SQL

 Source Server         : 4th
 Source Server Type    : MySQL
 Source Server Version : 50735 (5.7.35-log)
 Source Host           : *************:3306
 Source Schema         : ylydb

 Target Server Type    : MySQL
 Target Server Version : 50735 (5.7.35-log)
 File Encoding         : 65001

 Date: 25/07/2025 18:20:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for content_template
-- ----------------------------
DROP TABLE IF EXISTS `content_template`;
CREATE TABLE `content_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板说明',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板分类（health_education:健康教育, medication_guide:用药指导, nursing_guide:护理指导, rehabilitation_guide:康复指导, diet_guide:饮食指导, other:其他）',
  `message_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'patientEducation' COMMENT '消息类型（patientEducation:患教内容）',
  `permission` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all' COMMENT '权限范围（all:全部用户, self:仅本人, department:同科室）',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板内容（富文本HTML）',
  `content_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容（纯文本，用于搜索）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态（active:启用, inactive:停用）',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `creator_dept_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人科室ID',
  `creator_dept_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人科室名称',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `last_used_time` datetime NULL DEFAULT NULL COMMENT '最后使用时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_creator_dept_id`(`creator_dept_id`) USING BTREE,
  INDEX `idx_permission`(`permission`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  FULLTEXT INDEX `ft_content_text`(`content_text`, `title`, `name`) COMMENT '全文搜索索引'
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '内容模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of content_template
-- ----------------------------
INSERT INTO `content_template` VALUES (1, '高血压患教模板', '用于高血压患者的健康教育指导', 'health_education', 'patientEducation', 'all', '高血压患者健康教育指导', '<h3>高血压患者健康教育指导</h3><p><strong>一、什么是高血压？</strong></p><p>高血压是指在安静状态下，收缩压≥140mmHg和/或舒张压≥90mmHg。</p><p><strong>二、日常注意事项：</strong></p><ul><li>低盐饮食，每日食盐摄入量不超过6克</li><li>规律作息，保证充足睡眠</li><li>适量运动，如散步、太极拳等</li><li>戒烟限酒</li><li>定期监测血压</li></ul><p><strong>三、用药指导：</strong></p><p>请按医嘱规律服药，不可自行停药或调整剂量。</p>', '高血压患者健康教育指导 什么是高血压 高血压是指在安静状态下 收缩压≥140mmHg和/或舒张压≥90mmHg 日常注意事项 低盐饮食 每日食盐摄入量不超过6克 规律作息 保证充足睡眠 适量运动 如散步 太极拳等 戒烟限酒 定期监测血压 用药指导 请按医嘱规律服药 不可自行停药或调整剂量', 'active', 1, '系统管理员', 1, '内科', 0, NULL, '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);
INSERT INTO `content_template` VALUES (2, 'cs', '', 'rehabilitation_guide', 'patientEducation', 'all', 'cs', '<p>aaaaaaaaaaaaaaaaaaaa</p>', 'aaaaaaaaaaaaaaaaaaaa', 'active', 1, '系统管理员', 1, '内科', 0, NULL, '2025-07-11 18:24:06', '2025-07-11 18:26:27', 1);
INSERT INTO `content_template` VALUES (3, 'CS', '', 'diet_guide', 'patientEducation', 'all', 'CSCS', '<p>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA</p><p>AAAAAAAAAAAAAA啊啊啊啊啊啊啊啊啊啊啊啊啊</p>', 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA啊啊啊啊啊啊啊啊啊啊啊啊啊', 'active', 1, '系统管理员', 1, '内科', 0, NULL, '2025-07-11 20:50:51', '2025-07-22 17:36:29', 0);

-- ----------------------------
-- Table structure for content_template_category
-- ----------------------------
DROP TABLE IF EXISTS `content_template_category`;
CREATE TABLE `content_template_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类说明',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态（active:启用, inactive:停用）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板分类字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of content_template_category
-- ----------------------------
INSERT INTO `content_template_category` VALUES (1, 'health_education', '健康教育', '健康知识教育相关模板', 1, 'active', '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);
INSERT INTO `content_template_category` VALUES (2, 'medication_guide', '用药指导', '药物使用指导相关模板', 2, 'active', '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);
INSERT INTO `content_template_category` VALUES (3, 'nursing_guide', '护理指导', '护理操作指导相关模板', 3, 'active', '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);
INSERT INTO `content_template_category` VALUES (4, 'rehabilitation_guide', '康复指导', '康复训练指导相关模板', 4, 'active', '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);
INSERT INTO `content_template_category` VALUES (5, 'diet_guide', '饮食指导', '饮食营养指导相关模板', 5, 'active', '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);
INSERT INTO `content_template_category` VALUES (6, 'other', '其他', '其他类型模板', 6, 'active', '2025-07-11 18:07:12', '2025-07-11 18:07:12', 0);

-- ----------------------------
-- Table structure for content_template_favorite
-- ----------------------------
DROP TABLE IF EXISTS `content_template_favorite`;
CREATE TABLE `content_template_favorite`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_template_user`(`template_id`, `user_id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  CONSTRAINT `fk_favorite_template` FOREIGN KEY (`template_id`) REFERENCES `content_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of content_template_favorite
-- ----------------------------

-- ----------------------------
-- Table structure for content_template_tag
-- ----------------------------
DROP TABLE IF EXISTS `content_template_tag`;
CREATE TABLE `content_template_tag`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签颜色',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签说明',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of content_template_tag
-- ----------------------------

-- ----------------------------
-- Table structure for content_template_tag_rel
-- ----------------------------
DROP TABLE IF EXISTS `content_template_tag_rel`;
CREATE TABLE `content_template_tag_rel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_template_tag`(`template_id`, `tag_id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_tag_id`(`tag_id`) USING BTREE,
  CONSTRAINT `fk_rel_tag` FOREIGN KEY (`tag_id`) REFERENCES `content_template_tag` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_rel_template` FOREIGN KEY (`template_id`) REFERENCES `content_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of content_template_tag_rel
-- ----------------------------

-- ----------------------------
-- Table structure for content_template_usage_log
-- ----------------------------
DROP TABLE IF EXISTS `content_template_usage_log`;
CREATE TABLE `content_template_usage_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `template_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `user_id` bigint(20) NOT NULL COMMENT '使用人ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '使用人姓名',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '使用人科室ID',
  `dept_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用人科室名称',
  `usage_scene` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用场景（follow_up:随访, consultation:咨询, notification:通知）',
  `patient_id` bigint(20) NULL DEFAULT NULL COMMENT '患者ID（如果适用）',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名（如果适用）',
  `use_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  INDEX `idx_usage_scene`(`usage_scene`) USING BTREE,
  INDEX `idx_use_time`(`use_time`) USING BTREE,
  CONSTRAINT `fk_usage_template` FOREIGN KEY (`template_id`) REFERENCES `content_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板使用记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of content_template_usage_log
-- ----------------------------

-- ----------------------------
-- Table structure for followup_c_user
-- ----------------------------
DROP TABLE IF EXISTS `followup_c_user`;
CREATE TABLE `followup_c_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号（新增字段）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别：male-男，female-女',
  `age` int(11) NULL DEFAULT NULL COMMENT '年龄',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期（新增字段）',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号（新增字段）',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址（新增字段）',
  `emergency_contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急联系人（新增字段）',
  `emergency_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急联系人电话（新增字段）',
  `bind_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'unbound' COMMENT '绑定状态（新增字段）',
  `auth_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'unauthorized' COMMENT '授权状态（新增字段）',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间（新增字段）',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登录IP（新增字段）',
  `id_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件类型：0-身份证，1-护照，2-军官证',
  `relationship` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关系：1-本人，2-家属，3-监护人',
  `id_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证件号码',
  `jhr_id_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人证件类型',
  `jhr_id_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人证件号码',
  `jhr_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人姓名',
  `jhr_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人电话',
  `is_bind` int(11) NULL DEFAULT 0 COMMENT '是否绑定：1-已绑定，0-未绑定',
  `is_auth` int(11) NULL DEFAULT 0 COMMENT '是否授权：1-已授权，0-未授权',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  INDEX `idx_bind_status`(`bind_status`) USING BTREE,
  INDEX `idx_auth_status`(`auth_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'C端用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of followup_c_user
-- ----------------------------
INSERT INTO `followup_c_user` VALUES (1, '13800138000', '张三', '1', 65, NULL, NULL, NULL, NULL, NULL, 'unbound', 'unauthorized', NULL, NULL, '0', '1', '110101195801011234', '0', '', '', '', 1, 1, '2025-07-24 13:04:04', '2025-07-24 13:04:04');
INSERT INTO `followup_c_user` VALUES (2, '18012345678', '张三', '1', 65, NULL, NULL, NULL, NULL, NULL, 'unbound', 'unauthorized', NULL, NULL, '0', '1', '110101195801011234', '0', '110101195801011235', '历史', '13800138001', 0, 0, '2025-07-25 07:36:41', '2025-07-25 07:36:41');

-- ----------------------------
-- Table structure for followup_plan
-- ----------------------------
DROP TABLE IF EXISTS `followup_plan`;
CREATE TABLE `followup_plan`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '计划描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划分类：postoperative-术后随访，chronic_disease-慢病管理，health_assessment-健康评估，rehabilitation-康复管理',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'draft' COMMENT '状态：draft-草稿，active-启用，paused-暂停，completed-已完成',
  `end_time_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '结束时间类型：day-天，week-周，month-月，year-年，unlimited-长期',
  `end_time_value` int(11) NULL DEFAULT NULL COMMENT '结束时间值',
  `group_id` bigint(20) NOT NULL COMMENT '关联的患者分组ID',
  `qr_code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入组申请二维码URL',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `patient_count` int(11) NOT NULL DEFAULT 0 COMMENT '患者数量',
  `task_count` int(11) NOT NULL DEFAULT 0 COMMENT '任务数量',
  `completed_task_count` int(11) NOT NULL DEFAULT 0 COMMENT '已完成任务数量',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE,
  INDEX `idx_patient_count`(`patient_count`) USING BTREE,
  INDEX `idx_task_count`(`task_count`) USING BTREE,
  INDEX `idx_completed_task_count`(`completed_task_count`) USING BTREE,
  CONSTRAINT `fk_plan_group` FOREIGN KEY (`group_id`) REFERENCES `patient_group` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访计划主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of followup_plan
-- ----------------------------
INSERT INTO `followup_plan` VALUES (34, '高血压随访计划', '高血压随访计划描述', 'chronic_disease', 'active', 'unlimited', NULL, 2, 'http://example.com/qr/group/2', 1, '系统管理员', '2025-07-24 14:40:30', '2025-07-25 15:45:05', 2, 0, 0);

-- ----------------------------
-- Table structure for followup_subplan
-- ----------------------------
DROP TABLE IF EXISTS `followup_subplan`;
CREATE TABLE `followup_subplan`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '子计划ID',
  `plan_id` bigint(20) NOT NULL COMMENT '所属计划ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子计划名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '子计划描述',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子计划类型：patient-患者填写，medical-仅医护填写',
  `execution_time_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行时间类型：all-全部时段，custom-自定义',
  `custom_start_day` int(11) NULL DEFAULT NULL COMMENT '自定义开始天数',
  `custom_end_day` int(11) NULL DEFAULT NULL COMMENT '自定义结束天数',
  `reminder_time` time NOT NULL DEFAULT '09:00:00' COMMENT '提醒时间（精确到分钟）',
  `plan_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划类型：cycle-周期循环，custom-自定义',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_type`(`type`) USING BTREE,
  INDEX `idx_plan_type`(`plan_type`) USING BTREE,
  CONSTRAINT `fk_subplan_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访子计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of followup_subplan
-- ----------------------------
INSERT INTO `followup_subplan` VALUES (44, 34, '子计划1', '', 'patient', 'all', NULL, NULL, '09:00:00', 'cycle', 0, '2025-07-25 15:45:05', '2025-07-25 15:45:05');
INSERT INTO `followup_subplan` VALUES (45, 34, '子计划2', '', 'patient', 'all', NULL, NULL, '08:00:00', 'custom', 0, '2025-07-25 15:45:05', '2025-07-25 15:45:05');

-- ----------------------------
-- Table structure for followup_subplan_item
-- ----------------------------
DROP TABLE IF EXISTS `followup_subplan_item`;
CREATE TABLE `followup_subplan_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `subplan_id` bigint(20) NOT NULL COMMENT '所属子计划ID',
  `item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `cycle_days` int(11) NULL DEFAULT NULL COMMENT '周期天数（周期循环时使用）',
  `join_day` int(11) NULL DEFAULT NULL COMMENT '加入计划第几天（自定义时使用）',
  `event_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件类型：instant_message-即时消息',
  `content_template_id` bigint(20) NULL DEFAULT NULL COMMENT '内容模板ID（即时消息时使用）',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_content_template_id`(`content_template_id`) USING BTREE,
  CONSTRAINT `fk_subplan_item_content_template` FOREIGN KEY (`content_template_id`) REFERENCES `content_template` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_subplan_item_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访子计划项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of followup_subplan_item
-- ----------------------------
INSERT INTO `followup_subplan_item` VALUES (36, 44, '子计划1的一个提醒', 3, NULL, 'instant_message', NULL, 1, '2025-07-25 15:45:05', '2025-07-25 15:45:05');
INSERT INTO `followup_subplan_item` VALUES (37, 45, '子计划2的一个提醒', NULL, 4, 'instant_message', NULL, 1, '2025-07-25 15:45:05', '2025-07-25 15:45:05');

-- ----------------------------
-- Table structure for followup_subplan_questionnaire
-- ----------------------------
DROP TABLE IF EXISTS `followup_subplan_questionnaire`;
CREATE TABLE `followup_subplan_questionnaire`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `subplan_id` bigint(20) NOT NULL COMMENT '子计划ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否必填',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_subplan_questionnaire`(`subplan_id`, `questionnaire_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  CONSTRAINT `fk_subplan_questionnaire_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_subplan_questionnaire_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访子计划问卷关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of followup_subplan_questionnaire
-- ----------------------------
INSERT INTO `followup_subplan_questionnaire` VALUES (50, 44, 1, 1, 1, '2025-07-25 15:45:05');
INSERT INTO `followup_subplan_questionnaire` VALUES (51, 45, 2, 1, 1, '2025-07-25 15:45:05');

-- ----------------------------
-- Table structure for followup_task
-- ----------------------------
DROP TABLE IF EXISTS `followup_task`;
CREATE TABLE `followup_task`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `plan_id` bigint(20) NOT NULL COMMENT '计划ID',
  `subplan_id` bigint(20) NOT NULL COMMENT '子计划ID',
  `subplan_item_id` bigint(20) NOT NULL COMMENT '子计划项目ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `scheduled_time` datetime NOT NULL COMMENT '计划执行时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待执行，executing-执行中，completed-已完成，failed-执行失败',
  `message_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '消息是否已发送',
  `message_sent_time` datetime NULL DEFAULT NULL COMMENT '消息发送时间',
  `questionnaire_completed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '问卷是否已完成',
  `questionnaire_completed_time` datetime NULL DEFAULT NULL COMMENT '问卷完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_subplan_item_id`(`subplan_item_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_scheduled_time`(`scheduled_time`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_task_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_task_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_task_subplan_item` FOREIGN KEY (`subplan_item_id`) REFERENCES `followup_subplan_item` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访任务表' ROW_FORMAT = Dynamic;




-- ----------------------------
-- Table structure for patient_group
-- ----------------------------
DROP TABLE IF EXISTS `patient_group`;
CREATE TABLE `patient_group`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分组描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组分类（新增字段）',
  `patient_count` int(11) NULL DEFAULT 0 COMMENT '患者数量',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of patient_group
-- ----------------------------
INSERT INTO `patient_group` VALUES (2, '高血压随访组', '高血压', NULL, 2, 'active', 1, '张勤', '2025-07-22 13:22:33', '2025-07-25 08:02:44');
INSERT INTO `patient_group` VALUES (3, 'copd慢阻肺组', '', NULL, 0, 'active', 1, '陈缵珅', '2025-07-24 21:27:48', '2025-07-24 21:28:48');

-- ----------------------------
-- Table structure for patient_group_relation
-- ----------------------------
DROP TABLE IF EXISTS `patient_group_relation`;
CREATE TABLE `patient_group_relation`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `group_id` bigint(20) NOT NULL COMMENT '分组ID',
  `patient_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者手机号',
  `join_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入组方式',
  `user_agree` tinyint(1) NULL DEFAULT NULL COMMENT '用户是否同意',
  `doctor_agree` tinyint(1) NULL DEFAULT NULL COMMENT '医生是否同意',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `agree_time` datetime NULL DEFAULT NULL COMMENT '同意时间',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `auditor_id` bigint(20) NULL DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人姓名',
  `audit_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '未审核' COMMENT '入组状态: 0-待审核, 1-已入组, 2-已拒绝',
  `join_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '加入方式: 院内渠道, 院外渠道',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者姓名',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_patient_group`(`patient_id`, `group_id`) USING BTREE COMMENT '患者和分组关系是唯一的',
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者分组关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of patient_group_relation
-- ----------------------------
INSERT INTO `patient_group_relation` VALUES (5, 1, 2, NULL, NULL, NULL, NULL, NULL, '2025-07-24 13:31:52', NULL, NULL, NULL, NULL, NULL, '1', 'C端自助入组', NULL, NULL, '2025-07-24 13:31:51', '2025-07-24 13:31:51');
INSERT INTO `patient_group_relation` VALUES (6, 2, 2, NULL, NULL, NULL, NULL, NULL, '2025-07-25 08:02:44', NULL, NULL, NULL, NULL, NULL, '1', 'C端自助入组', NULL, NULL, '2025-07-25 08:02:44', '2025-07-25 08:02:44');



-- ----------------------------
-- Table structure for questionnaire
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire`;
CREATE TABLE `questionnaire`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷名称',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'custom' COMMENT '问卷类型',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'active' COMMENT '状态',
  `is_scoring` tinyint(1) NULL DEFAULT 0 COMMENT '是否计分问卷',
  `is_guidance` tinyint(1) NULL DEFAULT 0 COMMENT '是否指导问卷',
  `is_trigger` tinyint(1) NULL DEFAULT 0 COMMENT '是否触发问卷',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of questionnaire
-- ----------------------------
INSERT INTO `questionnaire` VALUES (1, '疼痛相关不适评估', 'custom', 'active', 1, 1, 0, '2025-07-12 09:26:30', '2025-07-12 09:31:36');
INSERT INTO `questionnaire` VALUES (2, 'NRS动态疼痛评估', 'custom', 'active', 1, 0, 0, '2025-07-12 09:26:30', '2025-07-25 15:44:39');
INSERT INTO `questionnaire` VALUES (4, '测试', 'custom', 'active', 0, 1, 0, '2025-07-25 15:43:20', '2025-07-25 18:17:08');

-- ----------------------------
-- Table structure for questionnaire_answer
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_answer`;
CREATE TABLE `questionnaire_answer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `question_id` bigint(20) NOT NULL COMMENT '问题ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `answer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案类型：single-单选，multiple-多选，text-文本，number-数字，date-日期，slider-滑动',
  `answer_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '答案文本内容',
  `answer_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '答案值（选项ID、数字值等）',
  `answer_score` decimal(10, 2) NULL DEFAULT NULL COMMENT '答案得分',
  `answer_time` datetime NOT NULL COMMENT '答题时间',
  `time_spent` int(11) NULL DEFAULT NULL COMMENT '答题耗时（秒）',
  `task_id` bigint(20) NULL DEFAULT NULL COMMENT '任务ID（关联随访任务）',
  `subplan_id` bigint(20) NULL DEFAULT NULL COMMENT '子计划ID',
  `plan_id` bigint(20) NULL DEFAULT NULL COMMENT '计划ID',
  `answer_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'manual' COMMENT '答案来源：manual-手动填写，auto-自动填充，import-导入',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_question_id`(`question_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_subplan_id`(`subplan_id`) USING BTREE,
  INDEX `idx_plan_id`(`plan_id`) USING BTREE,
  INDEX `idx_answer_time`(`answer_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_patient_questionnaire_time`(`patient_id`, `questionnaire_id`, `answer_time`) USING BTREE,
  INDEX `idx_task_question`(`task_id`, `question_id`) USING BTREE,
  CONSTRAINT `fk_questionnaire_answer_plan` FOREIGN KEY (`plan_id`) REFERENCES `followup_plan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_question` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_questionnaire` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_subplan` FOREIGN KEY (`subplan_id`) REFERENCES `followup_subplan` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_questionnaire_answer_task` FOREIGN KEY (`task_id`) REFERENCES `followup_task` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷答案表（随访计划专用）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of questionnaire_answer
-- ----------------------------
INSERT INTO `questionnaire_answer` VALUES (1, 1, 1, 1, '张三', 'checkbox', '疼痛,麻木,咳嗽', '2,3,5', 0.00, '2025-07-24 21:06:07', NULL, NULL, NULL, 34, 'manual', '2025-07-24 21:06:07', '2025-07-24 21:06:07');

-- ----------------------------
-- Table structure for questionnaire_option
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_option`;
CREATE TABLE `questionnaire_option`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `option_text` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项文本',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `instruction` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `score` int(11) NULL DEFAULT 0 COMMENT '分值',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_id`(`question_id`) USING BTREE,
  CONSTRAINT `questionnaire_option_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷选项表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of questionnaire_option
-- ----------------------------
INSERT INTO `questionnaire_option` VALUES (1, 1, '天气下雨或阴天', '天气变化导致的不适', NULL, NULL, 5, 1);
INSERT INTO `questionnaire_option` VALUES (2, 1, '疼痛', '身体疼痛症状', NULL, NULL, 2, 2);
INSERT INTO `questionnaire_option` VALUES (3, 1, '麻木', '肢体麻木感觉', NULL, NULL, 2, 3);
INSERT INTO `questionnaire_option` VALUES (4, 1, '麻木僵硬', '肢体麻木且僵硬', NULL, NULL, 4, 4);
INSERT INTO `questionnaire_option` VALUES (5, 1, '咳嗽', '呼吸道症状', NULL, NULL, 2, 5);
INSERT INTO `questionnaire_option` VALUES (6, 1, '运动', '运动时的不适', NULL, NULL, 3, 6);
INSERT INTO `questionnaire_option` VALUES (7, 1, '僵硬', '关节僵硬', NULL, NULL, 2, 7);
INSERT INTO `questionnaire_option` VALUES (8, 1, '像针扎一样', '刺痛感', NULL, NULL, 4, 8);
INSERT INTO `questionnaire_option` VALUES (9, 1, '累', '疲劳感', NULL, NULL, 5, 9);
INSERT INTO `questionnaire_option` VALUES (10, 1, '没有', '无不适症状', NULL, NULL, 2, 10);
INSERT INTO `questionnaire_option` VALUES (11, 1, '其他', '', '', '', 0, 11);
INSERT INTO `questionnaire_option` VALUES (12, 4, '偶然', '', '', '', 0, 1);
INSERT INTO `questionnaire_option` VALUES (13, 4, '经常', '', '', '', 0, 2);
INSERT INTO `questionnaire_option` VALUES (14, 4, '几乎不', '', '', '', 0, 3);
INSERT INTO `questionnaire_option` VALUES (15, 2, '跑步', '', '', '', 0, 1);
INSERT INTO `questionnaire_option` VALUES (16, 2, '跳伞', '', '', '', 0, 2);
INSERT INTO `questionnaire_option` VALUES (17, 2, '爬山', '', '', '', 0, 3);
INSERT INTO `questionnaire_option` VALUES (18, 2, '走路', '', '', '', 0, 4);
INSERT INTO `questionnaire_option` VALUES (19, 2, '游泳', '', '', '', 0, 5);

-- ----------------------------
-- Table structure for questionnaire_question
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_question`;
CREATE TABLE `questionnaire_question`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题目内容',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'single' COMMENT '题目类型',
  `required` tinyint(1) NULL DEFAULT 0 COMMENT '是否必填',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `instruction` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `text_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文本类型',
  `min_value` int(11) NULL DEFAULT NULL COMMENT '最小值',
  `max_value` int(11) NULL DEFAULT NULL COMMENT '最大值',
  `slider_min` int(11) NULL DEFAULT NULL COMMENT '滑动最小值',
  `slider_max` int(11) NULL DEFAULT NULL COMMENT '滑动最大值',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `questionnaire_id`(`questionnaire_id`) USING BTREE,
  CONSTRAINT `questionnaire_question_ibfk_1` FOREIGN KEY (`questionnaire_id`) REFERENCES `questionnaire` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷题目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of questionnaire_question
-- ----------------------------
INSERT INTO `questionnaire_question` VALUES (1, 1, '请您选择您最近1周内是否出现以下不适症状', 'multiple', 1, '', '请根据实际情况选择', '症状评估说明', NULL, NULL, NULL, NULL, NULL, 1);
INSERT INTO `questionnaire_question` VALUES (2, 2, '平时喜欢做什么', 'multiple', 1, '按实回答', '请根据疼痛程度进行评分', '疼痛评估指导', NULL, NULL, NULL, 0, 10, 1);
INSERT INTO `questionnaire_question` VALUES (4, 4, '平时运动么啊啊啊', 'single', 1, '', '', '', NULL, NULL, NULL, NULL, NULL, 1);

-- ----------------------------
-- Table structure for questionnaire_rule
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_rule`;
CREATE TABLE `questionnaire_rule`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `rule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则类型',
  `rule_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则值',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则描述',
  `evaluation_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评定名称',
  `score` int(11) NULL DEFAULT 0 COMMENT '分值',
  `reminder_enabled` tinyint(1) NULL DEFAULT 0 COMMENT '是否启用提醒',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `question_id`(`question_id`) USING BTREE,
  CONSTRAINT `questionnaire_rule_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questionnaire_question` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of questionnaire_rule
-- ----------------------------

-- ----------------------------
-- Table structure for smart_followup_template
-- ----------------------------
DROP TABLE IF EXISTS `smart_followup_template`;
CREATE TABLE `smart_followup_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板分类：1-术后随访，2-慢病管理，3-健康体检',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `department_id` bigint(20) NULL DEFAULT NULL COMMENT '科室ID',
  `department_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室名称',
  `config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板配置（JSON格式）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_department_id`(`department_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能随访模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smart_followup_template
-- ----------------------------

-- ----------------------------
-- Table structure for smart_form_answer
-- ----------------------------
DROP TABLE IF EXISTS `smart_form_answer`;
CREATE TABLE `smart_form_answer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `submission_id` bigint(20) NOT NULL COMMENT '提交ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `answer_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案值',
  `answer_score` int(11) NULL DEFAULT NULL COMMENT '答案分值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_submission_id`(`submission_id`) USING BTREE,
  INDEX `idx_question_id`(`question_id`) USING BTREE,
  CONSTRAINT `fk_answer_submission` FOREIGN KEY (`submission_id`) REFERENCES `smart_form_submission` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单答案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smart_form_answer
-- ----------------------------

-- ----------------------------
-- Table structure for smart_form_other_data
-- ----------------------------
DROP TABLE IF EXISTS `smart_form_other_data`;
CREATE TABLE `smart_form_other_data`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `form_template_id` bigint(20) NOT NULL COMMENT '表单模板ID',
  `data_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据类型：patient_info-患者信息，medical_record-病历信息，lab_result-检验结果，imaging_result-影像结果，medication_info-用药信息，vital_signs-生命体征，other-其他',
  `data_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据名称',
  `data_description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据描述',
  `data_config` json NULL COMMENT '数据配置（JSON格式）',
  `sort_order` int(11) NOT NULL DEFAULT 1 COMMENT '排序顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0-非必填，1-必填',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_template_id`(`form_template_id`) USING BTREE,
  INDEX `idx_data_type`(`data_type`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  CONSTRAINT `fk_smart_form_other_data_form` FOREIGN KEY (`form_template_id`) REFERENCES `smart_form_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单其他数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smart_form_other_data
-- ----------------------------

-- ----------------------------
-- Table structure for smart_form_questionnaire_rel
-- ----------------------------
DROP TABLE IF EXISTS `smart_form_questionnaire_rel`;
CREATE TABLE `smart_form_questionnaire_rel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `form_template_id` bigint(20) NOT NULL COMMENT '表单模板ID',
  `questionnaire_id` bigint(20) NOT NULL COMMENT '问卷ID',
  `questionnaire_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷名称（冗余字段，便于搜索）',
  `questionnaire_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷类型：symptom-症状评估，scale-量表评估，side_effect-不良反应，custom-自定义',
  `question_count` int(11) NULL DEFAULT 0 COMMENT '题目数量（冗余字段）',
  `sort_order` int(11) NOT NULL DEFAULT 1 COMMENT '排序顺序',
  `is_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否必填：0-非必填，1-必填',
  `display_config` json NULL COMMENT '显示配置（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_template_id`(`form_template_id`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_form_questionnaire`(`form_template_id`, `questionnaire_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单问卷关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smart_form_questionnaire_rel
-- ----------------------------
INSERT INTO `smart_form_questionnaire_rel` VALUES (6, 1, 1, '疼痛相关不适评估', NULL, NULL, 1, 1, '\"{\\\"title\\\": \\\"疼痛相关不适评估\\\", \\\"description\\\": \\\"\\\"}\"', '2025-07-22 17:36:14', '2025-07-22 17:36:14', 0);
INSERT INTO `smart_form_questionnaire_rel` VALUES (7, 1, 2, 'NRS动态疼痛评估', NULL, NULL, 2, 1, '\"{\\\"title\\\": \\\"NRS动态疼痛评估\\\", \\\"description\\\": \\\"\\\"}\"', '2025-07-22 17:36:14', '2025-07-22 17:36:14', 0);
INSERT INTO `smart_form_questionnaire_rel` VALUES (8, 1, 3, '药物不良反应评估', NULL, NULL, 3, 1, '{\"title\": \"药物不良反应评估\", \"description\": \"\"}', '2025-07-22 17:36:14', '2025-07-22 17:36:14', 0);
INSERT INTO `smart_form_questionnaire_rel` VALUES (15, 2, 1, '疼痛相关不适评估', NULL, NULL, 1, 1, '\"\\\"{\\\\\\\"title\\\\\\\": \\\\\\\"疼痛相关不适评估\\\\\\\", \\\\\\\"description\\\\\\\": \\\\\\\"\\\\\\\"}\\\"\"', '2025-07-24 19:55:19', '2025-07-24 19:55:19', 0);
INSERT INTO `smart_form_questionnaire_rel` VALUES (16, 2, 2, 'NRS动态疼痛评估', NULL, NULL, 2, 1, '\"{\\\"title\\\": \\\"NRS动态疼痛评估\\\", \\\"description\\\": \\\"\\\"}\"', '2025-07-24 19:55:19', '2025-07-24 19:55:19', 0);
INSERT INTO `smart_form_questionnaire_rel` VALUES (17, 2, 3, '药物不良反应评估', NULL, NULL, 3, 1, '{\"title\": \"药物不良反应评估\", \"description\": \"\"}', '2025-07-24 19:55:19', '2025-07-24 19:55:19', 0);

-- ----------------------------
-- Table structure for smart_form_share_config
-- ----------------------------
DROP TABLE IF EXISTS `smart_form_share_config`;
CREATE TABLE `smart_form_share_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `form_id` bigint(20) NOT NULL COMMENT '表单ID',
  `share_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分享类型：qr_code-二维码，link-链接',
  `share_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分享配置JSON',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `max_submissions` int(11) NULL DEFAULT NULL COMMENT '最大提交次数',
  `current_submissions` int(11) NOT NULL DEFAULT 0 COMMENT '当前提交次数',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_form_id`(`form_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_share_config_form` FOREIGN KEY (`form_id`) REFERENCES `smart_form_template` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单分享配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smart_form_share_config
-- ----------------------------

-- ----------------------------
-- Table structure for smart_form_submission
-- ----------------------------
DROP TABLE IF EXISTS `smart_form_submission`;
CREATE TABLE `smart_form_submission`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `form_template_id` bigint(20) NOT NULL COMMENT '表单模板ID',
  `submission_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提交编号',
  `patient_id` bigint(20) NULL DEFAULT NULL COMMENT '患者ID',
  `patient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者手机号',
  `patient_id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者身份证号',
  `submitter_id` bigint(20) NULL DEFAULT NULL COMMENT '提交者ID',
  `submitter_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交者姓名',
  `submitter_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'patient' COMMENT '提交者类型：patient-患者，doctor-医生，nurse-护士',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'submitted' COMMENT '状态：submitted-已提交，reviewed-已审核，completed-已完成',
  `progress` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '完成进度（百分比）',
  `total_score` decimal(10, 2) NULL DEFAULT NULL COMMENT '总分数',
  `submit_source` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'web' COMMENT '提交来源：web-网页，app-APP，mini-小程序',
  `submit_device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交设备信息',
  `submit_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交IP地址',
  `submit_time` datetime NOT NULL COMMENT '提交时间',
  `review_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_submission_no`(`submission_no`) USING BTREE,
  INDEX `idx_form_template_id`(`form_template_id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_submitter_id`(`submitter_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_submit_time`(`submit_time`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE,
  CONSTRAINT `fk_smart_form_submission_form` FOREIGN KEY (`form_template_id`) REFERENCES `smart_form_template` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单提交记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of smart_form_submission
-- ----------------------------

-- ----------------------------
-- Table structure for smart_form_template
-- ----------------------------
DROP TABLE IF EXISTS `smart_form_template`;
CREATE TABLE `smart_form_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '表单ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单描述',
  `form_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'one_time' COMMENT '表单类型：one_time-一次性问卷，repeatable-重复性问卷',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单分类：postoperative_followup-术后随访，chronic_disease-慢病管理，health_assessment-健康评估，satisfaction_survey-满意度调查，symptom_monitoring-症状监测，rehabilitation_assessment-康复评估，other-其他',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-停用',
  `permission` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'self' COMMENT '权限范围：self-仅本人查看，department-本科室查看，all-全院查看',
  `config` json NULL COMMENT '表单配置信息（JSON格式）：showProgress, allowEdit, autoSave, theme, submitBtnText',
  `repeat_config` json NULL COMMENT '重复配置（仅重复性问卷使用）：interval, duration, unit, reminderEnabled, reminderTime',
  `remark_text` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注说明（最大50字符）',
  `consent_config` json NULL COMMENT '知情同意书配置（JSON格式）：enabled, content',
  `questionnaire_id` bigint(20) NULL DEFAULT NULL COMMENT '关联问卷ID',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者姓名',
  `creator_dept_id` bigint(20) NOT NULL COMMENT '创建者科室ID',
  `creator_dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者科室名称',
  `submission_count` int(11) NULL DEFAULT 0 COMMENT '提交次数',
  `last_submitted_time` datetime NULL DEFAULT NULL COMMENT '最后提交时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `qr_code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '填写二维码URL',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_creator_id`(`creator_id`) USING BTREE,
  INDEX `idx_creator_dept_id`(`creator_dept_id`) USING BTREE,
  INDEX `idx_form_type`(`form_type`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_permission`(`permission`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_questionnaire_id`(`questionnaire_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_is_deleted`(`is_deleted`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '智能表单模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of smart_form_template
-- ----------------------------
INSERT INTO `smart_form_template` VALUES (1, '测试', '测试', 'one_time', 'health_assessment', 'active', 'self', NULL, NULL, '', NULL, NULL, 1001, '张医生', 2001, '外科', 0, NULL, '2025-07-22 17:35:52', '2025-07-22 17:36:14', 0, NULL);
INSERT INTO `smart_form_template` VALUES (2, '测额嗯', '冲冲冲', 'one_time', 'health_assessment', 'active', 'self', NULL, NULL, '', NULL, NULL, 1001, '张医生', 2001, '外科', 0, NULL, '2025-07-23 03:55:14', '2025-07-24 19:55:19', 0, NULL);

-- ----------------------------
-- View structure for v_referral_audit_with_institution
-- ----------------------------
DROP VIEW IF EXISTS `v_referral_audit_with_institution`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_referral_audit_with_institution` AS select `r`.`id` AS `id`,`r`.`patient_id` AS `patient_id`,`r`.`patient_name` AS `patient_name`,`r`.`gender` AS `gender`,`r`.`age` AS `age`,`r`.`phone` AS `phone`,`r`.`id_card` AS `id_card`,`r`.`birth_date` AS `birth_date`,`r`.`discharge_date` AS `discharge_date`,`r`.`discharge_department` AS `discharge_department`,`r`.`primary_diagnosis` AS `primary_diagnosis`,`r`.`other_diagnosis` AS `other_diagnosis`,`r`.`referral_institution_code` AS `referral_institution_code`,`r`.`referral_institution_name` AS `referral_institution_name`,`r`.`referral_type` AS `referral_type`,`r`.`emergency_contact` AS `emergency_contact`,`r`.`emergency_phone` AS `emergency_phone`,`r`.`contact_relation` AS `contact_relation`,`r`.`audit_status` AS `audit_status`,`r`.`audit_time` AS `audit_time`,`r`.`auditor_id` AS `auditor_id`,`r`.`auditor_name` AS `auditor_name`,`r`.`audit_remarks` AS `audit_remarks`,`r`.`referral_letter_content` AS `referral_letter_content`,`r`.`create_time` AS `create_time`,`r`.`update_time` AS `update_time`,`r`.`create_by` AS `create_by`,`r`.`update_by` AS `update_by`,`r`.`is_deleted` AS `is_deleted`,`ni`.`name` AS `institution_name`,`ni`.`address` AS `institution_address`,`ni`.`phone` AS `institution_phone`,`ni`.`contact_person` AS `institution_contact`,`ni`.`contact_phone` AS `institution_contact_phone`,`ni`.`level` AS `institution_level`,`ni`.`capacity` AS `institution_capacity`,`ni`.`current_patients` AS `institution_current_patients` from (`nh_referral_audit` `r` left join `nursing_home_institution` `ni` on(((`r`.`referral_institution_code` = `ni`.`code`) and (`ni`.`is_deleted` = 0)))) where (`r`.`is_deleted` = 0);

SET FOREIGN_KEY_CHECKS = 1;

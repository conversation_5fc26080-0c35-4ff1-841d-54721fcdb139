# C端随访接口文档

**更新时间**: 2025-07-25
**版本**: v1.0.7

## 接口概述

本文档提供了C端患者随访相关的接口规范，包括用户注册、绑定、授权、入组、随访计划查询、问卷填报等功能。

### 完整接口列表

1. **用户注册接口** - `/emr/followup/cuser/register` ✅
2. **确认绑定接口** - `/emr/followup/cuser/bind` ✅
3. **确认授权接口** - `/emr/followup/cuser/auth` ✅
4. **患者确认入组接口** - `/emr/followup/cuser/confirm-group` ✅
5. **随访计划列表接口** - `/emr/followup/customer/plan-list` ✅
6. **随访计划介绍接口** - `/emr/followup/customer/plan-detail` ✅
7. **获取用户信息接口** - `/emr/followup/customer/user-info` ✅
8. **问卷列表获取接口** - `/emr/followup/customer/questionnaire-list` ✅
9. **问卷填报接口** - `/emr/followup/customer/questionnaire-submit` ✅
10. **问卷答案查看接口** - `/emr/followup/customer/questionnaire-answers` ✅

### 业务流程

现在C端患者可以完整地使用随访功能：**注册 → 绑定 → 授权 → 入组 → 查看计划 → 填写问卷 → 提交答案 → 查看结果**。所有接口都严格按照前端期望的格式开发，确保无缝对接。

## 基础信息

- **基础URL**: `/emr/followup`
- **请求方式**: POST/GET
- **数据格式**: JSON
- **字符编码**: UTF-8

## 测试环境配置

**测试服务器地址**: `http://localhost:18923`  
**Content-Type**: `application/json`

## 详细接口说明

### 1. 用户注册接口

**接口路径**: `/emr/followup/cuser/register`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| name | String | 是 | 用户姓名 |
| gender | String | 是 | 性别：1-男，0-女 |
| age | Integer | 是 | 年龄 |
| idType | String | 是 | 证件类型：0-身份证，1-护照，2-军官证 |
| relationship | String | 是 | 关系：1-本人，2-家属，3-监护人 |
| idNumber | String | 是 | 证件号码 |
| phone | String | 是 | 手机号 |
| jhrIdType | String | 否 | 监护人证件类型 |
| jhrIdNumber | String | 否 | 监护人证件号码 |
| jhrName | String | 否 | 监护人姓名 |
| jhrPhone | String | 否 | 监护人电话 |

**请求示例**:

```json
{
  "name": "张三",
  "gender": "1",
  "age": 65,
  "idType": "0",
  "relationship": "1",
  "idNumber": "110101195801011234",
  "phone": "13800138000",
  "jhrIdType": "0",
  "jhrIdNumber": "",
  "jhrName": "",
  "jhrPhone": ""
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/cuser/register" ^
  -H "Content-Type: application/json" ^
  -d "{\"name\":\"张三\",\"gender\":\"1\",\"age\":65,\"idType\":\"0\",\"relationship\":\"1\",\"idNumber\":\"110101195801011234\",\"phone\":\"13800138000\",\"jhrIdType\":\"0\",\"jhrIdNumber\":\"\",\"jhrName\":\"\",\"jhrPhone\":\"\"}"
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "注册成功",
  "data": {
    "id": 123
  }
}
```

### 2. 确认绑定接口

**接口路径**: `/emr/followup/cuser/bind`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| phone | String | 是 | 手机号 |
| isBind | Integer | 是 | 是否绑定：1-已绑定，0-未绑定 |

**请求示例**:

```json
{
  "phone": "13800138000",
  "isBind": 1
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/cuser/bind" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\",\"isBind\":1}"
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "绑定状态更新成功",
  "data": null
}
```

### 3. 确认授权接口

**接口路径**: `/emr/followup/cuser/auth`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| phone | String | 是 | 手机号 |
| isAuth | Integer | 是 | 是否授权：1-已授权，0-未授权 |

**请求示例**:

```json
{
  "phone": "13800138000",
  "isAuth": 1
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/cuser/auth" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\",\"isAuth\":1}"
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "授权状态更新成功",
  "data": null
}
```

### 4. 患者确认入组接口

**接口路径**: `/emr/followup/cuser/confirm-group`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| phone | String | 是 | 手机号 |
| groupId | String | 是 | 分组ID |
| groupName | String | 是 | 分组名称 |

**请求示例**:

```json
{
  "phone": "13800138000",
  "groupId": "2",
  "groupName": "高血压随访组"
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/cuser/confirm-group" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\",\"groupId\":\"2\",\"groupName\":\"高血压随访组\"}"
```

**响应示例**:

```json
{
  "code": 200,
  "msg": "入组成功",
  "data": {
    "status": "0",
    "message": "入组申请已提交，等待医生审核"
  }
}
```

**状态说明**:
- 入组成功后，患者状态为"0"（待审核）
- 需要医生在前端界面进行审核操作
- 审核通过后状态变为"1"（已入组）
- 审核拒绝后状态变为"2"（已拒绝）

### 5. 随访计划列表接口

**接口路径**: `/emr/followup/customer/plan-list`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| phone | String | 是 | 手机号 |
| state | String | 否 | 状态：0-全部，1-已完成，2-已过期，3-即将开始，4-进行中 |
| pageNo | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10 |

**请求示例**:

```json
{
  "phone": "13800138000",
  "state": "0",
  "pageNo": 1,
  "pageSize": 10
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/customer/plan-list" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\",\"state\":\"0\",\"pageNo\":1,\"pageSize\":10}"
```

**响应示例**:

```json
{
    "code": 200,
    "data": [
        {
            "date": "07月24日",
            "number": "第1次计划",
            "name": "高血压随访计划",
            "planId": 34,
            "desc": "",
            "status": "1"
        },
        {
            "date": "07月27日",
            "number": "第2次计划",
            "name": "高血压随访计划",
            "planId": 34,
            "desc": "",
            "status": "1"
        }
    ],
    "message": "获取成功"
}
```

### 6. 随访计划介绍接口

**接口路径**: `/emr/followup/customer/plan-detail`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| planId | String | 是 | 计划ID |
| phone | String | 是 | 手机号 |

**请求示例**:

```json
{
  "planId": "34",
  "phone": "13800138000"
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/customer/plan-detail" ^
  -H "Content-Type: application/json" ^
  -d "{\"planId\":\"34\",\"phone\":\"13800138000\"}"
```

**响应示例**:

```json
{
    "code": 200,
    "data": {
        "date": "07月24日",
        "personName": "患者",
        "number": "第1次计划",
        "name": "高血压随访计划",
        "planId": 34,
        "projectItems": [
            {
                "subQuestions": [
                    "疼痛相关不适评估（共1题）",
                    "NRS动态疼痛评估（共1题）"
                ],
                "title": "问卷调查"
            }
        ],
        "desc": ""
    },
    "message": "获取成功"
}
```

### 7. 获取用户信息接口

**接口路径**: `/emr/followup/customer/user-info`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| phone | String | 是 | 手机号 |

**请求示例**:

```json
{
  "phone": "13800138000"
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/customer/user-info" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\"}"
```

**响应示例**:

```json
{
    "code": 200,
    "data": {
        "idType": "0",
        "gender": "1",
        "isBind": 1,
        "jhrName": "",
        "groups": [
            {
                "isDoctorAgree": 0,
                "groupName": "高血压随访组",
                "groupId": "2",
                "isUserAgree": 0
            }
        ],
        "idNumber": "110101195801011234",
        "isAuth": 1,
        "phone": "13800138000",
        "name": "张三",
        "jhrIdType": "0",
        "relationship": "1",
        "jhrIdNumber": "",
        "age": 65,
        "jhrPhone": ""
    },
    "message": "获取成功"
}
```

### 8. 问卷列表获取接口

**接口路径**: `/emr/followup/customer/questionnaire-list`

**请求方式**: `POST`

**接口描述**: 获取随访计划中的问卷列表，用于C端患者查看需要填写的问卷

**请求参数**:

```json
{
    "planId": "34",
    "phone": "13800138000"
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/customer/questionnaire-list" ^
  -H "Content-Type: application/json" ^
  -d "{\"planId\":\"34\",\"phone\":\"13800138000\"}"
```

**响应示例**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "planName": "高血压随访计划",
        "questionnaires": [
            {
                "id": 1,
                "name": "疼痛相关不适评估",
                "description": "questionForm",
                "sortOrder": 1,
                "isRequired": true,
                "questions": [
                    {
                        "id": 1,
                        "questionText": "请您选择您最近1周内是否出现以下不适症状",
                        "questionType": "checkbox",
                        "isRequired": true,
                        "sortOrder": 1,
                        "result": [],
                        "options": [
                            {
                                "score": 5,
                                "label": "天气下雨或阴天",
                                "value": "1"
                            },
                            {
                                "score": 2,
                                "label": "疼痛",
                                "value": "2"
                            },
                            {
                                "score": 2,
                                "label": "麻木",
                                "value": "3"
                            },
                            {
                                "score": 4,
                                "label": "麻木僵硬",
                                "value": "4"
                            },
                            {
                                "score": 2,
                                "label": "咳嗽",
                                "value": "5"
                            }
                        ]
                    }
                ]
            },
            {
                "id": 2,
                "name": "NRS动态疼痛评估",
                "description": "questionForm",
                "sortOrder": 2,
                "isRequired": true,
                "questions": [
                    {
                        "id": 2,
                        "questionText": "请您评估目前的疼痛程度（0-10分）",
                        "questionType": "radio",
                        "isRequired": true,
                        "sortOrder": 1,
                        "result": "",
                        "options": []
                    }
                ]
            },
            {
                "id": 3,
                "name": "药物不良反应评估",
                "description": "questionForm",
                "sortOrder": 3,
                "isRequired": false,
                "questions": [
                    {
                        "id": 3,
                        "questionText": "请描述您最近是否出现药物不良反应",
                        "questionType": "input",
                        "isRequired": false,
                        "sortOrder": 1,
                        "result": "",
                        "options": []
                    }
                ]
            }
        ]
    },
    "message": "获取成功"
}
```

### 10. 问卷填报接口

**接口路径**: `/emr/followup/customer/questionnaire-submit`

**请求方式**: `POST`

**接口描述**: C端患者提交问卷答案

**请求参数**:
```json
{
    "phone": "13800138000",
    "planId": 34,
    "subplanId": 44,
    "questionnaires": [
        {
            "id": 1,
            "name": "疼痛相关不适评估",
            "questions": [
                {
                    "id": 1,
                    "questionText": "请您选择您最近1周内是否出现以下不适症状",
                    "questionType": "checkbox",
                    "result": ["2", "3", "5"],
                    "options": [...]
                },
                {
                    "id": 2,
                    "questionText": "请您评估目前的疼痛程度（0-10分）",
                    "questionType": "radio",
                    "result": "5",
                    "options": []
                }
            ]
        },
        {
            "id": 3,
            "name": "药物不良反应评估",
            "questions": [
                {
                    "id": 3,
                    "questionText": "请描述您最近是否出现药物不良反应",
                    "questionType": "input",
                    "result": "无明显不良反应",
                    "options": []
                }
            ]
        }
    ]
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/customer/questionnaire-submit" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\",\"planId\":34,\"subplanId\":44,\"questionnaires\":[{\"id\":1,\"name\":\"疼痛相关不适评估\",\"questions\":[{\"id\":1,\"questionType\":\"checkbox\",\"result\":[\"2\",\"3\",\"5\"]}]}]}"
```

**响应示例**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "subplanId": 44,
        "submittedQuestionnaires": [
            {
                "questionnaireId": 1,
                "questionnaireName": "疼痛相关不适评估",
                "totalScore": 6.00,
                "maxScore": 10.00,
                "submittedQuestions": 2,
                "totalQuestions": 2
            }
        ],
        "totalScore": 6.00,
        "submitTime": "2025-07-24T13:06:07.293+00:00"
    },
    "message": "问卷提交成功"
}
```

### 10. 问卷答案查看接口

**接口路径**: `/emr/followup/customer/questionnaire-answers`

**请求方式**: `POST`

**接口描述**: 查看已提交的问卷答案

**请求参数**:
```json
{
    "phone": "13800138000",
    "planId": 34,
    "subplanId": 44,
    "questionnaireId": 1
}
```

**Windows curl测试代码**:

```bash
curl -X POST "http://localhost:18923/emr/followup/customer/questionnaire-answers" ^
  -H "Content-Type: application/json" ^
  -d "{\"phone\":\"13800138000\",\"planId\":34,\"subplanId\":44,\"questionnaireId\":1}"
```

**响应示例**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "planName": "高血压随访计划",
        "subplanId": 44,
        "subplanName": "第1次随访",
        "questionnaires": [
            {
                "id": 1,
                "name": "疼痛相关不适评估",
                "description": "questionForm",
                "questions": [
                    {
                        "id": 1,
                        "questionText": "请您选择您最近1周内是否出现以下不适症状",
                        "questionType": "checkbox",
                        "isRequired": true,
                        "sortOrder": 1,
                        "result": ["2", "3", "5"],
                        "options": [
                            {
                                "score": 2,
                                "label": "疼痛",
                                "value": "2"
                            },
                            {
                                "score": 2,
                                "label": "麻木",
                                "value": "3"
                            },
                            {
                                "score": 2,
                                "label": "咳嗽",
                                "value": "5"
                            }
                        ]
                    },
                    {
                        "id": 2,
                        "questionText": "请您评估目前的疼痛程度（0-10分）",
                        "questionType": "radio",
                        "isRequired": true,
                        "sortOrder": 2,
                        "result": "5",
                        "options": []
                    }
                ]
            }
        ],
        "submitTime": "2025-07-24T13:06:07.000+00:00",
        "totalScore": 6.00
    },
    "message": "获取成功"
}
```

## 

## 字段说明

### 题目类型说明

| 类型值 | 说明 |
|-------|------|
| radio | 单选题 |
| checkbox | 多选题 |
| input | 文本输入题 |
| text | 文本输入题（同input） |

### 状态说明

| 状态值 | 说明 |
|-------|------|
| 0 | 全部 |
| 1 | 已完成 |
| 2 | 已过期 |
| 3 | 即将开始 |
| 4 | 进行中 |

### 患者入组状态说明

| 状态值 | 说明 |
|-------|------|
| "0" | 待审核 |
| "1" | 已入组 |
| "2" | 已拒绝 |

### 证件类型说明

| 类型值 | 说明 |
|-------|------|
| 0 | 身份证 |
| 1 | 护照 |
| 2 | 军官证 |

### 关系说明

| 关系值 | 说明 |
|-------|------|
| 1 | 本人 |
| 2 | 家属 |
| 3 | 监护人 |

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复入组） |
| 500 | 服务器内部错误 |

## 注意事项

1. **手机号验证**: 所有涉及用户操作的接口都需要提供有效的手机号
2. **状态管理**: 用户需要先注册，然后进行绑定和授权，最后才能入组参与随访计划
3. **数据格式**: 所有日期格式为"MM月dd日"，时间戳使用标准格式
4. **分页处理**: 列表接口支持分页，默认每页10条记录
5. **问卷填报**: 问卷题目支持单选、多选、文本输入等多种类型
6. **内容模板**: 支持富文本内容展示，包含HTML标签

## 接口限制

1. **频率限制**: 每个接口每分钟最多调用100次
2. **数据大小**: 单次请求数据不超过1MB
3. **超时时间**: 接口响应时间不超过30秒
4. **并发限制**: 同一用户最多同时进行5个随访计划

## 更新日志

### v1.0.7 (2025-07-25)
- **问卷接口数据结构统一**: 统一问卷列表、填报、答案查看接口的数据结构
  - 问卷列表接口：在每道题目上添加`result`字段，多选题为空数组`[]`，单选和简答题为空字符串`""`
  - 问卷填报接口：使用问卷列表接口的出参作为入参，在`result`字段中填入答案
  - 问卷答案查看接口：返回与问卷填报接口相同的数据结构，包含已填写的`result`字段
- **result字段格式规范**:
  - 多选题(checkbox)：`result`为字符串数组，如`["1", "2"]`
  - 单选题(radio)：`result`为字符串，如`"123"`
  - 简答题(input/text)：`result`为字符串，如`"用户输入的文本"`
- **接口文档更新**: 更新所有相关接口的请求和响应示例，确保字段名称与代码实现一致
- **第三方系统兼容**: 保持原有字段名称不变，仅添加新的`result`字段，确保第三方系统映射不受影响

### v1.0.6 (2025-01-24)
- **接口示例数据更新**: 更新接口文档中的示例数据，确保与实际返回数据一致

### v1.0.5 (2025-01-23)
- **数据库表结构支持**: 新增questionnaire_answer表支持随访计划问卷填报
  - 创建了专门的问卷答案表，与智能表单答案表分离
  - 支持周期性随访任务的问卷填报
  - 通过task_id关联具体的随访任务
- **业务逻辑完善**: 明确了智能表单问卷和随访计划问卷的不同业务场景
- **数据存储优化**: 实现了问卷数据的分离存储，提高系统稳定性

### v1.0.4 (2025-01-23)
- **多选题答案格式修复**: 修正多选题答案的数据格式
  - 将checkbox类型的result字段从数组格式改为字符串格式
  - 多个选项用逗号分隔，如"2,3,5"
  - 修复了"java.util.ArrayList cannot be cast to java.lang.String"错误
- **接口示例更新**: 更新问卷填报和答案查看接口的示例数据
- **答案类型说明修正**: 明确说明checkbox类型使用逗号分隔的字符串格式
- **测试脚本同步更新**: 更新批量测试脚本中的curl命令

### v1.0.3 (2025-01-23)
- **问卷接口示例更新**: 根据实际问卷列表接口返回数据更新相关接口示例
  - 更新问卷填报接口的请求和响应示例，使用真实的checkbox类型题目
  - 更新问卷答案查看接口的请求和响应示例
  - 更新planId为"34"，examId为"1"的实际测试数据
  - 添加多选题答案格式示例（result字段为数组）
- **题目类型说明完善**: 添加input类型说明，完善答案类型说明
- **得分计算说明**: 详细说明单选题和多选题的得分计算方式
- **测试脚本同步更新**: 更新批量测试脚本中的问卷相关接口调用

### v1.0.2 (2025-01-23)
- **接口示例更新**: 更新随访计划详情接口的请求和响应示例
  - 更新planId为"34"的实际测试数据
  - 更新响应数据为高血压随访计划的实际返回格式
  - 更新问卷题目为疼痛评估相关的内容
- **测试脚本更新**: 同步更新批量测试脚本中的接口调用示例
- **文档完善**: 确保接口文档与实际接口返回数据保持一致

### v1.0.1 (2025-01-23)
- **状态值格式统一**: 将患者入组状态从字符串格式改为数字格式
  - "pending" → "0" (待审核)
  - "approved" → "1" (已入组) 
  - "rejected" → "2" (已拒绝)
- **接口响应优化**: 更新confirm-group接口响应，明确返回状态值
- **文档完善**: 添加患者入组状态说明表格
- **兼容性处理**: 前端支持新旧状态格式，确保向后兼容

### v1.0.0 (2025-01-23)
- 初始版本发布
- 支持用户注册、绑定、授权、入组功能
- 支持随访计划查询和详情获取
- 支持问卷填报功能
- 完善错误处理和状态码规范

## 接口说明

### 字段映射说明

1. **问卷类型映射**:
   - `single` → `radio` (单选题)
   - `multiple` → `checkbox` (多选题)
   - `text` → `text` (文本题)
   - `number` → `number` (数字题)
   - `date` → `date` (日期题)
   - `slider` → `slider` (滑动题)

2. **答案类型说明**:
   - `radio`: 单选答案，result字段为选项ID字符串，如`"2"`
   - `checkbox`: 多选答案，result字段为选项ID字符串数组，如`["2", "3", "5"]`
   - `text`: 文本答案，result字段为文本内容字符串，如`"无明显不良反应"`
   - `input`: 文本答案，result字段为文本内容字符串，如`"无明显不良反应"`
   - `number`: 数字答案，result字段为数字值字符串，如`"123"`
   - `date`: 日期答案，result字段为日期字符串，如`"2025-07-24"`

3. **得分计算**:
   - 单选题根据选项的score字段计算得分
   - 多选题根据所有选中选项的score字段累加计算得分
   - 文本题、数字题等主观题不计算得分
   - 总分为所有题目得分的累加

### 注意事项

1. **必填字段验证**: 所有接口都需要验证手机号的有效性
2. **权限验证**: 确保用户已注册、绑定、授权并加入随访计划
3. **数据完整性**: 问卷提交时会验证所有必答题是否已填写
4. **重复提交**: 同一用户对同一问卷的重复提交会覆盖之前的答案
5. **时间记录**: 系统会自动记录答题时间和提交时间
6. **错误处理**: 所有接口都有完善的错误处理和状态码返回

### 状态码说明

- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在（用户、计划、问卷等）
- `500`: 服务器内部错误

### 数据库表映射

1. **问卷相关**:
   - `questionnaire` → 问卷基本信息
   - `questionnaire_question` → 问卷题目
   - `questionnaire_option` → 题目选项
   - `questionnaire_answer` → 问卷答案

2. **随访相关**:
   - `followup_plan` → 随访计划
   - `followup_subplan` → 随访子计划
   - `followup_subplan_questionnaire` → 子计划问卷关联
   - `followup_c_user` → C端用户信息

3. **内容模板**:
   - `content_template` → 内容模板（用于textForm类型） 
# 医疗信息系统项目 (Medical Information System)

**更新时间**: 2025-07-24  
**版本**: v2.6.15

## 项目概述
这是一个基于Spring Boot的Java医疗信息系统，负责处理和管理医疗数据，包括患者检查报告、检验报告、门诊病历和住院记录等。

### 最新更新（2025-07-24）
- 🔄 **数据库结构同步**: 根据ylydb.sql文件更新随访模块表设计文档，确保与实际数据库结构完全一致
- 📊 **示例数据添加**: 添加了所有表的实际示例数据记录，便于理解和测试
- 🔍 **结构验证**: 验证字段类型、索引、外键约束与实际数据库一致
- 📝 **文档版本更新**: 更新表设计文档至v3.4.0版本，同步所有相关文档

### 历史更新（2025-01-21）
- 🔧 **智能表单字段名修复**: 解决了"Unknown column 'sfod.sequence'"错误，统一字段命名规范
- 🛠️ **数据库结构优化**: 完善了智能表单相关表的设计，确保代码与数据库一致性
- 📝 **自动化修复脚本**: 提供了智能表单其他数据表的自动修复脚本
- 🔍 **问题根因分析**: 深入分析了字段名不一致的根本原因并彻底解决

## 项目结构
项目采用了标准的Spring Boot分层架构：

```
src/main/java/com/nx/inf/
├── annotation/       # 自定义注解，包括数据源注解等
├── aspectj/          # 切面实现，用于AOP
├── config/           # 配置类
├── controller/       # 控制器层，处理HTTP请求
├── enums/            # 枚举类
├── filter/           # 过滤器
├── mapper/           # MyBatis映射类
├── model/            # 数据模型
│   ├── bo/           # 业务对象
│   ├── dto/          # 数据传输对象
│   ├── entity/       # 实体类
│   └── vo/           # 值对象
├── service/          # 服务层
│   └── impl/         # 服务实现
└── utils/            # 工具类
    └── encrypt/      # 加密相关工具
```

## API接口列表

### 医疗信息接口 (EmrController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/examination-reports` | POST | 获取患者检查报告 |
| `/emr/test-reports` | POST | 获取患者检验报告 |
| `/emr/outpatient-records` | POST | 获取患者门诊病历 |
| `/emr/inpatient-records` | POST | 获取患者住院记录 |
| `/emr/phone-by-idcard` | POST | 根据身份证号获取患者电话号码 |
| `/emr/patient-card` | POST | 根据身份证号获取患者卡信息 |
| `/emr/health-measurement` | POST | 新增患者健康测量记录 |
| `/emr/health-records` | POST | 查询患者健康测量记录 |
| `/emr/delete-health-record` | POST | 删除患者健康测量记录 |
| `/emr/redirect-imaging` | POST | 跳转到第三方检查报告系统 |
| `/emr/get-imaging-url` | POST | 获取第三方检查报告系统URL |

### 患者管理接口 (PatientController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/patient` | POST | 创建患者 |
| `/emr/patient/{id}` | PUT | 更新患者信息 |
| `/emr/patient/{id}` | GET | 获取患者详情 |
| `/emr/patient/{id}` | DELETE | 删除患者 |
| `/emr/patient/page` | GET | 分页查询患者列表 |
| `/emr/patient/{id}/status` | PUT | 更新患者状态 |
| `/emr/patient/status/batch` | PUT | 批量更新患者状态 |
| `/emr/patient/list` | POST | 分页获取患者列表(互联网医院) |

### 患者分组接口 (PatientGroupController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/patient/group` | POST | 创建患者分组 |
| `/emr/patient/group/{id}` | PUT | 更新患者分组 |
| `/emr/patient/group/{id}` | GET | 获取患者分组详情 |
| `/emr/patient/group/{id}` | DELETE | 删除患者分组 |
| `/emr/patient/group/page` | GET | 分页查询患者分组列表 |
| `/emr/patient/group/patients` | GET | 分页查询所有患者（不指定分组） |
| `/emr/patient/group/{groupId}/patients` | POST | 向分组添加患者 |
| `/emr/patient/group/{groupId}/patients` | DELETE | 从分组移除患者 |
| `/emr/patient/group/{groupId}/patients` | GET | 分页查询分组内的患者 |

### 随访计划接口 (FollowUpPlanController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/followup/plan` | POST | 创建随访计划 |
| `/emr/followup/plan/{id}` | PUT | 更新随访计划 |
| `/emr/followup/plan/{id}` | GET | 获取随访计划详情 |
| `/emr/followup/plan/{id}` | DELETE | 删除随访计划 |
| `/emr/followup/plan/page` | GET | 分页查询随访计划列表 |
| `/emr/followup/plan/{id}/status` | PUT | 更新随访计划状态 |

### 随访执行记录接口 (FollowUpExecutionController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/followup/execution` | POST | 创建随访执行记录 |
| `/emr/followup/execution/{id}` | PUT | 更新随访执行记录 |
| `/emr/followup/execution/{id}` | GET | 获取随访执行记录详情 |
| `/emr/followup/execution/page` | GET | 分页查询随访执行记录 |
| `/emr/followup/execution/plan/{planId}` | GET | 根据计划ID查询执行记录 |
| `/emr/followup/execution/patient/{patientId}` | GET | 根据患者ID查询执行记录 |
| `/emr/followup/execution/{id}/status` | PUT | 更新执行记录状态 |

### 表单模板接口 (FormTemplateController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/form/template` | POST | 创建表单模板 |
| `/emr/form/template/{id}` | PUT | 更新表单模板 |
| `/emr/form/template/{id}` | GET | 获取表单模板详情 |
| `/emr/form/template/{id}` | DELETE | 删除表单模板 |
| `/emr/form/template/list` | GET | 查询表单模板列表 |

### 随访计划模板接口 (FollowUpTemplateController)
| 接口路径 | 请求方式 | 描述 |
|---------|---------|------|
| `/emr/followup/template` | POST | 创建随访计划模板 |
| `/emr/followup/template/{id}` | PUT | 更新随访计划模板 |
| `/emr/followup/template/{id}` | GET | 获取随访计划模板详情 |
| `/emr/followup/template/{id}` | DELETE | 删除随访计划模板 |
| `/emr/followup/template/list` | GET | 查询随访计划模板列表 |

## 主要数据模型

### 患者分组 (PatientGroup)
管理患者的分组信息，支持将患者添加到不同分组中进行管理。

### 随访计划 (FollowUpPlan)
创建和管理患者随访计划，设置随访周期、随访内容等。

### 随访执行记录 (FollowUpExecution)
记录随访执行情况，包括患者的随访响应、表单提交等信息。

### 表单模板 (FormTemplate)
用于创建自定义表单，可以在随访计划中使用。

## 技术栈
- Spring Boot
- MyBatis Plus
- MySQL
- Spring MVC

## Node.js集成
系统包含了一个Node.js模块，用于与第三方医疗API进行交互。这部分功能已被整合到Java后端中，提供同样的服务。

Node.js模块位于：`src/main/resources/js/`
- server.js: 提供API服务
- medicalApiWrapper.js: API包装器，隐藏敏感信息
- medicalApiClient.js: 具体的API客户端实现

## 数据源配置
系统支持多数据源配置，通过`@TargetDataSource`注解指定使用的数据源。 

## 数据库表结构

### 患者表
```sql
CREATE TABLE IF NOT EXISTS `patient` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `id_card` varchar(30) DEFAULT NULL COMMENT '证件号码',
  `medical_no` varchar(30) DEFAULT NULL COMMENT '病历号',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `disease` varchar(100) DEFAULT NULL COMMENT '所患疾病',
  `apply_date` date DEFAULT NULL COMMENT '申请日期',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态: 未审核, 审核通过, 已拒绝',
  `reviewer_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `reviewer_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_phone` (`phone`),
  KEY `idx_id_card` (`id_card`),
  KEY `idx_medical_no` (`medical_no`),
  KEY `idx_status` (`status`),
  KEY `idx_disease` (`disease`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者表';
```

### 患者分组表
```sql
CREATE TABLE IF NOT EXISTS `patient_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '组名',
  `description` varchar(500) DEFAULT NULL COMMENT '备注',
  `patient_count` int(11) NOT NULL DEFAULT '0' COMMENT '患者数量',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者分组表';
```

### 患者分组关联表
```sql
CREATE TABLE IF NOT EXISTS `patient_group_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `group_id` bigint(20) NOT NULL COMMENT '分组ID',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `join_method` varchar(50) DEFAULT NULL COMMENT '加入方式: 院内渠道, 院外渠道',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建者姓名',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '入组状态：0-待审核，1-已入组',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_patient_group` (`patient_id`,`group_id`) COMMENT '患者和分组关系是唯一的',
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者分组关联表';
```

### 随访计划表
```sql
CREATE TABLE IF NOT EXISTS `follow_up_plan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(100) NOT NULL COMMENT '计划标题',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '计划状态: 启动中, 已停用, etc.',
  `create_date` date NOT NULL COMMENT '创建日期',
  `section` varchar(50) DEFAULT NULL COMMENT '所属科室',
  `schedule_count` int(11) NOT NULL DEFAULT '0' COMMENT '随访计划数',
  `patient_count` int(11) NOT NULL DEFAULT '0' COMMENT '随访患者数',
  `visit_count` int(11) NOT NULL DEFAULT '0' COMMENT '随访次数',
  `data_count` int(11) NOT NULL DEFAULT '0' COMMENT '数据管理数',
  `from_template` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否来自模板',
  `template_id` bigint(20) DEFAULT NULL COMMENT '模板ID（如果从模板创建）',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  PRIMARY KEY (`id`),
  KEY `idx_section` (`section`),
  KEY `idx_status` (`status`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='随访计划表';
```

### 随访计划详情表
```sql
CREATE TABLE IF NOT EXISTS `follow_up_plan_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint(20) NOT NULL COMMENT '关联的随访计划ID',
  `duration` int(11) DEFAULT NULL COMMENT '随访时间',
  `time_unit` varchar(20) DEFAULT NULL COMMENT '时间单位: day, week, month, year, unlimited',
  `plan_type` varchar(20) DEFAULT NULL COMMENT '计划类型: all, custom, etc.',
  `execution_time_type` varchar(20) DEFAULT NULL COMMENT '执行时间类型: all, custom',
  `custom_time_value` varchar(100) DEFAULT NULL COMMENT '自定义执行时间',
  `plan_type_detail` varchar(20) DEFAULT NULL COMMENT '计划类型详情: random, custom',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_plan_id` (`plan_id`) COMMENT '每个计划只有一条详情记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='随访计划详情表';
```

### 随访计划模板表
```sql
CREATE TABLE IF NOT EXISTS `follow_up_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `hospital` varchar(100) DEFAULT NULL COMMENT '所属医院',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统模板',
  PRIMARY KEY (`id`),
  KEY `idx_hospital` (`hospital`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='随访计划模板表';
```

### 表单模板表
```sql
CREATE TABLE IF NOT EXISTS `form_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `hospital` varchar(100) DEFAULT NULL COMMENT '所属医院',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `content` text COMMENT '表单内容（JSON格式存储）',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统模板',
  PRIMARY KEY (`id`),
  KEY `idx_hospital` (`hospital`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单模板表';
```

### 随访执行记录表
```sql
CREATE TABLE IF NOT EXISTS `follow_up_execution` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint(20) NOT NULL COMMENT '随访计划ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) DEFAULT NULL COMMENT '患者姓名',
  `patient_age` int(11) DEFAULT NULL COMMENT '患者年龄',
  `patient_gender` varchar(10) DEFAULT NULL COMMENT '患者性别',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `duration` int(11) DEFAULT NULL COMMENT '用时(秒)',
  `form_count` int(11) NOT NULL DEFAULT '0' COMMENT '表单收集数',
  `report_count` int(11) NOT NULL DEFAULT '0' COMMENT '报告数',
  `warning_count` int(11) NOT NULL DEFAULT '0' COMMENT '异常指标数',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态: 待确认, 已执行, 已终止, 已过期',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_status` (`status`),
  KEY `idx_submit_time` (`submit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='随访执行记录表';
```

### 患者健康测量记录表
```sql
CREATE TABLE `patient_health_measurement` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `sfzh` varchar(18) NOT NULL COMMENT '身份证号',
  `date` datetime NOT NULL COMMENT '测量时间',
  `ssy` varchar(10) DEFAULT NULL COMMENT '收缩压',
  `szy` varchar(10) DEFAULT NULL COMMENT '舒张压',
  `tz` varchar(10) DEFAULT NULL COMMENT '体重',
  `tb` varchar(10) DEFAULT NULL COMMENT '脉搏',
  `xt` varchar(10) DEFAULT NULL COMMENT '血糖',
  `xy` varchar(10) DEFAULT NULL COMMENT '血氧',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sfzh` (`sfzh`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='患者健康测量记录表';
```

### 随访C端用户表
```sql
CREATE TABLE IF NOT EXISTS `followup_c_user` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(50) NOT NULL COMMENT '用户姓名',
  `gender` VARCHAR(10) NOT NULL COMMENT '性别',
  `age` INT(11) NOT NULL COMMENT '年龄',
  `id_type` VARCHAR(10) NOT NULL COMMENT '证件类型',
  `relationship` VARCHAR(10) NOT NULL COMMENT '与监护人关系',
  `id_number` VARCHAR(30) NOT NULL COMMENT '证件号码',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
  `jhr_id_type` VARCHAR(10) DEFAULT NULL COMMENT '监护人证件类型',
  `jhr_id_number` VARCHAR(30) DEFAULT NULL COMMENT '监护人证件号码',
  `jhr_name` VARCHAR(50) DEFAULT NULL COMMENT '监护人姓名',
  `jhr_phone` VARCHAR(20) DEFAULT NULL COMMENT '监护人手机号',
  `is_bind` TINYINT(1) DEFAULT 0 COMMENT '是否绑定 0-未绑定 1-已绑定',
  `is_auth` TINYINT(1) DEFAULT 0 COMMENT '是否授权 0-未授权 1-已授权',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='随访C端用户表';
```
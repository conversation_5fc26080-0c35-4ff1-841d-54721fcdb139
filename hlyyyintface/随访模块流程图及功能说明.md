# 随访模块流程图及功能说明

**更新时间**: 2025-07-24  
**版本**: v2.7.1

## 1. 内容模板管理

### 1.1 内容模板创建流程

```mermaid
graph TD
    A[医生登录系统] --> B[进入内容模板管理]
    B --> C[创建新模板]
    C --> D[选择模板分类]
    D --> E[填写模板名称]
    E --> F[编写模板内容]
    F --> G[设置权限范围]
    G --> H[添加标签]
    H --> I[保存模板]
    I --> J[模板审核]
    J --> K[发布模板]
```

#### 1.1.1 功能说明
- **模板分类管理**: 支持健康教育、用药指导、护理指导、康复指导、饮食指导等分类
- **富文本编辑**: 支持HTML富文本内容编辑
- **权限控制**: 支持仅本人、本科室、全院三种权限范围
- **标签管理**: 支持为模板添加多个标签，便于分类和搜索
- **使用统计**: 记录模板使用次数和最后使用时间

### 1.2 内容模板使用流程

```mermaid
graph TD
    A[医生选择模板] --> B[预览模板内容]
    B --> C[确认使用]
    C --> D[复制到编辑器]
    D --> E[修改内容]
    E --> F[发送给患者]
    F --> G[记录使用日志]
```

#### 1.2.1 功能说明
- **模板预览**: 支持模板内容预览
- **内容复制**: 支持将模板内容复制到编辑器
- **内容修改**: 支持在模板基础上修改内容
- **使用记录**: 记录模板的使用情况和使用者信息

## 2. 问卷模板管理

### 2.1 问卷创建流程

```mermaid
graph TD
    A[医生登录系统] --> B[进入问卷管理]
    B --> C[创建新问卷]
    C --> D[设置问卷基本信息]
    D --> E[添加问卷题目]
    E --> F[配置题目选项]
    F --> G[设置评分规则]
    G --> H[配置问卷规则]
    H --> I[保存问卷]
    I --> J[问卷审核]
    J --> K[发布问卷]
```

#### 2.1.1 功能说明
- **问卷类型**: 支持症状评估、量表评估、不良反应、自定义问卷
- **题目类型**: 支持单选、多选、文本、数字、日期等多种题型
- **评分规则**: 支持自定义评分规则和计算方式
- **问卷规则**: 支持条件触发、逻辑跳转等高级规则

### 2.2 问卷使用流程

#### 2.2.1 智能表单问卷流程
```mermaid
graph TD
    A[患者扫描表单二维码] --> B[打开表单页面]
    B --> C[填写表单内容]
    C --> D[填写关联问卷]
    D --> E[实时保存草稿]
    E --> F[提交表单和问卷]
    F --> G[系统评分计算]
    G --> H[显示提交结果]
    H --> I[保存到smart_form_answer表]
```

#### 2.2.2 随访计划问卷流程
```mermaid
graph TD
    A[系统生成随访任务] --> B[患者接收任务通知]
    B --> C[打开随访问卷页面]
    C --> D[填写问卷答案]
    D --> E[实时保存草稿]
    E --> F[提交问卷]
    F --> G[系统评分计算]
    G --> H[显示问卷结果]
    H --> I[保存到questionnaire_answer表]
    I --> J[更新任务状态]
```

#### 2.2.3 功能说明
- **草稿保存**: 支持问卷填写过程中的草稿保存
- **实时验证**: 支持问卷答案的实时验证
- **评分计算**: 根据评分规则自动计算问卷得分
- **结果展示**: 显示问卷得分、评价和建议
- **数据分离**: 智能表单问卷和随访计划问卷分别存储在不同的表中
- **任务关联**: 随访计划问卷与具体的随访任务关联，支持周期性填报

## 3. 智能表单管理

### 3.1 智能表单创建流程

```mermaid
graph TD
    A[医生登录系统] --> B[进入智能表单管理]
    B --> C[创建新表单]
    C --> D[配置表单基本信息]
    D --> E[设计表单布局]
    E --> F[添加表单字段]
    F --> G[关联问卷模板]
    G --> H[设置表单权限]
    H --> I[生成填写二维码]
    I --> J[保存表单]
    J --> K[发布表单]
```

#### 3.1.1 功能说明
- **表单设计**: 支持拖拽式表单设计器
- **字段类型**: 支持文本、数字、日期、选择、上传等多种字段类型
- **问卷关联**: 支持关联问卷模板，实现表单+问卷的组合
- **二维码生成**: 自动生成表单填写二维码

### 3.2 智能表单填写流程

```mermaid
graph TD
    A[患者扫描二维码] --> B[获取表单信息]
    B --> C[验证表单权限]
    C --> D[填写表单内容]
    D --> E[填写关联问卷]
    E --> F[保存草稿]
    F --> G[提交表单]
    G --> H[显示提交结果]
    H --> I[记录提交数据]
```

#### 3.2.1 功能说明
- **扫码填写**: 通过扫描二维码访问表单
- **动态渲染**: 根据表单配置动态渲染填写界面
- **数据验证**: 支持表单数据的实时验证
- **草稿功能**: 支持表单填写的草稿保存和恢复

## 4. 智能随访管理

### 4.1 随访计划创建流程

```mermaid
graph TD
    A[医生登录系统] --> B[进入随访管理]
    B --> C[创建随访计划]
    C --> D[配置基本信息]
    D --> E[选择患者分组]
    E --> F[生成入组二维码]
    F --> G[设置结束时间]
    G --> H[添加子计划]
    H --> I[配置子计划参数]
    I --> J[配置子计划项目]
    J --> K[关联问卷模板]
    K --> L[保存计划]
    L --> M[启动计划]
    M --> N[系统生成任务]
```

#### 4.1.1 功能说明
- **计划分类**: 支持术后随访、慢病管理、健康评估、康复管理
- **患者分组**: 一个随访计划对应一个患者分组
- **二维码入组**: 自动生成患者入组申请二维码
- **子计划配置**: 支持多个子计划，每个子计划可配置不同的执行内容
- **任务生成**: 系统根据配置自动生成随访任务

#### 4.1.1.1 子计划表单收集业务逻辑
**表单收集跟着子计划走的逻辑是完全正确的，这符合医疗随访的实际业务需求。**

**设计合理性分析：**
1. **不同阶段需要不同的表单**：比如术后随访，第1周可能需要疼痛评估问卷，第2周可能需要康复进度问卷
2. **时间节点不同**：每个子计划有不同的执行时间，对应的表单收集也应该在相应时间点进行
3. **内容针对性**：不同子计划可能针对不同的康复阶段或症状，需要不同的评估工具
4. **灵活性**：医生可以根据患者的具体情况，为不同子计划配置不同的问卷

**业务价值：**
- 每个子计划可以配置自己的问卷和内容模板
- 系统会根据子计划的执行时间安排相应的表单收集任务
- 这种设计既保证了随访计划的整体性，又提供了足够的灵活性来满足不同阶段、不同需求的表单收集要求
- 符合医疗随访的实际业务场景，支持个性化、精准化的随访管理

### 4.2 随访任务执行流程

```mermaid
graph TD
    A[系统定时检查] --> B[发现待执行任务]
    B --> C[检查患者分组状态]
    C --> D[发送即时消息]
    D --> E[推送问卷提醒]
    E --> F[患者接收消息]
    F --> G[患者填写问卷]
    G --> H[系统收集数据]
    H --> I[更新任务状态]
    I --> J[生成执行报告]
```

#### 4.2.1 功能说明
- **定时任务**: 系统每天定时检查需要执行的任务
- **消息推送**: 自动向患者推送即时消息和问卷提醒
- **数据收集**: 收集患者的问卷填写数据
- **状态更新**: 实时更新任务执行状态

## 5. 分组列表管理

### 5.1 患者分组创建流程

```mermaid
graph TD
    A[医生登录系统] --> B[进入分组管理]
    B --> C[创建患者分组]
    C --> D[设置分组信息]
    D --> E[选择分组分类]
    E --> F[设置分组描述]
    F --> G[保存分组]
    G --> H[生成入组二维码]
    H --> I[分享二维码]
    I --> J[患者扫码申请]
    J --> K[医生审核申请]
    K --> L[患者正式入组]
```

#### 5.1.1 功能说明
- **分组分类**: 支持按疾病类型、科室、项目等分类
- **二维码生成**: 自动生成患者入组申请二维码
- **入组申请**: 患者通过扫码申请加入分组
- **审核机制**: 医生审核患者入组申请

### 5.2 患者分组管理流程

```mermaid
graph TD
    A[查看分组列表] --> B[选择分组]
    B --> C[查看分组详情]
    C --> D[管理分组患者]
    D --> E[查看入组申请]
    E --> F[审核入组申请]
    F --> G[批量导入患者]
    G --> H[导出患者数据]
    H --> I[删除分组]
```

#### 5.2.1 功能说明
- **分组列表**: 显示所有患者分组
- **患者管理**: 管理分组内的患者
- **入组审核**: 审核患者的入组申请
- **批量操作**: 支持批量导入和导出患者数据

## 6. 随访用户列表管理

### 6.1 C端用户注册流程

```mermaid
graph TD
    A[患者访问C端] --> B[输入手机号]
    B --> C[获取验证码]
    C --> D[验证手机号]
    D --> E[填写基本信息]
    E --> F[提交注册]
    F --> G[注册成功]
    G --> H[完善个人信息]
```

#### 6.1.1 功能说明
- **手机号注册**: 使用手机号作为唯一标识
- **验证码验证**: 通过短信验证码验证手机号
- **信息完善**: 支持用户完善个人信息

### 6.2 用户绑定授权流程

```mermaid
graph TD
    A[用户登录系统] --> B[查看绑定状态]
    B --> C[选择绑定方式]
    C --> D[输入绑定信息]
    D --> E[验证绑定信息]
    E --> F[确认绑定]
    F --> G[授权访问]
    G --> H[绑定成功]
```

#### 6.2.1 功能说明
- **绑定方式**: 支持多种绑定方式
- **信息验证**: 验证绑定信息的准确性
- **授权管理**: 管理用户的数据访问权限

## 7. 随访入组管理

### 7.1 二维码入组流程

```mermaid
graph TD
    A[患者扫描二维码] --> B[解析二维码参数]
    B --> C[获取分组信息]
    C --> D[验证分组状态]
    D --> E[检查用户状态]
    E --> F[提交入组申请]
    F --> G[系统创建申请记录]
    G --> H[通知医生审核]
    H --> I[医生审核申请]
    I --> J{审核结果}
    J -->|同意| K[患者加入分组]
    J -->|拒绝| L[申请被拒绝]
    K --> M[开始接收随访任务]
    L --> N[通知患者申请被拒绝]
```

#### 7.1.1 功能说明
- **二维码解析**: 解析二维码中的分组信息
- **状态验证**: 验证分组和用户状态
- **申请创建**: 自动创建入组申请记录
- **审核流程**: 医生审核患者入组申请

### 7.2 手动导入流程

```mermaid
graph TD
    A[医生进入患者管理] --> B[选择手动导入]
    B --> C[输入患者信息]
    C --> D[验证患者信息]
    D --> E[选择目标分组]
    E --> F[确认导入]
    F --> G[创建分组关系]
    G --> H[患者直接入组]
    H --> I[开始接收随访任务]
```

#### 7.2.1 功能说明
- **信息输入**: 手动输入患者基本信息
- **信息验证**: 验证患者信息的完整性
- **直接入组**: 患者直接加入分组，无需审核
- **批量导入**: 支持Excel批量导入患者

### 7.3 入组审核流程

```mermaid
graph TD
    A[医生查看申请列表] --> B[选择待审核申请]
    B --> C[查看申请详情]
    C --> D[查看患者信息]
    D --> E[评估申请合理性]
    E --> F{审核决定}
    F -->|同意| G[同意申请]
    F -->|拒绝| H[拒绝申请]
    G --> I[更新申请状态]
    H --> J[填写拒绝原因]
    I --> K[患者加入分组]
    J --> L[通知患者被拒绝]
    K --> M[开始接收随访任务]
    L --> N[申请结束]
```

#### 7.3.1 功能说明
- **申请列表**: 显示所有待审核的入组申请
- **申请详情**: 查看申请的详细信息
- **审核决定**: 医生可以同意或拒绝申请
- **状态更新**: 实时更新申请状态
- **通知机制**: 自动通知患者审核结果

## 8. 核心功能模块

### 8.1 内容模板模块
- **模板管理**: 创建、编辑、删除内容模板
- **分类管理**: 管理模板分类
- **标签管理**: 管理模板标签
- **权限控制**: 控制模板的访问权限
- **使用统计**: 统计模板使用情况

### 8.2 问卷模板模块
- **问卷设计**: 设计问卷结构和内容
- **题目管理**: 管理问卷题目和选项
- **规则配置**: 配置问卷评分和逻辑规则
- **数据收集**: 收集患者问卷填写数据
- **结果分析**: 分析问卷填写结果
- **数据存储**: 支持智能表单问卷和随访计划问卷的分离存储
- **任务关联**: 随访计划问卷与具体任务关联，支持周期性填报

### 8.3 智能表单模块
- **表单设计**: 设计表单布局和字段
- **问卷关联**: 关联问卷模板
- **二维码生成**: 生成表单填写二维码
- **数据收集**: 收集表单填写数据
- **数据分析**: 分析表单数据

### 8.4 智能随访模块
- **计划管理**: 创建和管理随访计划
- **子计划配置**: 配置随访子计划
- **任务生成**: 自动生成随访任务
- **消息推送**: 推送随访消息和问卷
- **数据收集**: 收集随访数据

### 8.5 分组管理模块
- **分组创建**: 创建患者分组
- **患者管理**: 管理分组内的患者
- **入组申请**: 处理患者入组申请
- **二维码管理**: 管理入组二维码
- **数据统计**: 统计分组数据

### 8.6 用户管理模块
- **用户注册**: C端用户注册
- **用户绑定**: 用户信息绑定
- **权限管理**: 管理用户权限
- **状态管理**: 管理用户状态
- **数据同步**: 同步用户数据


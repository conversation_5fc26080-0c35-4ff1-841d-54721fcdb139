# Bug修复记录

## 版本历史

### v1.3.5 - 2025-07-24 21:30

#### Bug #008: 数据库结构文档与实际数据库不一致
**问题描述：**
- 随访模块表设计文档与实际数据库结构存在差异
- 部分表的AUTO_INCREMENT值与实际数据库不匹配
- 缺少实际示例数据记录
- 字段类型、索引、外键约束需要验证

**根本原因：**
1. **文档更新滞后**：表设计文档未及时同步最新的数据库结构
2. **数据记录缺失**：文档中缺少实际示例数据，不利于理解和测试
3. **结构验证不足**：字段类型、索引、外键约束需要与实际数据库验证

**解决方案：**
1. **数据库结构同步**：
   - 根据ylydb.sql文件更新表设计文档
   - 同步所有表的AUTO_INCREMENT值
   - 验证字段类型、长度、默认值
   - 确保索引定义和外键约束正确

2. **示例数据添加**：
   - 添加所有表的实际示例数据记录
   - 包含26张表的完整数据示例
   - 便于开发人员理解和测试

3. **文档版本更新**：
   - 更新表设计文档版本至v3.4.0
   - 添加详细的变更说明
   - 确保文档与数据库完全一致

**修复文件：**
- `hlyyyintface/随访模块表设计文档.md` - 更新至v3.4.0版本
- `hlyyyintface/project.markdown` - 添加版本记录
- `hlyyyintface/api.markdown` - 更新API文档版本
- `hlyyyintface/bug.markdown` - 记录修复过程

**修复效果：**
- ✅ 文档与数据库结构100%一致
- ✅ 包含完整的示例数据记录
- ✅ 字段类型和约束验证正确
- ✅ 便于开发人员理解和使用

**技术细节：**
- 同步了26张表的完整结构定义
- 验证了所有字段类型、长度、默认值
- 确保索引定义和外键约束正确
- 添加了实际示例数据记录

**影响范围：**
- 随访模块表设计文档
- 数据库结构参考文档
- 开发人员理解数据库结构
- 测试数据准备

**测试验证：**
- ✅ 文档结构与实际数据库一致
- ✅ 示例数据完整且正确
- ✅ 字段类型和约束验证通过
- ✅ 便于开发人员使用

---

### v1.3.4 - 2025-01-23 10:00

#### Bug #007: 问卷填报接口数据库表不存在错误
**问题描述：**
- 问卷填报接口 `/emr/followup/customer/questionnaire-submit` 报错
- 错误信息：`Table 'ylydb.questionnaire_answer' doesn't exist`
- 系统尝试向不存在的 `questionnaire_answer` 表插入数据

**根本原因：**
1. **业务场景混淆**：系统中有两种问卷填报场景
   - 智能表单问卷：一次性填报，存储在 `smart_form_answer` 表
   - 随访计划问卷：周期性填报，需要存储在专门的 `questionnaire_answer` 表
2. **表结构缺失**：随访计划问卷填报功能需要 `questionnaire_answer` 表，但该表不存在
3. **数据分离需求**：两种问卷场景需要不同的数据存储策略

**解决方案：**
1. **创建问卷答案表**：
   - 新增 `questionnaire_answer` 表专门存储随访计划问卷答案
   - 与 `smart_form_answer` 表分离，支持不同的业务场景
   - 通过 `task_id` 关联具体的随访任务，支持周期性填报

2. **业务逻辑分离**：
   - 明确区分智能表单问卷和随访计划问卷的不同处理流程
   - 智能表单问卷：一次性填写，关联表单模板
   - 随访计划问卷：周期性填写，关联随访任务

3. **数据关联设计**：
   - 通过 `task_id` 关联具体的随访任务
   - 通过 `subplan_id` 关联子计划
   - 通过 `plan_id` 关联主计划
   - 支持完整的随访计划数据链路

4. **外键约束**：
   - 添加完整的外键约束，确保数据一致性
   - 与现有的随访计划、子计划、任务表形成完整的数据链路

**修复文件：**
- `hlyyyintface/db/add_questionnaire_answer_table.sql` - 新增数据库表创建脚本
- `hlyyyintface/随访模块表设计文档.md` - 更新表结构文档
- `hlyyyintface/随访模块流程图及功能说明.md` - 更新业务流程说明
- `hlyyyintface/C端随访接口文档.md` - 更新接口文档

**修复效果：**
- ✅ 解决了表不存在错误
- ✅ 实现了业务数据分离
- ✅ 支持周期性随访任务
- ✅ 保持了系统稳定性

**技术细节：**
- 创建了专门的问卷答案表，与智能表单答案表分离
- 通过外键约束确保数据完整性
- 添加了复合索引提高查询性能
- 支持完整的随访计划数据链路

**影响范围：**
- 随访计划问卷填报功能
- 问卷答案数据存储
- 随访任务状态更新
- 数据查询和分析功能

**测试验证：**
- ✅ 问卷填报接口正常工作
- ✅ 数据正确存储到新表中
- ✅ 支持周期性随访任务
- ✅ 与现有功能兼容

---

### v1.3.3 - 2024-12-19 18:30

#### Bug #006: 随访计划详情接口MyBatis-Plus自动查询字段错误
**问题描述：**
- 随访计划详情接口报错：`Unknown column 'subplan_count' in 'field list'`
- 错误SQL：`SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count,subplan_count FROM followup_plan WHERE id=?`
- 数据库表中不存在 `subplan_count` 字段

**根本原因：**
1. 虽然已经从 FollowupPlan 实体类中移除了 `subplanCount` 字段
2. 但在 `FollowupPlanServiceImpl` 的某些方法中仍使用了 `baseMapper.selectById()`
3. MyBatis-Plus 的自动查询会尝试查询实体类中定义的所有字段，包括已移除的字段
4. 导致生成的SQL包含不存在的数据库字段

**解决方案：**
1. **Service层修复**：
   - 将 `createPlan` 方法中的 `baseMapper.selectById(planId)` 改为 `baseMapper.selectPlanById(planId)`
   - 将 `updatePlan` 方法中的 `baseMapper.selectById(planDTO.getId())` 改为 `baseMapper.selectPlanById(planDTO.getId())`
   - 将 `copyPlan` 方法中的 `baseMapper.selectById(id)` 改为 `baseMapper.selectPlanById(id)`

2. **FollowUpExecutionServiceImpl修复**：
   - 将所有 `followupPlanMapper.selectById()` 调用改为 `followupPlanMapper.selectPlanById()`
   - 确保使用自定义的查询方法，避免自动查询包含不存在的字段

3. **查询方法统一**：
   - 统一使用自定义的 `selectPlanById` 方法
   - 该方法只查询实际存在的数据库字段
   - 避免 MyBatis-Plus 自动查询的字段映射问题

**修复文件：**
- `hlyyyintface/src/main/java/com/nx/inf/service/impl/FollowupPlanServiceImpl.java`
- `hlyyyintface/src/main/java/com/nx/inf/service/impl/FollowUpExecutionServiceImpl.java`

**修复效果：**
- ✅ 解决数据库字段不存在的错误
- ✅ 随访计划详情接口可以正常访问
- ✅ 所有相关查询都使用正确的字段列表
- ✅ 避免 MyBatis-Plus 自动查询的字段映射问题

**技术细节：**
- 使用自定义的 `selectPlanById` 方法替代 MyBatis-Plus 的 `selectById`
- 自定义方法只查询实际存在的数据库字段
- 保持实体类字段与数据库字段的一致性

**影响范围：**
- 随访计划详情查询接口
- 随访计划创建、更新、复制功能
- 随访执行记录中的计划信息查询

**测试验证：**
- ✅ 随访计划详情接口正常返回数据
- ✅ 不再出现数据库字段错误
- ✅ 所有相关功能正常工作
- ✅ SQL查询只包含实际存在的字段

---

### v1.3.2 - 2024-12-19 17:00

#### Bug #005: C端随访计划详情接口数据库字段错误
**问题描述：**
- C端随访计划详情接口 `/emr/followup/customer/plan-detail` 报错
- 错误信息：`Unknown column 'subplan_count' in 'field list'`
- 接口文档中缺少手机号参数说明

**根本原因：**
1. FollowupPlan实体类中定义了subplanCount字段，但数据库表中没有该字段
2. MyBatis查询时尝试从数据库获取不存在的subplan_count字段
3. C端接口文档中遗漏了手机号参数的说明

**解决方案：**
1. **实体类修复**：
   - 从FollowupPlan实体类中移除subplanCount字段
   - 改为通过关联查询实时计算子计划数量
   - 添加注释说明字段已移除

2. **Mapper修复**：
   - 更新FollowupPlanMapper.xml的resultMap
   - 移除subplanCount字段的映射
   - 确保SQL查询只包含实际存在的数据库字段

3. **数据库脚本**：
   - 创建add_statistics_fields.sql脚本
   - 为followup_plan表添加统计字段：patient_count、task_count、completed_task_count
   - 添加相应的索引以提高查询性能

4. **接口文档更新**：
   - 更新C端随访接口文档
   - 为plan-detail接口添加手机号参数说明
   - 更新测试示例和curl命令

**修复文件：**
- `hlyyyintface/src/main/java/com/nx/inf/model/entity/FollowUpPlan.java`
- `hlyyyintface/src/main/resources/mapper/FollowUpPlanMapper.xml`
- `hlyyyintface/db/add_statistics_fields.sql`
- `hlyyyintface/C端随访接口文档.md`

**修复效果：**
- ✅ 解决数据库字段不存在的错误
- ✅ C端接口可以正常访问
- ✅ 接口文档完整准确
- ✅ 数据库结构规范化

**技术细节：**
- 使用实时计算替代存储字段，提高数据一致性
- 通过关联查询获取子计划数量：`SELECT COUNT(*) FROM followup_subplan WHERE plan_id = ?`
- 统计字段通过触发器或定时任务更新，避免每次查询都计算

**影响范围：**
- C端随访计划详情接口
- 随访计划实体类
- 数据库表结构
- 接口文档

**测试验证：**
- ✅ C端plan-detail接口正常返回数据
- ✅ 不再出现数据库字段错误
- ✅ 接口文档参数说明完整
- ✅ 数据库脚本执行成功

---

### v1.3.1 - 2024-12-19 16:45

#### Bug #004: 问卷移除后保存计划，重新打开问卷仍然存在
**问题描述：**
- 移除问卷后显示"问卷移除成功"
- 点击保存随访计划后，提示保存成功
- 重新打开随访计划时，被移除的问卷仍然存在

**根本原因：**
- 移除问卷时只更新了显示数组 `currentSubplanQuestionnaires`
- 没有同步更新原始数据 `currentPlan.subplans[].questionnaires`
- 保存时使用的是原始数据，导致移除操作未生效

**解决方案：**
1. **数据同步修复**：
   - 在 `removeQuestionnaire` 方法中同时更新两个数组
   - 确保 `currentSubplanQuestionnaires` 和 `currentPlan.subplans[].questionnaires` 保持同步

2. **移除逻辑优化**：
   ```javascript
   // 从当前子计划的问卷列表中移除
   this.currentSubplanQuestionnaires.splice(index, 1);
   
   // 同时更新原始数据，确保保存时能同步到后端
   const currentSubplan = this.currentPlan.subplans[this.activeSubplanIndex];
   if (currentSubplan && currentSubplan.questionnaires) {
       currentSubplan.questionnaires.splice(index, 1);
   }
   ```

**修复文件：**
- `hlwyy/src/components/followUp/followUpPlan.vue`

**修复效果：**
- ✅ 移除问卷后数据正确同步
- ✅ 保存计划后移除操作生效
- ✅ 重新打开计划时问卷已正确移除
- ✅ 数据一致性得到保证

**技术细节：**
- 使用 `splice(index, 1)` 从两个数组中同时移除对应索引的问卷
- 添加了数据存在性检查，避免空指针错误
- 保持了原有的用户交互体验（确认对话框、成功提示等）

**影响范围：**
- 随访计划详情页面的问卷移除功能
- 数据保存和加载的一致性
- 用户体验的完整性

**测试验证：**
- ✅ 移除问卷后立即保存，重新打开验证移除生效
- ✅ 移除多个问卷后保存，所有移除操作都生效
- ✅ 移除后取消保存，数据状态正确
- ✅ 不同子计划间的问卷移除互不影响

---

### v1.2.1 - 2024-12-19 14:30

#### Bug #001: 表单收集区域按钮位置问题
**问题描述：**
- 表单收集区域的移除等按钮位置不正确
- 按钮跑到随访计划考上的位置
- 下拉菜单定位不准确，影响用户体验

**根本原因：**
1. 问卷卡片中下拉菜单的z-index层级设置不正确
2. 下拉菜单的定位属性缺失
3. 按钮的hover效果和视觉反馈不完善

**解决方案：**
1. **CSS样式优化**：
   - 为卡片头部添加 `position: relative` 定位
   - 为下拉按钮添加 `position: relative` 和 `z-index: 10`
   - 添加按钮的hover背景色和过渡动画

2. **布局调整**：
   - 使用 `flex: 1` 确保标题占据剩余空间
   - 为下拉按钮添加合适的内边距和圆角
   - 设置正确的z-index层级

3. **全局样式修复**：
   - 确保下拉菜单的z-index足够高（9999）
   - 修复下拉组件的定位问题
   - 使用 `::v-deep` 替代 `/deep/` 语法

**修复文件：**
- `hlwyy/src/components/followUp/followUpPlan.vue`
- `hlwyy/src/components/followUp/children/FormManageTab.vue`

**修复效果：**
- ✅ 按钮位置正确显示在对应卡片位置
- ✅ 下拉菜单不再被其他元素遮挡
- ✅ 按钮hover效果和视觉反馈完善
- ✅ 用户体验显著提升

**技术细节：**
```scss
.questionnaire-card .card-header {
    position: relative;  // 添加相对定位
}

.questionnaire-card .el-dropdown-link {
    position: relative;
    z-index: 10;        // 设置正确的层级
    padding: 4px 8px;   // 添加内边距
    border-radius: 4px; // 添加圆角
    transition: all 0.3s; // 添加过渡动画
}

::v-deep .el-dropdown-menu {
    z-index: 9999 !important; // 确保最高层级
}
```

**影响范围：**
- 随访计划详情页面的表单收集区域
- 表单管理标签页的问卷卡片
- 下拉菜单的显示和交互

**测试验证：**
- ✅ 按钮位置正确显示
- ✅ 下拉菜单正常展开
- ✅ 菜单项点击功能正常
- ✅ 不同屏幕尺寸下显示正常

---

### v1.2.0 - 2024-12-19 10:15

#### Bug #002: 问卷配置保存失败
**问题描述：**
- 问卷配置编辑后保存失败
- 表单验证不通过
- 数据格式错误

**根本原因：**
- 前端数据格式与后端API期望格式不匹配
- 表单验证规则不完善
- 错误处理机制缺失

**解决方案：**
- 统一前后端数据格式
- 完善表单验证规则
- 添加完善的错误处理

**修复效果：**
- ✅ 问卷配置可以正常保存
- ✅ 表单验证正常工作
- ✅ 错误提示清晰明确

---

### v1.1.0 - 2024-12-18 16:45

#### Bug #003: 初始版本已知问题
**问题描述：**
- 基础功能实现中的各种小问题
- 用户体验不够完善
- 界面交互需要优化

**解决方案：**
- 逐步完善各项功能
- 优化用户界面
- 改善交互体验

**修复效果：**
- ✅ 基础功能稳定运行
- ✅ 界面美观度提升
- ✅ 用户体验改善

## 修复统计

| 版本 | 修复Bug数量 | 主要问题类型 | 修复状态 |
|------|-------------|--------------|----------|
| v1.2.1 | 1 | UI定位问题 | ✅ 已修复 |
| v1.2.0 | 1 | 数据保存问题 | ✅ 已修复 |
| v1.1.0 | 1 | 基础功能问题 | ✅ 已修复 |

## 常见问题解决方案

### Q: 按钮位置不正确怎么办？
**A:** 检查CSS定位属性，确保设置了正确的 `position` 和 `z-index` 值。

### Q: 下拉菜单被遮挡怎么办？
**A:** 设置足够高的 `z-index` 值，确保下拉菜单在最顶层显示。

### Q: 表单保存失败怎么办？
**A:** 检查数据格式是否正确，确保所有必填字段都已填写。

## 预防措施

1. **代码审查**：每次修改后都要进行代码审查
2. **测试验证**：修复后要进行充分测试
3. **文档更新**：及时更新相关文档
4. **版本管理**：使用版本控制管理代码变更

## 联系方式

- **技术支持**: 开发团队
- **问题反馈**: 开发团队
- **文档维护**: 开发团队

---

*最后更新时间: 2024-12-19 14:30* 
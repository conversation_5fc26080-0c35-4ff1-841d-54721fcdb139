import cn.hutool.core.date.DateUtil;
import com.gyx.superscheduled.core.SuperScheduledManager;
import com.nx.inf.MainApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 测试
 * @Author: Jin<PERSON>hi<PERSON><PERSON>
 * @CreateDate: 2023/6/21 23:42
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MainApplication.class)
public class ApplicationTests {

    @Resource
    private SuperScheduledManager superScheduledManager;

    @Test
    public void test() {
        System.out.println(DateUtil.date(1659165659000L));
        System.out.println(DateUtil.date(1689420098921L));
        //获取所有定时任务
//        List<String> allSuperScheduledName = superScheduledManager.getAllSuperScheduledName();
//        String name = allSuperScheduledName.get(0);
//        //终止定时任务
//        superScheduledManager.cancelScheduled(name);
//
//        try {
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        System.out.println("任务名：" + name);
//        //启动定时任务
//        superScheduledManager.addCronScheduled(name, "0/2 * * * * ?");
//        //获取启动汇总的定时任务
//        List<String> runScheduledName = superScheduledManager.getRunScheduledName();
//        runScheduledName.forEach(System.out::println);
//
//        try {
//            Thread.sleep(10000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        //修改定时任务执行周期
//        superScheduledManager.setScheduledCron(name, "0/5 * * * * ?");
    }
}

package com.nx.inf.service;

import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

public class BaiduCloudAccessTokenTest {

    @Test
    public void testGetAccessToken() {
        // 百度智慧云API认证信息
        String apiKey = "oOSfGCoXlAL3h9MrZnFgRZX9";
        String secretKey = "u28okfJt7yc3OSwaU4Dm3bZSw4ZSWCNj";

        // 百度智慧云获取AccessToken的URL
        String authUrl = "https://aip.baidubce.com/oauth/2.0/token";

        // 构建请求参数
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", "client_credentials");
        params.add("client_id", apiKey);
        params.add("client_secret", secretKey);

        // 使用RestTemplate发送请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(authUrl, params, String.class);

        // 打印响应结果
        System.out.println("Access Token Response: " + response.getBody());
    }
}

spring:
    application:
        name: hlyyyintface
    jackson:
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
    # 默认 dev 环境
    profiles:
        active: prod
    # 允许Bean定义覆盖，避免同名Bean冲突
    main:
        allow-bean-definition-overriding: true
    # 文件上传配置
    servlet:
        multipart:
            enabled: true
            max-file-size: 10MB
            max-request-size: 10MB
            file-size-threshold: 2KB
    # 数据库配置
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        mysql-datasource:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ************************************************************************************************************************************************************************************************************************************************************************************
            username: jgdj
            password: Jgdj@123456

        sqlserver-datasource:
            driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
            url: *************************************************
            username: sa
            password: jsb310229.
    #定时任务增强
    super:
        scheduled:
            thread-pool:
                #线程池大小
                poolSize: 30
                #线程名前缀
                threadNamePrefix: super
                #设置是否关闭时等待执行中的任务执行完成
                waitForTasksToCompleteOnShutdown: false
                #设置此执行器被关闭时等待的最长时间，用于在其余容器继续关闭之前等待剩余任务执行完成
                #需要将waitForTasksToCompleteOnShutdown设置为true，此配置才起作用
                awaitTerminationSeconds: 0
            plug-interfaces:
                #日志存放位置，不设置默认位置为程序同级目录下
                logPath: E:/tmp/log-scheduled
            plug-in:
                #开启执行标志
                executionFlag: false
                #开启定时任务调度日志，日志文件是存在本地磁盘上的
                executionLog: false
                #日志存放位置，不设置默认位置为程序同级目录下
                logPath: E:/tmp/log-scheduled

# 日志配置
logging:
    level:
        root: INFO
        com.nx.inf: DEBUG
        com.baomidou.mybatisplus: DEBUG
        org.springframework.web: DEBUG
        org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping: TRACE
    pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
        file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file:
        name: logs/hlyyyintface.log
    logback:
        rollingpolicy:
            max-file-size: 10MB
            max-history: 30

server:
    address: 0.0.0.0
    port: 18923
    servlet:
        context-path: /
mybatis-plus:
    configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        cache-enabled: true
        default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
        type-handlers-package: com.nx.inf.config
        auto-mapping-behavior: PARTIAL
        auto-mapping-unknown-column-behavior: WARNING
    global-config:
        db-config:
            logic-delete-field: isDelete # 全局逻辑删除的实体字段名
            logic-delete-value: 1 # 逻辑已删除值（默认为 1）
            logic-not-delete-value: 0 # 逻辑未删除值（默认为 0）
    type-aliases-package: com.nx.inf.model.entity

# 第三方API配置
third-api:
    # 医疗API配置
    medical:
        secret-key: dNN2zOEx6RIu472sNywkHupr2m1hD4RQ
        app-id: OUuYjuSe99LvnbmE
        host-name: http://************:6887/platform/Tx
        hospital-branch: yuexiu
    feitu:
        # 飞图医疗影像系统配置 - 上海市第四人民医院
        hsCode: 02109845
        accessKey: UTXYE73ISVKQLOW8
        accessSecret: QZXA4BU8S3L7J6EHCWVFTD5I0R29NOMG
        aesKey: aex5lgqcpwhd06m2
        aesIv: ca63hd2lm87rzt1s

# 文件上传配置
file:
    upload:
        # 文件上传根路径
        path: ./uploads
        # 图片文件夹
        image-folder: images
        # 最大文件大小 (10MB)
        max-size: 10485760
        # 允许的图片类型
        allowed-types: image/jpeg,image/jpg,image/png,image/gif,image/bmp,image/webp

# OCR配置
ocr:
    # Tesseract配置
    tesseract:
        # tessdata数据路径
        data-path: ./tessdata
        # 默认识别语言
        language: chi_sim+eng
        # OCR引擎模式 (0=Legacy, 1=LSTM, 2=Legacy+LSTM, 3=Default)
        engine-mode: 1
        # 页面分割模式 (3=自动检测)
        page-seg-mode: 3

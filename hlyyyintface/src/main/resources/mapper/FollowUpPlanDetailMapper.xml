<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowUpPlanDetailMapper">

    <!-- 随访计划详情结果映射 -->
    <resultMap id="FollowUpPlanDetailResultMap" type="com.nx.inf.model.entity.FollowUpPlanDetail">
        <id column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="duration" property="duration" />
        <result column="time_unit" property="timeUnit" />
        <result column="plan_type" property="planType" />
        <result column="execution_time_type" property="executionTimeType" />
        <result column="custom_time_value" property="customTimeValue" />
        <result column="plan_type_detail" property="planTypeDetail" />
    </resultMap>

    <!-- 根据计划ID查询详情 -->
    <select id="selectByPlanId" resultMap="FollowUpPlanDetailResultMap">
        SELECT 
            id,
            plan_id,
            duration,
            time_unit,
            plan_type,
            execution_time_type,
            custom_time_value,
            plan_type_detail
        FROM follow_up_plan_detail
        WHERE plan_id = #{planId}
    </select>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.SmartFormOtherDataMapper">

    <!-- 根据表单模板ID查询其他数据列表 -->
    <select id="selectOtherDataByFormId" resultType="com.nx.inf.model.entity.SmartFormOtherData">
        SELECT 
            id, form_template_id, sort_order, data_type, data_name, data_config, create_time, update_time, is_deleted
        FROM smart_form_other_data
        WHERE form_template_id = #{formTemplateId} AND is_deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 批量插入其他数据 -->
    <insert id="batchInsertOtherData">
        INSERT INTO smart_form_other_data (
            form_template_id, sort_order, data_type, data_name, data_config, create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="dataList" item="data" separator=",">
            (
                #{data.formTemplateId}, #{data.sortOrder}, #{data.dataType}, #{data.dataName}, #{data.dataConfig},
                #{data.createTime}, #{data.updateTime}, #{data.isDeleted}
            )
        </foreach>
    </insert>

    <!-- 根据表单模板ID删除其他数据 -->
    <update id="deleteByFormTemplateId">
        UPDATE smart_form_other_data 
        SET is_deleted = 1, update_time = NOW()
        WHERE form_template_id = #{formTemplateId}
    </update>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.NursingHomeInstitutionMapper">

    <!-- 机构实体结果映射 -->
    <resultMap id="NursingHomeInstitutionResultMap" type="com.nx.inf.model.entity.NursingHomeInstitution">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="address" property="address" />
        <result column="phone" property="phone" />
        <result column="fax" property="fax" />
        <result column="email" property="email" />
        <result column="capacity" property="capacity" />
        <result column="current_patients" property="currentPatients" />
        <result column="license_number" property="licenseNumber" />
        <result column="legal_person" property="legalPerson" />
        <result column="director" property="director" />
        <result column="director_phone" property="directorPhone" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="description" property="description" />
        <result column="level" property="level" />
        <result column="service_types" property="serviceTypes" />
        <result column="establish_date" property="establishDate" jdbcType="DATE" />
        <result column="cooperation_start_date" property="cooperationStartDate" jdbcType="DATE" />
        <result column="cooperation_end_date" property="cooperationEndDate" jdbcType="DATE" />
        <result column="status" property="status" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="district" property="district" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    
    <!-- 机构DTO结果映射 -->
    <resultMap id="NursingHomeInstitutionDTOResultMap" type="com.nx.inf.model.dto.NursingHomeInstitutionDTO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="address" property="address" />
        <result column="phone" property="phone" />
        <result column="fax" property="fax" />
        <result column="email" property="email" />
        <result column="capacity" property="capacity" />
        <result column="current_patients" property="currentPatients" />
        <result column="occupancy_rate" property="occupancyRate" />
        <result column="license_number" property="licenseNumber" />
        <result column="legal_person" property="legalPerson" />
        <result column="director" property="director" />
        <result column="director_phone" property="directorPhone" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="description" property="description" />
        <result column="level" property="level" />
        <result column="level_text" property="levelText" />
        <result column="service_types" property="serviceTypes" />
        <result column="establish_date" property="establishDate" />
        <result column="cooperation_start_date" property="cooperationStartDate" />
        <result column="cooperation_end_date" property="cooperationEndDate" />
        <result column="status" property="status" />
        <result column="status_text" property="statusText" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="district" property="district" />
        <result column="full_address" property="fullAddress" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 分页查询机构列表 -->
    <select id="selectInstitutionPage" resultMap="NursingHomeInstitutionDTOResultMap">
        SELECT 
            id, name, code, address, phone, fax, email, capacity, current_patients,
            CASE 
                WHEN capacity > 0 THEN ROUND((current_patients / capacity) * 100, 2)
                ELSE 0.00 
            END as occupancy_rate,
            license_number, legal_person, director, director_phone, 
            contact_person, contact_phone, description, level,
            CASE level
                WHEN 'STANDARD' THEN '标准'
                WHEN 'PREMIUM' THEN '高级'
                WHEN 'LUXURY' THEN '豪华'
                ELSE '未知'
            END as level_text,
            service_types,
            DATE_FORMAT(establish_date, '%Y-%m-%d') as establish_date,
            DATE_FORMAT(cooperation_start_date, '%Y-%m-%d') as cooperation_start_date,
            DATE_FORMAT(cooperation_end_date, '%Y-%m-%d') as cooperation_end_date,
            status,
            CASE status
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END as status_text,
            province, city, district,
            CONCAT(IFNULL(province, ''), IFNULL(city, ''), IFNULL(district, ''), IFNULL(address, '')) as full_address,
            longitude, latitude,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
            DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
            create_by, update_by
        FROM nursing_home_institution
        <where>
            is_deleted = 0
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="level != null and level != ''">
                AND level = #{level}
            </if>
            <if test="province != null and province != ''">
                AND province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
            <if test="district != null and district != ''">
                AND district = #{district}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                     OR address LIKE CONCAT('%', #{keyword}, '%')
                     OR contact_person LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="startTime != null and startTime != ''">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据ID查询机构详情 -->
    <select id="selectInstitutionById" resultMap="NursingHomeInstitutionDTOResultMap">
        SELECT 
            id, name, code, address, phone, fax, email, capacity, current_patients,
            CASE 
                WHEN capacity > 0 THEN ROUND((current_patients / capacity) * 100, 2)
                ELSE 0.00 
            END as occupancy_rate,
            license_number, legal_person, director, director_phone, 
            contact_person, contact_phone, description, level,
            CASE level
                WHEN 'STANDARD' THEN '标准'
                WHEN 'PREMIUM' THEN '高级'
                WHEN 'LUXURY' THEN '豪华'
                ELSE '未知'
            END as level_text,
            service_types,
            DATE_FORMAT(establish_date, '%Y-%m-%d') as establish_date,
            DATE_FORMAT(cooperation_start_date, '%Y-%m-%d') as cooperation_start_date,
            DATE_FORMAT(cooperation_end_date, '%Y-%m-%d') as cooperation_end_date,
            status,
            CASE status
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END as status_text,
            province, city, district,
            CONCAT(IFNULL(province, ''), IFNULL(city, ''), IFNULL(district, ''), IFNULL(address, '')) as full_address,
            longitude, latitude,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
            DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
            create_by, update_by
        FROM nursing_home_institution
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 根据机构代码查询机构 -->
    <select id="selectByCode" resultMap="NursingHomeInstitutionResultMap">
        SELECT * FROM nursing_home_institution
        WHERE code = #{code} AND is_deleted = 0
    </select>

    <!-- 根据机构名称查询机构（模糊搜索） -->
    <select id="selectByNameLike" resultMap="NursingHomeInstitutionResultMap">
        SELECT * FROM nursing_home_institution
        WHERE name LIKE CONCAT('%', #{name}, '%') AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 获取所有启用状态的机构 -->
    <select id="selectAllActive" resultMap="NursingHomeInstitutionDTOResultMap">
        SELECT 
            id, name, code, address, phone, capacity, current_patients,
            CASE 
                WHEN capacity > 0 THEN ROUND((current_patients / capacity) * 100, 2)
                ELSE 0.00 
            END as occupancy_rate,
            contact_person, contact_phone, level,
            CASE level
                WHEN 'STANDARD' THEN '标准'
                WHEN 'PREMIUM' THEN '高级'
                WHEN 'LUXURY' THEN '豪华'
                ELSE '未知'
            END as level_text,
            status,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time
        FROM nursing_home_institution
        WHERE status = 1 AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新机构状态 -->
    <update id="batchUpdateStatus">
        UPDATE nursing_home_institution 
        SET status = #{status}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 批量删除机构（逻辑删除） -->
    <update id="batchDelete">
        UPDATE nursing_home_institution 
        SET is_deleted = 1, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 统计机构数量 -->
    <select id="countInstitutions" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM nursing_home_institution
        <where>
            is_deleted = 0
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="level != null and level != ''">
                AND level = #{level}
            </if>
            <if test="province != null and province != ''">
                AND province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND city = #{city}
            </if>
        </where>
    </select>

    <!-- 更新机构入住人数 -->
    <update id="updateCurrentPatients">
        UPDATE nursing_home_institution 
        SET current_patients = #{currentPatients}, update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <!-- 根据地理位置查询附近机构 -->
    <select id="selectNearbyInstitutions" resultMap="NursingHomeInstitutionDTOResultMap">
        SELECT 
            id, name, code, address, phone, capacity, current_patients,
            CASE 
                WHEN capacity > 0 THEN ROUND((current_patients / capacity) * 100, 2)
                ELSE 0.00 
            END as occupancy_rate,
            contact_person, contact_phone, level,
            CASE level
                WHEN 'STANDARD' THEN '标准'
                WHEN 'PREMIUM' THEN '高级'
                WHEN 'LUXURY' THEN '豪华'
                ELSE '未知'
            END as level_text,
            longitude, latitude,
            ROUND(
                6371 * ACOS(
                    COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * 
                    COS(RADIANS(longitude) - RADIANS(#{longitude})) + 
                    SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))
                ), 2
            ) AS distance
        FROM nursing_home_institution
        WHERE longitude IS NOT NULL 
          AND latitude IS NOT NULL 
          AND status = 1 
          AND is_deleted = 0
        HAVING distance &lt;= #{radius}
        ORDER BY distance ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowupSubplanItemMapper">

    <!-- 定义结果映射 -->
    <resultMap id="FollowupSubplanItemMap" type="com.nx.inf.model.entity.FollowupSubplanItem">
        <id column="id" property="id" />
        <result column="subplan_id" property="subplanId" />
        <result column="item_name" property="itemName" />
        <result column="cycle_days" property="cycleDays" />
        <result column="join_day" property="joinDay" />
        <result column="event_type" property="eventType" />
        <result column="content_template_id" property="contentTemplateId" />
        <result column="sort_order" property="sortOrder" />
        <result column="create_time" property="createTime" typeHandler="com.nx.inf.config.DateTypeHandler" />
        <result column="update_time" property="updateTime" typeHandler="com.nx.inf.config.DateTypeHandler" />
    </resultMap>

    <!-- 根据子计划ID查询项目列表 -->
    <select id="selectBySubplanId" resultMap="FollowupSubplanItemMap">
        SELECT 
            id,
            subplan_id,
            item_name,
            cycle_days,
            join_day,
            event_type,
            content_template_id,
            sort_order,
            create_time,
            update_time
        FROM followup_subplan_item
        WHERE subplan_id = #{subplanId}
        ORDER BY sort_order ASC, create_time ASC
    </select>

</mapper> 
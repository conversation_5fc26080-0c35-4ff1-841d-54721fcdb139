<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.PatientMapper">

    <!-- 患者结果映射 -->
    <resultMap id="PatientResultMap" type="com.nx.inf.model.entity.Patient">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="age" property="age" />
        <result column="gender" property="gender" />
        <result column="phone" property="phone" />
        <result column="id_card" property="idCard" />
        <result column="medical_no" property="medicalNo" />
        <result column="birth_date" property="birthDate" typeHandler="com.nx.inf.config.LocalDateTypeHandler" />
        <result column="disease" property="disease" />
        <result column="apply_date" property="applyDate" typeHandler="com.nx.inf.config.LocalDateTypeHandler" />
        <result column="status" property="status" />
        <result column="reviewer_id" property="reviewerId" />
        <result column="reviewer_name" property="reviewerName" />
        <result column="review_time" property="reviewTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="create_time" property="createTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="update_time" property="updateTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 根据ID查询患者 -->
    <select id="selectPatientById" resultMap="PatientResultMap">
        SELECT 
            id,
            name,
            age,
            gender,
            phone,
            id_card,
            medical_no,
            birth_date,
            disease,
            apply_date,
            status,
            reviewer_id,
            reviewer_name,
            review_time,
            create_time,
            update_time,
            remark
        FROM patient
        WHERE id = #{id}
    </select>

    <!-- 分页查询患者列表 -->
    <select id="selectPatientPage" resultMap="PatientResultMap">
        SELECT 
            id,
            name,
            age,
            gender,
            phone,
            id_card,
            medical_no,
            birth_date,
            disease,
            apply_date,
            status,
            reviewer_id,
            reviewer_name,
            review_time,
            create_time,
            update_time,
            remark
        FROM patient
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="idCard != null and idCard != ''">
                AND id_card LIKE CONCAT('%', #{idCard}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="disease != null and disease != ''">
                AND disease LIKE CONCAT('%', #{disease}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 查询某个分组下的患者 -->
    <select id="selectByGroupId" resultMap="PatientResultMap">
        SELECT 
            p.id,
            p.name,
            p.age,
            p.gender,
            p.phone,
            p.id_card,
            p.medical_no,
            p.birth_date,
            p.disease,
            p.apply_date,
            p.status,
            p.reviewer_id,
            p.reviewer_name,
            p.review_time,
            p.create_time,
            p.update_time,
            p.remark
        FROM patient p
        JOIN patient_group_relation pgr ON p.id = pgr.patient_id
        WHERE pgr.group_id = #{groupId}
        ORDER BY p.create_time DESC
    </select>
    
    <!-- 分页查询某个分组下的患者 -->
    <select id="selectPatientPageByGroupId" resultMap="PatientResultMap">
        SELECT 
            p.id,
            p.name,
            p.age,
            p.gender,
            p.phone,
            p.id_card,
            p.medical_no,
            p.birth_date,
            p.disease,
            p.apply_date,
            p.status,
            p.reviewer_id,
            p.reviewer_name,
            p.review_time,
            p.create_time,
            p.update_time,
            p.remark
        FROM patient p
        JOIN patient_group_relation pgr ON p.id = pgr.patient_id
        <where>
            pgr.group_id = #{groupId}
            <if test="status != null and status != ''">
                AND p.status = #{status}
            </if>
            <if test="name != null and name != ''">
                AND p.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="idCard != null and idCard != ''">
                AND p.id_card LIKE CONCAT('%', #{idCard}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND p.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="disease != null and disease != ''">
                AND p.disease LIKE CONCAT('%', #{disease}, '%')
            </if>
        </where>
        ORDER BY p.create_time DESC
    </select>
    
</mapper>

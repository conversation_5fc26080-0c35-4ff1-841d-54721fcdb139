<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.QuestionnaireAnswerMapper">

    <!-- 结果映射 -->
    <resultMap id="QuestionnaireAnswerDTOMap" type="com.nx.inf.model.dto.QuestionnaireAnswerDTO">
        <id column="id" property="id"/>
        <result column="questionnaire_id" property="questionnaireId"/>
        <result column="question_id" property="questionId"/>
        <result column="patient_id" property="patientId"/>
        <result column="patient_name" property="patientName"/>
        <result column="answer_type" property="answerType"/>
        <result column="answer_text" property="answerText"/>
        <result column="answer_value" property="answerValue"/>
        <result column="answer_score" property="answerScore"/>
        <result column="answer_time" property="answerTime"/>
        <result column="time_spent" property="timeSpent"/>
        <result column="task_id" property="taskId"/>
        <result column="subplan_id" property="subplanId"/>
        <result column="plan_id" property="planId"/>
        <result column="answer_source" property="answerSource"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="questionnaire_name" property="questionnaireName"/>
        <result column="question_title" property="questionTitle"/>
        <result column="question_type" property="questionType"/>
        <result column="question_required" property="questionRequired"/>
    </resultMap>

    <!-- 分页查询问卷答案 -->
    <select id="selectAnswerPage" resultMap="QuestionnaireAnswerDTOMap">
        SELECT 
            qa.*,
            q.name as questionnaire_name,
            qq.title as question_title,
            qq.type as question_type,
            qq.required as question_required
        FROM questionnaire_answer qa
        LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id
        LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id
        <where>
            <if test="questionnaireId != null">
                AND qa.questionnaire_id = #{questionnaireId}
            </if>
            <if test="patientId != null">
                AND qa.patient_id = #{patientId}
            </if>
            <if test="taskId != null">
                AND qa.task_id = #{taskId}
            </if>
            <if test="startTime != null">
                AND qa.answer_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND qa.answer_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY qa.answer_time DESC
    </select>

    <!-- 根据任务ID查询问卷答案 -->
    <select id="selectAnswersByTaskId" resultMap="QuestionnaireAnswerDTOMap">
        SELECT 
            qa.*,
            q.name as questionnaire_name,
            qq.title as question_title,
            qq.type as question_type,
            qq.required as question_required
        FROM questionnaire_answer qa
        LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id
        LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id
        WHERE qa.task_id = #{taskId}
        ORDER BY qq.sort, qa.create_time
    </select>

    <!-- 根据患者ID和问卷ID查询答案 -->
    <select id="selectAnswersByPatientAndQuestionnaire" resultMap="QuestionnaireAnswerDTOMap">
        SELECT 
            qa.*,
            q.name as questionnaire_name,
            qq.title as question_title,
            qq.type as question_type,
            qq.required as question_required
        FROM questionnaire_answer qa
        LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id
        LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id
        WHERE qa.patient_id = #{patientId} 
        AND qa.questionnaire_id = #{questionnaireId}
        ORDER BY qa.answer_time DESC
    </select>

    <!-- 根据患者ID查询所有答案 -->
    <select id="selectAnswersByPatientId" resultMap="QuestionnaireAnswerDTOMap">
        SELECT 
            qa.*,
            q.name as questionnaire_name,
            qq.title as question_title,
            qq.type as question_type,
            qq.required as question_required
        FROM questionnaire_answer qa
        LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id
        LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id
        WHERE qa.patient_id = #{patientId}
        ORDER BY qa.answer_time DESC
    </select>

    <!-- 统计患者问卷完成情况 -->
    <select id="selectPatientQuestionnaireStats" resultMap="QuestionnaireAnswerDTOMap">
        SELECT 
            qa.questionnaire_id,
            q.name as questionnaire_name,
            COUNT(DISTINCT qa.question_id) as answer_count,
            COUNT(DISTINCT qq.id) as total_questions,
            MAX(qa.answer_time) as last_answer_time,
            AVG(qa.answer_score) as avg_score
        FROM questionnaire_answer qa
        LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id
        LEFT JOIN questionnaire_question qq ON qa.questionnaire_id = qq.questionnaire_id
        WHERE qa.patient_id = #{patientId}
        GROUP BY qa.questionnaire_id, q.name
        ORDER BY qa.questionnaire_id
    </select>

    <!-- 批量插入问卷答案 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO questionnaire_answer (
            questionnaire_id, question_id, patient_id, patient_name,
            answer_type, answer_text, answer_value, answer_score,
            answer_time, time_spent, task_id, subplan_id, plan_id,
            answer_source, create_time, update_time
        ) VALUES
        <foreach collection="answers" item="answer" separator=",">
            (
                #{answer.questionnaireId}, #{answer.questionId}, #{answer.patientId}, #{answer.patientName},
                #{answer.answerType}, #{answer.answerText}, #{answer.answerValue}, #{answer.answerScore},
                #{answer.answerTime}, #{answer.timeSpent}, #{answer.taskId}, #{answer.subplanId}, #{answer.planId},
                #{answer.answerSource}, #{answer.createTime}, #{answer.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据条件删除答案 -->
    <delete id="deleteByCondition">
        DELETE FROM questionnaire_answer
        <where>
            <if test="questionnaireId != null">
                AND questionnaire_id = #{questionnaireId}
            </if>
            <if test="patientId != null">
                AND patient_id = #{patientId}
            </if>
            <if test="taskId != null">
                AND task_id = #{taskId}
            </if>
        </where>
    </delete>

</mapper> 
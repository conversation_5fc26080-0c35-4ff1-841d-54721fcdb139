<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.SmartFormTemplateMapper">

    <!-- 结果映射 -->
    <resultMap id="SmartFormTemplateVOMap" type="com.nx.inf.model.vo.SmartFormTemplateVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="form_type" property="formType"/>
        <result column="category" property="category"/>
        <result column="status" property="status"/>
        <result column="permission" property="permission"/>
        <result column="config" property="config" typeHandler="com.nx.inf.config.JsonTypeHandler"/>
        <result column="repeat_config" property="repeatConfig" typeHandler="com.nx.inf.config.JsonTypeHandler"/>
        <result column="remark_text" property="remarkText"/>
        <result column="consent_config" property="consentConfig" typeHandler="com.nx.inf.config.JsonTypeHandler"/>
        <result column="creator_name" property="creatorName"/>
        <result column="creator_dept_name" property="creatorDeptName"/>
        <result column="submission_count" property="submissionCount"/>
        <result column="questionnaire_count" property="questionnaireCount"/>
        <result column="last_submitted_time" property="lastSubmittedTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询表单模板列表 -->
    <select id="selectFormTemplatePage" resultMap="SmartFormTemplateVOMap">
        SELECT 
            sft.id,
            sft.name,
            sft.description,
            sft.form_type,
            sft.category,
            sft.status,
            sft.permission,
            sft.config,
            sft.repeat_config,
            sft.remark_text,
            sft.consent_config,
            sft.creator_name,
            sft.creator_dept_name,
            sft.submission_count,
            COUNT(DISTINCT sfqr.questionnaire_id) AS questionnaire_count,
            CASE 
                WHEN sft.last_submitted_time IS NULL THEN NULL 
                ELSE DATE_FORMAT(sft.last_submitted_time, '%Y-%m-%d %H:%i:%s')
            END AS last_submitted_time,
            sft.create_time,
            sft.update_time
        FROM smart_form_template sft
        LEFT JOIN smart_form_questionnaire_rel sfqr ON sft.id = sfqr.form_template_id AND sfqr.is_deleted = 0
        WHERE sft.is_deleted = 0
        <if test="params.name != null and params.name != ''">
            AND sft.name LIKE CONCAT('%', #{params.name}, '%')
        </if>
        <if test="params.formType != null and params.formType != ''">
            AND sft.form_type = #{params.formType}
        </if>
        <if test="params.category != null and params.category != ''">
            AND sft.category = #{params.category}
        </if>
        <if test="params.status != null and params.status != ''">
            AND sft.status = #{params.status}
        </if>
        <if test="params.creatorId != null">
            AND sft.creator_id = #{params.creatorId}
        </if>
        <if test="params.startTime != null and params.startTime != ''">
            AND sft.create_time >= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND sft.create_time &lt;= #{params.endTime}
        </if>
        GROUP BY sft.id
        ORDER BY sft.create_time DESC
    </select>

    <!-- 根据ID查询表单模板详情 -->
    <select id="selectFormTemplateDetailById" resultMap="SmartFormTemplateVOMap">
        SELECT 
            sft.id,
            sft.name,
            sft.description,
            sft.form_type,
            sft.category,
            sft.status,
            sft.permission,
            sft.config,
            sft.repeat_config,
            sft.remark_text,
            sft.consent_config,
            sft.creator_name,
            sft.creator_dept_name,
            sft.submission_count,
            COUNT(DISTINCT sfqr.questionnaire_id) AS questionnaire_count,
            CASE 
                WHEN sft.last_submitted_time IS NULL THEN NULL 
                ELSE DATE_FORMAT(sft.last_submitted_time, '%Y-%m-%d %H:%i:%s')
            END AS last_submitted_time,
            sft.create_time,
            sft.update_time
        FROM smart_form_template sft
        LEFT JOIN smart_form_questionnaire_rel sfqr ON sft.id = sfqr.form_template_id AND sfqr.is_deleted = 0
        WHERE sft.id = #{id} AND sft.is_deleted = 0
        GROUP BY sft.id
    </select>

    <!-- 查询表单模板的问卷关联详情 -->
    <select id="selectFormQuestionnaireRelations" resultType="java.util.Map">
        SELECT 
            sfqr.id,
            sfqr.questionnaire_id AS questionnaireId,
            sfqr.questionnaire_name AS questionnaireName,
            sfqr.questionnaire_type AS questionnaireType,
            sfqr.question_count AS questionCount,
            sfqr.sort_order AS sortOrder,
            sfqr.is_required AS isRequired,
            sfqr.display_config AS displayConfig
        FROM smart_form_questionnaire_rel sfqr
        WHERE sfqr.form_template_id = #{formTemplateId} AND sfqr.is_deleted = 0
        ORDER BY sfqr.sort_order ASC
    </select>

    <!-- 查询表单模板的其他数据配置 -->
    <select id="selectFormOtherData" resultType="java.util.Map">
        SELECT 
            sfod.id,
            sfod.sort_order AS sequence,
            sfod.data_type AS type,
            sfod.data_name AS name,
            sfod.data_config AS config
        FROM smart_form_other_data sfod
        WHERE sfod.form_template_id = #{formTemplateId} AND sfod.is_deleted = 0
        ORDER BY sfod.sort_order ASC
    </select>

    <!-- 查询表单模板统计信息 -->
    <select id="selectFormTemplateStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(DISTINCT sfs.id) AS submission_count,
            COUNT(DISTINCT CASE WHEN sfs.patient_id IS NOT NULL THEN sfs.patient_id END) AS patient_count,
            AVG(sfs.total_score) AS avg_score,
            CASE 
                WHEN MAX(sfs.submit_time) IS NULL THEN NULL 
                ELSE DATE_FORMAT(MAX(sfs.submit_time), '%Y-%m-%d %H:%i:%s')
            END AS last_submitted_time
        FROM smart_form_template sft
        LEFT JOIN smart_form_submission sfs ON sft.id = sfs.form_template_id AND sfs.is_deleted = 0
        WHERE sft.id = #{id} AND sft.is_deleted = 0
    </select>

    <!-- 批量删除表单模板 -->
    <update id="batchDeleteFormTemplates">
        UPDATE smart_form_template 
        SET is_deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 复制表单模板 -->
    <insert id="copyFormTemplate" parameterType="com.nx.inf.model.entity.SmartFormTemplate">
        INSERT INTO smart_form_template (
            name, description, form_type, category, status, permission,
            config, repeat_config, remark_text, consent_config,
            creator_id, creator_name, creator_dept_id, creator_dept_name,
            submission_count, last_submitted_time, create_time, update_time, is_deleted
        )
        SELECT 
            #{newTemplate.name}, description, form_type, category, status, permission,
            config, repeat_config, remark_text, consent_config,
            #{newTemplate.creatorId}, #{newTemplate.creatorName}, #{newTemplate.creatorDeptId}, #{newTemplate.creatorDeptName},
            0, NULL, #{newTemplate.createTime}, #{newTemplate.updateTime}, 0
        FROM smart_form_template 
        WHERE id = #{sourceId} AND is_deleted = 0
    </insert>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowupPlanMapper">

    <!-- 定义结果映射 -->
    <resultMap id="FollowupPlanMap" type="com.nx.inf.model.entity.FollowupPlan">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="category" property="category" />
        <result column="status" property="status" />
        <result column="end_time_type" property="endTimeType" />
        <result column="end_time_value" property="endTimeValue" />
        <result column="group_id" property="groupId" />
        <result column="qr_code_url" property="qrCodeUrl" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="patient_count" property="patientCount" />
        <result column="task_count" property="taskCount" />
        <result column="completed_task_count" property="completedTaskCount" />
        <!-- 注意：subplanCount字段已移除，改为实时计算 -->
    </resultMap>

    <!-- 分页查询随访计划列表 -->
    <select id="selectPlanPage" resultMap="FollowupPlanMap">
        SELECT 
            fp.id,
            fp.name,
            fp.description,
            fp.category,
            fp.status,
            fp.end_time_type,
            fp.end_time_value,
            fp.group_id,
            fp.qr_code_url,
            fp.creator_id,
            fp.creator_name,
            fp.create_time,
            fp.update_time,
            COALESCE(fp.patient_count, 0) as patient_count,
            COALESCE(fp.task_count, 0) as task_count,
            COALESCE(fp.completed_task_count, 0) as completed_task_count
        FROM followup_plan fp
        <where>
            <if test="category != null and category != ''">
                AND fp.category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND fp.status = #{status}
            </if>
            <if test="name != null and name != ''">
                AND fp.name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
        ORDER BY fp.create_time DESC
    </select>
    
    <!-- 根据ID查询随访计划详情 -->
    <select id="selectPlanById" resultMap="FollowupPlanMap">
        SELECT 
            fp.id,
            fp.name,
            fp.description,
            fp.category,
            fp.status,
            fp.end_time_type,
            fp.end_time_value,
            fp.group_id,
            fp.qr_code_url,
            fp.creator_id,
            fp.creator_name,
            fp.create_time,
            fp.update_time,
            COALESCE(fp.patient_count, 0) as patient_count,
            COALESCE(fp.task_count, 0) as task_count,
            COALESCE(fp.completed_task_count, 0) as completed_task_count
        FROM followup_plan fp
        WHERE fp.id = #{id}
    </select>
    
    <!-- 更新患者计数 -->
    <update id="updatePatientCount">
        UPDATE followup_plan fp
        SET fp.patient_count = (
            SELECT COUNT(DISTINCT pgr.patient_id)
            FROM patient_group_relation pgr
            WHERE pgr.group_id = fp.group_id
            AND pgr.status = '1'
        )
        WHERE fp.id = #{planId}
    </update>
    
    <!-- 更新任务计数 -->
    <update id="updateTaskCount">
        UPDATE followup_plan fp
        SET fp.task_count = (
            SELECT COUNT(*)
            FROM followup_task ft
            WHERE ft.plan_id = fp.id
        ),
        fp.completed_task_count = (
            SELECT COUNT(*)
            FROM followup_task ft
            WHERE ft.plan_id = fp.id
            AND ft.status = 'completed'
        )
        WHERE fp.id = #{planId}
    </update>
    
    <!-- 更新随访次数 -->
    <update id="updateVisitCount">
        UPDATE followup_plan fp
        SET fp.visit_count = (
            SELECT COUNT(*)
            FROM follow_up_execution fe
            WHERE fe.plan_id = fp.id
        )
        WHERE fp.id = #{planId}
    </update>
    
</mapper>

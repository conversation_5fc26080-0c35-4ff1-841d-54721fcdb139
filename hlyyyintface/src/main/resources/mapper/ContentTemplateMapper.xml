<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.ContentTemplateMapper">

    <!-- 模板结果映射 -->
    <resultMap id="ContentTemplateResultMap" type="com.nx.inf.model.entity.ContentTemplate">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="category" property="category" />
        <result column="message_type" property="messageType" />
        <result column="permission" property="permission" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="content_text" property="contentText" />
        <result column="status" property="status" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="creator_dept_id" property="creatorDeptId" />
        <result column="creator_dept_name" property="creatorDeptName" />
        <result column="use_count" property="useCount" />
        <result column="last_used_time" property="lastUsedTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    
    <!-- 权限过滤条件片段 - 用于列表查询 -->
    <sql id="permissionFilter">
        <choose>
            <when test="permission != null and permission == 'all'">
                AND permission = 'all'
            </when>
            <when test="permission != null and permission == 'self'">
                AND (permission = 'all' OR (permission = 'self' AND creator_id = #{currentUserId}))
            </when>
            <when test="permission != null and permission == 'department'">
                AND (permission = 'all' OR (permission = 'department' AND creator_dept_id = #{currentDeptId}) OR (permission = 'self' AND creator_id = #{currentUserId}))
            </when>
            <otherwise>
                AND (permission = 'all' OR (permission = 'department' AND creator_dept_id = #{currentDeptId}) OR (permission = 'self' AND creator_id = #{currentUserId}))
            </otherwise>
        </choose>
    </sql>
    
    <!-- 权限过滤条件片段 - 用于单个查询 -->
    <sql id="singlePermissionFilter">
        AND (permission = 'all' OR (permission = 'department' AND creator_dept_id = #{currentDeptId}) OR (permission = 'self' AND creator_id = #{currentUserId}))
    </sql>

    <!-- 分页查询模板列表 -->
    <select id="selectTemplatePage" resultMap="ContentTemplateResultMap">
        SELECT 
            id, name, description, category, message_type, permission, title,
            content, content_text, status, creator_id, creator_name, 
            creator_dept_id, creator_dept_name, use_count, 
            CASE WHEN last_used_time IS NULL THEN NULL ELSE last_used_time END as last_used_time,
            create_time, update_time, is_deleted
        FROM content_template
        <where>
            is_deleted = 0
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                     OR title LIKE CONCAT('%', #{keyword}, '%') 
                     OR content_text LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <include refid="permissionFilter"/>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据分类获取模板列表 -->
    <select id="selectTemplatesByCategory" resultMap="ContentTemplateResultMap">
        SELECT 
            id, name, description, category, message_type, permission, title,
            content, content_text, status, creator_id, creator_name, 
            creator_dept_id, creator_dept_name, use_count, 
            CASE WHEN last_used_time IS NULL THEN NULL ELSE last_used_time END as last_used_time,
            create_time, update_time, is_deleted
        FROM content_template
        <where>
            is_deleted = 0
            AND category = #{category}
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <include refid="singlePermissionFilter"/>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 搜索模板 -->
    <select id="searchTemplates" resultMap="ContentTemplateResultMap">
        SELECT 
            id, name, description, category, message_type, permission, title,
            content, content_text, status, creator_id, creator_name, 
            creator_dept_id, creator_dept_name, use_count, 
            CASE WHEN last_used_time IS NULL THEN NULL ELSE last_used_time END as last_used_time,
            create_time, update_time, is_deleted
        FROM content_template
        <where>
            is_deleted = 0
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                     OR title LIKE CONCAT('%', #{keyword}, '%') 
                     OR content_text LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <include refid="permissionFilter"/>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据ID查询模板详情（包含权限检查） -->
    <select id="selectTemplateByIdWithPermission" resultMap="ContentTemplateResultMap">
        SELECT 
            id, name, description, category, message_type, permission, title,
            content, content_text, status, creator_id, creator_name, 
            creator_dept_id, creator_dept_name, use_count, 
            CASE WHEN last_used_time IS NULL THEN NULL ELSE last_used_time END as last_used_time,
            create_time, update_time, is_deleted
        FROM content_template
        <where>
            id = #{id} AND is_deleted = 0
            <include refid="singlePermissionFilter"/>
        </where>
    </select>
    
    <!-- 更新模板使用次数 -->
    <update id="updateUseCount">
        UPDATE content_template 
        SET use_count = use_count + 1, 
            last_used_time = NOW(),
            update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>
    
    <!-- 批量删除模板（逻辑删除） -->
    <update id="batchDeleteTemplates">
        UPDATE content_template 
        SET is_deleted = 1, update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>
    
    <!-- 批量删除模板（物理删除，仅限权限检查通过的） -->
    <delete id="batchDeleteTemplatesWithPermission">
        DELETE FROM content_template
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
        AND (creator_id = #{currentUserId} OR permission = 'all')
    </delete>

</mapper> 
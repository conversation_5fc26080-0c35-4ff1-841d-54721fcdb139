<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.NhPatientInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nx.inf.model.entity.NhPatientInfo">
        <id column="patient_id" property="patientId"/>
        <result column="patient_name" property="patientName"/>
        <result column="gender" property="gender"/>
        <result column="birth_date" property="birthDate"/>
        <result column="card_no" property="cardNo"/>
        <result column="phone" property="phone"/>
        <result column="id_card" property="idCard"/>
        <result column="address" property="address"/>
        <result column="medical_history" property="medicalHistory"/>
        <result column="medications" property="medications"/>
        <result column="blood_pressure" property="bloodPressure"/>
        <result column="heart_rate" property="heartRate"/>
        <result column="blood_sugar" property="bloodSugar"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="nursing_home_code" property="nursingHomeCode"/>
        <result column="nursing_home_name" property="nursingHomeName"/>
        <result column="admission_date" property="admissionDate"/>
        <result column="bed_number" property="bedNumber"/>
        <result column="care_level" property="careLevel"/>
        <result column="health_status" property="healthStatus"/>
        <result column="emergency_contact" property="emergencyContact"/>
        <result column="emergency_phone" property="emergencyPhone"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.patient_id, a.patient_name, a.gender, a.birth_date, a.card_no, a.phone, a.id_card, a.address,
        a.medical_history, a.medications, a.blood_pressure, a.heart_rate, a.blood_sugar,
        a.create_time, a.update_time, a.nursing_home_code,
        a.admission_date, a.bed_number, a.care_level, a.health_status, a.emergency_contact, a.emergency_phone, a.remark,
        i.name as nursing_home_name
    </sql>

    <!-- 获取病人信息列表 -->
    <select id="getNhPatientInfoList" parameterType="com.nx.inf.model.entity.NhPatientInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nh_patient_info a
        LEFT JOIN nursing_home_institution i ON a.nursing_home_code COLLATE utf8mb4_general_ci = i.code COLLATE utf8mb4_general_ci AND i.is_deleted = 0
        WHERE NOT EXISTS (SELECT 1 FROM nh_hospitalization_info b WHERE a.patient_id = b.patient_id AND b.discharge_date IS NULL)
        <if test="patientName != null">
            AND a.patient_name LIKE CONCAT('%', #{patientName}, '%')
        </if>
        <if test="gender != null">
            AND a.gender = #{gender}
        </if>
        <if test="birthDate != null">
            AND a.birth_date = #{birthDate}
        </if>
        <if test="cardNo != null">
            AND a.card_no = #{cardNo}
        </if>
        <if test="phone != null">
            AND a.phone = #{phone}
        </if>
        <if test="nursingHomeCode != null">
            AND a.nursing_home_code = #{nursingHomeCode}
        </if>
        ORDER BY a.create_time DESC
    </select>

    <!-- 更新病人信息 -->
    <update id="updateNhPatientInfo" parameterType="com.nx.inf.model.entity.NhPatientInfo">
        UPDATE nh_patient_info
        <set>
            <if test="patientName != null">
                patient_name = #{patientName},
            </if>
            <if test="gender != null">
                gender = #{gender},
            </if>
            <if test="birthDate != null">
                birth_date = #{birthDate},
            </if>
            <if test="cardNo != null">
                card_no = #{cardNo},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="idCard != null">
                id_card = #{idCard},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="medicalHistory != null">
                medical_history = #{medicalHistory},
            </if>
            <if test="medications != null">
                medications = #{medications},
            </if>
            <if test="bloodPressure != null">
                blood_pressure = #{bloodPressure},
            </if>
            <if test="heartRate != null">
                heart_rate = #{heartRate},
            </if>
            <if test="bloodSugar != null">
                blood_sugar = #{bloodSugar},
            </if>
            <if test="nursingHomeCode != null">
                nursing_home_code = #{nursingHomeCode},
            </if>
            <if test="admissionDate != null">
                admission_date = #{admissionDate},
            </if>
            <if test="bedNumber != null">
                bed_number = #{bedNumber},
            </if>
            <if test="careLevel != null">
                care_level = #{careLevel},
            </if>
            <if test="healthStatus != null">
                health_status = #{healthStatus},
            </if>
            <if test="emergencyContact != null">
                emergency_contact = #{emergencyContact},
            </if>
            <if test="emergencyPhone != null">
                emergency_phone = #{emergencyPhone},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        WHERE patient_id = #{patientId}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.PatientGroupMapper">

    <!-- 分页查询患者分组列表 -->
    <select id="selectGroupPage" resultType="com.nx.inf.model.entity.PatientGroup">
        SELECT 
            id,
            name,
            description,
            patient_count,
            creator_id,
            creator_name,
            create_time,
            update_time
        FROM patient_group
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 更新患者数量 -->
    <update id="updatePatientCount">
        UPDATE patient_group pg
        SET patient_count = (
            SELECT COUNT(*)
            FROM patient_group_relation pgr
            WHERE pgr.group_id = pg.id
        )
        WHERE id = #{groupId}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FormTemplateMapper">

    <!-- 查询表单模板列表 -->
    <select id="selectTemplateList" resultType="com.nx.inf.model.entity.FormTemplate">
        SELECT 
            id,
            name,
            hospital,
            category,
            content,
            creator_id,
            creator_name,
            is_system
        FROM form_template
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        </where>
        ORDER BY id DESC
    </select>
    
</mapper> 
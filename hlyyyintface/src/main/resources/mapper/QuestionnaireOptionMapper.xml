<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.QuestionnaireOptionMapper">

    <resultMap id="BaseResultMap" type="com.nx.inf.model.entity.QuestionnaireOption">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="question_id" property="questionId" jdbcType="BIGINT"/>
        <result column="option_text" property="optionText" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="instruction" property="instruction" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="score" property="score" jdbcType="INTEGER"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByQuestionId" resultMap="BaseResultMap">
        SELECT * FROM questionnaire_option 
        WHERE question_id = #{questionId} 
        ORDER BY sort ASC
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper">

    <!-- 定义结果映射 -->
    <resultMap id="FollowupSubplanQuestionnaireMap" type="com.nx.inf.model.entity.FollowupSubplanQuestionnaire">
        <id column="id" property="id" />
        <result column="subplan_id" property="subplanId" />
        <result column="questionnaire_id" property="questionnaireId" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_required" property="isRequired" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 根据子计划ID查询问卷列表 -->
    <select id="selectBySubplanId" resultMap="FollowupSubplanQuestionnaireMap">
        SELECT 
            id,
            subplan_id,
            questionnaire_id,
            sort_order,
            is_required,
            create_time
        FROM followup_subplan_questionnaire
        WHERE subplan_id = #{subplanId}
        ORDER BY sort_order ASC, create_time ASC
    </select>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.NhHospitalizationInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nx.inf.model.entity.NhHospitalizationInfo">
        <id column="id" property="id"/>
        <result column="patient_id" property="patientId"/>
        <result column="patient_name" property="patientName"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="bed_number" property="bedNumber"/>
        <result column="card_no" property="cardNo"/>
        <result column="diagnosis" property="diagnosis"/>
        <result column="department" property="department"/>
        <result column="nurse_level" property="nurseLevel"/>
        <result column="responsible_nurse" property="responsibleNurse"/>
        <result column="attending_doctor" property="attendingDoctor"/>
        <result column="admission_date" property="admissionDate"/>
        <result column="discharge_date" property="dischargeDate"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, patient_id, patient_name, gender, age, bed_number, card_no, diagnosis, department,
        nurse_level, responsible_nurse, attending_doctor, admission_date, discharge_date,
        create_time, update_time
    </sql>

    <!-- 根据病人ID查询住院信息（示例自定义查询方法） -->
    <select id="selectByPatientId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nh_hospitalization_info
        WHERE patient_id = #{patientId}
    </select>

    <!-- 根据科室统计在院病人数量（示例自定义统计方法） -->
    <select id="countByDepartment" resultType="java.util.Map">
        SELECT
            department,
            COUNT(*) as patient_count
        FROM nh_hospitalization_info
        WHERE discharge_date IS NULL
        GROUP BY department
    </select>

    <!-- 查询指定日期范围内的入院记录（示例自定义查询方法） -->
    <select id="selectByAdmissionDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nh_hospitalization_info
        WHERE admission_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY admission_date DESC
    </select>

    <select id="genInHospitalId" resultType="java.lang.String">
        SELECT CONCAT('H', LPAD(IFNULL(MAX(SUBSTRING(in_hospital_id, 2)), 0) + 1, 6, '0')) FROM nh_hospitalization_info
    </select>
</mapper>

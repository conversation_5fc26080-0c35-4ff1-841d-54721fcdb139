<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.SmartFormQuestionnaireRelMapper">

    <!-- 根据表单模板ID查询问卷关联列表 -->
    <select id="selectQuestionnaireRelsByFormId" resultType="java.util.Map">
        SELECT 
            sfqr.id,
            sfqr.questionnaire_id AS questionnaireId,
            sfqr.questionnaire_name AS questionnaireName,
            sfqr.questionnaire_type AS questionnaireType,
            sfqr.question_count AS questionCount,
            sfqr.sort_order AS sortOrder,
            sfqr.is_required AS isRequired,
            sfqr.display_config AS displayConfig
        FROM smart_form_questionnaire_rel sfqr
        WHERE sfqr.form_template_id = #{formTemplateId} AND sfqr.is_deleted = 0
        ORDER BY sfqr.sort_order ASC
    </select>

    <!-- 批量插入问卷关联 -->
    <insert id="batchInsertQuestionnaireRels">
        INSERT INTO smart_form_questionnaire_rel (
            form_template_id, questionnaire_id, questionnaire_name, questionnaire_type, question_count,
            sort_order, is_required, display_config, create_time, update_time, is_deleted
        ) VALUES
        <foreach collection="rels" item="rel" separator=",">
            (
                #{rel.formTemplateId}, #{rel.questionnaireId}, #{rel.questionnaireName}, #{rel.questionnaireType}, #{rel.questionCount},
                #{rel.sortOrder}, #{rel.isRequired}, #{rel.displayConfig}, #{rel.createTime}, #{rel.updateTime}, #{rel.isDeleted}
            )
        </foreach>
    </insert>

    <!-- 根据表单模板ID删除问卷关联 -->
    <update id="deleteByFormTemplateId">
        UPDATE smart_form_questionnaire_rel 
        SET is_deleted = 1, update_time = NOW()
        WHERE form_template_id = #{formTemplateId}
    </update>

    <!-- 根据表单模板ID彻底删除已软删除的问卷关联 -->
    <delete id="deleteByFormTemplateIdAndDeleted">
        DELETE FROM smart_form_questionnaire_rel 
        WHERE form_template_id = #{formTemplateId} AND is_deleted = 1
    </delete>

</mapper> 
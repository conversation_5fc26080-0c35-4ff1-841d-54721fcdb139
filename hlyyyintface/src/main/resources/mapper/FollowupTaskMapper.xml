<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowupTaskMapper">

    <!-- 定义结果映射 -->
    <resultMap id="FollowupTaskMap" type="com.nx.inf.model.entity.FollowupTask">
        <id column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="subplan_id" property="subplanId" />
        <result column="subplan_item_id" property="subplanItemId" />
        <result column="patient_id" property="patientId" />
        <result column="patient_name" property="patientName" />
        <result column="scheduled_time" property="scheduledTime" />
        <result column="status" property="status" />
        <result column="message_sent" property="messageSent" />
        <result column="message_sent_time" property="messageSentTime" />
        <result column="questionnaire_completed" property="questionnaireCompleted" />
        <result column="questionnaire_completed_time" property="questionnaireCompletedTime" />
        <result column="create_time" property="createTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="update_time" property="updateTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
    </resultMap>

    <!-- 分页查询随访任务列表 -->
    <select id="selectTaskPage" resultMap="FollowupTaskMap">
        SELECT 
            id,
            plan_id,
            subplan_id,
            subplan_item_id,
            patient_id,
            patient_name,
            scheduled_time,
            status,
            message_sent,
            message_sent_time,
            questionnaire_completed,
            questionnaire_completed_time,
            create_time,
            update_time
        FROM followup_task
        <where>
            <if test="planId != null">
                AND plan_id = #{planId}
            </if>
            <if test="subplanId != null">
                AND subplan_id = #{subplanId}
            </if>
            <if test="patientId != null">
                AND patient_id = #{patientId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY scheduled_time DESC, create_time DESC
    </select>

    <!-- 根据计划ID查询任务列表 -->
    <select id="selectByPlanId" resultMap="FollowupTaskMap">
        SELECT 
            id,
            plan_id,
            subplan_id,
            subplan_item_id,
            patient_id,
            patient_name,
            scheduled_time,
            status,
            message_sent,
            message_sent_time,
            questionnaire_completed,
            questionnaire_completed_time,
            create_time,
            update_time
        FROM followup_task
        WHERE plan_id = #{planId}
        ORDER BY scheduled_time DESC, create_time DESC
    </select>

    <!-- 根据患者ID查询任务列表 -->
    <select id="selectByPatientId" resultMap="FollowupTaskMap">
        SELECT 
            id,
            plan_id,
            subplan_id,
            subplan_item_id,
            patient_id,
            patient_name,
            scheduled_time,
            status,
            message_sent,
            message_sent_time,
            questionnaire_completed,
            questionnaire_completed_time,
            create_time,
            update_time
        FROM followup_task
        WHERE patient_id = #{patientId}
        ORDER BY scheduled_time DESC, create_time DESC
    </select>

    <!-- 统计计划任务数量 -->
    <select id="countByPlanId" resultType="int">
        SELECT COUNT(*)
        FROM followup_task
        WHERE plan_id = #{planId}
    </select>

    <!-- 统计计划已完成任务数量 -->
    <select id="countCompletedByPlanId" resultType="int">
        SELECT COUNT(*)
        FROM followup_task
        WHERE plan_id = #{planId}
        AND status = 'completed'
    </select>

</mapper> 
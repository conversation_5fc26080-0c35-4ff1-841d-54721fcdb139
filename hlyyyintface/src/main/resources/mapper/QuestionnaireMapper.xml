<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.QuestionnaireMapper">

    <resultMap id="BaseResultMap" type="com.nx.inf.model.entity.Questionnaire">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="is_scoring" property="isScoring" jdbcType="BOOLEAN"/>
        <result column="is_guidance" property="isGuidance" jdbcType="BOOLEAN"/>
        <result column="is_trigger" property="isTrigger" jdbcType="BOOLEAN"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM questionnaire ORDER BY create_time DESC
    </select>

</mapper>

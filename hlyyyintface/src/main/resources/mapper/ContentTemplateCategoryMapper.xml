<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.ContentTemplateCategoryMapper">

    <!-- 分类结果映射 -->
    <resultMap id="ContentTemplateCategoryResultMap" type="com.nx.inf.model.entity.ContentTemplateCategory">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 获取所有启用的分类列表，按排序号排序 -->
    <select id="selectActiveCategoriesOrderBySort" resultMap="ContentTemplateCategoryResultMap">
        SELECT 
            id, code, name, description, sort_order, status, 
            create_time, update_time, is_deleted
        FROM content_template_category
        WHERE status = 'active' AND is_deleted = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.NhReferralAuditMapper">

    <!-- 转介审核记录结果映射 -->
    <resultMap id="BaseResultMap" type="com.nx.inf.model.entity.NhReferralAudit">
        <id column="id" property="id"/>
        <result column="patient_id" property="patientId"/>
        <result column="patient_name" property="patientName"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="phone" property="phone"/>
        <result column="id_card" property="idCard"/>
        <result column="birth_date" property="birthDate"/>
        <result column="discharge_date" property="dischargeDate"/>
        <result column="discharge_department" property="dischargeDepartment"/>
        <result column="primary_diagnosis" property="primaryDiagnosis"/>
        <result column="other_diagnosis" property="otherDiagnosis"/>
        <result column="referral_institution_code" property="referralInstitutionCode"/>
        <result column="referral_institution_name" property="referralInstitutionName"/>
        <result column="referral_type" property="referralType"/>
        <result column="emergency_contact" property="emergencyContact"/>
        <result column="emergency_phone" property="emergencyPhone"/>
        <result column="contact_relation" property="contactRelation"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_time" property="auditTime"/>
        <result column="auditor_id" property="auditorId"/>
        <result column="auditor_name" property="auditorName"/>
        <result column="audit_remarks" property="auditRemarks"/>
        <result column="referral_letter_content" property="referralLetterContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_deleted" property="isDeleted"/>
        <!-- 关联机构信息 -->
        <result column="institution_name" property="institutionName"/>
        <result column="institution_address" property="institutionAddress"/>
        <result column="institution_phone" property="institutionPhone"/>
        <result column="institution_contact" property="institutionContact"/>
        <result column="institution_contact_phone" property="institutionContactPhone"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        r.id, r.patient_id, r.patient_name, r.gender, r.age, r.phone, r.id_card, r.birth_date,
        r.discharge_date, r.discharge_department, r.primary_diagnosis, r.other_diagnosis,
        r.referral_institution_code, r.referral_institution_name, r.referral_type,
        r.emergency_contact, r.emergency_phone, r.contact_relation,
        r.audit_status, r.audit_time, r.auditor_id, r.auditor_name, r.audit_remarks,
        r.referral_letter_content, r.create_time, r.update_time, r.create_by, r.update_by, r.is_deleted,
        ni.name as institution_name,
        ni.address as institution_address,
        ni.phone as institution_phone,
        ni.contact_person as institution_contact,
        ni.contact_phone as institution_contact_phone
    </sql>

    <!-- 获取转介审核记录列表（基于患者信息，左连接转介审核记录） -->
    <select id="getReferralAuditList" parameterType="com.nx.inf.model.entity.NhReferralAudit" resultMap="BaseResultMap">
        SELECT
        p.patient_id as patient_id,
        p.patient_name as patient_name,
        p.gender as gender,
        TIMESTAMPDIFF(YEAR, p.birth_date, CURDATE()) as age,
        p.phone as phone,
        p.id_card as id_card,
        p.birth_date as birth_date,
        COALESCE(r.discharge_date, NULL) as discharge_date,
        COALESCE(r.discharge_department, '待分配') as discharge_department,
        COALESCE(r.primary_diagnosis, '待完善') as primary_diagnosis,
        r.other_diagnosis as other_diagnosis,
        COALESCE(r.referral_institution_code, p.nursing_home_code) as referral_institution_code,
        COALESCE(r.referral_institution_name, pni.name) as referral_institution_name,
        COALESCE(r.referral_type, 'nursing') as referral_type,
        COALESCE(r.emergency_contact, '待完善') as emergency_contact,
        COALESCE(r.emergency_phone, p.phone) as emergency_phone,
        COALESCE(r.contact_relation, '本人') as contact_relation,
        COALESCE(r.audit_status, '0') as audit_status,
        r.audit_time as audit_time,
        r.auditor_id as auditor_id,
        r.auditor_name as auditor_name,
        r.audit_remarks as audit_remarks,
        r.referral_letter_content as referral_letter_content,
        COALESCE(r.create_time, p.create_time) as create_time,
        COALESCE(r.update_time, p.update_time) as update_time,
        r.create_by as create_by,
        r.update_by as update_by,
        COALESCE(r.is_deleted, 0) as is_deleted,
        COALESCE(ni.name, pni.name) as institution_name,
        COALESCE(ni.address, pni.address) as institution_address,
        COALESCE(ni.phone, pni.phone) as institution_phone,
        COALESCE(ni.contact_person, pni.contact_person) as institution_contact,
        COALESCE(ni.contact_phone, pni.contact_phone) as institution_contact_phone,
        COALESCE(r.id, 0) as id
        FROM nh_patient_info p
        LEFT JOIN nh_referral_audit r ON p.patient_id COLLATE utf8mb4_general_ci = r.patient_id COLLATE utf8mb4_general_ci AND r.is_deleted = 0
        LEFT JOIN nursing_home_institution ni ON r.referral_institution_code COLLATE utf8mb4_general_ci = ni.code COLLATE utf8mb4_general_ci AND ni.is_deleted = 0
        LEFT JOIN nursing_home_institution pni ON p.nursing_home_code COLLATE utf8mb4_general_ci = pni.code COLLATE utf8mb4_general_ci AND pni.is_deleted = 0
        WHERE NOT EXISTS (SELECT 1 FROM nh_hospitalization_info h WHERE p.patient_id COLLATE utf8mb4_general_ci = h.patient_id COLLATE utf8mb4_general_ci AND h.discharge_date IS NULL)
        <if test="record.patientName != null and record.patientName != ''">
            AND p.patient_name LIKE CONCAT('%', #{record.patientName}, '%')
        </if>
        <if test="record.phone != null and record.phone != ''">
            AND p.phone LIKE CONCAT('%', #{record.phone}, '%')
        </if>
        <if test="record.idCard != null and record.idCard != ''">
            AND p.id_card LIKE CONCAT('%', #{record.idCard}, '%')
        </if>
        <if test="record.auditStatus != null and record.auditStatus != ''">
            AND COALESCE(r.audit_status, '0') = #{record.auditStatus}
        </if>
        <if test="record.referralInstitutionCode != null and record.referralInstitutionCode != ''">
            AND COALESCE(r.referral_institution_code, p.nursing_home_code) COLLATE utf8mb4_general_ci = #{record.referralInstitutionCode}
        </if>
        <if test="record.dischargeDepartment != null and record.dischargeDepartment != ''">
            AND COALESCE(r.discharge_department, '待分配') LIKE CONCAT('%', #{record.dischargeDepartment}, '%')
        </if>
        ORDER BY COALESCE(r.create_time, p.create_time) DESC
    </select>

    <!-- 根据ID获取转介审核记录详情 -->
    <select id="getReferralAuditWithInstitution" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nh_referral_audit r
        LEFT JOIN nursing_home_institution ni ON r.referral_institution_code COLLATE utf8mb4_general_ci = ni.code COLLATE utf8mb4_general_ci AND ni.is_deleted = 0
        WHERE r.id = #{id} AND r.is_deleted = 0
    </select>

    <!-- 更新审核状态 -->
    <update id="updateAuditStatus">
        UPDATE nh_referral_audit
        SET audit_status = #{auditStatus},
            audit_time = NOW(),
            auditor_id = #{auditorId},
            auditor_name = #{auditorName},
            audit_remarks = #{auditRemarks},
            update_time = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>

</mapper> 
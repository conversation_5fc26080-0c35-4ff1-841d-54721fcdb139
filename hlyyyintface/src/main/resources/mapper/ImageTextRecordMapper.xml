<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.ImageTextRecordMapper">

    <!-- 图片文字识别记录结果映射 -->
    <resultMap id="ImageTextRecordResultMap" type="com.nx.inf.model.entity.ImageTextRecord">
        <id column="id" property="id" />
        <result column="original_filename" property="originalFilename" />
        <result column="file_path" property="filePath" />
        <result column="file_size" property="fileSize" />
        <result column="file_type" property="fileType" />
        <result column="image_width" property="imageWidth" />
        <result column="image_height" property="imageHeight" />
        <result column="recognized_text" property="recognizedText" />
        <result column="confidence" property="confidence" />
        <result column="ocr_engine" property="ocrEngine" />
        <result column="processing_time" property="processingTime" />
        <result column="status" property="status" />
        <result column="error_message" property="errorMessage" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="create_time" property="createTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="update_time" property="updateTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 根据ID查询记录 -->
    <select id="selectRecordById" resultMap="ImageTextRecordResultMap">
        SELECT 
            id,
            original_filename,
            file_path,
            file_size,
            file_type,
            image_width,
            image_height,
            recognized_text,
            confidence,
            ocr_engine,
            processing_time,
            status,
            error_message,
            creator_id,
            creator_name,
            create_time,
            update_time,
            is_deleted
        FROM image_text_record
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 分页查询记录列表 -->
    <select id="selectRecordPage" resultMap="ImageTextRecordResultMap">
        SELECT 
            id,
            original_filename,
            file_path,
            file_size,
            file_type,
            image_width,
            image_height,
            recognized_text,
            confidence,
            ocr_engine,
            processing_time,
            status,
            error_message,
            creator_id,
            creator_name,
            create_time,
            update_time,
            is_deleted
        FROM image_text_record
        <where>
            is_deleted = 0
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="creatorId != null">
                AND creator_id = #{creatorId}
            </if>
            <if test="originalFilename != null and originalFilename != ''">
                AND original_filename LIKE CONCAT('%', #{originalFilename}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据创建人ID查询记录列表 -->
    <select id="selectByCreatorId" resultMap="ImageTextRecordResultMap">
        SELECT 
            id,
            original_filename,
            file_path,
            file_size,
            file_type,
            image_width,
            image_height,
            recognized_text,
            confidence,
            ocr_engine,
            processing_time,
            status,
            error_message,
            creator_id,
            creator_name,
            create_time,
            update_time,
            is_deleted
        FROM image_text_record
        WHERE creator_id = #{creatorId} AND is_deleted = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据状态查询记录列表 -->
    <select id="selectByStatus" resultMap="ImageTextRecordResultMap">
        SELECT 
            id,
            original_filename,
            file_path,
            file_size,
            file_type,
            image_width,
            image_height,
            recognized_text,
            confidence,
            ocr_engine,
            processing_time,
            status,
            error_message,
            creator_id,
            creator_name,
            create_time,
            update_time,
            is_deleted
        FROM image_text_record
        WHERE status = #{status} AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

</mapper>

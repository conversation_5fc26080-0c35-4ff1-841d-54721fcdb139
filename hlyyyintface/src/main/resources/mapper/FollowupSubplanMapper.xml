<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowupSubplanMapper">

    <!-- 定义结果映射 -->
    <resultMap id="FollowupSubplanMap" type="com.nx.inf.model.entity.FollowupSubplan">
        <id column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="type" property="type" />
        <result column="execution_time_type" property="executionTimeType" />
        <result column="custom_start_day" property="customStartDay" />
        <result column="custom_end_day" property="customEndDay" />
        <result column="reminder_time" property="reminderTime" />
        <result column="plan_type" property="planType" />
        <result column="sort_order" property="sortOrder" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据计划ID查询子计划列表 -->
    <select id="selectByPlanId" resultMap="FollowupSubplanMap">
        SELECT 
            id,
            plan_id,
            name,
            description,
            type,
            execution_time_type,
            custom_start_day,
            custom_end_day,
            reminder_time,
            plan_type,
            sort_order,
            create_time,
            update_time
        FROM followup_subplan
        WHERE plan_id = #{planId}
        ORDER BY sort_order ASC, create_time ASC
    </select>

</mapper> 
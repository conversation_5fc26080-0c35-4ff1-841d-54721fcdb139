<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.QuestionnaireQuestionMapper">

    <resultMap id="BaseResultMap" type="com.nx.inf.model.entity.QuestionnaireQuestion">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="questionnaire_id" property="questionnaireId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="required" property="required" jdbcType="BOOLEAN"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="instruction" property="instruction" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="text_type" property="textType" jdbcType="VARCHAR"/>
        <result column="min_value" property="minValue" jdbcType="INTEGER"/>
        <result column="max_value" property="maxValue" jdbcType="INTEGER"/>
        <result column="slider_min" property="sliderMin" jdbcType="INTEGER"/>
        <result column="slider_max" property="sliderMax" jdbcType="INTEGER"/>
        <result column="sort" property="sort" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByQuestionnaireId" resultMap="BaseResultMap">
        SELECT * FROM questionnaire_question 
        WHERE questionnaire_id = #{questionnaireId} 
        ORDER BY sort ASC
    </select>

</mapper>

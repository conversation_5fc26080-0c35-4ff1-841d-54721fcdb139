<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nx.inf.mapper.FollowUpExecutionMapper">

    <!-- 随访执行记录结果映射 -->
    <resultMap id="FollowUpExecutionResultMap" type="com.nx.inf.model.entity.FollowUpExecution">
        <id column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="patient_id" property="patientId" />
        <result column="patient_name" property="patientName" />
        <result column="patient_age" property="patientAge" />
        <result column="patient_gender" property="patientGender" />
        <result column="submit_time" property="submitTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="duration" property="duration" />
        <result column="form_count" property="formCount" />
        <result column="report_count" property="reportCount" />
        <result column="warning_count" property="warningCount" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
        <result column="update_time" property="updateTime" typeHandler="com.nx.inf.config.LocalDateTimeTypeHandler" />
    </resultMap>

    <!-- 分页查询随访执行记录 -->
    <select id="selectExecutionPage" resultMap="FollowUpExecutionResultMap">
        SELECT 
            fue.id,
            fue.plan_id,
            fue.patient_id,
            fue.patient_name,
            fue.patient_age,
            fue.patient_gender,
            fue.submit_time,
            fue.duration,
            fue.form_count,
            fue.report_count,
            fue.warning_count,
            fue.status,
            fue.create_time,
            fue.update_time
        FROM follow_up_execution fue
        LEFT JOIN patient p ON fue.patient_id = p.id
        <where>
            <if test="planId != null">
                AND fue.plan_id = #{planId}
            </if>
            <if test="patientName != null and patientName != ''">
                AND p.name LIKE CONCAT('%', #{patientName}, '%')
            </if>
            <if test="warningFlag != null">
                AND fue.warning_count > 0
            </if>
        </where>
        ORDER BY fue.submit_time DESC
    </select>
    
    <!-- 根据计划ID查询执行记录 -->
    <select id="selectByPlanId" resultMap="FollowUpExecutionResultMap">
        SELECT 
            id,
            plan_id,
            patient_id,
            patient_name,
            patient_age,
            patient_gender,
            submit_time,
            duration,
            form_count,
            report_count,
            warning_count,
            status,
            create_time,
            update_time
        FROM follow_up_execution
        WHERE plan_id = #{planId}
        ORDER BY submit_time DESC
    </select>
    
    <!-- 根据患者ID查询执行记录 -->
    <select id="selectByPatientId" resultMap="FollowUpExecutionResultMap">
        SELECT 
            id,
            plan_id,
            patient_id,
            patient_name,
            patient_age,
            patient_gender,
            submit_time,
            duration,
            form_count,
            report_count,
            warning_count,
            status,
            create_time,
            update_time
        FROM follow_up_execution
        WHERE patient_id = #{patientId}
        ORDER BY submit_time DESC
    </select>
    
    <!-- 根据计划ID和患者ID查询执行记录 -->
    <select id="selectByPlanIdAndPatientId" resultMap="FollowUpExecutionResultMap">
        SELECT 
            id,
            plan_id,
            patient_id,
            patient_name,
            patient_age,
            patient_gender,
            submit_time,
            duration,
            form_count,
            report_count,
            warning_count,
            status,
            create_time,
            update_time
        FROM follow_up_execution
        WHERE plan_id = #{planId} AND patient_id = #{patientId}
        ORDER BY submit_time DESC
    </select>
    
    <!-- 根据计划ID查询患者列表（去重） -->
    <select id="selectPatientsByPlanId" resultMap="FollowUpExecutionResultMap">
        SELECT 
            MIN(fue.id) as id,
            fue.plan_id,
            fue.patient_id,
            fue.patient_name,
            fue.patient_age,
            fue.patient_gender,
            MAX(fue.submit_time) as submit_time,
            SUM(fue.duration) as duration,
            SUM(fue.form_count) as form_count,
            SUM(fue.report_count) as report_count,
            SUM(fue.warning_count) as warning_count,
            MAX(fue.status) as status,
            MIN(fue.create_time) as create_time,
            MAX(fue.update_time) as update_time
        FROM follow_up_execution fue
        WHERE fue.plan_id = #{planId}
        GROUP BY fue.patient_id, fue.patient_name, fue.patient_age, fue.patient_gender, fue.plan_id
        ORDER BY MAX(fue.submit_time) DESC
    </select>
    
</mapper>

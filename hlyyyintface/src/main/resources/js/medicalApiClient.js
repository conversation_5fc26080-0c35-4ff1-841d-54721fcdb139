const crypto = require('crypto');
const https = require('https');
const http = require('http');
const url = require('url');

/**
 * Generate HMAC-SHA1 signature
 * @param {string} str - The string to sign
 * @param {string} secretKey - The secret key
 * @returns {string} - Base64 encoded signature
 */
function hmacSHA1(str, secretKey) {
  return crypto.createHmac('sha1', secretKey)
    .update(str)
    .digest('base64');
}

/**
 * Generate a UUID v4 string
 * @returns {string} UUID string
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Format current date time as YYYYMMDDHHmm
 * @returns {string} Formatted date time
 */
function formatDateTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  
  return `${year}${month}${day}${hours}${minutes}`;
}

/**
 * Encode sign string based on API type
 * @param {Object} params - Parameters used for signature
 * @param {number} xl - Signature method indicator
 * @returns {string} - The signature string
 */
function encodeSign(params, xl) {
  const { 
    appId, 
    nonce, 
    sigTime, 
    safePatientId = '', 
    hospitalBranch = '',
    patientIds = '', 
    startTime = '', 
    endTime = '',
    pageIndex = '',
    pageSize = '',
    source = ''
  } = params;
  
  let sign = '';
  
  switch(xl) {
    case 1:
      sign = `appId=${appId}&nonce=${nonce}&sigTime=${sigTime}&hospitalBranch=${hospitalBranch}&startTime=${startTime}&endTime=${endTime}&pageIndex=${pageIndex}&pageSize=${pageSize}&source=${source}`;
      break;
    case 2:
      sign = `appId=${appId}&nonce=${nonce}&sigTime=${sigTime}&safePatientId=${safePatientId}&hospitalBranch=${hospitalBranch}`;
      break;
    case 3:
    case 4:
    case 5:
    case 6:
      sign = `appId=${appId}&nonce=${nonce}&sigTime=${sigTime}&patientIds=${patientIds}&startTime=${startTime}&endTime=${endTime}&hospitalBranch=${hospitalBranch}`;
      break;
    case 7:
      sign = `appId=${appId}&nonce=${nonce}&sigTime=${sigTime}&patientIds=${patientIds}&startTime=${startTime}&endTime=${endTime}`;
      break;
    default:
      sign = `appId=${appId}&nonce=${nonce}&sigTime=${sigTime}&safePatientId=${safePatientId}&hospitalBranch=${hospitalBranch}`;
  }
  
  return sign;
}

/**
 * Query patient basic information
 * @param {Object} options - The request options
 * @param {string} options.secretKey - The secret key provided by Tencent
 * @param {string} options.appId - The appId provided by Tencent
 * @param {string} options.safePatientId - The encrypted patient ID
 * @param {string} [options.hospitalBranch] - Optional hospital branch code
 * @param {string} [options.channelCode] - Optional channel code
 * @param {string} [options.platformType] - Optional platform type
 * @param {string} [options.hostName] - Optional hostname, defaults to "http://************:6887/platform/Tx"
 * @param {boolean} [options.debugMode] - Whether to enable debug mode, defaults to true
 * @returns {Promise<Object>} - The patient information
 */
function queryPatientBaseInfo(options) {
  return new Promise((resolve, reject) => {
    const {
      secretKey,
      appId,
      safePatientId,
      hospitalBranch = '',
      channelCode = '5',
      platformType = '3',
      hostName = 'http://************:6887/platform/Tx',
      debugMode = true
    } = options;
    
    // 打印请求参数 - 便于调试
    if (debugMode) {
      console.log('\n======= 请求参数 =======');
      console.log('secretKey:', secretKey);
      console.log('appId:', appId);
      console.log('safePatientId:', safePatientId);
      console.log('hospitalBranch:', hospitalBranch);
      console.log('channelCode:', channelCode);
      console.log('platformType:', platformType);
      console.log('hostName:', hostName);
      console.log('========================\n');
    }
    
    // 生成签名所需参数
    const nonce = generateUUID();
    const sigTime = formatDateTime();
    
    // 生成签名
    const signParams = {
      appId,
      nonce,
      sigTime,
      safePatientId,
      hospitalBranch
    };
    
    const signString = encodeSign(signParams, 2);
    const signature = hmacSHA1(signString, secretKey);
    
    if (debugMode) {
      console.log('\n======= 签名信息 =======');
      console.log('nonce:', nonce);
      console.log('sigTime:', sigTime);
      console.log('签名原文 (完全匹配服务端xl=2):', signString);
      console.log('生成的签名:', signature);
      console.log('========================\n');
    }
    
    // 解析 URL
    const parsedUrl = url.parse(hostName);
    const isHttps = parsedUrl.protocol === 'https:';
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port || (isHttps ? 443 : 80);
    const path = `${parsedUrl.path}/queryPatientBaseInfo`.replace(/\/\//g, '/');
    
    // 构建请求体
    const requestBody = JSON.stringify({
      safePatientId,
      channelCode,
      platformType,
      ...(hospitalBranch ? { hospitalBranch } : {})
    });
    
    // 设置请求选项
    const requestOptions = {
      hostname,
      port,
      path,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBody),
        'signature': signature,
        'appid': appId,
        'nonce': nonce,
        'sigtime': sigTime
      }
    };
    
    if (debugMode) {
      console.log('\n======= 请求信息 =======');
      console.log('请求方法:', requestOptions.method);
      console.log('请求URL:', `${parsedUrl.protocol}//${hostname}:${port}${path}`);
      console.log('主机名:', hostname);
      console.log('端口:', port);
      console.log('路径:', path);
      console.log('请求头:');
      Object.entries(requestOptions.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      console.log('请求体:', requestBody);
      console.log('========================\n');
      
      // 生成 cURL 命令 - 便于排查问题
      let curlCommand = `curl -X POST "${parsedUrl.protocol}//${hostname}:${port}${path}" \\
  -H "Accept: application/json" \\
  -H "Content-Type: application/json" \\
  -H "signature: ${signature}" \\
  -H "appid: ${appId}" \\
  -H "nonce: ${nonce}" \\
  -H "sigtime: ${sigTime}" \\
  -d '${requestBody}'`;
      
      console.log('\n======= cURL命令 =======');
      console.log(curlCommand);
      console.log('========================\n');
    }
    
    // 创建请求
    const req = (isHttps ? https : http).request(requestOptions, (res) => {
      let data = '';
      
      // 接收数据
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // 接收完成
      res.on('end', () => {
        if (debugMode) {
          console.log('\n======= 响应信息 =======');
          console.log('响应状态码:', res.statusCode);
          console.log('响应头:');
          Object.entries(res.headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
          console.log('原始响应内容:');
          console.log(data);
          console.log('========================\n');
        }
        
        let responseData;
        
        try {
          responseData = JSON.parse(data);
          resolve(responseData);
        } catch (error) {
          if (debugMode) {
            console.error('解析响应JSON失败:', error.message);
            console.error('原始数据:', data);
          }
          reject(new Error(`解析响应JSON失败: ${error.message}`));
        }
      });
    });
    
    // 请求错误处理
    req.on('error', (error) => {
      if (debugMode) {
        console.error('\n======= 请求错误 =======');
        console.error(`错误类型: ${error.name}`);
        console.error(`错误消息: ${error.message}`);
        if (error.code) {
          console.error(`错误代码: ${error.code}`);
        }
        console.error('========================\n');
      }
      reject(error);
    });
    
    // 发送请求体
    req.write(requestBody);
    req.end();
    
    if (debugMode) {
      console.log('请求已发送，等待响应...\n');
    }
  });
}

/**
 * Query patient examination reports
 * @param {Object} options - The request options
 * @param {string} options.secretKey - The secret key
 * @param {string} options.appId - The appId
 * @param {string} options.patientIds - Comma-separated list of patient IDs
 * @param {string} options.startTime - Start time in format YYYY-MM-DD HH:mm:ss
 * @param {string} options.endTime - End time in format YYYY-MM-DD HH:mm:ss
 * @param {string} options.hospitalBranch - Hospital branch code, can be empty string
 * @param {string} [options.channelCode] - Optional channel code
 * @param {string} [options.platformType] - Optional platform type
 * @param {string} [options.hostName] - Optional hostname, defaults to "http://************:6887/platform/Tx"
 * @param {boolean} [options.debugMode] - Whether to enable debug mode, defaults to true
 * @returns {Promise<Object>} - The examination reports
 */
function queryPatientExaminationReport(options) {
  return new Promise((resolve, reject) => {
    const {
      secretKey,
      appId,
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode = '5',
      platformType = '3',
      hostName = 'http://************:6887/platform/Tx',
      debugMode = true
    } = options;
    
    // 打印请求参数 - 便于调试
    if (debugMode) {
      console.log('\n======= 请求参数 =======');
      console.log('secretKey:', secretKey);
      console.log('appId:', appId);
      console.log('patientIds:', patientIds);
      console.log('startTime:', startTime);
      console.log('endTime:', endTime);
      console.log('hospitalBranch:', hospitalBranch);
      console.log('channelCode:', channelCode);
      console.log('platformType:', platformType);
      console.log('hostName:', hostName);
      console.log('========================\n');
    }
    
    // 验证必填参数 - 修改验证逻辑，允许hospitalBranch为空字符串
    if (!secretKey || !appId || !patientIds || !startTime || !endTime || hospitalBranch === undefined) {
      return reject(new Error('secretKey, appId, patientIds, startTime, endTime, and hospitalBranch are required'));
    }
    
    // 生成签名所需参数
    const nonce = generateUUID();
    const sigTime = formatDateTime();
    
    // 生成签名
    const signParams = {
      appId,
      nonce,
      sigTime,
      patientIds,
      startTime,
      endTime,
      hospitalBranch
    };
    
    const signString = encodeSign(signParams, 5); 
    const signature = hmacSHA1(signString, secretKey);
    
    if (debugMode) {
      console.log('\n======= 签名信息 =======');
      console.log('nonce:', nonce);
      console.log('sigTime:', sigTime);
      console.log('签名原文:', signString);
      console.log('生成的签名:', signature);
      console.log('========================\n');
    }
    
    // 解析 URL
    const parsedUrl = url.parse(hostName);
    const isHttps = parsedUrl.protocol === 'https:';
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port || (isHttps ? 443 : 80);
    const path = `${parsedUrl.path}/queryPatientExaminationReport`.replace(/\/\//g, '/');
    
    // 构建请求体
    const requestBody = JSON.stringify({
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode,
      platformType
    });
    
    // 设置请求选项
    const requestOptions = {
      hostname,
      port,
      path,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBody),
        'signature': signature,
        'appid': appId,
        'nonce': nonce,
        'sigtime': sigTime
      }
    };
    
    if (debugMode) {
      console.log('\n======= 请求信息 =======');
      console.log('请求方法:', requestOptions.method);
      console.log('请求URL:', `${parsedUrl.protocol}//${hostname}:${port}${path}`);
      console.log('主机名:', hostname);
      console.log('端口:', port);
      console.log('路径:', path);
      console.log('请求头:');
      Object.entries(requestOptions.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      console.log('请求体:', requestBody);
      console.log('========================\n');
      
      // 生成 cURL 命令 - 便于排查问题
      let curlCommand = `curl -X POST "${parsedUrl.protocol}//${hostname}:${port}${path}" \\
  -H "Accept: application/json" \\
  -H "Content-Type: application/json" \\
  -H "signature: ${signature}" \\
  -H "appid: ${appId}" \\
  -H "nonce: ${nonce}" \\
  -H "sigtime: ${sigTime}" \\
  -d '${requestBody}'`;
      
      console.log('\n======= cURL命令 =======');
      console.log(curlCommand);
      console.log('========================\n');
    }
    
    // 创建请求
    const req = (isHttps ? https : http).request(requestOptions, (res) => {
      let data = '';
      
      // 接收数据
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // 接收完成
      res.on('end', () => {
        if (debugMode) {
          console.log('\n======= 响应信息 =======');
          console.log('响应状态码:', res.statusCode);
          console.log('响应头:');
          Object.entries(res.headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
          console.log('原始响应内容:');
          console.log(data);
          console.log('========================\n');
        }
        
        let responseData;
        
        try {
          responseData = JSON.parse(data);
          resolve(responseData);
        } catch (error) {
          if (debugMode) {
            console.error('解析响应JSON失败:', error.message);
            console.error('原始数据:', data);
          }
          reject(new Error(`解析响应JSON失败: ${error.message}`));
        }
      });
    });
    
    // 请求错误处理
    req.on('error', (error) => {
      if (debugMode) {
        console.error('\n======= 请求错误 =======');
        console.error(`错误类型: ${error.name}`);
        console.error(`错误消息: ${error.message}`);
        if (error.code) {
          console.error(`错误代码: ${error.code}`);
        }
        console.error('========================\n');
      }
      reject(error);
    });
    
    // 发送请求体
    req.write(requestBody);
    req.end();
    
    if (debugMode) {
      console.log('请求已发送，等待响应...\n');
    }
  });
}

/**
 * Query patient test reports
 * @param {Object} options - The request options
 * @param {string} options.secretKey - The secret key
 * @param {string} options.appId - The appId
 * @param {string} options.patientIds - Comma-separated list of patient IDs
 * @param {string} options.startTime - Start time in format YYYY-MM-DD HH:mm:ss
 * @param {string} options.endTime - End time in format YYYY-MM-DD HH:mm:ss
 * @param {string} options.hospitalBranch - Hospital branch code, can be empty string
 * @param {string} [options.channelCode] - Optional channel code
 * @param {string} [options.platformType] - Optional platform type
 * @param {string} [options.hostName] - Optional hostname, defaults to "http://************:6887/platform/Tx"
 * @param {boolean} [options.debugMode] - Whether to enable debug mode, defaults to true
 * @returns {Promise<Object>} - The test reports
 */
function queryPatientTestReport(options) {
  return new Promise((resolve, reject) => {
    const {
      secretKey,
      appId,
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode = '5',
      platformType = '3',
      hostName = 'http://************:6887/platform/Tx',
      debugMode = true
    } = options;
    
    // 打印请求参数 - 便于调试
    if (debugMode) {
      console.log('\n======= 请求参数 =======');
      console.log('secretKey:', secretKey);
      console.log('appId:', appId);
      console.log('patientIds:', patientIds);
      console.log('startTime:', startTime);
      console.log('endTime:', endTime);
      console.log('hospitalBranch:', hospitalBranch);
      console.log('channelCode:', channelCode);
      console.log('platformType:', platformType);
      console.log('hostName:', hostName);
      console.log('========================\n');
    }
    
    // 验证必填参数 - 允许hospitalBranch为空字符串
    if (!secretKey || !appId || !patientIds || !startTime || !endTime || hospitalBranch === undefined) {
      return reject(new Error('secretKey, appId, patientIds, startTime, endTime, and hospitalBranch are required'));
    }
    
    // 生成签名所需参数
    const nonce = generateUUID();
    const sigTime = formatDateTime();
    
    // 生成签名
    const signParams = {
      appId,
      nonce,
      sigTime,
      patientIds,
      startTime,
      endTime,
      hospitalBranch
    };
    
    const signString = encodeSign(signParams, 6); 
    const signature = hmacSHA1(signString, secretKey);
    
    if (debugMode) {
      console.log('\n======= 签名信息 =======');
      console.log('nonce:', nonce);
      console.log('sigTime:', sigTime);
      console.log('签名原文:', signString);
      console.log('生成的签名:', signature);
      console.log('========================\n');
    }
    
    // 解析 URL
    const parsedUrl = url.parse(hostName);
    const isHttps = parsedUrl.protocol === 'https:';
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port || (isHttps ? 443 : 80);
    const path = `${parsedUrl.path}/queryPatientTestReport`.replace(/\/\//g, '/');
    
    // 构建请求体
    const requestBody = JSON.stringify({
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode,
      platformType
    });
    
    // 设置请求选项
    const requestOptions = {
      hostname,
      port,
      path,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBody),
        'signature': signature,
        'appid': appId,
        'nonce': nonce,
        'sigtime': sigTime
      }
    };
    
    if (debugMode) {
      console.log('\n======= 请求信息 =======');
      console.log('请求方法:', requestOptions.method);
      console.log('请求URL:', `${parsedUrl.protocol}//${hostname}:${port}${path}`);
      console.log('主机名:', hostname);
      console.log('端口:', port);
      console.log('路径:', path);
      console.log('请求头:');
      Object.entries(requestOptions.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      console.log('请求体:', requestBody);
      console.log('========================\n');
      
      // 生成 cURL 命令 - 便于排查问题
      let curlCommand = `curl -X POST "${parsedUrl.protocol}//${hostname}:${port}${path}" \\
  -H "Accept: application/json" \\
  -H "Content-Type: application/json" \\
  -H "signature: ${signature}" \\
  -H "appid: ${appId}" \\
  -H "nonce: ${nonce}" \\
  -H "sigtime: ${sigTime}" \\
  -d '${requestBody}'`;
      
      console.log('\n======= cURL命令 =======');
      console.log(curlCommand);
      console.log('========================\n');
    }
    
    // 创建请求
    const req = (isHttps ? https : http).request(requestOptions, (res) => {
      let data = '';
      
      // 接收数据
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // 接收完成
      res.on('end', () => {
        if (debugMode) {
          console.log('\n======= 响应信息 =======');
          console.log('响应状态码:', res.statusCode);
          console.log('响应头:');
          Object.entries(res.headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
          console.log('原始响应内容:');
          console.log(data);
          console.log('========================\n');
        }
        
        let responseData;
        
        try {
          responseData = JSON.parse(data);
          resolve(responseData);
        } catch (error) {
          if (debugMode) {
            console.error('解析响应JSON失败:', error.message);
            console.error('原始数据:', data);
          }
          reject(new Error(`解析响应JSON失败: ${error.message}`));
        }
      });
    });
    
    // 请求错误处理
    req.on('error', (error) => {
      if (debugMode) {
        console.error('\n======= 请求错误 =======');
        console.error(`错误类型: ${error.name}`);
        console.error(`错误消息: ${error.message}`);
        if (error.code) {
          console.error(`错误代码: ${error.code}`);
        }
        console.error('========================\n');
      }
      reject(error);
    });
    
    // 发送请求体
    req.write(requestBody);
    req.end();
    
    if (debugMode) {
      console.log('请求已发送，等待响应...\n');
    }
  });
}

/**
 * Query outpatient medical records
 * @param {Object} options - The request options
 * @param {string} options.secretKey - The secret key
 * @param {string} options.appId - The appId
 * @param {string} options.patientIds - Comma-separated list of patient IDs
 * @param {string} options.startTime - Start time in format YYYY-MM-DD HH:mm:ss
 * @param {string} options.endTime - End time in format YYYY-MM-DD HH:mm:ss
 * @param {string} options.hospitalBranch - Hospital branch code, can be empty string
 * @param {string} [options.channelCode] - Optional channel code
 * @param {string} [options.platformType] - Optional platform type
 * @param {string} [options.hostName] - Optional hostname, defaults to "http://************:6887/platform/Tx"
 * @param {boolean} [options.debugMode] - Whether to enable debug mode, defaults to true
 * @returns {Promise<Object>} - The outpatient medical records
 */
function queryOutpatientMedicalRecords(options) {
  return new Promise((resolve, reject) => {
    const {
      secretKey,
      appId,
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode = '5',
      platformType = '3',
      hostName = 'http://************:6887/platform/Tx',
      debugMode = true
    } = options;
    
    // 打印请求参数 - 便于调试
    if (debugMode) {
      console.log('\n======= 请求参数 =======');
      console.log('secretKey:', secretKey);
      console.log('appId:', appId);
      console.log('patientIds:', patientIds);
      console.log('startTime:', startTime);
      console.log('endTime:', endTime);
      console.log('hospitalBranch:', hospitalBranch);
      console.log('channelCode:', channelCode);
      console.log('platformType:', platformType);
      console.log('hostName:', hostName);
      console.log('========================\n');
    }
    
    // 验证必填参数 - 允许hospitalBranch为空字符串
    if (!secretKey || !appId || !patientIds || !startTime || !endTime || hospitalBranch === undefined) {
      return reject(new Error('secretKey, appId, patientIds, startTime, endTime, and hospitalBranch are required'));
    }
    
    // 生成签名所需参数
    const nonce = generateUUID();
    const sigTime = formatDateTime();
    
    // 生成签名
    const signParams = {
      appId,
      nonce,
      sigTime,
      patientIds,
      startTime,
      endTime,
      hospitalBranch
    };
    
    const signString = encodeSign(signParams, 3); 
    const signature = hmacSHA1(signString, secretKey);
    
    if (debugMode) {
      console.log('\n======= 签名信息 =======');
      console.log('nonce:', nonce);
      console.log('sigTime:', sigTime);
      console.log('签名原文 (xl=5):', signString);
      console.log('生成的签名:', signature);
      console.log('========================\n');
    }
    
    // 解析 URL
    const parsedUrl = url.parse(hostName);
    const isHttps = parsedUrl.protocol === 'https:';
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port || (isHttps ? 443 : 80);
    const path = `${parsedUrl.path}/queryOutpatientMedicalRecords`.replace(/\/\//g, '/');
    
    // 构建请求体
    const requestBody = JSON.stringify({
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode,
      platformType
    });
    
    // 设置请求选项
    const requestOptions = {
      hostname,
      port,
      path,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBody),
        'signature': signature,
        'appid': appId,
        'nonce': nonce,
        'sigtime': sigTime
      }
    };
    
    if (debugMode) {
      console.log('\n======= 请求信息 =======');
      console.log('请求方法:', requestOptions.method);
      console.log('请求URL:', `${parsedUrl.protocol}//${hostname}:${port}${path}`);
      console.log('主机名:', hostname);
      console.log('端口:', port);
      console.log('路径:', path);
      console.log('请求头:');
      Object.entries(requestOptions.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      console.log('请求体:', requestBody);
      console.log('========================\n');
      
      // 生成 cURL 命令 - 便于排查问题
      let curlCommand = `curl -X POST "${parsedUrl.protocol}//${hostname}:${port}${path}" \\
  -H "Accept: application/json" \\
  -H "Content-Type: application/json" \\
  -H "signature: ${signature}" \\
  -H "appid: ${appId}" \\
  -H "nonce: ${nonce}" \\
  -H "sigtime: ${sigTime}" \\
  -d '${requestBody}'`;
      
      console.log('\n======= cURL命令 =======');
      console.log(curlCommand);
      console.log('========================\n');
    }
    
    // 创建请求
    const req = (isHttps ? https : http).request(requestOptions, (res) => {
      let data = '';
      
      // 接收数据
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // 接收完成
      res.on('end', () => {
        if (debugMode) {
          console.log('\n======= 响应信息 =======');
          console.log('响应状态码:', res.statusCode);
          console.log('响应头:');
          Object.entries(res.headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
          console.log('原始响应内容:');
          console.log(data);
          console.log('========================\n');
        }
        
        let responseData;
        
        try {
          responseData = JSON.parse(data);
          resolve(responseData);
        } catch (error) {
          if (debugMode) {
            console.error('解析响应JSON失败:', error.message);
            console.error('原始数据:', data);
          }
          reject(new Error(`解析响应JSON失败: ${error.message}`));
        }
      });
    });
    
    // 请求错误处理
    req.on('error', (error) => {
      if (debugMode) {
        console.error('\n======= 请求错误 =======');
        console.error(`错误类型: ${error.name}`);
        console.error(`错误消息: ${error.message}`);
        if (error.code) {
          console.error(`错误代码: ${error.code}`);
        }
        console.error('========================\n');
      }
      reject(error);
    });
    
    // 发送请求体
    req.write(requestBody);
    req.end();
    
    if (debugMode) {
      console.log('请求已发送，等待响应...\n');
    }
  });
}

/**
 * 查询患者住院记录
 * @param {Object} options - 请求选项
 * @param {string} options.secretKey - 腾讯提供的密钥
 * @param {string} options.appId - 应用ID
 * @param {string} options.patientIds - 患者ID列表，用逗号分隔
 * @param {string} options.startTime - 开始时间，格式YYYY-MM-DD HH:MM:SS
 * @param {string} options.endTime - 结束时间，格式YYYY-MM-DD HH:MM:SS
 * @param {string} options.hospitalBranch - 分院编码
 * @param {string} [options.channelCode='5'] - 渠道编码
 * @param {string} [options.platformType='3'] - 平台类型
 * @param {string} [options.hostName='http://***************:19077/platform/Tx'] - 主机名
 * @param {boolean} [options.debugMode=true] - 是否启用调试模式
 * @returns {Promise<Object>} - 住院记录
 */
function queryInpatientRecords(options) {
  return new Promise((resolve, reject) => {
    const {
      secretKey,
      appId,
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode = '5',
      platformType = '3',
      hostName = 'http://***************:19077/platform/Tx',
      debugMode = true
    } = options;
    
    // 打印请求参数 - 便于调试
    if (debugMode) {
      console.log('\n======= 请求参数 =======');
      console.log('secretKey:', secretKey);
      console.log('appId:', appId);
      console.log('patientIds:', patientIds);
      console.log('startTime:', startTime);
      console.log('endTime:', endTime);
      console.log('hospitalBranch:', hospitalBranch);
      console.log('channelCode:', channelCode);
      console.log('platformType:', platformType);
      console.log('hostName:', hostName);
      console.log('========================\n');
    }
    
    // 验证必填参数
    if (!secretKey || !appId || !patientIds || !startTime || !endTime || hospitalBranch === undefined) {
      return reject(new Error('secretKey, appId, patientIds, startTime, endTime, and hospitalBranch are required'));
    }
    
    // 生成签名所需参数
    const nonce = generateUUID();
    const sigTime = formatDateTime();
    
    // 生成签名
    const signParams = {
      appId,
      nonce,
      sigTime,
      patientIds,
      startTime,
      endTime,
      hospitalBranch
    };
    
    const signString = encodeSign(signParams, 4); 
    const signature = hmacSHA1(signString, secretKey);
    
    if (debugMode) {
      console.log('\n======= 签名信息 =======');
      console.log('nonce:', nonce);
      console.log('sigTime:', sigTime);
      console.log('签名原文 (xl=6):', signString);
      console.log('生成的签名:', signature);
      console.log('========================\n');
    }
    
    // 解析 URL
    const parsedUrl = url.parse(hostName);
    const isHttps = parsedUrl.protocol === 'https:';
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port || (isHttps ? 443 : 80);
    const path = `${parsedUrl.path}/queryInpatientRecords`.replace(/\/\//g, '/');
    
    // 构建请求体
    const requestBody = JSON.stringify({
      patientIds,
      startTime,
      endTime,
      hospitalBranch,
      channelCode,
      platformType
    });
    
    // 设置请求选项
    const requestOptions = {
      hostname,
      port,
      path,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBody),
        'signature': signature,
        'appid': appId,
        'nonce': nonce,
        'sigtime': sigTime
      }
    };
    
    if (debugMode) {
      console.log('\n======= 请求信息 =======');
      console.log('请求方法:', requestOptions.method);
      console.log('请求URL:', `${parsedUrl.protocol}//${hostname}:${port}${path}`);
      console.log('主机名:', hostname);
      console.log('端口:', port);
      console.log('路径:', path);
      console.log('请求头:');
      Object.entries(requestOptions.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      console.log('请求体:', requestBody);
      console.log('========================\n');
      
      // 生成 cURL 命令 - 便于排查问题
      let curlCommand = `curl -X POST "${parsedUrl.protocol}//${hostname}:${port}${path}" \\
  -H "Accept: application/json" \\
  -H "Content-Type: application/json" \\
  -H "signature: ${signature}" \\
  -H "appid: ${appId}" \\
  -H "nonce: ${nonce}" \\
  -H "sigtime: ${sigTime}" \\
  -d '${requestBody}'`;
      
      console.log('\n======= cURL命令 =======');
      console.log(curlCommand);
      console.log('========================\n');
    }
    
    // 创建请求
    const req = (isHttps ? https : http).request(requestOptions, (res) => {
      let data = '';
      
      // 接收数据
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // 接收完成
      res.on('end', () => {
        if (debugMode) {
          console.log('\n======= 响应信息 =======');
          console.log('响应状态码:', res.statusCode);
          console.log('响应头:');
          Object.entries(res.headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
          console.log('原始响应内容:');
          console.log(data);
          console.log('========================\n');
        }
        
        let responseData;
        
        try {
          responseData = JSON.parse(data);
          resolve(responseData);
        } catch (error) {
          if (debugMode) {
            console.error('解析响应JSON失败:', error.message);
            console.error('原始数据:', data);
          }
          reject(new Error(`解析响应JSON失败: ${error.message}`));
        }
      });
    });
    
    // 请求错误处理
    req.on('error', (error) => {
      if (debugMode) {
        console.error('\n======= 请求错误 =======');
        console.error(`错误类型: ${error.name}`);
        console.error(`错误消息: ${error.message}`);
        if (error.code) {
          console.error(`错误代码: ${error.code}`);
        }
        console.error('========================\n');
      }
      reject(error);
    });
    
    // 发送请求体
    req.write(requestBody);
    req.end();
    
    if (debugMode) {
      console.log('请求已发送，等待响应...\n');
    }
  });
}

/**
 * 查询患者就诊记录(门诊、急诊、住院、体检)
 * @param {Object} options - 请求选项
 * @param {string} options.secretKey - 腾讯提供的密钥
 * @param {string} options.appId - 应用ID
 * @param {string} options.hospitalBranch - 分院编码
 * @param {string} options.startTime - 开始时间，格式YYYY-MM-DD HH:MM:SS
 * @param {string} options.endTime - 结束时间，格式YYYY-MM-DD HH:MM:SS
 * @param {number} options.pageIndex - 分页索引
 * @param {number} options.pageSize - 分页大小
 * @param {string} options.source - 就诊来源(门诊:outpatient、急诊:emergency、住院:inpatient、体检:physicalExam)
 * @param {string} [options.channelCode='5'] - 渠道编码
 * @param {string} [options.platformType='3'] - 平台类型
 * @param {string} [options.hostName='http://***************:19077/platform/Tx'] - 主机名
 * @param {boolean} [options.debugMode=true] - 是否启用调试模式
 * @returns {Promise<Object>} - 就诊记录
 */
function queryVisitPatientList(options) {
  return new Promise((resolve, reject) => {
    const {
      secretKey,
      appId,
      hospitalBranch,
      startTime,
      endTime,
      pageIndex,
      pageSize,
      source,
      channelCode = '5',
      platformType = '3',
      hostName = 'http://***************:19077/platform/Tx',
      debugMode = true
    } = options;
    
    // 打印请求参数 - 便于调试
    if (debugMode) {
      console.log('\n======= 请求参数 =======');
      console.log('secretKey:', secretKey);
      console.log('appId:', appId);
      console.log('hospitalBranch:', hospitalBranch);
      console.log('startTime:', startTime);
      console.log('endTime:', endTime);
      console.log('pageIndex:', pageIndex);
      console.log('pageSize:', pageSize);
      console.log('source:', source);
      console.log('channelCode:', channelCode);
      console.log('platformType:', platformType);
      console.log('hostName:', hostName);
      console.log('========================\n');
    }
    
    // 验证必填参数
    if (!secretKey || !appId || !hospitalBranch || !startTime || !endTime || 
        pageIndex === undefined || pageSize === undefined || !source) {
      return reject(new Error('secretKey, appId, hospitalBranch, startTime, endTime, pageIndex, pageSize, and source are required'));
    }
    
    // 验证source参数
    const validSources = ['outpatient', 'emergency', 'inpatient', 'physicalExam'];
    if (!validSources.includes(source)) {
      return reject(new Error('source must be one of: outpatient, emergency, inpatient, physicalExam'));
    }
    
    // 生成签名所需参数
    const nonce = generateUUID();
    const sigTime = formatDateTime();
    
    // 生成签名
    const signParams = {
      appId,
      nonce,
      sigTime,
      hospitalBranch,
      startTime,
      endTime,
      pageIndex,
      pageSize,
      source
    };
    
    const signString = encodeSign(signParams, 1); 
    const signature = hmacSHA1(signString, secretKey);
    
    if (debugMode) {
      console.log('\n======= 签名信息 =======');
      console.log('nonce:', nonce);
      console.log('sigTime:', sigTime);
      console.log('签名原文 (xl=1):', signString);
      console.log('生成的签名:', signature);
      console.log('========================\n');
    }
    
    // 解析 URL
    const parsedUrl = url.parse(hostName);
    const isHttps = parsedUrl.protocol === 'https:';
    const hostname = parsedUrl.hostname;
    const port = parsedUrl.port || (isHttps ? 443 : 80);
    const path = `${parsedUrl.path}/queryVisitPatientList`.replace(/\/\//g, '/');
    
    // 构建请求体
    const requestBody = JSON.stringify({
      hospitalBranch,
      startTime,
      endTime,
      pageIndex,
      pageSize,
      source,
      channelCode,
      platformType
    });
    
    // 设置请求选项
    const requestOptions = {
      hostname,
      port,
      path,
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBody),
        'signature': signature,
        'appid': appId,
        'nonce': nonce,
        'sigtime': sigTime
      }
    };
    
    if (debugMode) {
      console.log('\n======= 请求信息 =======');
      console.log('请求方法:', requestOptions.method);
      console.log('请求URL:', `${parsedUrl.protocol}//${hostname}:${port}${path}`);
      console.log('主机名:', hostname);
      console.log('端口:', port);
      console.log('路径:', path);
      console.log('请求头:');
      Object.entries(requestOptions.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      console.log('请求体:', requestBody);
      console.log('========================\n');
      
      // 生成 cURL 命令 - 便于排查问题
      let curlCommand = `curl -X POST "${parsedUrl.protocol}//${hostname}:${port}${path}" \\
  -H "Accept: application/json" \\
  -H "Content-Type: application/json" \\
  -H "signature: ${signature}" \\
  -H "appid: ${appId}" \\
  -H "nonce: ${nonce}" \\
  -H "sigtime: ${sigTime}" \\
  -d '${requestBody}'`;
      
      console.log('\n======= cURL命令 =======');
      console.log(curlCommand);
      console.log('========================\n');
    }
    
    // 创建请求
    const req = (isHttps ? https : http).request(requestOptions, (res) => {
      let data = '';
      
      // 接收数据
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // 接收完成
      res.on('end', () => {
        if (debugMode) {
          console.log('\n======= 响应信息 =======');
          console.log('响应状态码:', res.statusCode);
          console.log('响应头:');
          Object.entries(res.headers).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
          console.log('原始响应内容:');
          console.log(data);
          console.log('========================\n');
        }
        
        let responseData;
        
        try {
          responseData = JSON.parse(data);
          resolve(responseData);
        } catch (error) {
          if (debugMode) {
            console.error('解析响应JSON失败:', error.message);
            console.error('原始数据:', data);
          }
          reject(new Error(`解析响应JSON失败: ${error.message}`));
        }
      });
    });
    
    // 请求错误处理
    req.on('error', (error) => {
      if (debugMode) {
        console.error('\n======= 请求错误 =======');
        console.error(`错误类型: ${error.name}`);
        console.error(`错误消息: ${error.message}`);
        if (error.code) {
          console.error(`错误代码: ${error.code}`);
        }
        console.error('========================\n');
      }
      reject(error);
    });
    
    // 发送请求体
    req.write(requestBody);
    req.end();
    
    if (debugMode) {
      console.log('请求已发送，等待响应...\n');
    }
  });
}

module.exports = {
  queryPatientBaseInfo,
  queryPatientExaminationReport,
  queryPatientTestReport,
  queryOutpatientMedicalRecords,
  queryInpatientRecords,
  queryVisitPatientList,
  hmacSHA1,
  encodeSign
}; 
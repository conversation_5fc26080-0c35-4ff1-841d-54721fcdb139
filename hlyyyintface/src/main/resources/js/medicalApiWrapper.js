// 医疗API包装模块
// 此模块封装了医疗API调用，隐藏敏感信息如secretKey、appId、hostName等

const { 
  queryPatientExaminationReport, 
  queryPatientTestReport,
  queryOutpatientMedicalRecords,
  queryInpatientRecords
} = require('./medicalApiClient');

// 隐藏的配置信息
const DEFAULT_CONFIG = {
  secretKey: 'dNN2zOEx6RIu472sNywkHupr2m1hD4RQ',
  appId: 'OUuYjuSe99LvnbmE',
  hostName: 'http://************:6887/platform/Tx',
  hospitalBranch: 'yuexiu'
};

/**
 * 获取患者检查报告
 * @param {Object} params - 请求参数
 * @param {string} params.patientIds - 患者ID，多个ID用逗号分隔
 * @param {string} params.startTime - 开始时间，格式: YYYY-MM-DD HH:MM:SS
 * @param {string} params.endTime - 结束时间，格式: YYYY-MM-DD HH:MM:SS
 * @returns {Promise<Object>} - 检查报告数据
 */
async function getPatientExaminationReports(params) {
  const options = {
    ...DEFAULT_CONFIG,
    patientIds: params.patientIds,
    startTime: params.startTime,
    endTime: params.endTime
  };
  
  return await queryPatientExaminationReport(options);
}

/**
 * 获取患者检验报告
 * @param {Object} params - 请求参数
 * @param {string} params.patientIds - 患者ID，多个ID用逗号分隔
 * @param {string} params.startTime - 开始时间，格式: YYYY-MM-DD HH:MM:SS
 * @param {string} params.endTime - 结束时间，格式: YYYY-MM-DD HH:MM:SS
 * @returns {Promise<Object>} - 检验报告数据
 */
async function getPatientTestReports(params) {
  const options = {
    ...DEFAULT_CONFIG,
    patientIds: params.patientIds,
    startTime: params.startTime,
    endTime: params.endTime
  };
  
  return await queryPatientTestReport(options);
}

/**
 * 获取患者门诊病历
 * @param {Object} params - 请求参数
 * @param {string} params.patientIds - 患者ID，多个ID用逗号分隔
 * @param {string} params.startTime - 开始时间，格式: YYYY-MM-DD HH:MM:SS
 * @param {string} params.endTime - 结束时间，格式: YYYY-MM-DD HH:MM:SS
 * @returns {Promise<Object>} - 门诊病历数据
 */
async function getOutpatientRecords(params) {
  const options = {
    ...DEFAULT_CONFIG,
    patientIds: params.patientIds,
    startTime: params.startTime,
    endTime: params.endTime
  };
  
  return await queryOutpatientMedicalRecords(options);
}

/**
 * 获取患者住院记录
 * @param {Object} params - 请求参数
 * @param {string} params.patientIds - 患者ID，多个ID用逗号分隔
 * @param {string} params.startTime - 开始时间，格式: YYYY-MM-DD HH:MM:SS
 * @param {string} params.endTime - 结束时间，格式: YYYY-MM-DD HH:MM:SS
 * @returns {Promise<Object>} - 住院记录数据
 */
async function getInpatientRecords(params) {
  const options = {
    ...DEFAULT_CONFIG,
    patientIds: params.patientIds,
    startTime: params.startTime,
    endTime: params.endTime
  };
  
  return await queryInpatientRecords(options);
}

module.exports = {
  getPatientExaminationReports,
  getPatientTestReports,
  getOutpatientRecords,
  getInpatientRecords
}; 
// 医疗API服务器
const express = require('express');
const bodyParser = require('body-parser');
const {
  getPatientExaminationReports,
  getPatientTestReports,
  getOutpatientRecords,
  getInpatientRecords
} = require('./medicalApiWrapper');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(bodyParser.json());

// API路由

// 1. 获取患者检查报告
app.post('/api/examination-reports', async (req, res) => {
  try {
    const { patientIds, startTime, endTime } = req.body;
    
    // 验证必要参数
    if (!patientIds || !startTime || !endTime) {
      return res.status(400).json({ 
        code: 400, 
        msg: '缺少必要参数，需要提供patientIds、startTime和endTime',
        body: null
      });
    }
    
    const result = await getPatientExaminationReports({
      patientIds,
      startTime,
      endTime
    });
    
    // 处理API返回结果
    if (result.retCode === 0) {
      res.json({
        code: 200,
        msg: '获取患者检查报告成功',
        body: result.examReports || []
      });
    } else {
      res.status(500).json({
        code: 500,
        msg: result.retMsg || '获取患者检查报告失败',
        body: null
      });
    }
  } catch (error) {
    console.error('获取检查报告出错:', error);
    res.status(500).json({ 
      code: 500, 
      msg: '服务器内部错误: ' + error.message,
      body: null
    });
  }
});

// 2. 获取患者检验报告
app.post('/api/test-reports', async (req, res) => {
  try {
    const { patientIds, startTime, endTime } = req.body;
    
    // 验证必要参数
    if (!patientIds || !startTime || !endTime) {
      return res.status(400).json({ 
        code: 400, 
        msg: '缺少必要参数，需要提供patientIds、startTime和endTime',
        body: null
      });
    }
    
    const result = await getPatientTestReports({
      patientIds,
      startTime,
      endTime
    });
    
    // 处理API返回结果
    if (result.retCode === 0) {
      res.json({
        code: 200,
        msg: '获取患者检验报告成功',
        body: result.testReports || []
      });
    } else {
      res.status(500).json({
        code: 500,
        msg: result.retMsg || '获取患者检验报告失败',
        body: null
      });
    }
  } catch (error) {
    console.error('获取检验报告出错:', error);
    res.status(500).json({ 
      code: 500, 
      msg: '服务器内部错误: ' + error.message,
      body: null
    });
  }
});

// 3. 获取患者门诊病历
app.post('/api/outpatient-records', async (req, res) => {
  try {
    const { patientIds, startTime, endTime } = req.body;
    
    // 验证必要参数
    if (!patientIds || !startTime || !endTime) {
      return res.status(400).json({ 
        code: 400, 
        msg: '缺少必要参数，需要提供patientIds、startTime和endTime',
        body: null
      });
    }
    
    const result = await getOutpatientRecords({
      patientIds,
      startTime,
      endTime
    });
    
    // 处理API返回结果
    if (result.retCode === 0) {
      res.json({
        code: 200,
        msg: '获取患者门诊病历成功',
        body: result.outpatientRecords || []
      });
    } else {
      res.status(500).json({
        code: 500,
        msg: result.retMsg || '获取患者门诊病历失败',
        body: null
      });
    }
  } catch (error) {
    console.error('获取门诊病历出错:', error);
    res.status(500).json({ 
      code: 500, 
      msg: '服务器内部错误: ' + error.message,
      body: null
    });
  }
});

// 4. 获取患者住院记录
app.post('/api/inpatient-records', async (req, res) => {
  try {
    const { patientIds, startTime, endTime } = req.body;
    
    // 验证必要参数
    if (!patientIds || !startTime || !endTime) {
      return res.status(400).json({ 
        code: 400, 
        msg: '缺少必要参数，需要提供patientIds、startTime和endTime',
        body: null
      });
    }
    
    const result = await getInpatientRecords({
      patientIds,
      startTime,
      endTime
    });
    
    // 处理API返回结果
    if (result.retCode === 0) {
      // 合并住院记录和病案首页为一个数组
      const combinedRecords = {
        frontSheetRecords: result.frontSheetRecords || [],
        inpatientRecords: result.inpatientRecords || []
      };
      
      res.json({
        code: 200,
        msg: '获取患者住院记录成功',
        body: combinedRecords
      });
    } else {
      res.status(500).json({
        code: 500,
        msg: result.retMsg || '获取患者住院记录失败',
        body: null
      });
    }
  } catch (error) {
    console.error('获取住院记录出错:', error);
    res.status(500).json({ 
      code: 500, 
      msg: '服务器内部错误: ' + error.message,
      body: null
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`医疗API服务器已启动，监听端口: ${PORT}`);
  console.log('可用API路径:');
  console.log('- POST /api/examination-reports - 获取患者检查报告');
  console.log('- POST /api/test-reports - 获取患者检验报告');
  console.log('- POST /api/outpatient-records - 获取患者门诊病历');
  console.log('- POST /api/inpatient-records - 获取患者住院记录');
}); 
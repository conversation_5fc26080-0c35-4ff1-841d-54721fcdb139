package com.nx.inf.controller;

import com.nx.inf.model.entity.QuestionnaireQuestion;
import com.nx.inf.service.QuestionnaireQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/emr/questionnaire-questions")
public class QuestionnaireQuestionController {

    @Autowired
    private QuestionnaireQuestionService questionService;

    @PostMapping
    public QuestionnaireQuestion createQuestion(@RequestBody QuestionnaireQuestion question) {
        return questionService.createQuestion(question);
    }

    @PutMapping("/{id}")
    public QuestionnaireQuestion updateQuestion(@PathVariable Long id, @RequestBody QuestionnaireQuestion question) {
        question.setId(id);
        return questionService.updateQuestion(question);
    }

    @DeleteMapping("/{id}")
    public void deleteQuestion(@PathVariable Long id) {
        questionService.deleteQuestion(id);
    }

    @GetMapping("/{id}")
    public QuestionnaireQuestion getQuestionById(@PathVariable Long id) {
        return questionService.getQuestionById(id);
    }

    @GetMapping("/by-questionnaire/{questionnaireId}")
    public List<QuestionnaireQuestion> getQuestionsByQuestionnaireId(@PathVariable Long questionnaireId) {
        return questionService.getQuestionsByQuestionnaireId(questionnaireId);
    }

    @GetMapping
    public List<QuestionnaireQuestion> getAllQuestions() {
        return questionService.getAllQuestions();
    }
}

package com.nx.inf.controller;

import com.nx.inf.model.entity.NhPatientInfo;
import com.nx.inf.service.NhPatientInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 病人信息管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/emr/nhpatient")
public class NhPatientInfoController {

    @Resource
    private NhPatientInfoService nhPatientInfoService;

    @RequestMapping("/list")
    public ResponseEntity<Map<String, Object>> getPatientList(@RequestBody Map<String, Object> parms) {
        log.info("获取病人信息列表{}", parms);
        Map<String, Object> response = new HashMap<>();

        NhPatientInfo nhPatientInfo = new NhPatientInfo();

        if (parms.get("patientId") != null) {
            nhPatientInfo.setPatientId(parms.get("patientId").toString());
        }

        if (parms.get("patientName") != null) {
            nhPatientInfo.setPatientName(parms.get("patientName").toString());
        }

        if (parms.get("gender") != null) {
            nhPatientInfo.setGender(parms.get("gender").toString());
        }

        if (parms.get("phone") != null) {
            nhPatientInfo.setPhone(parms.get("phone").toString());
        }

        // 新增：支持按养老机构代码过滤
        if (parms.get("nursingHomeCode") != null) {
            nhPatientInfo.setNursingHomeCode(parms.get("nursingHomeCode").toString());
        }

        List<NhPatientInfo> patientList = nhPatientInfoService.getNhPatientInfoList(nhPatientInfo);

        if (patientList != null) {
            response.put("data", patientList);
            response.put("code", 200);
            response.put("message", "获取病人信息列表成功");
        } else {
            response.put("code", 500);
            response.put("message", "获取病人信息列表失败");
        }

        log.info("获取病人信息列表{}", response);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/{patientId}")
    public NhPatientInfo getPatientById(
            @PathVariable String patientId) {
        return nhPatientInfoService.getPatientInfoById(patientId);
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> addPatient(
            @RequestBody NhPatientInfo patientInfo) {
        log.info("新增患者信息{}", patientInfo);
        Map<String, Object> response = new HashMap<>();
        
        boolean success = nhPatientInfoService.addPatientInfo(patientInfo);
        
        if (success) {
            response.put("code", 200);
            response.put("message", "新增患者信息成功");
            response.put("data", patientInfo);
        } else {
            response.put("code", 500);
            response.put("message", "新增患者信息失败");
        }
        
        log.info("新增患者信息结果{}", response);
        return ResponseEntity.ok(response);
    }

    @PutMapping
    public boolean updatePatient(
            @RequestBody NhPatientInfo nhPatientInfo) {
        return nhPatientInfoService.updateNhPatientInfo(nhPatientInfo);
    }

    @DeleteMapping("/{patientId}")
    public boolean deletePatient(
            @PathVariable String patientId) {
        return nhPatientInfoService.deletePatientInfoById(patientId);
    }
}

package com.nx.inf.controller;

import com.nx.inf.annotation.TargetDataSource;
import com.nx.inf.enums.DataSourceType;
import com.nx.inf.model.entity.InternetPatient;
import com.nx.inf.model.vo.ApiResponseVO;
import com.nx.inf.model.vo.PageResponseVO;
import com.nx.inf.model.vo.PatientListRequestVO;
import com.nx.inf.service.InternetPatientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 患者信息接口
 * @Author: zhongx
 * @CreateDate: 2024/5/30
 * @Version: 1.0
 */
@RestController
@RequestMapping("/emr/patient")
@TargetDataSource(value = DataSourceType.MYSQL_DATASOURCE)
@Slf4j
public class InternetPatientController {
    
    @Autowired
    private InternetPatientService patientService;
    
    /**
     * 分页获取患者列表
     * @param requestVO 查询参数
     * @return 患者列表数据
     */
    @PostMapping("/list")
    public ResponseEntity<ApiResponseVO<?>> getPatientList(@RequestBody @Validated PatientListRequestVO requestVO) {
        try {
            log.info("请求获取患者列表，参数：{}", requestVO);
            
            // 查询患者列表
            PageResponseVO<InternetPatient> pageData = patientService.getPatientList(requestVO);
            
            return ResponseEntity.ok(ApiResponseVO.success(
                "获取患者列表成功",
                pageData
            ));
        } catch (Exception e) {
            log.error("获取患者列表出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }
} 
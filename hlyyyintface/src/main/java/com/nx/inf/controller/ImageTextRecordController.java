package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nx.inf.model.dto.ImageTextRecordDTO;
import com.nx.inf.service.ImageTextRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片文字识别记录控制器
 */
@RestController
@RequestMapping("/emr/image-text")
public class ImageTextRecordController {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageTextRecordController.class);
    
    @Autowired
    private ImageTextRecordService imageTextRecordService;
    
    /**
     * 上传图片并进行文字识别
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadAndRecognize(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "creatorId", required = false) Long creatorId,
            @RequestParam(value = "creatorName", required = false) String creatorName) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 参数验证
            if (file == null || file.isEmpty()) {
                result.put("code", 400);
                result.put("message", "请选择要上传的图片文件");
                return ResponseEntity.ok(result);
            }
            
            // 设置默认值
            if (creatorId == null) {
                creatorId = 0L; // 系统默认
            }
            if (creatorName == null || creatorName.trim().isEmpty()) {
                creatorName = "系统"; // 系统默认
            }
            
            logger.info("开始处理图片上传，文件名: {}, 大小: {} bytes, 创建人: {}", 
                       file.getOriginalFilename(), file.getSize(), creatorName);
            
            // 上传并识别
            Long recordId = imageTextRecordService.uploadAndRecognize(file, creatorId, creatorName);
            
            result.put("code", 200);
            result.put("message", "图片上传成功，正在进行文字识别");
            result.put("data", recordId);
            
            logger.info("图片上传成功，记录ID: {}", recordId);
            
        } catch (IllegalArgumentException e) {
            logger.warn("图片上传参数错误: {}", e.getMessage());
            result.put("code", 400);
            result.put("message", e.getMessage());
        } catch (Exception e) {
            logger.error("图片上传失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "图片上传失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据ID获取记录详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getRecord(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            ImageTextRecordDTO record = imageTextRecordService.getRecordById(id);
            
            if (record != null) {
                result.put("code", 200);
                result.put("message", "获取记录成功");
                result.put("data", record);
            } else {
                result.put("code", 404);
                result.put("message", "记录不存在");
            }
            
        } catch (Exception e) {
            logger.error("获取记录失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "获取记录失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 分页查询记录列表
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> getRecordPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(required = false) String originalFilename) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            IPage<ImageTextRecordDTO> recordPage = imageTextRecordService.getRecordPage(
                    page, size, status, creatorId, originalFilename);
            
            result.put("code", 200);
            result.put("message", "获取记录列表成功");
            result.put("data", recordPage);
            
        } catch (Exception e) {
            logger.error("获取记录列表失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "获取记录列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据创建人ID查询记录列表
     */
    @GetMapping("/creator/{creatorId}")
    public ResponseEntity<Map<String, Object>> getRecordsByCreatorId(@PathVariable Long creatorId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ImageTextRecordDTO> records = imageTextRecordService.getRecordsByCreatorId(creatorId);
            
            result.put("code", 200);
            result.put("message", "获取用户记录列表成功");
            result.put("data", records);
            
        } catch (Exception e) {
            logger.error("获取用户记录列表失败，创建人ID: {}, 错误: {}", creatorId, e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "获取用户记录列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据状态查询记录列表
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<Map<String, Object>> getRecordsByStatus(@PathVariable String status) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<ImageTextRecordDTO> records = imageTextRecordService.getRecordsByStatus(status);
            
            result.put("code", 200);
            result.put("message", "获取状态记录列表成功");
            result.put("data", records);
            
        } catch (Exception e) {
            logger.error("获取状态记录列表失败，状态: {}, 错误: {}", status, e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "获取状态记录列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 删除记录
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteRecord(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = imageTextRecordService.deleteRecord(id);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "删除记录成功");
            } else {
                result.put("code", 404);
                result.put("message", "记录不存在或删除失败");
            }
            
        } catch (Exception e) {
            logger.error("删除记录失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "删除记录失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 重新识别图片文字
     */
    @PostMapping("/{id}/re-recognize")
    public ResponseEntity<Map<String, Object>> reRecognize(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = imageTextRecordService.reRecognize(id);

            if (success) {
                result.put("code", 200);
                result.put("message", "重新识别任务已启动");
            } else {
                result.put("code", 404);
                result.put("message", "记录不存在或重新识别失败");
            }

        } catch (Exception e) {
            logger.error("重新识别失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "重新识别失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 百度OCR识别测试接口
     */
    @PostMapping("/ocr-test")
    public ResponseEntity<Map<String, Object>> testBaiduOCR(
            @RequestParam("file") MultipartFile file) {

        Map<String, Object> result = new HashMap<>();

        try {
            if (file == null || file.isEmpty()) {
                result.put("code", 400);
                result.put("message", "请选择要测试的图片文件");
                return ResponseEntity.ok(result);
            }

            logger.info("开始百度OCR测试，文件: {}, 大小: {} bytes",
                       file.getOriginalFilename(), file.getSize());

            // 调用百度OCR识别
            com.nx.inf.utils.BaiduOCRUtil.OCRResult ocrResult =
                com.nx.inf.utils.BaiduOCRUtil.recognizeText(file.getBytes());

            Map<String, Object> ocrData = new HashMap<>();
            ocrData.put("originalFilename", file.getOriginalFilename());
            ocrData.put("fileSize", file.getSize());
            ocrData.put("success", ocrResult.isSuccess());
            ocrData.put("recognizedText", ocrResult.getRecognizedText() != null ? ocrResult.getRecognizedText() : "");
            ocrData.put("textLength", ocrResult.getRecognizedText() != null ? ocrResult.getRecognizedText().length() : 0);
            ocrData.put("wordsCount", ocrResult.getWordsCount());
            ocrData.put("processingTime", ocrResult.getProcessingTime());
            ocrData.put("errorMessage", ocrResult.getErrorMessage());

            result.put("code", 200);
            result.put("message", "百度OCR测试完成");
            result.put("data", ocrData);

        } catch (Exception e) {
            logger.error("百度OCR测试失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "百度OCR测试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 百度OCR服务状态检查
     */
    @GetMapping("/ocr-status")
    public ResponseEntity<Map<String, Object>> checkOCRStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean serviceAvailable = com.nx.inf.utils.BaiduOCRUtil.testService();

            Map<String, Object> statusData = new HashMap<>();
            statusData.put("serviceAvailable", serviceAvailable);
            statusData.put("provider", "百度OCR");
            statusData.put("apiUrl", "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic");
            statusData.put("features", new String[]{
                "图片方向自动检测和矫正",
                "置信度返回",
                "中英文混合识别",
                "屏幕拍摄优化"
            });

            result.put("code", 200);
            result.put("message", "OCR服务状态检查完成");
            result.put("data", statusData);

        } catch (Exception e) {
            logger.error("OCR服务状态检查失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "OCR服务状态检查失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 医疗检验报告单识别测试接口
     */
    @PostMapping("/medical-report-test")
    public ResponseEntity<Map<String, Object>> testMedicalReport(
            @RequestParam("file") MultipartFile file) {

        Map<String, Object> result = new HashMap<>();

        try {
            if (file == null || file.isEmpty()) {
                result.put("code", 400);
                result.put("message", "请选择要识别的医疗检验报告单图片");
                return ResponseEntity.ok(result);
            }

            logger.info("开始医疗检验报告单识别测试，文件: {}, 大小: {} bytes",
                       file.getOriginalFilename(), file.getSize());

            // 调用百度医疗OCR识别
            com.nx.inf.utils.BaiduOCRUtil.MedicalReportResult medicalResult =
                com.nx.inf.utils.BaiduOCRUtil.recognizeMedicalReport(file.getBytes());

            Map<String, Object> medicalData = new HashMap<>();
            medicalData.put("originalFilename", file.getOriginalFilename());
            medicalData.put("fileSize", file.getSize());
            medicalData.put("success", medicalResult.isSuccess());
            medicalData.put("processingTime", medicalResult.getProcessingTime());
            medicalData.put("errorMessage", medicalResult.getErrorMessage());

            if (medicalResult.isSuccess()) {
                // 患者基本信息
                Map<String, Object> patientInfo = new HashMap<>();
                patientInfo.put("patientName", medicalResult.getPatientName());
                patientInfo.put("patientId", medicalResult.getPatientId());
                patientInfo.put("gender", medicalResult.getGender());
                patientInfo.put("age", medicalResult.getAge());
                medicalData.put("patientInfo", patientInfo);

                // 报告信息
                Map<String, Object> reportInfo = new HashMap<>();
                reportInfo.put("reportDate", medicalResult.getReportDate());
                reportInfo.put("hospitalName", medicalResult.getHospitalName());
                reportInfo.put("departmentName", medicalResult.getDepartmentName());
                medicalData.put("reportInfo", reportInfo);

                // 检验项目
                List<Map<String, Object>> testItems = new ArrayList<>();
                for (com.nx.inf.utils.BaiduOCRUtil.MedicalTestItem item : medicalResult.getTestItems()) {
                    Map<String, Object> itemMap = new HashMap<>();
                    itemMap.put("itemName", item.getItemName());
                    itemMap.put("result", item.getResult());
                    itemMap.put("unit", item.getUnit());
                    itemMap.put("referenceRange", item.getReferenceRange());
                    itemMap.put("abnormalFlag", item.getAbnormalFlag());
                    testItems.add(itemMap);
                }
                medicalData.put("testItems", testItems);
                medicalData.put("testItemsCount", testItems.size());

                // 原始识别文本（用于调试）
                medicalData.put("rawText", medicalResult.getRawText());
            }

            result.put("code", 200);
            result.put("message", "医疗检验报告单识别测试完成");
            result.put("data", medicalData);

        } catch (Exception e) {
            logger.error("医疗检验报告单识别测试失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "医疗检验报告单识别测试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 医疗OCR功能状态检查
     */
    @GetMapping("/medical-ocr-status")
    public ResponseEntity<Map<String, Object>> checkMedicalOCRStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("serviceAvailable", true);
            statusData.put("provider", "百度医疗分析OCR");
            statusData.put("apiUrl", "https://aip.baidubce.com/rest/2.0/ocr/v1/medical_analysis");
            statusData.put("features", new String[]{
                "医疗检验报告单识别",
                "患者信息提取",
                "检验项目解析",
                "异常指标识别",
                "图片方向自动矫正"
            });
            statusData.put("supportedFields", new String[]{
                "患者姓名", "患者ID", "性别", "年龄",
                "报告日期", "医院名称", "科室名称",
                "检验项目名称", "检验结果", "参考范围", "异常标识"
            });

            result.put("code", 200);
            result.put("message", "医疗OCR功能状态检查完成");
            result.put("data", statusData);

        } catch (Exception e) {
            logger.error("医疗OCR功能状态检查失败: {}", e.getMessage(), e);
            result.put("code", 500);
            result.put("message", "医疗OCR功能状态检查失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}

package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.FollowupPlan;
import com.nx.inf.model.dto.FollowupPlanDTO;
import com.nx.inf.service.FollowupPlanService;
import com.nx.inf.utils.encrypt.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 随访计划管理控制器
 */
@RestController
@RequestMapping("/emr/followup/plan")
@CrossOrigin(origins = "*", maxAge = 3600)
public class FollowupPlanController {

    @Autowired
    private FollowupPlanService followupPlanService;

    /**
     * 分页获取随访计划列表
     */
    @GetMapping("/page")
    public Result<Object> getPlanPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status) {
        try {
            IPage<FollowupPlanDTO> result = followupPlanService.getFollowupPlanPage(current, size, category, status, name);
            return Result.OK(result);
        } catch (Exception e) {
            return Result.error("获取随访计划列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取随访计划详情
     */
    @GetMapping("/{id}")
    public Result<Object> getPlanById(@PathVariable Long id) {
        try {
            FollowupPlanDTO plan = followupPlanService.getPlanById(id);
            if (plan == null) {
                return Result.error("随访计划不存在");
            }
            return Result.OK(plan);
        } catch (Exception e) {
            return Result.error("获取随访计划详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建随访计划
     */
    @PostMapping
    public Result<Object> createPlan(@RequestBody FollowupPlanDTO planDTO) {
        try {
            FollowupPlan result = followupPlanService.createPlan(planDTO);
            return Result.OK(result);
        } catch (Exception e) {
            return Result.error("创建随访计划失败: " + e.getMessage());
        }
    }

    /**
     * 更新随访计划
     */
    @PutMapping("/{id}")
    public Result<Object> updatePlan(@PathVariable Long id, @RequestBody FollowupPlanDTO planDTO) {
        try {
            planDTO.setId(id);
            FollowupPlan result = followupPlanService.updatePlan(planDTO);
            return Result.OK(result);
        } catch (Exception e) {
            return Result.error("更新随访计划失败: " + e.getMessage());
        }
    }

    /**
     * 删除随访计划
     */
    @DeleteMapping("/{id}")
    public Result<Object> deletePlan(@PathVariable Long id) {
        try {
            followupPlanService.deletePlan(id);
            return Result.OK("删除成功");
        } catch (Exception e) {
            return Result.error("删除随访计划失败: " + e.getMessage());
        }
    }

    /**
     * 更新计划状态
     */
    @PutMapping("/{id}/status")
    public Result<Object> updatePlanStatus(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String status = request.get("status");
            followupPlanService.updatePlanStatus(id, status);
            return Result.OK("状态更新成功");
        } catch (Exception e) {
            return Result.error("更新计划状态失败: " + e.getMessage());
        }
    }

    /**
     * 生成二维码
     */
    @PostMapping("/{id}/qrcode")
    public Result<Object> generateQRCode(@PathVariable Long id) {
        try {
            String qrCodeUrl = followupPlanService.generateQRCode(id);
            return Result.OK(qrCodeUrl);
        } catch (Exception e) {
            return Result.error("生成二维码失败: " + e.getMessage());
        }
    }

    /**
     * 获取计划统计信息
     */
    @GetMapping("/{id}/statistics")
    public Result<Object> getPlanStatistics(@PathVariable Long id) {
        try {
            Map<String, Object> statistics = followupPlanService.getPlanStats(id);
            return Result.OK(statistics);
        } catch (Exception e) {
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新计划统计信息
     */
    @PostMapping("/{id}/update-statistics")
    public Result<Object> updatePlanStatistics(@PathVariable Long id) {
        try {
            followupPlanService.updatePlanStatistics(id);
            return Result.OK("统计信息更新成功");
        } catch (Exception e) {
            return Result.error("更新统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新所有计划统计信息
     */
    @PostMapping("/update-all-statistics")
    public Result<Object> updateAllPlanStatistics() {
        try {
            followupPlanService.updateAllPlanStatistics();
            return Result.OK("所有计划统计信息更新成功");
        } catch (Exception e) {
            return Result.error("批量更新统计信息失败: " + e.getMessage());
        }
    }
} 
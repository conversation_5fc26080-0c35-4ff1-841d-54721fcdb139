package com.nx.inf.controller;

import com.nx.inf.model.entity.FollowupCUser;
import com.nx.inf.service.FollowupCUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;
import com.nx.inf.service.PatientService;
import com.nx.inf.service.PatientGroupService;
import com.nx.inf.model.dto.PatientDTO;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nx.inf.model.entity.PatientGroupRelation;
import com.nx.inf.mapper.PatientGroupRelationMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.PatientGroup;
import java.util.List;

@RestController
@RequestMapping("/emr/followup/cuser")
public class FollowupCUserController {
    @Autowired
    private FollowupCUserService followupCUserService;
    @Autowired
    private PatientService patientService;
    @Autowired
    private PatientGroupService patientGroupService;
    @Autowired
    private PatientGroupRelationMapper relationMapper;

    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody FollowupCUser user) {
        Map<String, Object> result = new HashMap<>();
        try {
            FollowupCUser saved = followupCUserService.register(user);
            result.put("code", 200);
            result.put("msg", "注册成功");
            Map<String, Object> data = new HashMap<>();
            data.put("id", saved.getId());
            result.put("data", data);
        } catch (Exception e) {
            // 打印详细错误信息到控制台
            System.err.println("注册接口异常: " + e.getMessage());
            e.printStackTrace();
            
            result.put("code", 400);
            result.put("msg", e.getMessage());
            result.put("data", null);
        }
        return result;
    }

    @PostMapping("/bind")
    public Map<String, Object> bind(@RequestBody Map<String, Object> req) {
        String phone = (String) req.get("phone");
        Integer isBind = (Integer) req.get("isBind");
        Map<String, Object> result = new HashMap<>();
        boolean ok = followupCUserService.updateBindStatus(phone, isBind);
        if (ok) {
            result.put("code", 200);
            result.put("msg", "绑定状态更新成功");
        } else {
            result.put("code", 400);
            result.put("msg", "用户不存在");
        }
        result.put("data", null);
        return result;
    }

    @PostMapping("/auth")
    public Map<String, Object> auth(@RequestBody Map<String, Object> req) {
        String phone = (String) req.get("phone");
        Integer isAuth = (Integer) req.get("isAuth");
        Map<String, Object> result = new HashMap<>();
        boolean ok = followupCUserService.updateAuthStatus(phone, isAuth);
        if (ok) {
            result.put("code", 200);
            result.put("msg", "授权状态更新成功");
        } else {
            result.put("code", 400);
            result.put("msg", "用户不存在");
        }
        result.put("data", null);
        return result;
    }

    /**
     * C端患者确认入组
     */
    @PostMapping("/confirm-group")
    public Map<String, Object> confirmGroup(@RequestBody Map<String, Object> req) {
        String phone = (String) req.get("phone");
        Long groupId = null;
        try {
            Object groupIdObj = req.get("groupId");
            if (groupIdObj instanceof Number) {
                groupId = ((Number) groupIdObj).longValue();
            } else if (groupIdObj instanceof String) {
                groupId = Long.parseLong((String) groupIdObj);
            }
        } catch (Exception e) {
            groupId = null;
        }
        String groupName = (String) req.get("groupName");
        Map<String, Object> result = new HashMap<>();
        if (phone == null || groupId == null) {
            result.put("code", 400);
            result.put("msg", "参数不完整");
            result.put("data", null);
            return result;
        }
        // 校验分组是否存在且名称一致
        com.nx.inf.model.dto.PatientGroupDTO group = patientGroupService.getPatientGroupById(groupId);
        if (group == null) {
            result.put("code", 404);
            result.put("msg", "分组不存在");
            result.put("data", null);
            return result;
        }
        if (groupName != null && !groupName.equals(group.getName())) {
            result.put("code", 400);
            result.put("msg", "分组名称与ID不匹配");
            result.put("data", null);
            return result;
        }
        // 通过手机号查找C端用户ID
        LambdaQueryWrapper<FollowupCUser> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(FollowupCUser::getPhone, phone);
        FollowupCUser cUser = followupCUserService.getOne(userQuery);
        if (cUser == null || cUser.getId() == null) {
            result.put("code", 404);
            result.put("msg", "未找到对应C端用户");
            result.put("data", null);
            return result;
        }
        Date now = new Date();
        // 先查是否已存在关联
        LambdaQueryWrapper<PatientGroupRelation> relCheck = new LambdaQueryWrapper<>();
        relCheck.eq(PatientGroupRelation::getGroupId, groupId).eq(PatientGroupRelation::getPatientId, cUser.getId());
        PatientGroupRelation existRel = relationMapper.selectOne(relCheck);
        if (existRel != null) {
            result.put("code", 409);
            result.put("msg", "已入组，无需重复申请");
            result.put("data", null);
            return result;
        }
        // 构造关联对象，带当前时间
        PatientGroupRelation rel = new PatientGroupRelation();
        rel.setGroupId(groupId);
        rel.setPatientId(cUser.getId());
        rel.setJoinTime(now);
        rel.setJoinMethod("C端自助入组");
        rel.setStatus("0"); // 待审核 - 使用数字字符串
        // 直接插入关联
        relationMapper.insert(rel);
        // 更新分组人数
        patientGroupService.updatePatientCount(groupId);
        result.put("code", 200);
        result.put("msg", "入组成功");
        result.put("data", null);
        return result;
    }

    /**
     * 分页查询随访C端用户及其分组
     */
    @GetMapping("/list")
    public Map<String, Object> list(@RequestParam(defaultValue = "0") int page,
                                    @RequestParam(defaultValue = "10") int size,
                                    @RequestParam(required = false) String name,
                                    @RequestParam(required = false) String phone) {
        Map<String, Object> result = new HashMap<>();
        Page<FollowupCUser> pageable = new Page<>(Math.max(0, page), size);
        LambdaQueryWrapper<FollowupCUser> userWrapper = new LambdaQueryWrapper<>();
        if (name != null && !name.isEmpty()) userWrapper.like(FollowupCUser::getName, name);
        if (phone != null && !phone.isEmpty()) userWrapper.eq(FollowupCUser::getPhone, phone);
        IPage<FollowupCUser> userPage = followupCUserService.page(pageable, userWrapper);
        // 查询所有分组关系
        List<Long> userIds = userPage.getRecords().stream().map(FollowupCUser::getId).collect(java.util.stream.Collectors.toList());
        List<PatientGroupRelation> relations = userIds.isEmpty() ? java.util.Collections.emptyList() : relationMapper.selectList(new LambdaQueryWrapper<PatientGroupRelation>().in(PatientGroupRelation::getPatientId, userIds));
        // 查询所有分组
        List<Long> groupIds = relations.stream().map(PatientGroupRelation::getGroupId).distinct().collect(java.util.stream.Collectors.toList());
        List<PatientGroup> groups = groupIds.isEmpty() ? java.util.Collections.emptyList() : patientGroupService.listByIds(groupIds);
        // 分组信息映射
        Map<Long, PatientGroup> groupMap = groups.stream().collect(java.util.stream.Collectors.toMap(PatientGroup::getId, g -> g));
        // 用户ID -> 分组列表
        Map<Long, List<PatientGroup>> userGroupMap = new java.util.HashMap<>();
        for (PatientGroupRelation rel : relations) {
            userGroupMap.computeIfAbsent(rel.getPatientId(), k -> new java.util.ArrayList<>()).add(groupMap.get(rel.getGroupId()));
        }
        // 封装结果
        List<Map<String, Object>> records = new java.util.ArrayList<>();
        for (FollowupCUser user : userPage.getRecords()) {
            Map<String, Object> item = new java.util.HashMap<>();
            item.put("id", user.getId());
            item.put("name", user.getName());
            item.put("gender", user.getGender());
            item.put("age", user.getAge());
            item.put("phone", user.getPhone());
            String idCard = null;
            try {
                idCard = user.getIdNumber();
            } catch (Exception e) {}
            item.put("idCard", user.getIdNumber());
            List<Map<String, Object>> groupList = new java.util.ArrayList<>();
            List<PatientGroup> userGroups = userGroupMap.get(user.getId());
            if (userGroups != null) {
                for (PatientGroup g : userGroups) {
                    if (g != null) {
                        Map<String, Object> gMap = new java.util.HashMap<>();
                        gMap.put("id", g.getId());
                        gMap.put("name", g.getName());
                        gMap.put("description", g.getDescription());
                        groupList.add(gMap);
                    }
                }
            }
            item.put("groups", groupList);
            records.add(item);
        }
        Map<String, Object> data = new HashMap<>();
        data.put("records", records);
        data.put("total", userPage.getTotal());
        data.put("size", userPage.getSize());
        data.put("current", userPage.getCurrent());
        result.put("code", 200);
        result.put("data", data);
        result.put("message", "获取C端用户及分组成功");
        return result;
    }
    
    /**
     * 更新用户登录信息
     */
    @PutMapping("/updateLoginInfo")
    public Map<String, Object> updateLoginInfo(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String lastLoginTime = (String) request.get("lastLoginTime");
            String lastLoginIp = (String) request.get("lastLoginIp");
            
            boolean success = followupCUserService.updateLoginInfo(userId, lastLoginTime, lastLoginIp);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "更新成功");
            } else {
                result.put("code", 400);
                result.put("message", "更新失败，用户不存在");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新用户基本信息
     */
    @PutMapping("/updateUserInfo")
    public Map<String, Object> updateUserInfo(@RequestBody FollowupCUser user) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = followupCUserService.updateUserInfo(user);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "更新成功");
            } else {
                result.put("code", 400);
                result.put("message", "更新失败，用户不存在");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据手机号查询用户
     */
    @GetMapping("/getByPhone")
    public Map<String, Object> getUserByPhone(@RequestParam String phone) {
        Map<String, Object> result = new HashMap<>();
        try {
            FollowupCUser user = followupCUserService.getUserByPhone(phone);
            
            if (user != null) {
                result.put("code", 200);
                result.put("message", "获取成功");
                result.put("data", user);
            } else {
                result.put("code", 404);
                result.put("message", "用户不存在");
                result.put("data", null);
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 分页查询用户列表
     */
    @GetMapping("/page")
    public Map<String, Object> getUserPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String bindStatus,
            @RequestParam(required = false) String authStatus) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            IPage<FollowupCUser> userPage = followupCUserService.getUserPage(
                    page, size, name, phone, bindStatus, authStatus);
            
            Map<String, Object> data = new HashMap<>();
            data.put("records", userPage.getRecords());
            data.put("total", userPage.getTotal());
            data.put("size", userPage.getSize());
            data.put("current", userPage.getCurrent());
            
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", data);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取失败: " + e.getMessage());
        }
        return result;
    }
} 
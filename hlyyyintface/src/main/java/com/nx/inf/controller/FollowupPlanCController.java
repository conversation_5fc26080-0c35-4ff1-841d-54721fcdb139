package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.FollowupPlan;
import com.nx.inf.model.entity.PatientGroupRelation;
import com.nx.inf.model.dto.FollowupPlanDTO;
import com.nx.inf.service.FollowupPlanService;
import com.nx.inf.service.PatientGroupRelationService;
import com.nx.inf.service.FollowupCUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * C端随访计划控制器
 */
@RestController
@RequestMapping("/emr/followup/plan/c")
@CrossOrigin(origins = "*", maxAge = 3600)
public class FollowupPlanCController {

    @Autowired
    private FollowupPlanService planService;
    
    @Autowired
    private PatientGroupRelationService groupRelationService;
    
    @Autowired
    private FollowupCUserService cUserService;

    /**
     * 获取患者参与的随访计划列表
     */
    @GetMapping("/patient-plans")
    public ResponseEntity<?> getPatientPlans(
            @RequestParam Long patientId,
            @RequestParam(defaultValue = "all") String status,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            Page<FollowupPlanDTO> page = new Page<>(current, size);
            IPage<FollowupPlanDTO> result = planService.getPatientPlans(page, patientId, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取随访计划详情
     */
    @GetMapping("/plan-detail/{planId}")
    public ResponseEntity<?> getPlanDetail(@PathVariable Long planId) {
        try {
            FollowupPlanDTO plan = planService.getPlanDetail(planId);
            if (plan == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("code", 404);
                error.put("message", "随访计划不存在");
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", plan);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 患者确认入组申请
     */
    @PostMapping("/join-group")
    public ResponseEntity<?> joinGroup(@RequestBody Map<String, Object> request) {
        try {
            Long patientId = Long.valueOf(request.get("patientId").toString());
            Long groupId = Long.valueOf(request.get("groupId").toString());
            String patientName = (String) request.get("patientName");
            String patientPhone = (String) request.get("patientPhone");
            
            // 创建入组申请
            PatientGroupRelation relation = new PatientGroupRelation();
            relation.setPatientId(patientId);
            relation.setGroupId(groupId);
            relation.setPatientName(patientName);
            relation.setPatientPhone(patientPhone);
            relation.setJoinType("qr_code");
            relation.setUserAgree(true);
            relation.setAgreeTime(new Date());
            relation.setStatus("pending");
            relation.setJoinTime(new Date());
            
            boolean result = groupRelationService.save(relation);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", result ? "申请提交成功，等待医生审核" : "申请提交失败");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "申请失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取患者分组状态
     */
    @GetMapping("/group-status/{patientId}")
    public ResponseEntity<?> getGroupStatus(@PathVariable Long patientId) {
        try {
            List<PatientGroupRelation> relations = groupRelationService.getByPatientId(patientId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", relations);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取患者基本信息
     */
    @GetMapping("/patient-info/{patientId}")
    public ResponseEntity<?> getPatientInfo(@PathVariable Long patientId) {
        try {
            // 这里应该调用患者服务获取患者信息
            // 暂时返回基本信息
            Map<String, Object> patientInfo = new HashMap<>();
            patientInfo.put("patientId", patientId);
            patientInfo.put("name", "患者姓名");
            patientInfo.put("phone", "患者手机号");
            patientInfo.put("bindStatus", "bound");
            patientInfo.put("authStatus", "authorized");
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", patientInfo);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取患者任务列表
     */
    @GetMapping("/patient-tasks/{patientId}")
    public ResponseEntity<?> getPatientTasks(
            @PathVariable Long patientId,
            @RequestParam(defaultValue = "all") String status,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            Page<Map<String, Object>> page = new Page<>(current, size);
            IPage<Map<String, Object>> result = planService.getPatientTasks(page, patientId, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取患者统计信息
     */
    @GetMapping("/patient-stats/{patientId}")
    public ResponseEntity<?> getPatientStats(@PathVariable Long patientId) {
        try {
            Map<String, Object> stats = planService.getPatientStats(patientId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 验证二维码参数
     */
    @GetMapping("/validate-qr")
    public ResponseEntity<?> validateQRCode(
            @RequestParam String phone,
            @RequestParam Long groupId,
            @RequestParam String groupName) {
        try {
            // 验证二维码参数的有效性
            boolean valid = planService.validateQRCode(phone, groupId, groupName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "验证成功");
            response.put("data", valid);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "验证失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
} 
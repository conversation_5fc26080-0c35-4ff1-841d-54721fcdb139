package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.nx.inf.model.dto.PatientDTO;
import com.nx.inf.model.dto.PatientGroupDTO;
import com.nx.inf.service.PatientGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 患者分组控制器
 */
@RestController
@RequestMapping("/emr/patient/group")
public class PatientGroupController {
    
    @Autowired
    private PatientGroupService patientGroupService;
    
    /**
     * 创建患者分组
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createPatientGroup(@RequestBody PatientGroupDTO patientGroupDTO) {
        Long id = patientGroupService.createPatientGroup(patientGroupDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "创建患者分组成功");
        result.put("data", id);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 更新患者分组
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updatePatientGroup(@PathVariable Long id, 
                                                                @RequestBody PatientGroupDTO patientGroupDTO) {
        patientGroupDTO.setId(id);
        boolean success = patientGroupService.updatePatientGroup(patientGroupDTO);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "更新患者分组成功");
        } else {
            result.put("code", 400);
            result.put("message", "更新患者分组失败，分组不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取患者分组详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getPatientGroup(@PathVariable Long id) {
        PatientGroupDTO patientGroupDTO = patientGroupService.getPatientGroupById(id);
        
        Map<String, Object> result = new HashMap<>();
        if (patientGroupDTO != null) {
            result.put("code", 200);
            result.put("message", "获取患者分组成功");
            result.put("data", patientGroupDTO);
        } else {
            result.put("code", 400);
            result.put("message", "获取患者分组失败，分组不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 删除患者分组
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deletePatientGroup(@PathVariable Long id) {
        boolean success = patientGroupService.deletePatientGroup(id);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "删除患者分组成功");
        } else {
            result.put("code", 400);
            result.put("message", "删除患者分组失败，分组不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 分页查询患者分组列表
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> getPatientGroupPage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name) {
        
        IPage<PatientGroupDTO> groupPage = patientGroupService.getPatientGroupPage(page, size, name);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取患者分组列表成功");
        result.put("data", groupPage);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 分页查询所有患者（不指定分组）
     */
    @GetMapping("/patients")
    public ResponseEntity<Map<String, Object>> getAllPatients(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String idCard,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String disease,
            @RequestParam(required = false) Long groupId) {
        IPage<PatientDTO> patientPage;
        if (groupId == null) {
            // 查询所有已入组的患者
            patientPage = patientGroupService.getAllJoinedPatients(page, size, status, name, idCard, phone, disease);
        } else {
            // 查询指定分组下的患者
            patientPage = patientGroupService.getPatientsByGroupId(
                page, size, groupId, status, name, idCard, phone, disease);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", groupId == null ? "获取所有已入组患者列表成功" : "获取分组患者列表成功");
        result.put("data", patientPage);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 向分组添加患者
     */
    @PostMapping("/{groupId}/patients")
    public ResponseEntity<Map<String, Object>> addPatientsToGroup(
            @PathVariable Long groupId,
            @RequestParam String joinMethod,
            @RequestBody List<Long> patientIds) {
        
        boolean success = patientGroupService.addPatientsToGroup(groupId, patientIds, joinMethod);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "添加患者到分组成功");
        } else {
            result.put("code", 400);
            result.put("message", "添加患者到分组失败，分组不存在或患者ID列表为空");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 从分组移除患者
     */
    @DeleteMapping("/{groupId}/patients")
    public ResponseEntity<Map<String, Object>> removePatientsFromGroup(
            @PathVariable Long groupId,
            @RequestBody List<Long> patientIds) {
        
        boolean success = patientGroupService.removePatientsFromGroup(groupId, patientIds);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "从分组移除患者成功");
        } else {
            result.put("code", 400);
            result.put("message", "从分组移除患者失败，分组不存在或患者ID列表为空");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 分页查询分组内的患者
     */
    @GetMapping("/{groupId}/patients")
    public ResponseEntity<Map<String, Object>> getPatientsByGroupId(
            @PathVariable Long groupId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String idCard,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String disease) {
        
        IPage<PatientDTO> patientPage = patientGroupService.getPatientsByGroupId(
                page, size, groupId, status, name, idCard, phone, disease);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取分组患者列表成功");
        result.put("data", patientPage);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 查询所有分组及入组状态
     */
    @GetMapping("/all-with-status")
    public ResponseEntity<Map<String, Object>> getAllGroupsWithJoinStatus(@RequestParam Long patientId) {
        List<PatientGroupDTO> groups = patientGroupService.getAllGroupsWithJoinStatus(patientId);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取所有分组及入组状态成功");
        result.put("data", groups);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 审核患者入组申请
     */
    @PostMapping("/audit")
    public ResponseEntity<Map<String, Object>> auditPatientJoinRequest(
            @RequestParam Long relationId,
            @RequestParam String auditResult,
            @RequestParam Long auditorId,
            @RequestParam String auditorName,
            @RequestParam(required = false) String auditRemark) {
        
        boolean success = patientGroupService.auditPatientJoinRequest(
                relationId, auditResult, auditorId, auditorName, auditRemark);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "审核患者入组申请成功");
        } else {
            result.put("code", 400);
            result.put("message", "审核患者入组申请失败，关联记录不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 批量审核患者入组申请
     */
    @PostMapping("/audit/batch")
    public ResponseEntity<Map<String, Object>> batchAuditPatientJoinRequest(
            @RequestParam List<Long> relationIds,
            @RequestParam String auditResult,
            @RequestParam Long auditorId,
            @RequestParam String auditorName,
            @RequestParam(required = false) String auditRemark) {
        
        int successCount = patientGroupService.batchAuditPatientJoinRequest(
                relationIds, auditResult, auditorId, auditorName, auditRemark);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "批量审核患者入组申请完成");
        Map<String, Object> data = new HashMap<>();
        data.put("totalCount", relationIds.size());
        data.put("successCount", successCount);
        data.put("failCount", relationIds.size() - successCount);
        result.put("data", data);
        
        return ResponseEntity.ok(result);
    }
} 
package com.nx.inf.controller;

import com.nx.inf.annotation.TargetDataSource;
import com.nx.inf.enums.DataSourceType;
import com.nx.inf.mapper.NhReferralAuditMapper;
import com.nx.inf.model.entity.NhReferralAudit;
import com.nx.inf.service.NursingHomeInstitutionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 转介审核控制器
 */
@RestController
@TargetDataSource(value = DataSourceType.MYSQL_DATASOURCE)
@Slf4j
public class NhReferralAuditController {

    @Autowired
    private NhReferralAuditMapper referralAuditMapper;
    
    @Autowired
    private NursingHomeInstitutionService nursingHomeInstitutionService;

    /**
     * 获取转介审核记录列表（集成患者数据作为初始状态）
     * 
     * @param request 查询条件
     * @return 转介审核记录列表
     */
    @PostMapping("/emr/referral-audit/list")
    public ResponseEntity<Map<String, Object>> getReferralAuditList(@RequestBody(required = false) Map<String, Object> request) {
        try {
            log.info("获取转介审核记录列表（集成患者数据），查询条件：{}", request);
            
            NhReferralAudit queryCondition = new NhReferralAudit();
            
            if (request != null) {
                // 设置查询条件
                if (request.get("patientName") != null) {
                    queryCondition.setPatientName(request.get("patientName").toString());
                }
                if (request.get("phone") != null) {
                    queryCondition.setPhone(request.get("phone").toString());
                }
                if (request.get("idCard") != null) {
                    queryCondition.setIdCard(request.get("idCard").toString());
                }
                if (request.get("auditStatus") != null) {
                    queryCondition.setAuditStatus(request.get("auditStatus").toString());
                }
                if (request.get("referralInstitutionCode") != null) {
                    queryCondition.setReferralInstitutionCode(request.get("referralInstitutionCode").toString());
                }
                if (request.get("dischargeDepartment") != null) {
                    queryCondition.setDischargeDepartment(request.get("dischargeDepartment").toString());
                }
            }
            
            // 查询患者数据和转介审核数据的集成列表
            List<NhReferralAudit> auditList = referralAuditMapper.getReferralAuditList(queryCondition);
            
            // 对数据进行后处理，确保默认值的正确设置
            auditList.forEach(item -> {
                // 确保审核状态默认为未审核
                if (item.getAuditStatus() == null || item.getAuditStatus().isEmpty()) {
                    item.setAuditStatus("0");
                }
                
                // 确保机构信息完整
                if (item.getInstitutionName() == null || item.getInstitutionName().isEmpty()) {
                    item.setInstitutionName("待分配机构");
                }
                
                // 确保转介类型默认值
                if (item.getReferralType() == null || item.getReferralType().isEmpty()) {
                    item.setReferralType("nursing");
                }
                
                // 如果是来自患者表的数据（id为0或null），则生成一个临时ID用于前端操作
                if (item.getId() == null || item.getId() == 0) {
                    // 使用"TEMP_" + 患者ID作为临时ID，便于后端识别和处理
                    item.setId(null); // 前端会使用临时ID字符串
                    // 标记这是临时记录，需要创建新的转介审核记录
                    item.setIsTempRecord(Boolean.TRUE);
                }
            });
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("code", 200);
            result.put("message", "获取转介审核记录列表成功（包含所有患者数据）");
            result.put("data", auditList);
            
            log.info("返回转介审核记录列表，共{}条记录", auditList.size());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取转介审核记录列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 500);
            result.put("message", "获取转介审核记录列表失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 根据ID获取转介审核记录详情
     * 
     * @param id 记录ID
     * @return 转介审核记录详情
     */
    @GetMapping("/emr/referral-audit/{id}")
    public ResponseEntity<Map<String, Object>> getReferralAuditDetail(@PathVariable Long id) {
        try {
            log.info("获取转介审核记录详情，ID：{}", id);
            
            NhReferralAudit auditDetail = referralAuditMapper.getReferralAuditWithInstitution(id);
            
            Map<String, Object> result = new HashMap<>();
            if (auditDetail != null) {
                result.put("success", true);
                result.put("code", 200);
                result.put("message", "获取转介审核记录详情成功");
                result.put("data", auditDetail);
            } else {
                result.put("success", false);
                result.put("code", 404);
                result.put("message", "未找到指定的转介审核记录");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取转介审核记录详情失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 500);
            result.put("message", "获取转介审核记录详情失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 更新审核状态（支持临时记录处理）
     * 
     * @param id 记录ID（可能是临时ID）
     * @param request 审核信息
     * @return 更新结果
     */
    @PutMapping("/emr/referral-audit/{id}/audit")
    public ResponseEntity<Map<String, Object>> updateAuditStatus(@PathVariable String id, @RequestBody Map<String, Object> request) {
        try {
            log.info("更新转介审核状态，ID：{}，审核信息：{}", id, request);
            
            // 检查是否为临时ID（以"TEMP_"开头或为负数）
            boolean isTempId = id.startsWith("TEMP_") || id.startsWith("-");
            
            if (isTempId) {
                // 处理临时记录：先创建转介审核记录，再更新状态
                return handleTempRecordAudit(id, request);
            } else {
                // 处理正常记录：直接更新状态
                return handleNormalRecordAudit(Long.valueOf(id), request);
            }
            
        } catch (Exception e) {
            log.error("更新转介审核状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 500);
            result.put("message", "更新转介审核状态失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 处理临时记录的审核
     */
    private ResponseEntity<Map<String, Object>> handleTempRecordAudit(String tempId, Map<String, Object> request) {
        try {
            // 从临时ID中提取患者ID
            String patientId = tempId.replace("TEMP_", "");
            
            // 验证转介机构代码是否有效
            String referralInstitutionCode = request.get("referralInstitutionCode") != null ? 
                request.get("referralInstitutionCode").toString().trim() : "";
            
            // 如果机构代码为空或无效，设置为null以避免外键约束错误
            if (referralInstitutionCode.isEmpty()) {
                referralInstitutionCode = null;
            } else {
                // 验证机构代码是否存在 - 通过查询机构来验证
                try {
                    boolean institutionExists = nursingHomeInstitutionService.isCodeExists(referralInstitutionCode, null);
                    if (!institutionExists) {
                        log.warn("转介机构代码不存在: {}, 将设置为null", referralInstitutionCode);
                        referralInstitutionCode = null;
                    }
                } catch (Exception e) {
                    log.warn("验证机构代码时出错: {}, 将设置为null", referralInstitutionCode, e);
                    referralInstitutionCode = null;
                }
            }
            
            // 创建新的转介审核记录
            NhReferralAudit newRecord = new NhReferralAudit();
            newRecord.setPatientId(patientId);
            newRecord.setPatientName(request.get("patientName") != null ? request.get("patientName").toString() : "");
            newRecord.setGender(request.get("gender") != null ? request.get("gender").toString() : "");
            newRecord.setPhone(request.get("phone") != null ? request.get("phone").toString() : "");
            newRecord.setIdCard(request.get("idCard") != null ? request.get("idCard").toString() : "");
            newRecord.setReferralInstitutionCode(referralInstitutionCode); // 使用验证后的机构代码
            newRecord.setReferralInstitutionName(request.get("referralInstitutionName") != null ? request.get("referralInstitutionName").toString() : "");
            newRecord.setDischargeDepartment(request.get("dischargeDepartment") != null ? request.get("dischargeDepartment").toString() : "待分配");
            newRecord.setPrimaryDiagnosis(request.get("primaryDiagnosis") != null ? request.get("primaryDiagnosis").toString() : "待完善");
            newRecord.setEmergencyContact(request.get("emergencyContact") != null ? request.get("emergencyContact").toString() : "待完善");
            newRecord.setEmergencyPhone(request.get("emergencyPhone") != null ? request.get("emergencyPhone").toString() : "");
            newRecord.setContactRelation(request.get("contactRelation") != null ? request.get("contactRelation").toString() : "本人");
            newRecord.setReferralType("nursing");
            newRecord.setAuditStatus("1"); // 直接设置为已审核
            newRecord.setAuditorId(request.get("auditorId") != null ? Long.valueOf(request.get("auditorId").toString()) : 0L);
            newRecord.setAuditorName(request.get("auditorName") != null ? request.get("auditorName").toString() : "系统管理员");
            newRecord.setAuditRemarks(request.get("auditRemarks") != null ? request.get("auditRemarks").toString() : "审核通过");
            newRecord.setCreateBy("系统管理员");
            
            // 插入新记录
            int affectedRows = referralAuditMapper.insert(newRecord);
            
            Map<String, Object> result = new HashMap<>();
            if (affectedRows > 0) {
                result.put("success", true);
                result.put("code", 200);
                result.put("message", "转介审核记录创建并审核成功");
                result.put("data", newRecord);
            } else {
                result.put("success", false);
                result.put("code", 500);
                result.put("message", "转介审核记录创建失败");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("处理临时记录审核失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 500);
            result.put("message", "处理临时记录审核失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 处理正常记录的审核
     */
    private ResponseEntity<Map<String, Object>> handleNormalRecordAudit(Long id, Map<String, Object> request) {
        try {
            String auditStatus = request.get("auditStatus") != null ? request.get("auditStatus").toString() : "1";
            Long auditorId = request.get("auditorId") != null ? Long.valueOf(request.get("auditorId").toString()) : 0L;
            String auditorName = request.get("auditorName") != null ? request.get("auditorName").toString() : "系统管理员";
            String auditRemarks = request.get("auditRemarks") != null ? request.get("auditRemarks").toString() : "";
            
            int affectedRows = referralAuditMapper.updateAuditStatus(id, auditStatus, auditorId, auditorName, auditRemarks);
            
            Map<String, Object> result = new HashMap<>();
            if (affectedRows > 0) {
                result.put("success", true);
                result.put("code", 200);
                result.put("message", "转介审核状态更新成功");
            } else {
                result.put("success", false);
                result.put("code", 404);
                result.put("message", "未找到指定的转介审核记录或更新失败");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("处理正常记录审核失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 500);
            result.put("message", "处理正常记录审核失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 创建转介审核记录
     * 
     * @param auditRecord 转介审核记录
     * @return 创建结果
     */
    @PostMapping("/emr/referral-audit")
    public ResponseEntity<Map<String, Object>> createReferralAudit(@RequestBody NhReferralAudit auditRecord) {
        try {
            log.info("创建转介审核记录：{}", auditRecord);
            
            int affectedRows = referralAuditMapper.insert(auditRecord);
            
            Map<String, Object> result = new HashMap<>();
            if (affectedRows > 0) {
                result.put("success", true);
                result.put("code", 200);
                result.put("message", "转介审核记录创建成功");
                result.put("data", auditRecord);
            } else {
                result.put("success", false);
                result.put("code", 500);
                result.put("message", "转介审核记录创建失败");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("创建转介审核记录失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("code", 500);
            result.put("message", "创建转介审核记录失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
} 
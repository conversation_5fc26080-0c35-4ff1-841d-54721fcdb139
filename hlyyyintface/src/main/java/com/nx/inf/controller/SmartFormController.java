package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nx.inf.model.dto.SmartFormQueryDTO;
import com.nx.inf.model.dto.SmartFormTemplateDTO;
import com.nx.inf.model.vo.QuestionnaireVO;
import com.nx.inf.model.vo.SmartFormTemplateVO;
import com.nx.inf.service.SmartFormTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智能表单管理系统控制器
 */
@Slf4j
@RestController
@RequestMapping("/emr/smart-form")
@CrossOrigin(origins = "*", maxAge = 3600)
public class SmartFormController {
    
    @Autowired
    private SmartFormTemplateService smartFormTemplateService;
    
    /**
     * 1. 获取表单模板列表
     */
    @PostMapping("/getFormTemplateList")
    public ResponseEntity<Map<String, Object>> getFormTemplateList(@RequestBody SmartFormQueryDTO queryDTO) {
        try {
            IPage<SmartFormTemplateVO> page = smartFormTemplateService.getFormTemplateList(queryDTO);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "获取成功");
            result.put("data", page);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取表单模板列表失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 2. 创建表单模板（简化版）
     */
    @PostMapping("/createFormTemplate")
    public ResponseEntity<Map<String, Object>> createFormTemplate(@RequestBody SmartFormTemplateDTO templateDTO) {
        try {
            SmartFormTemplateVO result = smartFormTemplateService.createFormTemplate(templateDTO);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "创建成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.error("创建表单模板参数错误", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            log.error("创建表单模板失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "创建失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 3. 更新表单模板（完整版）
     */
    @PostMapping("/updateFormTemplate")
    public ResponseEntity<Map<String, Object>> updateFormTemplate(@RequestBody SmartFormTemplateDTO templateDTO) {
        try {
            SmartFormTemplateVO result = smartFormTemplateService.updateFormTemplate(templateDTO);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "更新成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.error("更新表单模板参数错误", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            log.error("更新表单模板失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 4. 根据ID获取表单模板详情
     */
    @PostMapping("/getFormTemplateById")
    public ResponseEntity<Map<String, Object>> getFormTemplateById(@RequestBody Map<String, Object> request) {
        try {
            Long id = Long.valueOf(request.get("id").toString());
            SmartFormTemplateVO result = smartFormTemplateService.getFormTemplateById(id);
            
            if (result == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("message", "表单模板不存在");
                return ResponseEntity.badRequest().body(error);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单模板详情失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 5. 删除表单模板
     */
    @PostMapping("/deleteFormTemplate")
    public ResponseEntity<Map<String, Object>> deleteFormTemplate(@RequestBody Map<String, Object> request) {
        try {
            Long id = Long.valueOf(request.get("id").toString());
            boolean result = smartFormTemplateService.deleteFormTemplate(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "删除成功" : "删除失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除表单模板失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 6. 复制表单模板
     */
    @PostMapping("/copyFormTemplate")
    public ResponseEntity<Map<String, Object>> copyFormTemplate(@RequestBody Map<String, Object> request) {
        try {
            Long sourceId = Long.valueOf(request.get("sourceId").toString());
            String newName = request.get("newName").toString();
            
            SmartFormTemplateVO result = smartFormTemplateService.copyFormTemplate(sourceId, newName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "复制成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("复制表单模板失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "复制失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 7. 批量删除表单模板
     */
    @PostMapping("/batchDeleteFormTemplates")
    public ResponseEntity<Map<String, Object>> batchDeleteFormTemplates(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");
            boolean result = smartFormTemplateService.batchDeleteFormTemplates(ids);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "批量删除成功" : "批量删除失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量删除表单模板失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "批量删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 8. 更新表单模板状态
     */
    @PostMapping("/updateFormTemplateStatus")
    public ResponseEntity<Map<String, Object>> updateFormTemplateStatus(@RequestBody Map<String, Object> request) {
        try {
            Long id = Long.valueOf(request.get("id").toString());
            String status = request.get("status").toString();
            boolean result = smartFormTemplateService.updateFormTemplateStatus(id, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "状态更新成功" : "状态更新失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新表单模板状态失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "状态更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 9. 获取可用问卷列表（支持搜索）
     */
    @PostMapping("/getAvailableQuestionnaires")
    public ResponseEntity<Map<String, Object>> getAvailableQuestionnaires(@RequestBody Map<String, Object> request) {
        try {
            String keyword = (String) request.get("keyword");
            String type = (String) request.get("type");
            String status = (String) request.get("status");
            
            List<QuestionnaireVO> result = smartFormTemplateService.getAvailableQuestionnaires(keyword, type, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取可用问卷列表失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 10. 添加表单问卷关联
     */
    @PostMapping("/addFormQuestionnaire")
    public ResponseEntity<Map<String, Object>> addFormQuestionnaire(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            Long questionnaireId = Long.valueOf(request.get("questionnaireId").toString());
            Integer sortOrder = request.get("sortOrder") != null ? Integer.valueOf(request.get("sortOrder").toString()) : null;
            Boolean isRequired = request.get("isRequired") != null ? Boolean.valueOf(request.get("isRequired").toString()) : null;
            @SuppressWarnings("unchecked")
            Map<String, Object> displayConfig = (Map<String, Object>) request.get("displayConfig");
            
            boolean result = smartFormTemplateService.addFormQuestionnaire(formTemplateId, questionnaireId, sortOrder, isRequired, displayConfig);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "添加成功" : "添加失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("添加表单问卷关联失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "添加失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 11. 更新表单问卷关联
     */
    @PostMapping("/updateFormQuestionnaire")
    public ResponseEntity<Map<String, Object>> updateFormQuestionnaire(@RequestBody Map<String, Object> request) {
        try {
            Long relId = Long.valueOf(request.get("relId").toString());
            Integer sortOrder = request.get("sortOrder") != null ? Integer.valueOf(request.get("sortOrder").toString()) : null;
            Boolean isRequired = request.get("isRequired") != null ? Boolean.valueOf(request.get("isRequired").toString()) : null;
            @SuppressWarnings("unchecked")
            Map<String, Object> displayConfig = (Map<String, Object>) request.get("displayConfig");
            
            boolean result = smartFormTemplateService.updateFormQuestionnaire(relId, sortOrder, isRequired, displayConfig);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "更新成功" : "更新失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新表单问卷关联失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 12. 删除表单问卷关联
     */
    @PostMapping("/removeFormQuestionnaire")
    public ResponseEntity<Map<String, Object>> removeFormQuestionnaire(@RequestBody Map<String, Object> request) {
        try {
            Long relId = Long.valueOf(request.get("relId").toString());
            boolean result = smartFormTemplateService.removeFormQuestionnaire(relId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "删除成功" : "删除失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除表单问卷关联失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 13. 获取表单填写页面数据
     */
    @PostMapping("/getFormForFilling")
    public ResponseEntity<Map<String, Object>> getFormForFilling(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String patientId = (String) request.get("patientId");
            Map<String, Object> result = smartFormTemplateService.getFormForFilling(formTemplateId, patientId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单填写页面数据失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 14. 提交表单数据
     */
    @PostMapping("/submitFormData")
    public ResponseEntity<Map<String, Object>> submitFormData(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String patientId = (String) request.get("patientId");
            @SuppressWarnings("unchecked")
            Map<String, Object> formData = (Map<String, Object>) request.get("formData");
            
            Long submissionId = smartFormTemplateService.submitFormData(formTemplateId, patientId, formData);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "提交成功");
            response.put("data", submissionId);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("提交表单数据失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "提交失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 15. 保存草稿
     */
    @PostMapping("/saveFormDraft")
    public ResponseEntity<Map<String, Object>> saveFormDraft(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String patientId = (String) request.get("patientId");
            @SuppressWarnings("unchecked")
            Map<String, Object> draftData = (Map<String, Object>) request.get("draftData");
            
            boolean result = smartFormTemplateService.saveFormDraft(formTemplateId, patientId, draftData);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result);
            response.put("message", result ? "保存成功" : "保存失败");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("保存草稿失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 16. 获取草稿数据
     */
    @PostMapping("/getFormDraft")
    public ResponseEntity<Map<String, Object>> getFormDraft(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String patientId = (String) request.get("patientId");
            
            Map<String, Object> result = smartFormTemplateService.getFormDraft(formTemplateId, patientId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取草稿数据失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 17. 获取表单提交数据列表
     */
    @PostMapping("/getFormSubmissionList")
    public ResponseEntity<Map<String, Object>> getFormSubmissionList(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            Integer pageNum = request.get("pageNum") != null ? Integer.valueOf(request.get("pageNum").toString()) : 1;
            Integer pageSize = request.get("pageSize") != null ? Integer.valueOf(request.get("pageSize").toString()) : 10;
            String patientId = (String) request.get("patientId");
            String status = (String) request.get("status");
            String startTime = (String) request.get("startTime");
            String endTime = (String) request.get("endTime");
            
            Map<String, Object> result = smartFormTemplateService.getFormSubmissionList(
                formTemplateId, pageNum, pageSize, patientId, status, startTime, endTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单提交数据列表失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 18. 获取表单提交详情
     */
    @PostMapping("/getFormSubmissionDetail")
    public ResponseEntity<Map<String, Object>> getFormSubmissionDetail(@RequestBody Map<String, Object> request) {
        try {
            Long submissionId = Long.valueOf(request.get("id").toString());
            
            Map<String, Object> result = smartFormTemplateService.getFormSubmissionDetail(submissionId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单提交详情失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 19. 获取表单数据分析
     */
    @PostMapping("/getFormDataAnalysis")
    public ResponseEntity<Map<String, Object>> getFormDataAnalysis(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            Map<String, Object> result = smartFormTemplateService.getFormDataAnalysis(formTemplateId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单数据分析失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 20. 获取表单统计分析
     */
    @PostMapping("/getFormStatistics")
    public ResponseEntity<Map<String, Object>> getFormStatistics(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            Map<String, Object> result = smartFormTemplateService.getFormStatistics(formTemplateId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单统计分析失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 21. 生成表单填写二维码
     */
    @PostMapping("/generateQRCode")
    public ResponseEntity<Map<String, Object>> generateFormQRCode(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String qrCodeUrl = smartFormTemplateService.generateFormQRCode(formTemplateId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "生成二维码成功");
            Map<String, Object> data = new HashMap<>();
            data.put("qrCodeUrl", qrCodeUrl);
            response.put("data", data);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("生成表单二维码失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "生成失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 22. 更新表单二维码
     */
    @PostMapping("/updateQRCode")
    public ResponseEntity<Map<String, Object>> updateFormQRCode(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String qrCodeUrl = (String) request.get("qrCodeUrl");
            
            boolean success = smartFormTemplateService.updateFormQRCode(formTemplateId, qrCodeUrl);
            
            Map<String, Object> response = new HashMap<>();
            if (success) {
                response.put("success", true);
                response.put("message", "更新二维码成功");
            } else {
                response.put("success", false);
                response.put("message", "更新二维码失败，表单模板不存在");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新表单二维码失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    /**
     * 23. 获取表单二维码
     */
    @PostMapping("/getQRCode")
    public ResponseEntity<Map<String, Object>> getFormQRCode(@RequestBody Map<String, Object> request) {
        try {
            Long formTemplateId = Long.valueOf(request.get("formTemplateId").toString());
            String qrCodeUrl = smartFormTemplateService.getFormQRCode(formTemplateId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "获取二维码成功");
            Map<String, Object> data = new HashMap<>();
            data.put("qrCodeUrl", qrCodeUrl);
            response.put("data", data);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取表单二维码失败", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
} 
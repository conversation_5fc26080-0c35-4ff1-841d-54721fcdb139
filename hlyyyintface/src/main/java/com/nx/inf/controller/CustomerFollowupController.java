package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.*;
import com.nx.inf.model.dto.QuestionnaireAnswerDTO;
import com.nx.inf.service.*;
import com.nx.inf.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;

import java.text.SimpleDateFormat;
import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * C端患者随访接口控制器
 * 专门为C端患者提供随访相关的接口
 */
@RestController
@RequestMapping("/emr/followup/customer")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CustomerFollowupController {

    @Autowired
    private FollowupCUserService followupCUserService;
    
    @Autowired
    private FollowupPlanService followupPlanService;
    
    @Autowired
    private FollowupSubplanService followupSubplanService;
    
    @Autowired
    private FollowupSubplanItemService followupSubplanItemService;
    
    @Autowired
    private FollowupSubplanQuestionnaireService followupSubplanQuestionnaireService;
    
    @Autowired
    private FollowupTaskService followupTaskService;
    
    @Autowired
    private PatientGroupService patientGroupService;
    
    @Autowired
    private PatientGroupRelationMapper patientGroupRelationMapper;
    
    @Autowired
    private QuestionnaireService questionnaireService;
    
    @Autowired
    private QuestionnaireQuestionService questionnaireQuestionService;
    
    @Autowired
    private QuestionnaireOptionService questionnaireOptionService;
    
    @Autowired
    private ContentTemplateService contentTemplateService;
    
    @Autowired
    private QuestionnaireAnswerService questionnaireAnswerService;

    @Autowired
    private QuestionnaireAnswerMapper questionnaireAnswerMapper;

    @Autowired
    private FollowupCUserMapper followupCUserMapper;

    /**
     * 1. 随访计划列表接口
     * 根据手机号获取用户的随访计划列表
     */
    @PostMapping("/plan-list")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Map<String, Object>> getFollowupPlanList(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String phone = (String) request.get("phone");
            String state = (String) request.get("state");
            Integer pageNo = (Integer) request.get("pageNo");
            Integer pageSize = (Integer) request.get("pageSize");
            
            if (phone == null || phone.isEmpty()) {
                result.put("code", 400);
                result.put("message", "手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 根据手机号查找C端用户
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.ok(result);
            }
            
            // 查询用户所在的分组
            LambdaQueryWrapper<PatientGroupRelation> relationWrapper = new LambdaQueryWrapper<>();
            relationWrapper.eq(PatientGroupRelation::getPatientId, cUser.getId());
            List<PatientGroupRelation> relations = patientGroupRelationMapper.selectList(relationWrapper);
            
            if (relations.isEmpty()) {
                result.put("code", 200);
                result.put("message", "暂无随访计划");
                result.put("data", new ArrayList<>());
                return ResponseEntity.ok(result);
            }
            
            // 获取分组ID列表
            List<Long> groupIds = new ArrayList<>();
            for (PatientGroupRelation relation : relations) {
                groupIds.add(relation.getGroupId());
            }
            
            // 查询这些分组关联的随访计划
            LambdaQueryWrapper<FollowupPlan> planWrapper = new LambdaQueryWrapper<>();
            planWrapper.in(FollowupPlan::getGroupId, groupIds);
            planWrapper.eq(FollowupPlan::getStatus, "active"); // 只查询启用的计划
            planWrapper.select(FollowupPlan::getId, FollowupPlan::getName, FollowupPlan::getDescription, 
                             FollowupPlan::getCategory, FollowupPlan::getStatus, FollowupPlan::getEndTimeType, 
                             FollowupPlan::getEndTimeValue, FollowupPlan::getGroupId, FollowupPlan::getQrCodeUrl, 
                             FollowupPlan::getCreatorId, FollowupPlan::getCreatorName, FollowupPlan::getCreateTime, 
                             FollowupPlan::getUpdateTime, FollowupPlan::getPatientCount, FollowupPlan::getTaskCount, 
                             FollowupPlan::getCompletedTaskCount);
            List<FollowupPlan> plans = followupPlanService.list(planWrapper);
            
            // 构建返回数据
            List<Map<String, Object>> planList = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM月dd日");
            
            for (FollowupPlan plan : plans) {
                // 查询该计划的子计划
                LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
                subplanWrapper.eq(FollowupSubplan::getPlanId, plan.getId());
                List<FollowupSubplan> subplans = followupSubplanService.list(subplanWrapper);
                
                for (int i = 0; i < subplans.size(); i++) {
                    FollowupSubplan subplan = subplans.get(i);
                    
                    // 根据子计划配置计算实际日期
                    String dateStr = calculatePlanDate(subplan, plan, phone);
                    
                    // 确定状态：根据用户是否填写了该子计划关联的问卷来判断
                    String status = calculateSubplanStatus(subplan.getId(), cUser.getId());
                    
                    Map<String, Object> planItem = new HashMap<>();
                    planItem.put("planId", plan.getId());
                    planItem.put("subplanId", subplan.getId()); // 新增：子计划ID
                    planItem.put("date", dateStr);
                    planItem.put("name", plan.getName());
                    planItem.put("desc", subplan.getDescription() != null ? subplan.getDescription() : plan.getDescription());
                    planItem.put("number", "第" + (i + 1) + "次计划");
                    planItem.put("status", status);
                    
                    planList.add(planItem);
                }
            }
            
            // 分页处理
            int start = (pageNo - 1) * pageSize;
            int end = Math.min(start + pageSize, planList.size());
            List<Map<String, Object>> pagedList = planList.subList(start, end);
            
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", pagedList);
            
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取随访计划列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 2. 随访计划介绍接口
     * 获取指定计划的详细信息
     */
    @PostMapping("/plan-detail")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Map<String, Object>> getFollowupPlanDetail(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String planIdStr = (String) request.get("planId");
            String phone = (String) request.get("phone");
            Object numberObj = request.get("number"); // 新增：第几次计划

            if (planIdStr == null || planIdStr.isEmpty()) {
                result.put("code", 400);
                result.put("message", "计划ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (phone == null || phone.isEmpty()) {
                result.put("code", 400);
                result.put("message", "手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            Long planId = Long.parseLong(planIdStr);
            int planNumber = 1; // 默认第1次计划
            if (numberObj != null) {
                try {
                    planNumber = Integer.parseInt(numberObj.toString());
                } catch (NumberFormatException e) {
                    // 如果传入的是"第1次计划"这样的格式，提取数字
                    String numberStr = numberObj.toString();
                    if (numberStr.contains("第") && numberStr.contains("次")) {
                        String numStr = numberStr.replaceAll("[^0-9]", "");
                        if (!numStr.isEmpty()) {
                            planNumber = Integer.parseInt(numStr);
                        }
                    }
                }
            }

            // 查询C端用户（提前查询）
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.ok(result);
            }

            // 查询随访计划
            FollowupPlan plan = followupPlanService.getById(planId);
            if (plan == null) {
                result.put("code", 404);
                result.put("message", "随访计划不存在");
                return ResponseEntity.ok(result);
            }
            
            // 查询子计划
            LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
            subplanWrapper.eq(FollowupSubplan::getPlanId, planId);
            subplanWrapper.orderByAsc(FollowupSubplan::getSortOrder); // 按排序字段排序
            List<FollowupSubplan> subplans = followupSubplanService.list(subplanWrapper);

            if (subplans.isEmpty()) {
                result.put("code", 404);
                result.put("message", "该计划暂无子计划");
                return ResponseEntity.ok(result);
            }

            // 如果没有指定number参数，返回所有子计划的汇总信息
            if (numberObj == null) {
                // 返回包含所有子计划信息的详情
                return buildAllSubplansDetail(plan, subplans, cUser);
            }

            // 根据planNumber选择对应的子计划
            if (planNumber > subplans.size()) {
                result.put("code", 404);
                result.put("message", "第" + planNumber + "次计划不存在");
                return ResponseEntity.ok(result);
            }

            FollowupSubplan mainSubplan = subplans.get(planNumber - 1); // 数组索引从0开始
            
            // 查询该子计划关联的问卷
            LambdaQueryWrapper<FollowupSubplanQuestionnaire> questionnaireWrapper = new LambdaQueryWrapper<>();
            questionnaireWrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, mainSubplan.getId());
            questionnaireWrapper.orderByAsc(FollowupSubplanQuestionnaire::getSortOrder);
            List<FollowupSubplanQuestionnaire> questionnaireRels = followupSubplanQuestionnaireService.list(questionnaireWrapper);
            
            // 构建项目列表
            List<Map<String, Object>> projectItems = new ArrayList<>();
            
            // 添加问卷调查项目
            if (!questionnaireRels.isEmpty()) {
                Map<String, Object> questionnaireItem = new HashMap<>();
                questionnaireItem.put("title", "问卷调查");
                
                List<String> subQuestions = new ArrayList<>();
                for (FollowupSubplanQuestionnaire rel : questionnaireRels) {
                    Questionnaire questionnaire = questionnaireService.getQuestionnaireById(rel.getQuestionnaireId());
                    if (questionnaire != null) {
                        // 查询问卷题目数量
                        List<QuestionnaireQuestion> questions = questionnaireQuestionService.getQuestionsByQuestionnaireId(questionnaire.getId());
                        long questionCount = questions.size();
                        
                        subQuestions.add(questionnaire.getName() + "（共" + questionCount + "题）");
                    }
                }
                questionnaireItem.put("subQuestions", subQuestions);
                projectItems.add(questionnaireItem);
            }
            
            // 构建返回数据
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM月dd日");
            Map<String, Object> planDetail = new HashMap<>();
            planDetail.put("planId", plan.getId());
            planDetail.put("subplanId", mainSubplan.getId()); // 新增：子计划ID
            planDetail.put("date", calculatePlanDate(mainSubplan, plan, phone));
            planDetail.put("name", plan.getName());
            planDetail.put("desc", mainSubplan.getDescription() != null ? mainSubplan.getDescription() : plan.getDescription());
            planDetail.put("number", "第" + planNumber + "次计划");
            planDetail.put("personName", "患者"); // 这里应该从用户信息中获取
            planDetail.put("projectItems", projectItems);
            
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", planDetail);
            
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取随访计划详情失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 3. 获取用户信息接口
     * 用于获取用户信息，判断是否注册，是否绑定，是否授权，是否入组
     */
    @PostMapping("/user-info")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Map<String, Object>> getUserInfo(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String phone = (String) request.get("phone");
            if (phone == null || phone.isEmpty()) {
                result.put("code", 400);
                result.put("message", "手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }
            
            // 查询C端用户
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.ok(result);
            }
            
            // 查询用户所在的分组
            LambdaQueryWrapper<PatientGroupRelation> relationWrapper = new LambdaQueryWrapper<>();
            relationWrapper.eq(PatientGroupRelation::getPatientId, cUser.getId());
            List<PatientGroupRelation> relations = patientGroupRelationMapper.selectList(relationWrapper);
            
            List<Map<String, Object>> groups = new ArrayList<>();
            for (PatientGroupRelation relation : relations) {
                PatientGroup group = patientGroupService.getById(relation.getGroupId());
                if (group != null) {
                    Map<String, Object> groupInfo = new HashMap<>();
                    groupInfo.put("groupId", group.getId().toString());
                    groupInfo.put("groupName", group.getName());
                    
                    // 智能判断同意状态：
                    // 业务流程：
                    // 1. 用户确认入组 → user_agree=true, status='0'(待审核), doctor_agree=null
                    // 2. 医生审核通过 → doctor_agree=true, status='1'(已入组)
                    // 3. 医生审核不通过 → doctor_agree=false, status='2'(已拒绝)
                    // 4. 待审核状态 → doctor_agree=null, 代表等待医生审核
                    
                    Boolean userAgree = relation.getUserAgree();
                    if (userAgree == null) {
                        // 如果user_agree字段为NULL，根据status推断
                        if ("1".equals(relation.getStatus())) {
                            userAgree = true; // 已入组状态，说明用户已同意
                        } else {
                            userAgree = false; // 其他状态，说明用户未同意
                        }
                    }
                    groupInfo.put("isUserAgree", userAgree ? 1 : 0);
                    
                    Boolean doctorAgree = relation.getDoctorAgree();
                    if (doctorAgree == null) {
                        // 如果doctor_agree字段为NULL，根据status推断
                        if ("1".equals(relation.getStatus())) {
                            doctorAgree = true; // 已入组状态，说明医生已审核通过
                        } else if ("2".equals(relation.getStatus())) {
                            doctorAgree = false; // 已拒绝状态，说明医生不同意
                        } else {
                            doctorAgree = false; // 待审核状态，说明医生还没审核（返回false表示还没同意）
                        }
                    }
                    groupInfo.put("isDoctorAgree", doctorAgree ? 1 : 0);
                    
                    groups.add(groupInfo);
                }
            }
            
            // 构建返回数据
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("name", cUser.getName());
            userInfo.put("gender", cUser.getGender());
            userInfo.put("age", cUser.getAge());
            userInfo.put("idType", cUser.getIdType());
            userInfo.put("relationship", cUser.getRelationship());
            userInfo.put("idNumber", cUser.getIdNumber());
            userInfo.put("phone", cUser.getPhone());
            userInfo.put("jhrIdType", cUser.getJhrIdType());
            userInfo.put("jhrIdNumber", cUser.getJhrIdNumber());
            userInfo.put("jhrName", cUser.getJhrName());
            userInfo.put("jhrPhone", cUser.getJhrPhone());
            userInfo.put("isBind", cUser.getIsBind() != null && cUser.getIsBind() == 1 ? 1 : 0);
            userInfo.put("isAuth", cUser.getIsAuth() != null && cUser.getIsAuth() == 1 ? 1 : 0);
            userInfo.put("groups", groups);
            
            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", userInfo);
            
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取用户信息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }



    /**
     * 4. C端患者随访子计划的问卷填报接口
     * 获取指定子计划的问卷列表
     */
    @PostMapping("/questionnaire-list")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Map<String, Object>> getQuestionnaireList(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String planIdStr = (String) request.get("planId");
            String phone = (String) request.get("phone");
            String subplanIdStr = (String) request.get("subplanId");

            if (planIdStr == null || planIdStr.isEmpty()) {
                result.put("code", 400);
                result.put("message", "计划ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (phone == null || phone.isEmpty()) {
                result.put("code", 400);
                result.put("message", "手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            Long planId = Long.parseLong(planIdStr);
            Long subplanId = subplanIdStr != null ? Long.parseLong(subplanIdStr) : null;

            // 查询C端用户
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.ok(result);
            }

            // 验证用户是否有权限访问该计划
            if (!validateUserPlanAccess(cUser.getId(), planId)) {
                result.put("code", 403);
                result.put("message", "无权限访问该随访计划");
                return ResponseEntity.ok(result);
            }

            // 查询随访计划
            FollowupPlan plan = followupPlanService.getById(planId);
            if (plan == null) {
                result.put("code", 404);
                result.put("message", "随访计划不存在");
                return ResponseEntity.ok(result);
            }
            
            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("planId", planId);
            data.put("planName", plan.getName());

            if (subplanId != null) {
                // 获取指定子计划的问卷
                FollowupSubplan subplan = followupSubplanService.getById(subplanId);
                if (subplan == null) {
                    result.put("code", 404);
                    result.put("message", "子计划不存在");
                    return ResponseEntity.ok(result);
                }
                data.put("subplanId", subplanId);
                data.put("subplanName", subplan.getName());
                data.put("questionnaires", buildQuestionnaireList(subplanId));
            } else {
                // 获取所有子计划的问卷
                LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
                subplanWrapper.eq(FollowupSubplan::getPlanId, planId);
                subplanWrapper.orderByAsc(FollowupSubplan::getSortOrder);
                List<FollowupSubplan> subplans = followupSubplanService.list(subplanWrapper);

                List<Map<String, Object>> allQuestionnaires = new ArrayList<>();
                for (FollowupSubplan subplan : subplans) {
                    List<Map<String, Object>> subplanQuestionnaires = buildQuestionnaireList(subplan.getId());
                    allQuestionnaires.addAll(subplanQuestionnaires);
                }
                data.put("questionnaires", allQuestionnaires);
            }

            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取问卷列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 5. C端患者问卷填报接口
     * 提交问卷答案
     */
    @PostMapping("/questionnaire-submit")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Map<String, Object>> submitQuestionnaire(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String phone = (String) request.get("phone");
            Object planIdObj = request.get("planId");
            Object subplanIdObj = request.get("subplanId");
            List<Map<String, Object>> questionnaires = (List<Map<String, Object>>) request.get("questionnaires");

            if (phone == null || phone.isEmpty()) {
                result.put("code", 400);
                result.put("message", "手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (planIdObj == null) {
                result.put("code", 400);
                result.put("message", "计划ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (questionnaires == null || questionnaires.isEmpty()) {
                result.put("code", 400);
                result.put("message", "问卷数据不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            Long planId = Long.parseLong(planIdObj.toString());
            Long subplanId = subplanIdObj != null ? Long.parseLong(subplanIdObj.toString()) : null;
            
            // 查询C端用户
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.ok(result);
            }

            // 验证用户权限
            if (!validateUserPlanAccess(cUser.getId(), planId)) {
                result.put("code", 403);
                result.put("message", "无权限访问该随访计划");
                return ResponseEntity.ok(result);
            }

            // 处理每个问卷的提交
            Date submitTime = new Date();
            List<Map<String, Object>> submittedQuestionnaires = new ArrayList<>();
            java.math.BigDecimal totalScore = java.math.BigDecimal.ZERO;

            for (Map<String, Object> questionnaireData : questionnaires) {
                Object questionnaireIdObj = questionnaireData.get("id");
                String questionnaireName = (String) questionnaireData.get("name");
                List<Map<String, Object>> questions = (List<Map<String, Object>>) questionnaireData.get("questions");

                if (questionnaireIdObj == null || questions == null) {
                    continue;
                }

                Long questionnaireId = Long.parseLong(questionnaireIdObj.toString());

                // 验证问卷是否属于该子计划
                if (subplanId != null && !validateQuestionnaireInSubplan(questionnaireId, subplanId)) {
                    result.put("code", 400);
                    result.put("message", "问卷不属于指定的子计划");
                    return ResponseEntity.ok(result);
                }

                // 处理问卷中的每个题目
                java.math.BigDecimal questionnaireScore = java.math.BigDecimal.ZERO;
                java.math.BigDecimal maxQuestionnaireScore = java.math.BigDecimal.ZERO;
                int submittedQuestions = 0;

                for (Map<String, Object> questionData : questions) {
                    Object questionIdObj = questionData.get("id");
                    String questionType = (String) questionData.get("questionType");
                    Object resultObj = questionData.get("result");

                    if (questionIdObj == null || resultObj == null) {
                        continue;
                    }

                    // 跳过未填写的题目
                    if (resultObj instanceof String && ((String) resultObj).trim().isEmpty()) {
                        continue;
                    }

                    Long questionId = Long.parseLong(questionIdObj.toString());

                    // 处理不同类型题目的答案
                    String answerValue = "";
                    String answerText = "";
                    java.math.BigDecimal questionScore = java.math.BigDecimal.ZERO;

                    if ("radio".equals(questionType)) {
                        // 单选题处理
                        answerValue = resultObj.toString();
                        QuestionnaireOption option = findOptionByValue(questionId, answerValue);
                        if (option != null) {
                            answerText = option.getOptionText();
                            questionScore = option.getScore() != null ? new java.math.BigDecimal(option.getScore()) : java.math.BigDecimal.ZERO;
                        }
                    } else if ("checkbox".equals(questionType)) {
                        // 多选题处理
                        List<String> selectedValues = (List<String>) resultObj;
                        answerValue = String.join(",", selectedValues);

                        List<String> selectedTexts = new ArrayList<>();
                        for (String value : selectedValues) {
                            QuestionnaireOption option = findOptionByValue(questionId, value);
                            if (option != null) {
                                selectedTexts.add(option.getOptionText());
                                questionScore = questionScore.add(option.getScore() != null ? new java.math.BigDecimal(option.getScore()) : java.math.BigDecimal.ZERO);
                            }
                        }
                        answerText = String.join(",", selectedTexts);
                    } else {
                        // 文本题、数字题等
                        answerValue = resultObj.toString();
                        answerText = answerValue;
                        questionScore = java.math.BigDecimal.ZERO; // 文本题不计分
                    }

                    // 保存答案记录
                    QuestionnaireAnswer answer = new QuestionnaireAnswer();
                    answer.setQuestionnaireId(questionnaireId);
                    answer.setQuestionId(questionId);
                    answer.setPatientId(cUser.getId());
                    answer.setPatientName(cUser.getName());
                    answer.setAnswerType(questionType);
                    answer.setAnswerText(answerText);
                    answer.setAnswerValue(answerValue);
                    answer.setPlanId(planId);
                    answer.setSubplanId(subplanId);
                    answer.setAnswerTime(submitTime);
                    answer.setAnswerSource("manual");
                    answer.setAnswerScore(questionScore);
                    answer.setCreateTime(submitTime);
                    answer.setUpdateTime(submitTime);

                    // 保存单个答案
                    questionnaireAnswerService.saveAnswer(answer);

                    questionnaireScore = questionnaireScore.add(questionScore);
                    submittedQuestions++;

                    // 计算题目最大分数
                    java.math.BigDecimal maxQuestionScore = calculateMaxQuestionScore(questionId, questionType);
                    maxQuestionnaireScore = maxQuestionnaireScore.add(maxQuestionScore);
                }

                // 记录问卷提交信息
                Map<String, Object> submittedInfo = new HashMap<>();
                submittedInfo.put("questionnaireId", questionnaireId);
                submittedInfo.put("questionnaireName", questionnaireName);
                submittedInfo.put("totalScore", questionnaireScore);
                submittedInfo.put("maxScore", maxQuestionnaireScore);
                submittedInfo.put("submittedQuestions", submittedQuestions);
                submittedInfo.put("totalQuestions", questions.size());

                submittedQuestionnaires.add(submittedInfo);
                totalScore = totalScore.add(questionnaireScore);
            }
            
            // 返回结果
            Map<String, Object> data = new HashMap<>();
            data.put("planId", planId);
            data.put("subplanId", subplanId);
            data.put("submittedQuestionnaires", submittedQuestionnaires);
            data.put("totalScore", totalScore);
            data.put("submitTime", submitTime);

            result.put("code", 200);
            result.put("data", data);
            result.put("message", "问卷提交成功");
            
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "问卷提交失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 6. C端患者问卷答案查看接口
     * 查看已提交的问卷答案
     */
    @PostMapping("/questionnaire-answers")
    @SuppressWarnings("unchecked")
    public ResponseEntity<Map<String, Object>> getQuestionnaireAnswers(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String phone = (String) request.get("phone");
            Object planIdObj = request.get("planId");
            Object subplanIdObj = request.get("subplanId");
            Object questionnaireIdObj = request.get("questionnaireId");

            if (phone == null || phone.isEmpty()) {
                result.put("code", 400);
                result.put("message", "手机号不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            if (planIdObj == null) {
                result.put("code", 400);
                result.put("message", "计划ID不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            Long planId = Long.parseLong(planIdObj.toString());
            Long subplanId = subplanIdObj != null ? Long.parseLong(subplanIdObj.toString()) : null;
            Long questionnaireId = questionnaireIdObj != null ? Long.parseLong(questionnaireIdObj.toString()) : null;

            // 查询C端用户
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                result.put("code", 404);
                result.put("message", "用户不存在");
                return ResponseEntity.ok(result);
            }

            // 添加调试信息
            System.out.println("查询到用户: ID=" + cUser.getId() + ", 姓名=" + cUser.getName() + ", 手机号=" + phone);

            // 验证用户权限
            if (!validateUserPlanAccess(cUser.getId(), planId)) {
                result.put("code", 403);
                result.put("message", "无权限访问该随访计划");
                return ResponseEntity.ok(result);
            }
            
            // 查询随访计划
            FollowupPlan plan = followupPlanService.getById(planId);
            if (plan == null) {
                result.put("code", 404);
                result.put("message", "随访计划不存在");
                return ResponseEntity.ok(result);
            }

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("planId", planId);
            data.put("planName", plan.getName());

            if (subplanId != null) {
                // 获取指定子计划的问卷答案
                FollowupSubplan subplan = followupSubplanService.getById(subplanId);
                if (subplan == null) {
                    result.put("code", 404);
                    result.put("message", "子计划不存在");
                    return ResponseEntity.ok(result);
                }
                data.put("subplanId", subplanId);
                data.put("subplanName", subplan.getName());

                if (questionnaireId != null) {
                    // 获取指定问卷的答案
                    List<Map<String, Object>> questionnaires = buildQuestionnaireAnswers(questionnaireId, cUser.getId(), subplanId);
                    data.put("questionnaires", questionnaires);
                } else {
                    // 获取子计划下所有问卷的答案
                    List<Map<String, Object>> questionnaires = buildSubplanQuestionnaireAnswers(subplanId, cUser.getId());
                    data.put("questionnaires", questionnaires);
                }
            } else {
                // 获取计划下所有问卷的答案
                LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
                subplanWrapper.eq(FollowupSubplan::getPlanId, planId);
                subplanWrapper.orderByAsc(FollowupSubplan::getSortOrder);
                List<FollowupSubplan> subplans = followupSubplanService.list(subplanWrapper);

                List<Map<String, Object>> allQuestionnaires = new ArrayList<>();
                for (FollowupSubplan subplan : subplans) {
                    List<Map<String, Object>> subplanQuestionnaires = buildSubplanQuestionnaireAnswers(subplan.getId(), cUser.getId());
                    allQuestionnaires.addAll(subplanQuestionnaires);
                }
                data.put("questionnaires", allQuestionnaires);
            }

            // 计算总分和提交时间
            Date latestSubmitTime = getLatestSubmitTime(cUser.getId(), planId, subplanId, questionnaireId);
            java.math.BigDecimal totalScore = calculateTotalScore(cUser.getId(), planId, subplanId, questionnaireId);

            data.put("submitTime", latestSubmitTime);
            data.put("totalScore", totalScore);

            result.put("code", 200);
            result.put("message", "获取成功");
            result.put("data", data);
            
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取问卷答案失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * 根据子计划配置计算实际日期
     */
    private String calculatePlanDate(FollowupSubplan subplan, FollowupPlan plan, String phone) {
        try {
            // 获取患者入组时间
            FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
            if (cUser == null) {
                return new SimpleDateFormat("MM月dd日").format(new Date());
            }
            
            // 查询患者入组时间
            LambdaQueryWrapper<PatientGroupRelation> relationWrapper = new LambdaQueryWrapper<>();
            relationWrapper.eq(PatientGroupRelation::getPatientId, cUser.getId())
                          .eq(PatientGroupRelation::getGroupId, plan.getGroupId())
                          .eq(PatientGroupRelation::getStatus, "1"); // 已审核通过
            PatientGroupRelation relation = patientGroupRelationMapper.selectOne(relationWrapper);
            
            if (relation == null || relation.getJoinTime() == null) {
                return new SimpleDateFormat("MM月dd日").format(new Date());
            }
            
            Date joinTime = relation.getJoinTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(joinTime);
            
            // 根据子计划配置计算日期
            if ("cycle".equals(subplan.getPlanType())) {
                // 周期循环模式：根据子计划序号和周期天数计算
                // 查询子计划项目获取周期天数
                LambdaQueryWrapper<FollowupSubplanItem> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.eq(FollowupSubplanItem::getSubplanId, subplan.getId());
                List<FollowupSubplanItem> items = followupSubplanItemService.list(itemWrapper);
                
                if (!items.isEmpty() && items.get(0).getCycleDays() != null) {
                    int cycleDays = items.get(0).getCycleDays();
                    // 计算第几个周期（子计划序号从0开始）
                    int cycleNumber = subplan.getSortOrder() != null ? subplan.getSortOrder() : 0;
                    calendar.add(Calendar.DAY_OF_MONTH, cycleDays * cycleNumber);
                }
            } else if ("custom".equals(subplan.getPlanType())) {
                // 自定义模式：根据子计划项目的joinDay计算
                LambdaQueryWrapper<FollowupSubplanItem> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.eq(FollowupSubplanItem::getSubplanId, subplan.getId());
                List<FollowupSubplanItem> items = followupSubplanItemService.list(itemWrapper);
                
                if (!items.isEmpty() && items.get(0).getJoinDay() != null) {
                    int joinDay = items.get(0).getJoinDay();
                    calendar.add(Calendar.DAY_OF_MONTH, joinDay - 1); // joinDay从1开始，所以减1
                }
            }
            
            return new SimpleDateFormat("MM月dd日").format(calendar.getTime());
            
        } catch (Exception e) {
            // 如果计算出错，返回当前日期
            return new SimpleDateFormat("MM月dd日").format(new Date());
        }
    }

    /**
     * 转换题目类型
     */
    private String convertQuestionType(String type) {
        if (type == null) return "radio";
        
        switch (type.toLowerCase()) {
            case "single":
                return "radio";
            case "multiple":
                return "checkbox";
            case "text":
                return "input";
            case "textarea":
                return "input";
            case "number":
                return "input";
            case "date":
                return "input";
            default:
                return "radio";
        }
    }

    /**
     * 计算子计划状态：根据用户是否填写了该子计划关联的问卷来判断
     * @param subplanId 子计划ID
     * @param userId 用户ID
     * @return "0" = 未填报，"1" = 已填报
     */
    private String calculateSubplanStatus(Long subplanId, Long userId) {
        try {
            // 1. 查询该子计划关联的问卷
            LambdaQueryWrapper<FollowupSubplanQuestionnaire> questionnaireWrapper = new LambdaQueryWrapper<>();
            questionnaireWrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplanId);
            List<FollowupSubplanQuestionnaire> subplanQuestionnaires = followupSubplanQuestionnaireService.list(questionnaireWrapper);

            if (subplanQuestionnaires.isEmpty()) {
                // 如果没有关联问卷，默认为已完成
                return "1";
            }

            // 2. 检查用户是否已经填写了所有关联的问卷
            for (FollowupSubplanQuestionnaire subplanQuestionnaire : subplanQuestionnaires) {
                Long questionnaireId = subplanQuestionnaire.getQuestionnaireId();

                // 查询用户是否填写了该问卷
                List<QuestionnaireAnswerDTO> answers = questionnaireAnswerService.getAnswersByPatientAndQuestionnaire(userId, questionnaireId);

                // 进一步过滤：只查找属于当前子计划的答案
                boolean hasAnswerForSubplan = answers.stream()
                    .anyMatch(answer -> subplanId.equals(answer.getSubplanId()));

                if (!hasAnswerForSubplan) {
                    // 如果有任何一个问卷没有填写，返回未填报
                    return "0";
                }
            }

            // 所有问卷都已填写，返回已填报
            return "1";

        } catch (Exception e) {
            // 出现异常时，默认返回未填报
            return "0";
        }
    }

    /**
     * 验证用户是否有权限访问该计划
     */
    private boolean validateUserPlanAccess(Long userId, Long planId) {
        try {
            // 查询计划对应的分组
            FollowupPlan plan = followupPlanService.getById(planId);
            if (plan == null) {
                return false;
            }

            // 检查用户是否在该分组中
            LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PatientGroupRelation::getPatientId, userId);
            wrapper.eq(PatientGroupRelation::getGroupId, plan.getGroupId());
            wrapper.eq(PatientGroupRelation::getStatus, "1"); // 已入组

            int count = Math.toIntExact(patientGroupRelationMapper.selectCount(wrapper));
            return count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 构建问卷列表数据
     */
    private List<Map<String, Object>> buildQuestionnaireList(Long subplanId) {
        List<Map<String, Object>> questionnaires = new ArrayList<>();

        try {
            // 获取子计划关联的问卷
            LambdaQueryWrapper<FollowupSubplanQuestionnaire> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplanId);
            wrapper.orderByAsc(FollowupSubplanQuestionnaire::getSortOrder);
            List<FollowupSubplanQuestionnaire> relations = followupSubplanQuestionnaireService.list(wrapper);

            for (FollowupSubplanQuestionnaire relation : relations) {
                Questionnaire questionnaire = questionnaireService.getQuestionnaireById(relation.getQuestionnaireId());
                if (questionnaire != null) {
                    Map<String, Object> questionnaireData = new HashMap<>();
                    questionnaireData.put("id", questionnaire.getId());
                    questionnaireData.put("name", questionnaire.getName());
                    questionnaireData.put("description", questionnaire.getType());
                    questionnaireData.put("sortOrder", relation.getSortOrder());
                    questionnaireData.put("isRequired", relation.getIsRequired());

                    // 获取问卷题目
                    List<Map<String, Object>> questions = buildQuestionList(questionnaire.getId());
                    questionnaireData.put("questions", questions);

                    questionnaires.add(questionnaireData);
                }
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("构建问卷列表失败: " + e.getMessage());
        }

        return questionnaires;
    }

    /**
     * 构建题目列表数据
     */
    private List<Map<String, Object>> buildQuestionList(Long questionnaireId) {
        List<Map<String, Object>> questions = new ArrayList<>();

        try {
            // 获取问卷题目
            List<QuestionnaireQuestion> questionList = questionnaireQuestionService.getQuestionsByQuestionnaireId(questionnaireId);

            for (QuestionnaireQuestion question : questionList) {
                Map<String, Object> questionData = new HashMap<>();
                questionData.put("id", question.getId());
                questionData.put("questionText", question.getTitle());
                questionData.put("questionType", convertQuestionType(question.getType()));
                questionData.put("isRequired", question.getRequired() != null && question.getRequired());
                questionData.put("sortOrder", question.getSort() != null ? question.getSort().intValue() : 0);

                // 获取题目选项
                List<Map<String, Object>> options = buildOptionList(question.getId());
                questionData.put("options", options);

                questions.add(questionData);
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("构建题目列表失败: " + e.getMessage());
        }

        return questions;
    }

    /**
     * 构建选项列表数据
     */
    private List<Map<String, Object>> buildOptionList(Long questionId) {
        List<Map<String, Object>> options = new ArrayList<>();

        try {
            List<QuestionnaireOption> optionList = questionnaireOptionService.getOptionsByQuestionId(questionId);

            for (QuestionnaireOption option : optionList) {
                Map<String, Object> optionData = new HashMap<>();
                optionData.put("id", option.getId());
                optionData.put("optionText", option.getOptionText());
                optionData.put("optionValue", option.getId().toString()); // 使用选项ID作为值
                optionData.put("score", option.getScore() != null ? option.getScore() : 0);
                optionData.put("sortOrder", option.getSort() != null ? option.getSort().intValue() : 0);

                options.add(optionData);
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("构建选项列表失败: " + e.getMessage());
        }

        return options;
    }

    /**
     * 验证问卷是否属于指定子计划
     */
    private boolean validateQuestionnaireInSubplan(Long questionnaireId, Long subplanId) {
        try {
            LambdaQueryWrapper<FollowupSubplanQuestionnaire> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FollowupSubplanQuestionnaire::getQuestionnaireId, questionnaireId);
            wrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplanId);

            int count = (int) followupSubplanQuestionnaireService.count(wrapper);
            return count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据选项值查找选项
     */
    private QuestionnaireOption findOptionByValue(Long questionId, String optionValue) {
        try {
            List<QuestionnaireOption> options = questionnaireOptionService.getOptionsByQuestionId(questionId);
            for (QuestionnaireOption option : options) {
                if (optionValue.equals(option.getId().toString())) {
                    return option;
                }
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("查找选项失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 计算题目最大分数
     */
    private java.math.BigDecimal calculateMaxQuestionScore(Long questionId, String questionType) {
        try {
            if ("input".equals(questionType) || "number".equals(questionType) || "date".equals(questionType)) {
                return java.math.BigDecimal.ZERO; // 文本类题目不计分
            }

            List<QuestionnaireOption> options = questionnaireOptionService.getOptionsByQuestionId(questionId);

            if ("radio".equals(questionType)) {
                // 单选题：最大分数是所有选项中的最高分
                java.math.BigDecimal maxScore = java.math.BigDecimal.ZERO;
                for (QuestionnaireOption option : options) {
                    if (option.getScore() != null) {
                        java.math.BigDecimal score = new java.math.BigDecimal(option.getScore());
                        if (score.compareTo(maxScore) > 0) {
                            maxScore = score;
                        }
                    }
                }
                return maxScore;
            } else if ("checkbox".equals(questionType)) {
                // 多选题：最大分数是所有选项分数之和
                java.math.BigDecimal totalScore = java.math.BigDecimal.ZERO;
                for (QuestionnaireOption option : options) {
                    if (option.getScore() != null) {
                        totalScore = totalScore.add(new java.math.BigDecimal(option.getScore()));
                    }
                }
                return totalScore;
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("计算最大分数失败: " + e.getMessage());
        }

        return java.math.BigDecimal.ZERO;
    }

    /**
     * 构建子计划问卷答案数据
     */
    private List<Map<String, Object>> buildSubplanQuestionnaireAnswers(Long subplanId, Long patientId) {
        List<Map<String, Object>> questionnaires = new ArrayList<>();

        try {
            // 获取子计划关联的问卷
            LambdaQueryWrapper<FollowupSubplanQuestionnaire> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplanId);
            wrapper.orderByAsc(FollowupSubplanQuestionnaire::getSortOrder);
            List<FollowupSubplanQuestionnaire> relations = followupSubplanQuestionnaireService.list(wrapper);

            for (FollowupSubplanQuestionnaire relation : relations) {
                List<Map<String, Object>> questionnaireAnswers = buildQuestionnaireAnswers(relation.getQuestionnaireId(), patientId, subplanId);
                questionnaires.addAll(questionnaireAnswers);
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("构建子计划问卷答案失败: " + e.getMessage());
        }

        return questionnaires;
    }

    /**
     * 构建问卷答案数据
     */
    private List<Map<String, Object>> buildQuestionnaireAnswers(Long questionnaireId, Long patientId, Long subplanId) {
        List<Map<String, Object>> questionnaires = new ArrayList<>();

        try {
            Questionnaire questionnaire = questionnaireService.getQuestionnaireById(questionnaireId);
            if (questionnaire == null) {
                return questionnaires;
            }

            Map<String, Object> questionnaireData = new HashMap<>();
            questionnaireData.put("id", questionnaire.getId());
            questionnaireData.put("name", questionnaire.getName());
            questionnaireData.put("description", questionnaire.getType());

            // 获取问卷题目和答案
            List<Map<String, Object>> questions = buildQuestionAnswers(questionnaireId, patientId, subplanId);
            questionnaireData.put("questions", questions);

            questionnaires.add(questionnaireData);
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("构建问卷答案失败: " + e.getMessage());
        }

        return questionnaires;
    }

    /**
     * 构建题目答案数据
     */
    private List<Map<String, Object>> buildQuestionAnswers(Long questionnaireId, Long patientId, Long subplanId) {
        List<Map<String, Object>> questions = new ArrayList<>();

        try {
            // 获取问卷题目
            List<QuestionnaireQuestion> questionList = questionnaireQuestionService.getQuestionsByQuestionnaireId(questionnaireId);

            for (QuestionnaireQuestion question : questionList) {
                Map<String, Object> questionData = new HashMap<>();
                questionData.put("id", question.getId());
                questionData.put("questionText", question.getTitle());
                questionData.put("questionType", convertQuestionType(question.getType()));
                questionData.put("isRequired", question.getRequired() != null && question.getRequired());
                questionData.put("sortOrder", question.getSort() != null ? question.getSort().intValue() : 0);

                // 获取题目选项
                List<Map<String, Object>> options = buildOptionList(question.getId());
                questionData.put("options", options);

                // 获取用户答案
                Object result = getQuestionAnswerResult(questionnaireId, question.getId(), patientId, subplanId, convertQuestionType(question.getType()));
                questionData.put("result", result);

                questions.add(questionData);
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("构建题目答案失败: " + e.getMessage());
        }

        return questions;
    }

    /**
     * 获取题目答案
     */
    private String getQuestionAnswer(Long questionnaireId, Long questionId, Long patientId, Long subplanId) {
        try {
            LambdaQueryWrapper<QuestionnaireAnswer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QuestionnaireAnswer::getQuestionnaireId, questionnaireId);
            wrapper.eq(QuestionnaireAnswer::getQuestionId, questionId);
            wrapper.eq(QuestionnaireAnswer::getPatientId, patientId);
            // 注意：数据库中的数据subplan_id为NULL，所以不要加subplan_id的条件
            // if (subplanId != null) {
            //     wrapper.eq(QuestionnaireAnswer::getSubplanId, subplanId);
            // }
            wrapper.orderByDesc(QuestionnaireAnswer::getAnswerTime);
            wrapper.last("LIMIT 1");

            // 添加调试信息
            System.out.println("查询答案参数: questionnaireId=" + questionnaireId +
                             ", questionId=" + questionId +
                             ", patientId=" + patientId +
                             ", subplanId=" + subplanId);

            QuestionnaireAnswer answer = questionnaireAnswerMapper.selectOne(wrapper);
            if (answer != null) {
                String answerValue = answer.getAnswerValue();
                String questionType = answer.getAnswerType();

                System.out.println("找到答案: answerValue=" + answerValue + ", questionType=" + questionType);

                // 处理多选题答案格式 - 返回数组格式
                if ("checkbox".equals(questionType) && answerValue != null && answerValue.contains(",")) {
                    // 对于多选题，返回选项ID数组
                    return answerValue; // 先返回原始值，让前端处理数组格式
                }

                return answerValue;
            } else {
                System.out.println("未找到答案记录");
            }
        } catch (Exception e) {
            // 记录错误日志
            System.err.println("获取题目答案失败: " + e.getMessage());
            e.printStackTrace();
        }

        return "";
    }

    /**
     * 获取题目答案结果（根据题目类型返回正确格式）
     */
    private Object getQuestionAnswerResult(Long questionnaireId, Long questionId, Long patientId, Long subplanId, String questionType) {
        try {
            LambdaQueryWrapper<QuestionnaireAnswer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QuestionnaireAnswer::getQuestionnaireId, questionnaireId);
            wrapper.eq(QuestionnaireAnswer::getQuestionId, questionId);
            wrapper.eq(QuestionnaireAnswer::getPatientId, patientId);
            wrapper.orderByDesc(QuestionnaireAnswer::getAnswerTime);
            wrapper.last("LIMIT 1");

            // 添加调试信息
            System.out.println("查询答案参数: questionnaireId=" + questionnaireId +
                             ", questionId=" + questionId +
                             ", patientId=" + patientId +
                             ", questionType=" + questionType);

            QuestionnaireAnswer answer = questionnaireAnswerMapper.selectOne(wrapper);
            if (answer != null) {
                String answerValue = answer.getAnswerValue();

                System.out.println("找到答案: answerValue=" + answerValue + ", questionType=" + questionType);

                if ("checkbox".equals(questionType)) {
                    // 多选题：返回字符串数组
                    if (answerValue != null && answerValue.contains(",")) {
                        String[] values = answerValue.split(",");
                        List<String> valueList = new ArrayList<>();
                        for (String value : values) {
                            valueList.add(value.trim());
                        }
                        return valueList; // 返回List，Jackson会自动转换为JSON数组
                    } else if (answerValue != null && !answerValue.isEmpty()) {
                        // 单个选项的多选题
                        List<String> valueList = new ArrayList<>();
                        valueList.add(answerValue.trim());
                        return valueList;
                    } else {
                        return new ArrayList<String>(); // 空数组
                    }
                } else {
                    // 单选题和文本题：返回字符串
                    return answerValue != null ? answerValue : "";
                }
            } else {
                System.out.println("未找到答案记录");
            }
        } catch (Exception e) {
            System.err.println("获取题目答案失败: " + e.getMessage());
            e.printStackTrace();
        }

        // 根据题目类型返回默认值
        if ("checkbox".equals(questionType)) {
            return new ArrayList<String>(); // 多选题返回空数组
        } else {
            return ""; // 单选题和文本题返回空字符串
        }
    }

    /**
     * 获取最新提交时间
     */
    private Date getLatestSubmitTime(Long patientId, Long planId, Long subplanId, Long questionnaireId) {
        try {
            LambdaQueryWrapper<QuestionnaireAnswer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QuestionnaireAnswer::getPatientId, patientId);
            wrapper.eq(QuestionnaireAnswer::getPlanId, planId);
            if (subplanId != null) {
                wrapper.eq(QuestionnaireAnswer::getSubplanId, subplanId);
            }
            if (questionnaireId != null) {
                wrapper.eq(QuestionnaireAnswer::getQuestionnaireId, questionnaireId);
            }
            wrapper.orderByDesc(QuestionnaireAnswer::getAnswerTime);
            wrapper.last("LIMIT 1");

            QuestionnaireAnswer answer = questionnaireAnswerMapper.selectOne(wrapper);
            return answer != null ? answer.getAnswerTime() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 计算总分
     */
    private java.math.BigDecimal calculateTotalScore(Long patientId, Long planId, Long subplanId, Long questionnaireId) {
        try {
            LambdaQueryWrapper<QuestionnaireAnswer> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QuestionnaireAnswer::getPatientId, patientId);
            wrapper.eq(QuestionnaireAnswer::getPlanId, planId);
            if (subplanId != null) {
                wrapper.eq(QuestionnaireAnswer::getSubplanId, subplanId);
            }
            if (questionnaireId != null) {
                wrapper.eq(QuestionnaireAnswer::getQuestionnaireId, questionnaireId);
            }

            List<QuestionnaireAnswer> answers = questionnaireAnswerMapper.selectList(wrapper);
            java.math.BigDecimal totalScore = java.math.BigDecimal.ZERO;

            for (QuestionnaireAnswer answer : answers) {
                if (answer.getAnswerScore() != null) {
                    totalScore = totalScore.add(answer.getAnswerScore());
                }
            }

            return totalScore;
        } catch (Exception e) {
            return java.math.BigDecimal.ZERO;
        }
    }

    /**
     * 构建包含所有子计划信息的详情
     */
    private ResponseEntity<Map<String, Object>> buildAllSubplansDetail(FollowupPlan plan, List<FollowupSubplan> subplans, FollowupCUser cUser) {
        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> planDetail = new HashMap<>();
            planDetail.put("planId", plan.getId());
            planDetail.put("name", plan.getName());
            planDetail.put("desc", plan.getDescription() != null ? plan.getDescription() : "");
            planDetail.put("personName", "患者");

            // 汇总模式下，添加所有子计划ID列表
            List<Long> subplanIds = new ArrayList<>();
            for (FollowupSubplan subplan : subplans) {
                subplanIds.add(subplan.getId());
            }
            planDetail.put("subplanIds", subplanIds);

            // 汇总所有子计划的信息
            List<Map<String, Object>> allProjectItems = new ArrayList<>();

            for (int i = 0; i < subplans.size(); i++) {
                FollowupSubplan subplan = subplans.get(i);

                // 查询子计划关联的问卷
                LambdaQueryWrapper<FollowupSubplanQuestionnaire> questionnaireWrapper = new LambdaQueryWrapper<>();
                questionnaireWrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplan.getId());
                questionnaireWrapper.orderByAsc(FollowupSubplanQuestionnaire::getSortOrder);
                List<FollowupSubplanQuestionnaire> questionnaireRels = followupSubplanQuestionnaireService.list(questionnaireWrapper);

                if (!questionnaireRels.isEmpty()) {
                    Map<String, Object> projectItem = new HashMap<>();
                    projectItem.put("title", "问卷调查 - 第" + (i + 1) + "次计划");

                    List<String> subQuestions = new ArrayList<>();
                    for (FollowupSubplanQuestionnaire rel : questionnaireRels) {
                        Questionnaire questionnaire = questionnaireService.getQuestionnaireById(rel.getQuestionnaireId());
                        if (questionnaire != null) {
                            // 统计问卷题目数量
                            List<QuestionnaireQuestion> questions = questionnaireQuestionService.getQuestionsByQuestionnaireId(questionnaire.getId());
                            subQuestions.add(questionnaire.getName() + "（共" + questions.size() + "题）");
                        }
                    }

                    projectItem.put("subQuestions", subQuestions);
                    allProjectItems.add(projectItem);
                }
            }

            planDetail.put("projectItems", allProjectItems);

            // 使用第一个子计划的日期作为主要日期
            if (!subplans.isEmpty()) {
                String dateStr = calculatePlanDate(subplans.get(0), plan, cUser.getPhone());
                planDetail.put("date", dateStr);
            }

            planDetail.put("number", "全部计划（共" + subplans.size() + "次）");

            result.put("code", 200);
            result.put("data", planDetail);
            result.put("message", "获取成功");

        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "获取计划详情失败: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
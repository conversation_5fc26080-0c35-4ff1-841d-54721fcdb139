package com.nx.inf.controller;

import com.nx.inf.annotation.TargetDataSource;
import com.nx.inf.enums.DataSourceType;
import com.nx.inf.model.dto.NursingHomeInstitutionDTO;
import com.nx.inf.model.vo.NursingHomeRequestVO;
import com.nx.inf.model.vo.PageResponseVO;
import com.nx.inf.service.NursingHomeInstitutionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 养老院机构管理控制器
 */
@RestController
@RequestMapping("/emr/nursing-home")
@TargetDataSource(value = DataSourceType.MYSQL_DATASOURCE)
@Slf4j
public class NursingHomeInstitutionController {
    
    @Autowired
    private NursingHomeInstitutionService nursingHomeInstitutionService;
    
    /**
     * 1.1 分页获取机构列表
     */
    @PostMapping("/getInstitutionList")
    public ResponseEntity<Map<String, Object>> getInstitutionList(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("获取养老院机构列表，请求参数: {}", request);
            
            PageResponseVO<NursingHomeInstitutionDTO> pageResult = nursingHomeInstitutionService.getInstitutionList(request);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", pageResult.getList());
            data.put("total", pageResult.getTotal());
            data.put("pageNum", pageResult.getPageNum());
            data.put("pageSize", pageResult.getPageSize());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "获取机构列表成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取机构列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取机构列表失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.2 根据ID获取机构详情
     */
    @PostMapping("/getInstitutionById")
    public ResponseEntity<Map<String, Object>> getInstitutionById(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("获取机构详情，ID: {}", request.getId());
            
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            NursingHomeInstitutionDTO institution = nursingHomeInstitutionService.getInstitutionById(request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", institution);
            result.put("message", "获取机构详情成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取机构详情失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取机构详情失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.3 新增养老院机构
     */
    @PostMapping("/createInstitution")
    public ResponseEntity<Map<String, Object>> createInstitution(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("创建养老院机构，请求参数: {}", request);
            
            Long institutionId = nursingHomeInstitutionService.createInstitution(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", institutionId);
            result.put("message", "创建机构成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建机构失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "创建机构失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.4 更新养老院机构
     */
    @PostMapping("/updateInstitution")
    public ResponseEntity<Map<String, Object>> updateInstitution(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("更新养老院机构，请求参数: {}", request);
            
            boolean success = nursingHomeInstitutionService.updateInstitution(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "更新机构成功" : "更新机构失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新机构失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新机构失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.5 删除养老院机构
     */
    @PostMapping("/deleteInstitution")
    public ResponseEntity<Map<String, Object>> deleteInstitution(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("删除养老院机构，ID: {}", request.getId());
            
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            boolean success = nursingHomeInstitutionService.deleteInstitution(request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "删除机构成功" : "删除机构失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除机构失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "删除机构失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.6 批量删除养老院机构
     */
    @PostMapping("/batchDeleteInstitutions")
    public ResponseEntity<Map<String, Object>> batchDeleteInstitutions(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("批量删除养老院机构，IDs: {}", request.getIds());
            
            if (request.getIds() == null || request.getIds().isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID列表不能为空");
                return ResponseEntity.ok(result);
            }
            
            int count = nursingHomeInstitutionService.batchDeleteInstitutions(request.getIds());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", count);
            result.put("message", "批量删除机构成功，删除数量：" + count);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量删除机构失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "批量删除机构失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.7 更新机构状态
     */
    @PostMapping("/updateInstitutionStatus")
    public ResponseEntity<Map<String, Object>> updateInstitutionStatus(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("更新机构状态，ID: {}, 状态: {}", request.getId(), request.getStatus());
            
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            if (request.getStatus() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "状态值不能为空");
                return ResponseEntity.ok(result);
            }
            
            boolean success = nursingHomeInstitutionService.updateInstitutionStatus(request.getId(), request.getStatus());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "更新机构状态成功" : "更新机构状态失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新机构状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新机构状态失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.8 批量更新机构状态
     */
    @PostMapping("/batchUpdateStatus")
    public ResponseEntity<Map<String, Object>> batchUpdateStatus(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("批量更新机构状态，IDs: {}, 状态: {}", request.getIds(), request.getStatus());
            
            if (request.getIds() == null || request.getIds().isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID列表不能为空");
                return ResponseEntity.ok(result);
            }
            
            if (request.getStatus() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "状态值不能为空");
                return ResponseEntity.ok(result);
            }
            
            int count = nursingHomeInstitutionService.batchUpdateStatus(request.getIds(), request.getStatus());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", count);
            result.put("message", "批量更新机构状态成功，更新数量：" + count);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量更新机构状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "批量更新机构状态失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.9 获取所有启用状态的机构
     */
    @PostMapping("/getAllActiveInstitutions")
    public ResponseEntity<Map<String, Object>> getAllActiveInstitutions(@RequestBody(required = false) Map<String, Object> request) {
        try {
            log.info("获取所有启用状态的机构");
            
            List<NursingHomeInstitutionDTO> institutions = nursingHomeInstitutionService.getAllActiveInstitutions();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", institutions);
            result.put("message", "获取启用机构列表成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取启用机构列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取启用机构列表失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.10 检查机构代码是否存在
     */
    @PostMapping("/checkCodeExists")
    public ResponseEntity<Map<String, Object>> checkCodeExists(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("检查机构代码是否存在，代码: {}, 排除ID: {}", request.getCode(), request.getId());
            
            if (request.getCode() == null || request.getCode().trim().isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构代码不能为空");
                return ResponseEntity.ok(result);
            }
            
            boolean exists = nursingHomeInstitutionService.isCodeExists(request.getCode(), request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", exists);
            result.put("message", exists ? "机构代码已存在" : "机构代码可用");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查机构代码失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "检查机构代码失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.11 统计机构数量
     */
    @PostMapping("/countInstitutions")
    public ResponseEntity<Map<String, Object>> countInstitutions(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("统计机构数量，条件: {}", request);
            
            Long count = nursingHomeInstitutionService.countInstitutions(
                request.getStatus(), 
                request.getLevel(), 
                request.getProvince(), 
                request.getCity()
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", count);
            result.put("message", "统计机构数量成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("统计机构数量失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "统计机构数量失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.12 更新机构入住人数
     */
    @PostMapping("/updateCurrentPatients")
    public ResponseEntity<Map<String, Object>> updateCurrentPatients(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("更新机构入住人数，ID: {}, 人数: {}", request.getId(), request.getCurrentPatients());
            
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            if (request.getCurrentPatients() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "入住人数不能为空");
                return ResponseEntity.ok(result);
            }
            
            boolean success = nursingHomeInstitutionService.updateCurrentPatients(request.getId(), request.getCurrentPatients());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "更新入住人数成功" : "更新入住人数失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新入住人数失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新入住人数失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.13 查询附近机构
     */
    @PostMapping("/getNearbyInstitutions")
    public ResponseEntity<Map<String, Object>> getNearbyInstitutions(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("查询附近机构，经度: {}, 纬度: {}", request.getLongitude(), request.getLatitude());
            
            if (request.getLongitude() == null || request.getLatitude() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "经纬度不能为空");
                return ResponseEntity.ok(result);
            }
            
            List<NursingHomeInstitutionDTO> institutions = nursingHomeInstitutionService.getNearbyInstitutions(
                request.getLongitude().toString(), 
                request.getLatitude().toString(), 
                10.0, // 默认10公里
                20    // 默认返回20个
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", institutions);
            result.put("message", "查询附近机构成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询附近机构失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询附近机构失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.14 复制机构
     */
    @PostMapping("/copyInstitution")
    public ResponseEntity<Map<String, Object>> copyInstitution(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("复制机构，ID: {}", request.getId());
            
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "机构ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            NursingHomeInstitutionDTO institution = nursingHomeInstitutionService.copyInstitution(request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", institution);
            result.put("message", "复制机构成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("复制机构失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "复制机构失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 1.15 导出机构数据
     */
    @PostMapping("/exportInstitutions")
    public ResponseEntity<Map<String, Object>> exportInstitutions(@RequestBody NursingHomeRequestVO request) {
        try {
            log.info("导出机构数据，条件: {}", request);
            
            List<NursingHomeInstitutionDTO> institutions = nursingHomeInstitutionService.exportInstitutions(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", institutions);
            result.put("message", "导出机构数据成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("导出机构数据失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "导出机构数据失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
} 
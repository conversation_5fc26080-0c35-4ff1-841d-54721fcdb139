package com.nx.inf.controller;

import com.nx.inf.model.entity.Questionnaire;
import com.nx.inf.model.dto.QuestionnaireDTO;
import com.nx.inf.service.QuestionnaireService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/emr/questionnaires")
@CrossOrigin(origins = "*", maxAge = 3600)
public class QuestionnaireController {

    @Autowired
    private QuestionnaireService questionnaireService;

    @PostMapping
    public ResponseEntity<?> createQuestionnaire(@RequestBody Questionnaire questionnaire) {
        try {
            Questionnaire result = questionnaireService.createQuestionnaire(questionnaire);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "创建问卷失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateQuestionnaire(@PathVariable Long id, @RequestBody Questionnaire questionnaire) {
        try {
            questionnaire.setId(id);
            Questionnaire result = questionnaireService.updateQuestionnaire(questionnaire);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "更新问卷失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteQuestionnaire(@PathVariable Long id) {
        try {
            questionnaireService.deleteQuestionnaire(id);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "删除成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "删除问卷失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getQuestionnaireById(@PathVariable Long id) {
        try {
            Questionnaire result = questionnaireService.getQuestionnaireById(id);
            if (result == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("error", true);
                error.put("message", "问卷不存在");
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "获取问卷失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @GetMapping
    public ResponseEntity<?> getAllQuestionnaires() {
        try {
            List<Questionnaire> result = questionnaireService.getAllQuestionnaires();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "获取问卷列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    // DTO接口
    @PostMapping("/with-details")
    public ResponseEntity<?> createQuestionnaireWithDetails(@RequestBody QuestionnaireDTO questionnaireDTO) {
        try {
            QuestionnaireDTO result = questionnaireService.createQuestionnaireWithDetails(questionnaireDTO);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "创建问卷失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @PutMapping("/with-details/{id}")
    public ResponseEntity<?> updateQuestionnaireWithDetails(@PathVariable Long id, @RequestBody QuestionnaireDTO questionnaireDTO) {
        try {
            questionnaireDTO.setId(id);
            QuestionnaireDTO result = questionnaireService.updateQuestionnaireWithDetails(questionnaireDTO);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "更新问卷失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @GetMapping("/with-details/{id}")
    public ResponseEntity<?> getQuestionnaireWithDetails(@PathVariable Long id) {
        try {
            QuestionnaireDTO result = questionnaireService.getQuestionnaireWithDetails(id);
            if (result == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("error", true);
                error.put("message", "问卷不存在");
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "获取问卷详情失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @GetMapping("/with-details")
    public ResponseEntity<?> getAllQuestionnairesWithDetails() {
        try {
            List<QuestionnaireDTO> result = questionnaireService.getAllQuestionnairesWithDetails();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", true);
            error.put("message", "获取问卷列表失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}

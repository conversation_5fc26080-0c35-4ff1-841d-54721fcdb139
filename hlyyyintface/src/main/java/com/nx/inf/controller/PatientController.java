package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.nx.inf.model.dto.PatientDTO;
import com.nx.inf.service.PatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 患者控制器
 */
@RestController
@RequestMapping("/emr/patient")
public class PatientController {
    
    @Autowired
    private PatientService patientService;
    
    /**
     * 创建患者
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createPatient(@RequestBody PatientDTO patientDTO) {
        Long id = patientService.createPatient(patientDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "创建患者成功");
        result.put("data", id);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 更新患者
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updatePatient(@PathVariable Long id, 
                                                          @RequestBody PatientDTO patientDTO) {
        patientDTO.setId(id);
        boolean success = patientService.updatePatient(patientDTO);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "更新患者成功");
        } else {
            result.put("code", 400);
            result.put("message", "更新患者失败，患者不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取患者详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getPatient(@PathVariable Long id) {
        PatientDTO patientDTO = patientService.getPatientById(id);
        
        Map<String, Object> result = new HashMap<>();
        if (patientDTO != null) {
            result.put("code", 200);
            result.put("message", "获取患者成功");
            result.put("data", patientDTO);
        } else {
            result.put("code", 400);
            result.put("message", "获取患者失败，患者不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 删除患者
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deletePatient(@PathVariable Long id) {
        boolean success = patientService.deletePatient(id);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "删除患者成功");
        } else {
            result.put("code", 400);
            result.put("message", "删除患者失败，患者不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 分页查询患者列表
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> getPatientPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String idCard,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String disease) {
        
        IPage<PatientDTO> patientPage = patientService.getPatientPage(
                page, size, status, name, idCard, phone, disease);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取患者列表成功");
        result.put("data", patientPage);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 更新患者状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updatePatientStatus(
            @PathVariable Long id,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long reviewerId,
            @RequestParam(required = false) String reviewerName,
            @RequestBody(required = false) Map<String, Object> requestBody) {
        
        // 优先使用请求体中的参数，如果没有则使用查询参数
        String finalStatus = status;
        Long finalReviewerId = reviewerId;
        String finalReviewerName = reviewerName;
        
        if (requestBody != null) {
            if (requestBody.containsKey("status")) {
                Object statusObj = requestBody.get("status");
                if (statusObj instanceof Integer) {
                    finalStatus = String.valueOf(statusObj);
                } else {
                    finalStatus = statusObj != null ? statusObj.toString() : null;
                }
            }
            if (requestBody.containsKey("reviewerId")) {
                finalReviewerId = Long.valueOf(requestBody.get("reviewerId").toString());
            }
            if (requestBody.containsKey("reviewerName")) {
                finalReviewerName = (String) requestBody.get("reviewerName");
            }
        }
        
        // status是必需的
        if (finalStatus == null || finalStatus.trim().isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 400);
            result.put("message", "status参数不能为空");
            return ResponseEntity.ok(result);
        }
        
        // 如果没有提供reviewerId和reviewerName，使用默认值
        if (finalReviewerId == null) {
            finalReviewerId = 0L; // 系统默认
        }
        if (finalReviewerName == null || finalReviewerName.trim().isEmpty()) {
            finalReviewerName = "系统"; // 系统默认
        }
        
        boolean success = patientService.updatePatientStatus(id, finalStatus, finalReviewerId, finalReviewerName);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "更新患者状态成功");
        } else {
            result.put("code", 400);
            result.put("message", "更新患者状态失败，患者不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 批量更新患者状态
     */
    @PutMapping("/status/batch")
    public ResponseEntity<Map<String, Object>> batchUpdatePatientStatus(
            @RequestParam String status,
            @RequestParam Long reviewerId,
            @RequestParam String reviewerName,
            @RequestBody List<Long> patientIds) {
        
        boolean success = patientService.batchUpdatePatientStatus(patientIds, status, reviewerId, reviewerName);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "批量更新患者状态成功");
        } else {
            result.put("code", 400);
            result.put("message", "批量更新患者状态失败，患者ID列表为空");
        }
        
        return ResponseEntity.ok(result);
    }
} 
package com.nx.inf.controller;

import com.nx.inf.model.entity.QuestionnaireOption;
import com.nx.inf.service.QuestionnaireOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/emr/questionnaire-options")
public class QuestionnaireOptionController {

    @Autowired
    private QuestionnaireOptionService optionService;

    @PostMapping
    public QuestionnaireOption createOption(@RequestBody QuestionnaireOption option) {
        return optionService.createOption(option);
    }

    @PutMapping("/{id}")
    public QuestionnaireOption updateOption(@PathVariable Long id, @RequestBody QuestionnaireOption option) {
        option.setId(id);
        return optionService.updateOption(option);
    }

    @DeleteMapping("/{id}")
    public void deleteOption(@PathVariable Long id) {
        optionService.deleteOption(id);
    }

    @GetMapping("/{id}")
    public QuestionnaireOption getOptionById(@PathVariable Long id) {
        return optionService.getOptionById(id);
    }

    @GetMapping("/by-question/{questionId}")
    public List<QuestionnaireOption> getOptionsByQuestionId(@PathVariable Long questionId) {
        return optionService.getOptionsByQuestionId(questionId);
    }

    @GetMapping
    public List<QuestionnaireOption> getAllOptions() {
        return optionService.getAllOptions();
    }
}

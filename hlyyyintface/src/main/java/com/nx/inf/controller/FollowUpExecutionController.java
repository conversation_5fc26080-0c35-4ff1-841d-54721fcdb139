package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;


import com.nx.inf.model.dto.FollowUpExecutionDTO;
import com.nx.inf.service.FollowUpExecutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 随访执行记录控制器
 */
@RestController
@RequestMapping("/emr/followup/execution")
public class FollowUpExecutionController {
    
    @Autowired
    private FollowUpExecutionService followUpExecutionService;
    
    /**
     * 创建随访执行记录
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createFollowUpExecution(@RequestBody FollowUpExecutionDTO followUpExecutionDTO) {
        Long id = followUpExecutionService.createFollowUpExecution(followUpExecutionDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "创建随访执行记录成功");
        result.put("data", id);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 更新随访执行记录
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateFollowUpExecution(@PathVariable Long id, 
                                                                    @RequestBody FollowUpExecutionDTO followUpExecutionDTO) {
        followUpExecutionDTO.setId(id);
        boolean success = followUpExecutionService.updateFollowUpExecution(followUpExecutionDTO);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "更新随访执行记录成功");
        } else {
            result.put("code", 400);
            result.put("message", "更新随访执行记录失败，记录不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取随访执行记录详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getFollowUpExecution(@PathVariable Long id) {
        FollowUpExecutionDTO followUpExecutionDTO = followUpExecutionService.getFollowUpExecutionById(id);
        
        Map<String, Object> result = new HashMap<>();
        if (followUpExecutionDTO != null) {
            result.put("code", 200);
            result.put("message", "获取随访执行记录成功");
            result.put("data", followUpExecutionDTO);
        } else {
            result.put("code", 400);
            result.put("message", "获取随访执行记录失败，记录不存在");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 分页查询随访执行记录
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> getExecutionPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long planId,
            @RequestParam(required = false) String patientName,
            @RequestParam(required = false) Boolean warningFlag) {
        
        IPage<FollowUpExecutionDTO> executionPage = followUpExecutionService.getExecutionPage(
                page, size, planId, patientName, warningFlag);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取随访执行记录列表成功");
        result.put("data", executionPage);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据计划ID查询执行记录
     */
    @GetMapping("/plan/{planId}")
    public ResponseEntity<Map<String, Object>> getExecutionsByPlanId(@PathVariable Long planId) {
        List<FollowUpExecutionDTO> executions = followUpExecutionService.getExecutionsByPlanId(planId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取计划的执行记录成功");
        result.put("data", executions);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据计划ID查询患者列表（去重）
     */
    @GetMapping("/plan/{planId}/patients")
    public ResponseEntity<Map<String, Object>> getPatientsByPlanId(@PathVariable Long planId) {
        List<FollowUpExecutionDTO> patients = followUpExecutionService.getPatientsByPlanId(planId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取计划的患者列表成功");
        result.put("data", patients);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据患者ID查询执行记录
     */
    @GetMapping("/patient/{patientId}")
    public ResponseEntity<Map<String, Object>> getExecutionsByPatientId(@PathVariable Long patientId) {
        List<FollowUpExecutionDTO> executions = followUpExecutionService.getExecutionsByPatientId(patientId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取患者的执行记录成功");
        result.put("data", executions);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 更新执行记录状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateExecutionStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        
        boolean success = followUpExecutionService.updateExecutionStatus(id, status);
        
        Map<String, Object> result = new HashMap<>();
        if (success) {
            result.put("code", 200);
            result.put("message", "更新执行记录状态成功");
        } else {
            result.put("code", 400);
            result.put("message", "更新执行记录状态失败，记录不存在");
        }
        
        return ResponseEntity.ok(result);
    }
} 
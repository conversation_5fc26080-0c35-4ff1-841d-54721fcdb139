package com.nx.inf.controller;

import com.nx.inf.annotation.TargetDataSource;
import com.nx.inf.enums.DataSourceType;
import com.nx.inf.model.vo.ApiResponseVO;
import com.nx.inf.model.vo.IdCardRequestVO;
import com.nx.inf.model.vo.MedicalRequestVO;
import com.nx.inf.model.vo.PatientCardRequestVO;
import com.nx.inf.model.vo.HealthMeasurementVO;
import com.nx.inf.model.vo.HealthQueryRequestVO;
import com.nx.inf.model.vo.HealthDeleteRequestVO;
import com.nx.inf.model.vo.MedicalImagingRequestVO;
import com.nx.inf.service.MedicalApiService;
import com.nx.inf.utils.encrypt.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 医疗信息传输接口
 * @Author: zhongx
 * @CreateDate: 2024/3/28
 * @Version: 1.0
 */
@RestController
@TargetDataSource(value = DataSourceType.MYSQL_DATASOURCE)
@Slf4j
public class EmrController {

    @Autowired
    private MedicalApiService medicalApiService;

    @Value("${third-api.feitu.hsCode}")
    private String hsCode;
    
    @Value("${third-api.feitu.accessKey}")
    private String accessKey;
    
    @Value("${third-api.feitu.accessSecret}")
    private String accessSecret;
    
    @Value("${third-api.feitu.aesKey}")
    private String aesKey;
    
    @Value("${third-api.feitu.aesIv}")
    private String aesIv;

    /**
     * 获取患者检查报告
     * @param request 请求参数
     * @return 检查报告数据
     */
    @PostMapping("/emr/examination-reports")
    public ResponseEntity<ApiResponseVO<?>> getExaminationReports(@RequestBody @Validated MedicalRequestVO request) {
        try {
            // 验证必要参数
            if (request.getPatientIds() == null || request.getStartTime() == null || request.getEndTime() == null) {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseVO.error(400, "缺少必要参数，需要提供patientIds、startTime和endTime"));
            }
            
            Map<String, Object> result = medicalApiService.getPatientExaminationReports(request);
            
            // 处理API返回结果
            if (result.get("retCode") != null && result.get("retCode").equals(0)) {
                return ResponseEntity.ok(ApiResponseVO.success(
                    "获取患者检查报告成功",
                    result.get("examReports")
                ));
            } else {
                // 查询不到数据时，仍然返回200状态码
                String message = result.get("retMsg") != null ? result.get("retMsg").toString() : "获取患者检查报告失败";
                
                // 返回空数组而非null，确保前端获得一致的数据结构
                return ResponseEntity.ok(ApiResponseVO.success(
                    message,
                    new ArrayList<>()
                ));
            }
        } catch (Exception e) {
            log.error("获取患者检查报告出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取患者检验报告
     * @param request 请求参数
     * @return 检验报告数据
     */
    @PostMapping("/emr/test-reports")
    public ResponseEntity<ApiResponseVO<?>> getTestReports(@RequestBody @Validated MedicalRequestVO request) {
        try {
            // 验证必要参数
            if (request.getPatientIds() == null || request.getStartTime() == null || request.getEndTime() == null) {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseVO.error(400, "缺少必要参数，需要提供patientIds、startTime和endTime"));
            }
            
            Map<String, Object> result = medicalApiService.getPatientTestReports(request);
            
            // 处理API返回结果
            if (result.get("retCode") != null && result.get("retCode").equals(0)) {
                return ResponseEntity.ok(ApiResponseVO.success(
                    "获取患者检验报告成功",
                    result.get("testReports")
                ));
            } else {
                // 查询不到数据时，仍然返回200状态码
                String message = result.get("retMsg") != null ? result.get("retMsg").toString() : "获取患者检验报告失败";
                
                // 返回空数组而非null，确保前端获得一致的数据结构
                return ResponseEntity.ok(ApiResponseVO.success(
                    message,
                    new ArrayList<>()
                ));
            }
        } catch (Exception e) {
            log.error("获取患者检验报告出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取患者门诊病历
     * @param request 请求参数
     * @return 门诊病历数据
     */
    @PostMapping("/emr/outpatient-records")
    public ResponseEntity<ApiResponseVO<?>> getOutpatientRecords(@RequestBody @Validated MedicalRequestVO request) {
        try {
            // 验证必要参数
            if (request.getPatientIds() == null || request.getStartTime() == null || request.getEndTime() == null) {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseVO.error(400, "缺少必要参数，需要提供patientIds、startTime和endTime"));
            }
            
            Map<String, Object> result = medicalApiService.getOutpatientRecords(request);
            
            // 处理API返回结果
            if (result.get("retCode") != null && result.get("retCode").equals(0)) {
                return ResponseEntity.ok(ApiResponseVO.success(
                    "获取患者门诊病历成功",
                    result.get("outpatientRecords")
                ));
            } else {
                // 查询不到数据时，仍然返回200状态码
                String message = result.get("retMsg") != null ? result.get("retMsg").toString() : "获取患者门诊病历失败";
                
                // 返回空数组而非null，确保前端获得一致的数据结构
                return ResponseEntity.ok(ApiResponseVO.success(
                    message,
                    new ArrayList<>()
                ));
            }
        } catch (Exception e) {
            log.error("获取患者门诊病历出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 获取患者住院记录
     * @param request 请求参数
     * @return 住院记录数据
     */
    @PostMapping("/emr/inpatient-records")
    public ResponseEntity<ApiResponseVO<?>> getInpatientRecords(@RequestBody @Validated MedicalRequestVO request) {
        try {
            // 验证必要参数
            if (request.getPatientIds() == null || request.getStartTime() == null || request.getEndTime() == null) {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseVO.error(400, "缺少必要参数，需要提供patientIds、startTime和endTime"));
            }
            
            Map<String, Object> result = medicalApiService.getInpatientRecords(request);
            
            // 处理API返回结果
            if (result.get("retCode") != null && result.get("retCode").equals(0)) {
                // 合并住院记录和病案首页为一个对象
                Map<String, Object> combinedRecords = new HashMap<>();
                combinedRecords.put("frontSheetRecords", result.get("frontSheetRecords") != null ? 
                                   result.get("frontSheetRecords") : new ArrayList<>());
                combinedRecords.put("inpatientRecords", result.get("inpatientRecords") != null ? 
                                   result.get("inpatientRecords") : new ArrayList<>());
                
                return ResponseEntity.ok(ApiResponseVO.success(
                    "获取患者住院记录成功",
                    combinedRecords
                ));
            } else {
                // 查询不到数据时，仍然返回200状态码
                String message = result.get("retMsg") != null ? result.get("retMsg").toString() : "获取患者住院记录失败";
                
                // 返回空数组而非null，确保前端获得一致的数据结构
                Map<String, Object> emptyRecords = new HashMap<>();
                emptyRecords.put("frontSheetRecords", new ArrayList<>());
                emptyRecords.put("inpatientRecords", new ArrayList<>());
                
                return ResponseEntity.ok(ApiResponseVO.success(
                    message,
                    emptyRecords
                ));
            }
        } catch (Exception e) {
            log.error("获取患者住院记录出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 根据身份证号获取患者电话号码
     * @param request 包含身份证号的请求对象
     * @return 患者电话号码
     */
    @PostMapping("/emr/phone-by-idcard")
    public ResponseEntity<ApiResponseVO<?>> getPhoneByIdCard(@RequestBody @Validated IdCardRequestVO request) {
        try {
            // 验证必要参数已通过@Validated处理
            String phoneNo = medicalApiService.getPhoneByIdCard(request.getIdCard());
            
            if (phoneNo != null) {
                return ResponseEntity.ok(ApiResponseVO.success(
                    "获取患者电话号码成功",
                    phoneNo
                ));
            } else {
                // 查询不到数据时，仍然返回200状态码
                return ResponseEntity.ok(ApiResponseVO.success(
                    "未找到该身份证对应的电话号码",
                    ""
                ));
            }
        } catch (Exception e) {
            log.error("根据身份证获取电话号码出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 根据身份证号获取患者卡信息
     * @param request 包含身份证号的请求对象
     * @return 患者卡信息
     */
    @PostMapping("/emr/patient-card")
    public ResponseEntity<ApiResponseVO<?>> getPatientCardByIdCard(@RequestBody @Validated PatientCardRequestVO request) {
        try {
            // 调用服务获取患者卡信息
            Map<String, Object> result = medicalApiService.getPatientCardByIdCard(request.getSfzh());
            
            // 处理API返回结果
            if (result.get("retCode") != null && result.get("retCode").equals(0)) {
                return ResponseEntity.ok(ApiResponseVO.success(
                    "获取患者卡信息成功",
                    result.get("patientCards")
                ));
            } else {
                // 查询不到数据时，仍然返回200状态码
                String message = result.get("retMsg") != null ? result.get("retMsg").toString() : "获取患者卡信息失败";
                
                // 返回空数组而非null，确保前端获得一致的数据结构
                return ResponseEntity.ok(ApiResponseVO.success(
                    message,
                    new ArrayList<>()
                ));
            }
        } catch (Exception e) {
            log.error("获取患者卡信息出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 新增患者健康测量记录
     * @param measurement 健康测量数据
     * @return 操作结果
     */
    @PostMapping("/emr/health-measurement")
    public ResponseEntity<ApiResponseVO<?>> addHealthMeasurement(@RequestBody @Validated HealthMeasurementVO measurement) {
        try {
            // 验证必要参数
            if (measurement.getSfzh() == null || measurement.getDate() == null) {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseVO.error(400, "缺少必要参数，需要提供身份证号和测量时间"));
            }
            
            boolean success = medicalApiService.addHealthMeasurement(measurement);
            
            if (success) {
                return ResponseEntity.ok(ApiResponseVO.success(
                    "添加健康测量记录成功",
                    null
                ));
            } else {
                // 即使添加失败，也返回200状态码
                return ResponseEntity.ok(ApiResponseVO.success(
                    "添加健康测量记录失败",
                    null
                ));
            }
        } catch (Exception e) {
            log.error("添加健康测量记录出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 查询患者健康测量记录
     * @param request 包含身份证号、开始时间和结束时间的请求对象
     * @return 患者健康测量记录列表
     */
    @PostMapping("/emr/health-records")
    public ResponseEntity<ApiResponseVO<?>> getHealthRecords(@RequestBody @Validated HealthQueryRequestVO request) {
        try {
            // 验证必要参数
            if (request.getSfzh() == null || request.getStartTime() == null || request.getEndTime() == null) {
                return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseVO.error(400, "缺少必要参数，需要提供身份证号、开始时间和结束时间"));
            }
            
            List<HealthMeasurementVO> records = medicalApiService.getHealthRecords(
                request.getSfzh(), 
                request.getStartTime(), 
                request.getEndTime()
            );
            
            // 如果记录为空，我们仍然返回200状态码
            String message = records.isEmpty() ? 
                "未找到患者健康测量记录" : "获取患者健康测量记录成功";
            
            return ResponseEntity.ok(ApiResponseVO.success(
                message,
                records
            ));
        } catch (Exception e) {
            log.error("获取患者健康测量记录出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 删除患者健康测量记录
     * @param request 包含ID和身份证号的请求对象
     * @return 操作结果
     */
    @PostMapping("/emr/delete-health-record")
    public ResponseEntity<ApiResponseVO<?>> deleteHealthRecord(@RequestBody @Validated HealthDeleteRequestVO request) {
        try {
            boolean success = medicalApiService.deleteHealthRecord(request.getId(), request.getSfzh());
            
            // 无论成功与否，都返回200状态码
            String message = success ? "删除健康测量记录成功" : "删除健康测量记录失败，未找到对应记录";
            
            return ResponseEntity.ok(ApiResponseVO.success(
                message,
                null
            ));
        } catch (Exception e) {
            log.error("删除健康测量记录出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }

    /**
     * 跳转到第三方检查报告系统
     * @param request 包含身份证号的请求对象
     * @return 重定向到第三方系统URL
     */
    @PostMapping("/emr/redirect-imaging")
    public RedirectView redirectToImagingSystem(@RequestBody @Validated MedicalImagingRequestVO request) {
        try {
            log.info("跳转第三方检查报告系统，参数：{}", request.getSfzh());
            
            // 获取跳转URL
            String redirectUrl = medicalApiService.generateImagingRedirectUrl(
                request.getSfzh(), 
                hsCode, 
                accessKey, 
                accessSecret,
                aesKey,
                aesIv
            );
            
            log.info("生成第三方系统跳转URL：{}", redirectUrl);
            return new RedirectView(redirectUrl);
        } catch (Exception e) {
            log.error("生成第三方系统跳转URL出错", e);
            // 跳转到错误页面
            return new RedirectView("/error");
        }
    }
    
    /**
     * 获取第三方检查报告系统URL
     * @param request 包含身份证号的请求对象
     * @return 第三方系统URL
     */
    @PostMapping("/emr/get-imaging-url")
    public ResponseEntity<ApiResponseVO<?>> getImagingSystemUrl(@RequestBody @Validated MedicalImagingRequestVO request) {
        try {
            log.info("获取第三方检查报告系统URL，参数：{}", request.getSfzh());
            
            // 获取URL
            String url = medicalApiService.generateImagingRedirectUrl(
                request.getSfzh(), 
                hsCode, 
                accessKey, 
                accessSecret,
                aesKey,
                aesIv
            );
            
            return ResponseEntity.ok(ApiResponseVO.success(
                "获取第三方检查报告系统URL成功",
                url
            ));
        } catch (Exception e) {
            log.error("获取第三方检查报告系统URL出错", e);
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponseVO.error(500, "服务器内部错误: " + e.getMessage()));
        }
    }
}

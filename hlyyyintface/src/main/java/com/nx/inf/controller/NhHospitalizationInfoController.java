package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.NhHospitalizationInfo;
import com.nx.inf.service.NhHospitalizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 住院信息控制器
 */
@RestController
@Slf4j
@RequestMapping("/emr/hospitalization")
public class NhHospitalizationInfoController {

    @Resource
    private NhHospitalizationInfoService nhHospitalizationInfoService;

    /**
     * 新增住院信息
     *
     * @param hospitalizationInfo 住院信息
     * @return 操作结果
     */
    @PostMapping("/save")
    public ResponseEntity<Map<String, Object>> save(@RequestBody NhHospitalizationInfo hospitalizationInfo) {
        log.info("新增住院信息入参{}", hospitalizationInfo);

        Map<String, Object> map = new HashMap<>();

        hospitalizationInfo.setInHospitalId(nhHospitalizationInfoService.genInHospitalId());

        if (nhHospitalizationInfoService.save(hospitalizationInfo)) {
            map.put("code", 200);
            map.put("message", "新增住院信息成功");
        } else {
            map.put("code", 500);
            map.put("message", "新增住院信息失败");
        }

        log.info("新增住院信息出参{}", map);

        return ResponseEntity.ok(map);
    }

    /**
     * 更新住院信息
     *
     * @param hospitalizationInfo 住院信息
     * @return 操作结果
     */
    @PostMapping("/update")
    public ResponseEntity<Boolean> update(@RequestBody NhHospitalizationInfo hospitalizationInfo) {
        return ResponseEntity.ok(nhHospitalizationInfoService.updateById(hospitalizationInfo));
    }

    /**
     * 删除住院信息
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Boolean> remove(@PathVariable Long id) {
        return ResponseEntity.ok(nhHospitalizationInfoService.removeById(id));
    }

    /**
     * 获取住院信息
     *
     * @param id 主键ID
     * @return 住院信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<NhHospitalizationInfo> getById(@PathVariable Long id) {
        return ResponseEntity.ok(nhHospitalizationInfoService.getById(id));
    }

    /**
     * 分页查询住院信息
     *
     * @param current 当前页
     * @param size    每页大小
     * @return 分页结果
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> page(
            @RequestParam(defaultValue = "1") long current,
            @RequestParam(defaultValue = "10") long size) {
        Page<NhHospitalizationInfo> page = new Page<>(current, size);

        Map<String, Object> map = new HashMap<>();

        IPage<NhHospitalizationInfo> iPage = nhHospitalizationInfoService.page(page);

        if (iPage.getRecords().isEmpty()) {
            map.put("code", 500);
            map.put("message", "查询成功，无数据");
        } else {
            map.put("code", 200);
            map.put("message", "查询成功");
            map.put("data", iPage);
        }

        return ResponseEntity.ok(map);
    }

    /**
     * 根据病人ID获取住院信息
     *
     * @param patientId 病人ID
     * @return 住院信息
     */
    @GetMapping("/patient/{patientId}")
    public ResponseEntity<NhHospitalizationInfo> getByPatientId(@PathVariable String patientId) {
        return ResponseEntity.ok(nhHospitalizationInfoService.getByPatientId(patientId));
    }

    /**
     * 获取各科室住院病人统计
     *
     * @return 科室统计信息
     */
    @GetMapping("/statistics/department")
    public ResponseEntity<List<Map<String, Object>>> getDepartmentStatistics() {
        return ResponseEntity.ok(nhHospitalizationInfoService.countByDepartment());
    }

    /**
     * 按日期范围查询入院记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 住院信息列表
     */
    @GetMapping("/admission-records")
    public ResponseEntity<List<NhHospitalizationInfo>> getAdmissionRecords(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        return ResponseEntity.ok(nhHospitalizationInfoService.getByAdmissionDateRange(startDate, endDate));
    }
}

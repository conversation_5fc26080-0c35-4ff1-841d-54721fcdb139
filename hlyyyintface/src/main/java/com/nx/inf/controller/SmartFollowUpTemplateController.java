package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nx.inf.model.dto.SmartFollowUpTemplateDTO;
import com.nx.inf.model.vo.SmartFollowUpTemplateRequestVO;
import com.nx.inf.service.SmartFollowUpTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能随访模板控制器
 */
@RestController
@RequestMapping("/emr/template/smart-followup")
@Slf4j
public class SmartFollowUpTemplateController {
    
    @Autowired
    private SmartFollowUpTemplateService smartFollowUpTemplateService;
    
    /**
     * 分页查询智能随访模板列表
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> getTemplatePage(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Integer status) {
        
        try {
            SmartFollowUpTemplateRequestVO request = new SmartFollowUpTemplateRequestVO();
            request.setPage(page);
            request.setSize(size);
            request.setName(name);
            request.setCategory(category);
            request.setStatus(status);
            
            IPage<SmartFollowUpTemplateDTO> pageResult = smartFollowUpTemplateService.getTemplatePage(request);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "获取智能随访模板列表成功");
            result.put("data", pageResult);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取智能随访模板列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "获取智能随访模板列表失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 创建智能随访模板
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createTemplate(@RequestBody SmartFollowUpTemplateDTO templateDTO) {
        try {
            Long id = smartFollowUpTemplateService.createTemplate(templateDTO);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "创建智能随访模板成功");
            result.put("data", id);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建智能随访模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "创建智能随访模板失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 更新智能随访模板
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateTemplate(@PathVariable Long id, 
                                                             @RequestBody SmartFollowUpTemplateDTO templateDTO) {
        try {
            templateDTO.setId(id);
            boolean success = smartFollowUpTemplateService.updateTemplate(templateDTO);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 200);
                result.put("message", "更新智能随访模板成功");
            } else {
                result.put("code", 400);
                result.put("message", "更新智能随访模板失败，模板不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新智能随访模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "更新智能随访模板失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取智能随访模板详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getTemplate(@PathVariable Long id) {
        try {
            SmartFollowUpTemplateDTO template = smartFollowUpTemplateService.getTemplateById(id);
            
            Map<String, Object> result = new HashMap<>();
            if (template != null) {
                result.put("code", 200);
                result.put("message", "获取智能随访模板成功");
                result.put("data", template);
            } else {
                result.put("code", 400);
                result.put("message", "获取智能随访模板失败，模板不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取智能随访模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "获取智能随访模板失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 删除智能随访模板
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteTemplate(@PathVariable Long id) {
        try {
            boolean success = smartFollowUpTemplateService.deleteTemplate(id);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 200);
                result.put("message", "删除智能随访模板成功");
            } else {
                result.put("code", 400);
                result.put("message", "删除智能随访模板失败，模板不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除智能随访模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "删除智能随访模板失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 更新智能随访模板状态
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateTemplateStatus(@PathVariable Long id, 
                                                                   @RequestBody Map<String, Object> request) {
        try {
            Integer status = (Integer) request.get("status");
            boolean success = smartFollowUpTemplateService.updateTemplateStatus(id, status);
            
            Map<String, Object> result = new HashMap<>();
            if (success) {
                result.put("code", 200);
                result.put("message", "更新智能随访模板状态成功");
            } else {
                result.put("code", 400);
                result.put("message", "更新智能随访模板状态失败，模板不存在");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新智能随访模板状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("message", "更新智能随访模板状态失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
} 
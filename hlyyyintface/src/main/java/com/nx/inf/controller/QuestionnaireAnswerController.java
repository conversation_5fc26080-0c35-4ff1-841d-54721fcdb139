package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.QuestionnaireAnswer;
import com.nx.inf.model.dto.QuestionnaireAnswerDTO;
import com.nx.inf.service.QuestionnaireAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 问卷答案控制器
 */
@RestController
@RequestMapping("/emr/questionnaire-answers")
@CrossOrigin(origins = "*", maxAge = 3600)
public class QuestionnaireAnswerController {

    @Autowired
    private QuestionnaireAnswerService answerService;

    /**
     * 保存问卷答案（C端患者填写问卷）
     */
    @PostMapping("/save")
    public ResponseEntity<?> saveAnswer(@RequestBody QuestionnaireAnswer answer) {
        try {
            QuestionnaireAnswer result = answerService.saveAnswer(answer);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "保存成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 批量保存问卷答案
     */
    @PostMapping("/batch-save")
    public ResponseEntity<?> batchSaveAnswers(@RequestBody List<QuestionnaireAnswer> answers) {
        try {
            boolean result = answerService.batchSaveAnswers(answers);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", result ? "批量保存成功" : "批量保存失败");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "批量保存失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 根据ID获取答案
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getAnswerById(@PathVariable Long id) {
        try {
            QuestionnaireAnswer answer = answerService.getAnswerById(id);
            if (answer == null) {
                Map<String, Object> error = new HashMap<>();
                error.put("code", 404);
                error.put("message", "答案不存在");
                return ResponseEntity.notFound().build();
            }
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", answer);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 分页查询问卷答案
     */
    @GetMapping("/page")
    public ResponseEntity<?> getAnswerPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long questionnaireId,
            @RequestParam(required = false) Long patientId,
            @RequestParam(required = false) Long taskId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        try {
            Page<QuestionnaireAnswerDTO> page = new Page<>(current, size);
            
            Date startDate = null;
            Date endDate = null;
            if (startTime != null && !startTime.isEmpty()) {
                startDate = new Date(Long.parseLong(startTime));
            }
            if (endTime != null && !endTime.isEmpty()) {
                endDate = new Date(Long.parseLong(endTime));
            }
            
            IPage<QuestionnaireAnswerDTO> result = answerService.getAnswerPage(
                page, questionnaireId, patientId, taskId, startDate, endDate);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 根据任务ID查询问卷答案
     */
    @GetMapping("/by-task/{taskId}")
    public ResponseEntity<?> getAnswersByTaskId(@PathVariable Long taskId) {
        try {
            List<QuestionnaireAnswerDTO> answers = answerService.getAnswersByTaskId(taskId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", answers);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 根据患者ID和问卷ID查询答案
     */
    @GetMapping("/by-patient-questionnaire")
    public ResponseEntity<?> getAnswersByPatientAndQuestionnaire(
            @RequestParam Long patientId,
            @RequestParam Long questionnaireId) {
        try {
            List<QuestionnaireAnswerDTO> answers = answerService.getAnswersByPatientAndQuestionnaire(patientId, questionnaireId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", answers);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 根据患者ID查询所有答案
     */
    @GetMapping("/by-patient/{patientId}")
    public ResponseEntity<?> getAnswersByPatientId(@PathVariable Long patientId) {
        try {
            List<QuestionnaireAnswerDTO> answers = answerService.getAnswersByPatientId(patientId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", answers);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 统计患者问卷完成情况
     */
    @GetMapping("/stats/{patientId}")
    public ResponseEntity<?> getPatientQuestionnaireStats(@PathVariable Long patientId) {
        try {
            List<QuestionnaireAnswerDTO> stats = answerService.getPatientQuestionnaireStats(patientId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 计算问卷得分
     */
    @GetMapping("/score")
    public ResponseEntity<?> calculateQuestionnaireScore(
            @RequestParam Long patientId,
            @RequestParam Long questionnaireId) {
        try {
            Double score = answerService.calculateQuestionnaireScore(patientId, questionnaireId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "计算成功");
            response.put("data", score);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "计算失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 检查问卷是否已完成
     */
    @GetMapping("/completed")
    public ResponseEntity<?> isQuestionnaireCompleted(
            @RequestParam Long patientId,
            @RequestParam Long questionnaireId,
            @RequestParam(required = false) Long taskId) {
        try {
            boolean completed = answerService.isQuestionnaireCompleted(patientId, questionnaireId, taskId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "检查成功");
            response.put("data", completed);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取患者问卷完成率
     */
    @GetMapping("/completion-rate/{patientId}")
    public ResponseEntity<?> getPatientCompletionRate(@PathVariable Long patientId) {
        try {
            Double rate = answerService.getPatientCompletionRate(patientId);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", rate);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 删除答案
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteAnswer(@PathVariable Long id) {
        try {
            boolean result = answerService.deleteAnswer(id);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", result ? "删除成功" : "删除失败");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", 400);
            error.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
} 
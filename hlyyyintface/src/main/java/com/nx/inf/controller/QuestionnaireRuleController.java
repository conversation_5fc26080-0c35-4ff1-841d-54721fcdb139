package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nx.inf.model.entity.QuestionnaireRule;
import com.nx.inf.service.QuestionnaireRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/emr/questionnaire-rules")
public class QuestionnaireRuleController {

    @Autowired
    private QuestionnaireRuleService ruleService;

    @PostMapping
    public boolean createRule(@RequestBody QuestionnaireRule rule) {
        return ruleService.save(rule);
    }

    @PutMapping("/{id}")
    public boolean updateRule(@PathVariable Long id, @RequestBody QuestionnaireRule rule) {
        rule.setId(id);
        return ruleService.updateById(rule);
    }

    @DeleteMapping("/{id}")
    public boolean deleteRule(@PathVariable Long id) {
        return ruleService.removeById(id);
    }

    @GetMapping("/{id}")
    public QuestionnaireRule getRuleById(@PathVariable Long id) {
        return ruleService.getById(id);
    }

    @GetMapping("/by-question/{questionId}")
    public List<QuestionnaireRule> getRulesByQuestionId(@PathVariable Long questionId) {
        return ruleService.getRulesByQuestionId(questionId);
    }

    @GetMapping
    public List<QuestionnaireRule> getAllRules() {
        return ruleService.list();
    }
}

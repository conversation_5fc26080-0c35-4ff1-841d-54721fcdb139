package com.nx.inf.controller;

import com.nx.inf.model.dto.FollowUpTemplateDTO;
import com.nx.inf.service.FollowUpTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 随访计划模板控制器
 */
@RestController
@RequestMapping("/emr/followup/template")
public class FollowUpTemplateController {
    
    @Autowired
    private FollowUpTemplateService followUpTemplateService;
    
    /**
     * 查询随访计划模板列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getTemplateList(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category) {
        
        List<FollowUpTemplateDTO> templateList = followUpTemplateService.getTemplateList(name, category);
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("message", "获取随访计划模板列表成功");
        result.put("data", templateList);
        
        return ResponseEntity.ok(result);
    }
} 
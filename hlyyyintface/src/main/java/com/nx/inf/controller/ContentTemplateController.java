package com.nx.inf.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nx.inf.model.dto.ContentTemplateCategoryDTO;
import com.nx.inf.model.dto.ContentTemplateDTO;
import com.nx.inf.model.vo.ContentTemplateRequestVO;
import com.nx.inf.model.vo.ContentTemplateResponseVO;
import com.nx.inf.service.ContentTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内容模板控制器
 */
@RestController
@RequestMapping("/emr/content-template")
@Slf4j
public class ContentTemplateController {
    
    @Autowired
    private ContentTemplateService contentTemplateService;
    
    /**
     * 0.1 获取模板列表
     */
    @PostMapping("/getTemplateList")
    public ResponseEntity<Map<String, Object>> getTemplateList(@RequestBody ContentTemplateRequestVO request) {
        try {
            IPage<ContentTemplateDTO> pageResult = contentTemplateService.getTemplateList(request);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", pageResult.getRecords());
            data.put("total", pageResult.getTotal());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "查询成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取模板列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.2 根据ID获取模板详情
     */
    @PostMapping("/getTemplateById")
    public ResponseEntity<Map<String, Object>> getTemplateById(@RequestBody ContentTemplateRequestVO request) {
        try {
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "模板ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            ContentTemplateDTO template = contentTemplateService.getTemplateById(request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", template);
            result.put("message", "查询成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取模板详情失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.3 创建内容模板
     */
    @PostMapping("/createTemplate")
    public ResponseEntity<Map<String, Object>> createTemplate(@RequestBody ContentTemplateRequestVO request) {
        try {
            Long templateId = contentTemplateService.createTemplate(request);
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", templateId);
            data.put("message", "模板创建成功");
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "创建成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "创建失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.4 更新内容模板
     */
    @PostMapping("/updateTemplate")
    public ResponseEntity<Map<String, Object>> updateTemplate(@RequestBody ContentTemplateRequestVO request) {
        try {
            boolean success = contentTemplateService.updateTemplate(request);
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("data", data);
            result.put("message", success ? "模板更新成功" : "模板更新失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.5 删除内容模板
     */
    @PostMapping("/deleteTemplate")
    public ResponseEntity<Map<String, Object>> deleteTemplate(@RequestBody ContentTemplateRequestVO request) {
        try {
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "模板ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            boolean success = contentTemplateService.deleteTemplate(request.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("data", null);
            result.put("message", success ? "模板删除成功" : "模板删除失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.6 复制内容模板
     */
    @PostMapping("/copyTemplate")
    public ResponseEntity<Map<String, Object>> copyTemplate(@RequestBody ContentTemplateRequestVO request) {
        try {
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "源模板ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            ContentTemplateDTO newTemplate = contentTemplateService.copyTemplate(request.getId());
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", newTemplate.getId());
            data.put("name", newTemplate.getName());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "模板复制成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("复制模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "复制失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.7 批量删除模板
     */
    @PostMapping("/batchDeleteTemplates")
    public ResponseEntity<Map<String, Object>> batchDeleteTemplates(@RequestBody ContentTemplateRequestVO request) {
        try {
            if (request.getIds() == null || request.getIds().isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "模板ID列表不能为空");
                return ResponseEntity.ok(result);
            }
            
            int deletedCount = contentTemplateService.batchDeleteTemplates(request.getIds());
            
            Map<String, Object> data = new HashMap<>();
            data.put("deletedCount", deletedCount);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "批量删除成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量删除模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "批量删除失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.8 获取模板分类列表
     */
    @PostMapping("/getCategoryList")
    public ResponseEntity<Map<String, Object>> getCategoryList(@RequestBody(required = false) Map<String, Object> request) {
        try {
            List<ContentTemplateCategoryDTO> categories = contentTemplateService.getCategoryList();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", categories);
            result.put("message", "查询成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.9 根据分类获取模板列表
     */
    @PostMapping("/getTemplatesByCategory")
    public ResponseEntity<Map<String, Object>> getTemplatesByCategory(@RequestBody ContentTemplateRequestVO request) {
        try {
            IPage<ContentTemplateDTO> pageResult = contentTemplateService.getTemplatesByCategory(request);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", pageResult.getRecords());
            data.put("total", pageResult.getTotal());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "查询成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("根据分类获取模板列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.10 搜索模板
     */
    @PostMapping("/searchTemplates")
    public ResponseEntity<Map<String, Object>> searchTemplates(@RequestBody ContentTemplateRequestVO request) {
        try {
            IPage<ContentTemplateDTO> pageResult = contentTemplateService.searchTemplates(request);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", pageResult.getRecords());
            data.put("total", pageResult.getTotal());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", data);
            result.put("message", "搜索成功");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("搜索模板失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "搜索失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 0.11 更新模板状态
     */
    @PostMapping("/updateTemplateStatus")
    public ResponseEntity<Map<String, Object>> updateTemplateStatus(@RequestBody ContentTemplateRequestVO request) {
        try {
            if (request.getId() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "模板ID不能为空");
                return ResponseEntity.ok(result);
            }
            
            if (request.getStatus() == null) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "状态不能为空");
                return ResponseEntity.ok(result);
            }
            
            boolean success = contentTemplateService.updateTemplateStatus(request.getId(), request.getStatus());
            
            Map<String, Object> data = new HashMap<>();
            data.put("id", request.getId());
            data.put("status", request.getStatus());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("data", data);
            result.put("message", success ? "状态更新成功" : "状态更新失败");
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新模板状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "状态更新失败：" + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
} 
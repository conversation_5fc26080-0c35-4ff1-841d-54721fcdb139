package com.nx.inf.annotation;

import com.nx.inf.enums.DataSourceType;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: 数据源管理器
 * @Author: JinShiBo
 * @CreateDate: 2023/7/14 0:00
 * @Version: 1.0
 */
@Primary
@Component
public class DataSourceManagement extends AbstractRoutingDataSource {

    public static ThreadLocal<String> flag = new ThreadLocal<>();
    @Resource
    private DataSource mysqlDataSource;
    @Resource
    private DataSource sqlserverDataSource;

    public DataSourceManagement() {
        flag.set(DataSourceType.MYSQL_DATASOURCE.name());
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return flag.get();
    }

    @Override
    public void afterPropertiesSet() {
        Map<Object, Object> targetDataSource = new ConcurrentHashMap<>();
        targetDataSource.put(DataSourceType.MYSQL_DATASOURCE.name(), mysqlDataSource);
        targetDataSource.put(DataSourceType.SQLSERVER_DATASOURCE.name(), sqlserverDataSource);
        super.setTargetDataSources(targetDataSource);
        super.setDefaultTargetDataSource(mysqlDataSource);
        super.afterPropertiesSet();
    }
}

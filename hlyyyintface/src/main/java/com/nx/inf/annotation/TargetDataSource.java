package com.nx.inf.annotation;

import com.nx.inf.enums.DataSourceType;

import java.lang.annotation.*;

/**
 * @Description: 数据源注解
 * @Author: Jin<PERSON>hiB<PERSON>
 * @CreateDate: 2023/7/14 0:05
 * @Version: 1.0
 */
@Target({ElementType.TYPE,ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TargetDataSource {
    DataSourceType value() default DataSourceType.MYSQL_DATASOURCE;
}

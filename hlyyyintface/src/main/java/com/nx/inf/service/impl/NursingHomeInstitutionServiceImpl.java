package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.NursingHomeInstitutionMapper;
import com.nx.inf.model.dto.NursingHomeInstitutionDTO;
import com.nx.inf.model.entity.NursingHomeInstitution;
import com.nx.inf.model.vo.NursingHomeRequestVO;
import com.nx.inf.model.vo.PageResponseVO;
import com.nx.inf.service.NursingHomeInstitutionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 养老院机构服务实现类
 */
@Service
@Slf4j
public class NursingHomeInstitutionServiceImpl extends ServiceImpl<NursingHomeInstitutionMapper, NursingHomeInstitution> 
        implements NursingHomeInstitutionService {
    
    // 模拟当前用户信息，实际项目中应该从SecurityContext或Token中获取
    private static final String CURRENT_USER = "系统管理员";
    
    private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATE_TIME_FORMATTER = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public PageResponseVO<NursingHomeInstitutionDTO> getInstitutionList(NursingHomeRequestVO request) {
        try {
            Page<NursingHomeInstitutionDTO> page = new Page<>(request.getPage(), request.getPageSize());
            
            IPage<NursingHomeInstitutionDTO> pageResult = baseMapper.selectInstitutionPage(
                page,
                request.getName(),
                request.getCode(),
                request.getStatus(),
                request.getLevel(),
                request.getProvince(),
                request.getCity(),
                request.getDistrict(),
                request.getKeyword(),
                request.getStartTime(),
                request.getEndTime()
            );
            
            return new PageResponseVO<>(
                pageResult.getTotal(),
                (int) pageResult.getCurrent(),
                (int) pageResult.getSize(),
                pageResult.getRecords()
            );
        } catch (Exception e) {
            log.error("获取机构列表失败", e);
            throw new RuntimeException("获取机构列表失败: " + e.getMessage());
        }
    }
    
    @Override
    public NursingHomeInstitutionDTO getInstitutionById(Long id) {
        try {
            if (id == null) {
                throw new IllegalArgumentException("机构ID不能为空");
            }
            
            NursingHomeInstitutionDTO institution = baseMapper.selectInstitutionById(id);
            if (institution == null) {
                throw new RuntimeException("机构不存在");
            }
            
            return institution;
        } catch (Exception e) {
            log.error("获取机构详情失败，ID: {}", id, e);
            throw new RuntimeException("获取机构详情失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInstitution(NursingHomeRequestVO request) {
        try {
            // 参数校验
            validateInstitutionRequest(request, false);
            
            // 检查机构代码是否重复
            if (isCodeExists(request.getCode(), null)) {
                throw new RuntimeException("机构代码已存在: " + request.getCode());
            }
            
            // 创建机构实体
            NursingHomeInstitution institution = new NursingHomeInstitution();
            BeanUtils.copyProperties(request, institution);
            
            // 设置默认值
            institution.setStatus(1); // 默认启用
            institution.setCurrentPatients(0); // 默认入住人数为0
            institution.setCreateBy(CURRENT_USER);
            institution.setUpdateBy(CURRENT_USER);
            institution.setCreateTime(new Date());
            institution.setUpdateTime(new Date());
            institution.setIsDeleted(0);
            
            // 如果没有设置等级，默认为STANDARD
            if (!StringUtils.hasText(institution.getLevel())) {
                institution.setLevel("STANDARD");
            }
            
            // 保存到数据库
            boolean success = save(institution);
            if (!success) {
                throw new RuntimeException("创建机构失败");
            }
            
            log.info("成功创建机构，ID: {}, 名称: {}", institution.getId(), institution.getName());
            return institution.getId();
            
        } catch (Exception e) {
            log.error("创建机构失败", e);
            throw new RuntimeException("创建机构失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInstitution(NursingHomeRequestVO request) {
        try {
            if (request.getId() == null) {
                throw new IllegalArgumentException("机构ID不能为空");
            }
            
            // 参数校验
            validateInstitutionRequest(request, true);
            
            // 检查机构是否存在
            NursingHomeInstitution existingInstitution = getById(request.getId());
            if (existingInstitution == null || existingInstitution.getIsDeleted() == 1) {
                throw new RuntimeException("机构不存在");
            }
            
            // 检查机构代码是否重复（排除自己）
            if (isCodeExists(request.getCode(), request.getId())) {
                throw new RuntimeException("机构代码已存在: " + request.getCode());
            }
            
            // 更新机构信息
            NursingHomeInstitution institution = new NursingHomeInstitution();
            BeanUtils.copyProperties(request, institution);
            institution.setUpdateBy(CURRENT_USER);
            institution.setUpdateTime(new Date());
            
            boolean success = updateById(institution);
            if (!success) {
                throw new RuntimeException("更新机构失败");
            }
            
            log.info("成功更新机构，ID: {}, 名称: {}", institution.getId(), institution.getName());
            return true;
            
        } catch (Exception e) {
            log.error("更新机构失败，ID: {}", request.getId(), e);
            throw new RuntimeException("更新机构失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInstitution(Long id) {
        try {
            if (id == null) {
                throw new IllegalArgumentException("机构ID不能为空");
            }
            
            // 检查机构是否存在
            NursingHomeInstitution institution = getById(id);
            if (institution == null || institution.getIsDeleted() == 1) {
                throw new RuntimeException("机构不存在");
            }
            
            // 逻辑删除
            UpdateWrapper<NursingHomeInstitution> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .set("is_deleted", 1)
                        .set("update_by", CURRENT_USER)
                        .set("update_time", new Date());
            
            boolean success = update(updateWrapper);
            if (!success) {
                throw new RuntimeException("删除机构失败");
            }
            
            log.info("成功删除机构，ID: {}, 名称: {}", id, institution.getName());
            return true;
            
        } catch (Exception e) {
            log.error("删除机构失败，ID: {}", id, e);
            throw new RuntimeException("删除机构失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteInstitutions(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                throw new IllegalArgumentException("机构ID列表不能为空");
            }
            
            int count = baseMapper.batchDelete(ids, CURRENT_USER);
            log.info("批量删除机构成功，数量: {}", count);
            return count;
            
        } catch (Exception e) {
            log.error("批量删除机构失败", e);
            throw new RuntimeException("批量删除机构失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInstitutionStatus(Long id, Integer status) {
        try {
            if (id == null) {
                throw new IllegalArgumentException("机构ID不能为空");
            }
            if (status == null || (status != 0 && status != 1)) {
                throw new IllegalArgumentException("状态值无效");
            }
            
            UpdateWrapper<NursingHomeInstitution> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id)
                        .eq("is_deleted", 0)
                        .set("status", status)
                        .set("update_by", CURRENT_USER)
                        .set("update_time", new Date());
            
            boolean success = update(updateWrapper);
            if (!success) {
                throw new RuntimeException("更新机构状态失败");
            }
            
            log.info("成功更新机构状态，ID: {}, 状态: {}", id, status);
            return true;
            
        } catch (Exception e) {
            log.error("更新机构状态失败，ID: {}, 状态: {}", id, status, e);
            throw new RuntimeException("更新机构状态失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<Long> ids, Integer status) {
        try {
            if (ids == null || ids.isEmpty()) {
                throw new IllegalArgumentException("机构ID列表不能为空");
            }
            if (status == null || (status != 0 && status != 1)) {
                throw new IllegalArgumentException("状态值无效");
            }
            
            int count = baseMapper.batchUpdateStatus(ids, status, CURRENT_USER);
            log.info("批量更新机构状态成功，数量: {}, 状态: {}", count, status);
            return count;
            
        } catch (Exception e) {
            log.error("批量更新机构状态失败", e);
            throw new RuntimeException("批量更新机构状态失败: " + e.getMessage());
        }
    }
    
    @Override
    public NursingHomeInstitution getByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        return baseMapper.selectByCode(code);
    }
    
    @Override
    public boolean isCodeExists(String code, Long excludeId) {
        if (!StringUtils.hasText(code)) {
            return false;
        }
        
        QueryWrapper<NursingHomeInstitution> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code).eq("is_deleted", 0);
        
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        
        return count(queryWrapper) > 0;
    }
    
    @Override
    public List<NursingHomeInstitution> getByNameLike(String name) {
        if (!StringUtils.hasText(name)) {
            return new java.util.ArrayList<>();
        }
        return baseMapper.selectByNameLike(name);
    }
    
    @Override
    public List<NursingHomeInstitutionDTO> getAllActiveInstitutions() {
        return baseMapper.selectAllActive();
    }
    
    @Override
    public Long countInstitutions(Integer status, String level, String province, String city) {
        return baseMapper.countInstitutions(status, level, province, city);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCurrentPatients(Long id, Integer currentPatients) {
        try {
            if (id == null || currentPatients == null || currentPatients < 0) {
                throw new IllegalArgumentException("参数无效");
            }
            
            int count = baseMapper.updateCurrentPatients(id, currentPatients);
            return count > 0;
            
        } catch (Exception e) {
            log.error("更新机构入住人数失败，ID: {}, 人数: {}", id, currentPatients, e);
            throw new RuntimeException("更新机构入住人数失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<NursingHomeInstitutionDTO> getNearbyInstitutions(String longitude, String latitude, 
                                                                Double radius, Integer limit) {
        try {
            if (!StringUtils.hasText(longitude) || !StringUtils.hasText(latitude)) {
                throw new IllegalArgumentException("经纬度不能为空");
            }
            if (radius == null || radius <= 0) {
                radius = 10.0; // 默认10公里
            }
            if (limit == null || limit <= 0) {
                limit = 20; // 默认返回20个
            }
            
            return baseMapper.selectNearbyInstitutions(longitude, latitude, radius, limit);
            
        } catch (Exception e) {
            log.error("查询附近机构失败", e);
            throw new RuntimeException("查询附近机构失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public NursingHomeInstitutionDTO copyInstitution(Long id) {
        try {
            if (id == null) {
                throw new IllegalArgumentException("机构ID不能为空");
            }
            
            NursingHomeInstitution source = getById(id);
            if (source == null || source.getIsDeleted() == 1) {
                throw new RuntimeException("源机构不存在");
            }
            
            // 创建副本
            NursingHomeInstitution copy = new NursingHomeInstitution();
            BeanUtils.copyProperties(source, copy);
            copy.setId(null);
            copy.setName(source.getName() + "_副本");
            copy.setCode(generateUniqueCode(source.getCode()));
            copy.setStatus(0); // 默认禁用
            copy.setCurrentPatients(0);
            copy.setCreateBy(CURRENT_USER);
            copy.setUpdateBy(CURRENT_USER);
            copy.setCreateTime(new Date());
            copy.setUpdateTime(new Date());
            copy.setIsDeleted(0);
            
            boolean success = save(copy);
            if (!success) {
                throw new RuntimeException("复制机构失败");
            }
            
            return getInstitutionById(copy.getId());
            
        } catch (Exception e) {
            log.error("复制机构失败，ID: {}", id, e);
            throw new RuntimeException("复制机构失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importInstitutions(List<NursingHomeRequestVO> institutionList) {
        if (institutionList == null || institutionList.isEmpty()) {
            return "导入数据为空";
        }
        
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMsg = new StringBuilder();
        
        for (int i = 0; i < institutionList.size(); i++) {
            try {
                NursingHomeRequestVO request = institutionList.get(i);
                createInstitution(request);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMsg.append("第").append(i + 1).append("行：").append(e.getMessage()).append("；");
            }
        }
        
        String result = String.format("导入完成，成功：%d，失败：%d", successCount, failCount);
        if (errorMsg.length() > 0) {
            result += "。错误详情：" + errorMsg.toString();
        }
        
        return result;
    }
    
    @Override
    public List<NursingHomeInstitutionDTO> exportInstitutions(NursingHomeRequestVO request) {
        try {
            // 设置大的页面大小来获取所有数据
            request.setPage(1);
            request.setPageSize(10000);
            
            PageResponseVO<NursingHomeInstitutionDTO> pageResult = getInstitutionList(request);
            return pageResult.getList();
            
        } catch (Exception e) {
            log.error("导出机构数据失败", e);
            throw new RuntimeException("导出机构数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验机构请求参数
     */
    private void validateInstitutionRequest(NursingHomeRequestVO request, boolean isUpdate) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        
        if (!StringUtils.hasText(request.getName())) {
            throw new IllegalArgumentException("机构名称不能为空");
        }
        
        if (request.getName().length() > 100) {
            throw new IllegalArgumentException("机构名称长度不能超过100个字符");
        }
        
        if (!StringUtils.hasText(request.getCode())) {
            throw new IllegalArgumentException("机构代码不能为空");
        }
        
        if (request.getCode().length() > 50) {
            throw new IllegalArgumentException("机构代码长度不能超过50个字符");
        }
        
        if (request.getCapacity() != null && request.getCapacity() < 0) {
            throw new IllegalArgumentException("床位数量不能为负数");
        }
        
        if (request.getCurrentPatients() != null && request.getCurrentPatients() < 0) {
            throw new IllegalArgumentException("当前入住人数不能为负数");
        }
        
        if (request.getCapacity() != null && request.getCurrentPatients() != null 
            && request.getCurrentPatients() > request.getCapacity()) {
            throw new IllegalArgumentException("当前入住人数不能超过床位数量");
        }
    }
    
    /**
     * 生成唯一的机构代码
     */
    private String generateUniqueCode(String baseCode) {
        String newCode = baseCode + "_COPY";
        int suffix = 1;
        
        while (isCodeExists(newCode, null)) {
            newCode = baseCode + "_COPY_" + suffix;
            suffix++;
        }
        
        return newCode;
    }
} 
package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.nx.inf.model.dto.PatientDTO;
import com.nx.inf.model.entity.Patient;

import java.util.List;

/**
 * 患者服务接口
 */
public interface PatientService extends IService<Patient> {
    
    /**
     * 创建患者
     * 
     * @param patientDTO 患者DTO
     * @return 患者ID
     */
    Long createPatient(PatientDTO patientDTO);
    
    /**
     * 更新患者信息
     * 
     * @param patientDTO 患者DTO
     * @return 是否成功
     */
    boolean updatePatient(PatientDTO patientDTO);
    
    /**
     * 删除患者
     * 
     * @param id 患者ID
     * @return 是否成功
     */
    boolean deletePatient(Long id);
    
    /**
     * 根据ID获取患者
     * 
     * @param id 患者ID
     * @return 患者DTO
     */
    PatientDTO getPatientById(Long id);
    
    /**
     * 分页查询患者
     * 
     * @param page 页码
     * @param size 每页数量
     * @param status 状态
     * @param name 姓名
     * @param idCard 证件号码
     * @param phone 手机号
     * @param disease 疾病
     * @return 分页结果
     */
    IPage<PatientDTO> getPatientPage(int page, int size, String status, String name, 
                                   String idCard, String phone, String disease);
    
    /**
     * 更新患者状态
     * 
     * @param id 患者ID
     * @param status 状态
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 是否成功
     */
    boolean updatePatientStatus(Long id, String status, Long reviewerId, String reviewerName);
    
    /**
     * 批量更新患者状态
     * 
     * @param ids 患者ID列表
     * @param status 状态
     * @param reviewerId 审核人ID
     * @param reviewerName 审核人姓名
     * @return 是否成功
     */
    boolean batchUpdatePatientStatus(List<Long> ids, String status, Long reviewerId, String reviewerName);
} 
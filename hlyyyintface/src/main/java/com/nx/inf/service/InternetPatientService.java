package com.nx.inf.service;

import com.nx.inf.model.entity.InternetPatient;
import com.nx.inf.model.vo.PageResponseVO;
import com.nx.inf.model.vo.PatientListRequestVO;

/**
 * @Description: 患者服务接口
 * @Author: zhongx
 * @CreateDate: 2024/5/30
 * @Version: 1.0
 */
public interface InternetPatientService {
    
    /**
     * 分页查询患者列表
     * @param requestVO 查询参数
     * @return 分页患者列表
     */
    PageResponseVO<InternetPatient> getPatientList(PatientListRequestVO requestVO);
} 
package com.nx.inf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.entity.FollowupTask;

import java.util.Map;

/**
 * 随访任务服务接口
 */
public interface FollowupTaskService extends IService<FollowupTask> {
    
    /**
     * 为指定计划生成任务
     * @param planId 计划ID
     * @return 是否成功
     */
    boolean generateTasks(Long planId);
    
    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateTaskStatus(Long taskId, String status);
    
    /**
     * 获取计划执行统计信息
     * @param planId 计划ID
     * @return 统计信息
     */
    Map<String, Object> getPlanExecutionStats(Long planId);
    
} 
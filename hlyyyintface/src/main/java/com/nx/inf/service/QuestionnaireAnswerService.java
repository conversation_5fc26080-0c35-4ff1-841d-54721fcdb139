package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.QuestionnaireAnswer;
import com.nx.inf.model.dto.QuestionnaireAnswerDTO;

import java.util.List;
import java.util.Date;

/**
 * 问卷答案服务接口
 */
public interface QuestionnaireAnswerService {
    
    /**
     * 保存问卷答案
     */
    QuestionnaireAnswer saveAnswer(QuestionnaireAnswer answer);
    
    /**
     * 批量保存问卷答案
     */
    boolean batchSaveAnswers(List<QuestionnaireAnswer> answers);
    
    /**
     * 根据ID获取答案
     */
    QuestionnaireAnswer getAnswerById(Long id);
    
    /**
     * 分页查询问卷答案
     */
    IPage<QuestionnaireAnswerDTO> getAnswerPage(Page<QuestionnaireAnswerDTO> page, 
                                               Long questionnaireId,
                                               Long patientId,
                                               Long taskId,
                                               Date startTime,
                                               Date endTime);
    
    /**
     * 根据任务ID查询问卷答案
     */
    List<QuestionnaireAnswerDTO> getAnswersByTaskId(Long taskId);
    
    /**
     * 根据患者ID和问卷ID查询答案
     */
    List<QuestionnaireAnswerDTO> getAnswersByPatientAndQuestionnaire(Long patientId, Long questionnaireId);
    
    /**
     * 根据患者ID查询所有答案
     */
    List<QuestionnaireAnswerDTO> getAnswersByPatientId(Long patientId);
    
    /**
     * 统计患者问卷完成情况
     */
    List<QuestionnaireAnswerDTO> getPatientQuestionnaireStats(Long patientId);
    
    /**
     * 删除答案
     */
    boolean deleteAnswer(Long id);
    
    /**
     * 根据条件删除答案
     */
    boolean deleteAnswersByCondition(Long questionnaireId, Long patientId, Long taskId);
    
    /**
     * 计算问卷得分
     */
    Double calculateQuestionnaireScore(Long patientId, Long questionnaireId);
    
    /**
     * 检查问卷是否已完成
     */
    boolean isQuestionnaireCompleted(Long patientId, Long questionnaireId, Long taskId);
    
    /**
     * 获取患者问卷完成率
     */
    Double getPatientCompletionRate(Long patientId);
} 
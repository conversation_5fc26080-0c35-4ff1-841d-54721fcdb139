package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.entity.FollowupCUser;

import java.util.Date;

public interface FollowupCUserService extends IService<FollowupCUser> {
    FollowupCUser register(FollowupCUser user);
    boolean updateBindStatus(String phone, Integer isBind);
    boolean updateAuthStatus(String phone, Integer isAuth);
    
    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param lastLoginTime 最后登录时间
     * @param lastLoginIp 最后登录IP
     * @return 是否成功
     */
    boolean updateLoginInfo(Long userId, String lastLoginTime, String lastLoginIp);
    
    /**
     * 更新用户基本信息
     * 
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(FollowupCUser user);
    
    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    FollowupCUser getUserByPhone(String phone);
    
    /**
     * 分页查询用户列表
     * 
     * @param page 页码
     * @param size 每页数量
     * @param name 姓名
     * @param phone 手机号
     * @param bindStatus 绑定状态
     * @param authStatus 授权状态
     * @return 分页结果
     */
    IPage<FollowupCUser> getUserPage(int page, int size, String name, String phone, 
                                   String bindStatus, String authStatus);
} 
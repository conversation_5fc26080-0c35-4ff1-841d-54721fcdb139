package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.dto.ContentTemplateCategoryDTO;
import com.nx.inf.model.dto.ContentTemplateDTO;
import com.nx.inf.model.entity.ContentTemplate;
import com.nx.inf.model.vo.ContentTemplateRequestVO;

import java.util.List;

/**
 * 内容模板服务接口
 */
public interface ContentTemplateService extends IService<ContentTemplate> {
    
    /**
     * 获取模板列表
     * 
     * @param request 请求参数
     * @return 分页结果
     */
    IPage<ContentTemplateDTO> getTemplateList(ContentTemplateRequestVO request);
    
    /**
     * 根据ID获取模板详情
     * 
     * @param id 模板ID
     * @return 模板详情
     */
    ContentTemplateDTO getTemplateById(Long id);
    
    /**
     * 创建内容模板
     * 
     * @param request 请求参数
     * @return 创建的模板ID
     */
    Long createTemplate(ContentTemplateRequestVO request);
    
    /**
     * 更新内容模板
     * 
     * @param request 请求参数
     * @return 是否成功
     */
    boolean updateTemplate(ContentTemplateRequestVO request);
    
    /**
     * 删除内容模板
     * 
     * @param id 模板ID
     * @return 是否成功
     */
    boolean deleteTemplate(Long id);
    
    /**
     * 复制内容模板
     * 
     * @param id 源模板ID
     * @return 新模板ID和名称
     */
    ContentTemplateDTO copyTemplate(Long id);
    
    /**
     * 批量删除模板
     * 
     * @param ids 模板ID列表
     * @return 删除的数量
     */
    int batchDeleteTemplates(List<Long> ids);
    
    /**
     * 获取模板分类列表
     * 
     * @return 分类列表
     */
    List<ContentTemplateCategoryDTO> getCategoryList();
    
    /**
     * 根据分类获取模板列表
     * 
     * @param request 请求参数
     * @return 分页结果
     */
    IPage<ContentTemplateDTO> getTemplatesByCategory(ContentTemplateRequestVO request);
    
    /**
     * 搜索模板
     * 
     * @param request 请求参数
     * @return 分页结果
     */
    IPage<ContentTemplateDTO> searchTemplates(ContentTemplateRequestVO request);
    
    /**
     * 更新模板状态
     * 
     * @param id 模板ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateTemplateStatus(Long id, String status);
} 
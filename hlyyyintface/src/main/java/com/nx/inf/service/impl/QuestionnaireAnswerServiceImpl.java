package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.mapper.QuestionnaireAnswerMapper;
import com.nx.inf.model.entity.QuestionnaireAnswer;
import com.nx.inf.model.entity.QuestionnaireQuestion;
import com.nx.inf.model.dto.QuestionnaireAnswerDTO;
import com.nx.inf.service.QuestionnaireAnswerService;
import com.nx.inf.service.QuestionnaireQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 问卷答案服务实现类
 */
@Service
public class QuestionnaireAnswerServiceImpl implements QuestionnaireAnswerService {

    @Autowired
    private QuestionnaireAnswerMapper answerMapper;
    
    @Autowired
    private QuestionnaireQuestionService questionService;

    @Override
    @Transactional
    public QuestionnaireAnswer saveAnswer(QuestionnaireAnswer answer) {
        answer.setCreateTime(new Date());
        answer.setUpdateTime(new Date());
        answer.setAnswerTime(new Date());
        answer.setAnswerSource("manual");
        
        // 计算得分
        if (answer.getAnswerScore() == null) {
            answer.setAnswerScore(BigDecimal.ZERO);
        }
        
        answerMapper.insert(answer);
        return answer;
    }

    @Override
    @Transactional
    public boolean batchSaveAnswers(List<QuestionnaireAnswer> answers) {
        if (answers == null || answers.isEmpty()) {
            return false;
        }
        
        Date now = new Date();
        for (QuestionnaireAnswer answer : answers) {
            answer.setCreateTime(now);
            answer.setUpdateTime(now);
            answer.setAnswerTime(now);
            answer.setAnswerSource("manual");
            
            if (answer.getAnswerScore() == null) {
                answer.setAnswerScore(BigDecimal.ZERO);
            }
        }
        
        return answerMapper.batchInsert(answers) > 0;
    }

    @Override
    public QuestionnaireAnswer getAnswerById(Long id) {
        return answerMapper.selectById(id);
    }

    @Override
    public IPage<QuestionnaireAnswerDTO> getAnswerPage(Page<QuestionnaireAnswerDTO> page, 
                                                      Long questionnaireId,
                                                      Long patientId,
                                                      Long taskId,
                                                      Date startTime,
                                                      Date endTime) {
        return answerMapper.selectAnswerPage(page, questionnaireId, patientId, taskId, startTime, endTime);
    }

    @Override
    public List<QuestionnaireAnswerDTO> getAnswersByTaskId(Long taskId) {
        return answerMapper.selectAnswersByTaskId(taskId);
    }

    @Override
    public List<QuestionnaireAnswerDTO> getAnswersByPatientAndQuestionnaire(Long patientId, Long questionnaireId) {
        return answerMapper.selectAnswersByPatientAndQuestionnaire(patientId, questionnaireId);
    }

    @Override
    public List<QuestionnaireAnswerDTO> getAnswersByPatientId(Long patientId) {
        return answerMapper.selectAnswersByPatientId(patientId);
    }

    @Override
    public List<QuestionnaireAnswerDTO> getPatientQuestionnaireStats(Long patientId) {
        return answerMapper.selectPatientQuestionnaireStats(patientId);
    }

    @Override
    @Transactional
    public boolean deleteAnswer(Long id) {
        return answerMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional
    public boolean deleteAnswersByCondition(Long questionnaireId, Long patientId, Long taskId) {
        return answerMapper.deleteByCondition(questionnaireId, patientId, taskId) > 0;
    }

    @Override
    public Double calculateQuestionnaireScore(Long patientId, Long questionnaireId) {
        List<QuestionnaireAnswerDTO> answers = getAnswersByPatientAndQuestionnaire(patientId, questionnaireId);
        
        if (answers.isEmpty()) {
            return 0.0;
        }
        
        double totalScore = 0.0;
        int validAnswers = 0;
        
        for (QuestionnaireAnswerDTO answer : answers) {
            if (answer.getAnswerScore() != null) {
                totalScore += answer.getAnswerScore().doubleValue();
                validAnswers++;
            }
        }
        
        return validAnswers > 0 ? totalScore / validAnswers : 0.0;
    }

    @Override
    public boolean isQuestionnaireCompleted(Long patientId, Long questionnaireId, Long taskId) {
        // 获取问卷的所有问题
        List<QuestionnaireQuestion> questions = questionService.getQuestionsByQuestionnaireId(questionnaireId);
        
        if (questions.isEmpty()) {
            return false;
        }
        
        // 获取已回答的问题
        QueryWrapper<QuestionnaireAnswer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("patient_id", patientId)
                   .eq("questionnaire_id", questionnaireId);
        
        if (taskId != null) {
            queryWrapper.eq("task_id", taskId);
        }
        
        List<QuestionnaireAnswer> answers = answerMapper.selectList(queryWrapper);
        
        // 检查必答题是否都已回答
        for (QuestionnaireQuestion question : questions) {
            if (Boolean.TRUE.equals(question.getRequired())) {
                boolean answered = answers.stream()
                    .anyMatch(answer -> answer.getQuestionId().equals(question.getId()));
                if (!answered) {
                    return false;
                }
            }
        }
        
        return true;
    }

    @Override
    public Double getPatientCompletionRate(Long patientId) {
        // 获取患者的所有答案
        List<QuestionnaireAnswerDTO> answers = getAnswersByPatientId(patientId);
        
        if (answers.isEmpty()) {
            return 0.0;
        }
        
        // 统计不同问卷的完成情况
        long completedQuestionnaires = answers.stream()
            .map(QuestionnaireAnswerDTO::getQuestionnaireId)
            .distinct()
            .count();
        
        // 这里可以根据实际需求调整计算逻辑
        // 暂时返回一个基于答案数量的完成率
        return Math.min(100.0, (double) answers.size() / completedQuestionnaires * 100);
    }
} 
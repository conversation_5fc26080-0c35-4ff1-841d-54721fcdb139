package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nx.inf.mapper.QuestionnaireOptionMapper;
import com.nx.inf.model.entity.QuestionnaireOption;
import com.nx.inf.service.QuestionnaireOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuestionnaireOptionServiceImpl implements QuestionnaireOptionService {

    @Autowired
    private QuestionnaireOptionMapper optionMapper;

    @Override
    public QuestionnaireOption createOption(QuestionnaireOption option) {
        optionMapper.insert(option);
        return option;
    }

    @Override
    public QuestionnaireOption updateOption(QuestionnaireOption option) {
        optionMapper.updateById(option);
        return optionMapper.selectById(option.getId());
    }

    @Override
    public void deleteOption(Long id) {
        optionMapper.deleteById(id);
    }

    @Override
    public void deleteOptionsByQuestionId(Long questionId) {
        QueryWrapper<QuestionnaireOption> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("question_id", questionId);
        optionMapper.delete(queryWrapper);
    }

    @Override
    public QuestionnaireOption getOptionById(Long id) {
        return optionMapper.selectById(id);
    }

    @Override
    public List<QuestionnaireOption> getOptionsByQuestionId(Long questionId) {
        return optionMapper.selectByQuestionId(questionId);
    }

    @Override
    public List<QuestionnaireOption> getAllOptions() {
        return optionMapper.selectList(null);
    }
}

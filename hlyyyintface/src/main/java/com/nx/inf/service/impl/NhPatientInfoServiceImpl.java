package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.NhPatientInfoMapper;
import com.nx.inf.model.entity.NhPatientInfo;
import com.nx.inf.service.NhPatientInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 病人信息服务实现类
 */
@Service
public class NhPatientInfoServiceImpl extends ServiceImpl<NhPatientInfoMapper, NhPatientInfo> implements NhPatientInfoService {

    @Resource
    private NhPatientInfoMapper nhPatientInfoMapper;

    @Override
    public List<NhPatientInfo> getNhPatientInfoList(NhPatientInfo nhPatientInfo) {
        return nhPatientInfoMapper.getNhPatientInfoList(nhPatientInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNhPatientInfo(NhPatientInfo nhPatientInfo) {
        return nhPatientInfoMapper.updateNhPatientInfo(nhPatientInfo) > 0;
    }

    @Override
    public NhPatientInfo getPatientInfoById(String nhPatientInfo) {
        return nhPatientInfoMapper.selectById(nhPatientInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPatientInfo(NhPatientInfo nhPatientInfo) {
        // 设置患者ID（如果未提供）
        if (nhPatientInfo.getPatientId() == null || nhPatientInfo.getPatientId().trim().isEmpty()) {
            nhPatientInfo.setPatientId("NH" + UUID.randomUUID().toString().substring(0, 8).toUpperCase());
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        nhPatientInfo.setCreateTime(new java.util.Date());
        nhPatientInfo.setUpdateTime(new java.util.Date());
        
        return nhPatientInfoMapper.insert(nhPatientInfo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePatientInfoById(String patientId) {
        return nhPatientInfoMapper.deleteById(patientId) > 0;
    }
}

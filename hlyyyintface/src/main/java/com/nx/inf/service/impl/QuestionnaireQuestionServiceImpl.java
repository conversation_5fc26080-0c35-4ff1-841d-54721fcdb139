package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nx.inf.mapper.QuestionnaireQuestionMapper;
import com.nx.inf.model.entity.QuestionnaireQuestion;
import com.nx.inf.service.QuestionnaireQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuestionnaireQuestionServiceImpl implements QuestionnaireQuestionService {

    @Autowired
    private QuestionnaireQuestionMapper questionMapper;

    @Override
    public QuestionnaireQuestion createQuestion(QuestionnaireQuestion question) {
        questionMapper.insert(question);
        return question;
    }

    @Override
    public QuestionnaireQuestion updateQuestion(QuestionnaireQuestion question) {
        questionMapper.updateById(question);
        return questionMapper.selectById(question.getId());
    }

    @Override
    public void deleteQuestion(Long id) {
        questionMapper.deleteById(id);
    }

    @Override
    public void deleteQuestionsByQuestionnaireId(Long questionnaireId) {
        QueryWrapper<QuestionnaireQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("questionnaire_id", questionnaireId);
        questionMapper.delete(queryWrapper);
    }

    @Override
    public QuestionnaireQuestion getQuestionById(Long id) {
        return questionMapper.selectById(id);
    }

    @Override
    public List<QuestionnaireQuestion> getQuestionsByQuestionnaireId(Long questionnaireId) {
        return questionMapper.selectByQuestionnaireId(questionnaireId);
    }

    @Override
    public List<QuestionnaireQuestion> getAllQuestions() {
        return questionMapper.selectList(null);
    }
}

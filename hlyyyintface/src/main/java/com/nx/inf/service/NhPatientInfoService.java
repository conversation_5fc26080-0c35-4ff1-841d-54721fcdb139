package com.nx.inf.service;

import com.nx.inf.model.entity.NhPatientInfo;

import java.util.List;

/**
 * 病人信息服务接口
 */
public interface NhPatientInfoService {

    /**
     * 获取病人信息列表
     *
     * @return 病人信息列表
     */
    List<NhPatientInfo> getNhPatientInfoList(NhPatientInfo nhPatientInfo);

    /**
     * 更新病人信息
     *
     * @param nhPatientInfo 病人信息
     * @return 是否更新成功
     */
    boolean updateNhPatientInfo(NhPatientInfo nhPatientInfo);

    /**
     * 根据ID获取病人信息
     *
     * @param patientId 病人ID
     * @return 病人信息
     */
    NhPatientInfo getPatientInfoById(String patientId);

    /**
     * 新增病人信息
     *
     * @param nhPatientInfo 病人信息
     * @return 是否新增成功
     */
    boolean addPatientInfo(NhPatientInfo nhPatientInfo);

    /**
     * 根据ID删除病人信息
     *
     * @param patientId 病人ID
     * @return 是否删除成功
     */
    boolean deletePatientInfoById(String patientId);
}

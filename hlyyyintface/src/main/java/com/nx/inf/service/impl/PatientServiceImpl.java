package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.nx.inf.model.dto.PatientDTO;
import com.nx.inf.model.entity.Patient;
import com.nx.inf.model.entity.PatientGroupRelation;
import com.nx.inf.model.entity.FollowupPlan;
import com.nx.inf.mapper.PatientGroupRelationMapper;
import com.nx.inf.mapper.PatientMapper;
import com.nx.inf.mapper.FollowupPlanMapper;
import com.nx.inf.service.PatientGroupService;
import com.nx.inf.service.PatientService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 患者服务实现
 */
@Service
public class PatientServiceImpl extends ServiceImpl<PatientMapper, Patient> implements PatientService {

    @Autowired
    private PatientGroupRelationMapper relationMapper;
    
    @Autowired
    private PatientGroupService patientGroupService;
    
    @Autowired
    private FollowupPlanMapper followupPlanMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPatient(PatientDTO patientDTO) {
        Patient patient = new Patient();
        BeanUtils.copyProperties(patientDTO, patient);
        
        patient.setCreateTime(LocalDateTime.now());
        patient.setUpdateTime(LocalDateTime.now());
        
        baseMapper.insert(patient);
        return patient.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePatient(PatientDTO patientDTO) {
        Patient patient = baseMapper.selectPatientById(patientDTO.getId());
        if (patient == null) {
            return false;
        }
        
        BeanUtils.copyProperties(patientDTO, patient);
        patient.setUpdateTime(LocalDateTime.now());
        
        return baseMapper.updateById(patient) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePatient(Long id) {
        // 先删除患者与分组的关联
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getPatientId, id);
        relationMapper.delete(wrapper);
        
        // 再删除患者
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public PatientDTO getPatientById(Long id) {
        Patient patient = baseMapper.selectPatientById(id);
        if (patient == null) {
            return null;
        }
        
        PatientDTO dto = new PatientDTO();
        BeanUtils.copyProperties(patient, dto);
        
        // 查询并设置患者所属分组
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getPatientId, id);
        List<PatientGroupRelation> relations = relationMapper.selectList(wrapper);
        
        if (!relations.isEmpty()) {
            List<PatientDTO.PatientGroupSimpleDTO> groups = patientGroupService.getPatientGroups(id).stream()
                    .map(groupDTO -> {
                        PatientDTO.PatientGroupSimpleDTO simpleDTO = new PatientDTO.PatientGroupSimpleDTO();
                        simpleDTO.setId(groupDTO.getId());
                        simpleDTO.setName(groupDTO.getName());
                        return simpleDTO;
                    })
                    .collect(Collectors.toList());
            
            dto.setGroups(groups);
        }
        
        return dto;
    }

    @Override
    public IPage<PatientDTO> getPatientPage(int page, int size, String status, String name, 
                                        String idCard, String phone, String disease) {
        Page<Patient> queryPage = new Page<>(page, size);
        IPage<Patient> patientPage = baseMapper.selectPatientPage(
                queryPage, status, name, idCard, phone, disease);
        
        // 转换为DTO
        IPage<PatientDTO> resultPage = patientPage.convert(patient -> {
            PatientDTO dto = new PatientDTO();
            BeanUtils.copyProperties(patient, dto);
            
            // 查询并设置患者所属分组
            List<PatientDTO.PatientGroupSimpleDTO> groups = patientGroupService.getPatientGroups(patient.getId()).stream()
                    .map(groupDTO -> {
                        PatientDTO.PatientGroupSimpleDTO simpleDTO = new PatientDTO.PatientGroupSimpleDTO();
                        simpleDTO.setId(groupDTO.getId());
                        simpleDTO.setName(groupDTO.getName());
                        return simpleDTO;
                    })
                    .collect(Collectors.toList());
            
            dto.setGroups(groups);
            
            return dto;
        });
        
        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePatientStatus(Long id, String status, Long reviewerId, String reviewerName) {
        // 只更新 patient_group_relation 的 status 字段
        try {
            LambdaQueryWrapper<PatientGroupRelation> relWrapper = new LambdaQueryWrapper<>();
            relWrapper.eq(PatientGroupRelation::getPatientId, id);
            PatientGroupRelation rel = relationMapper.selectOne(relWrapper);
            if (rel != null) {
                rel.setStatus(status);
                relationMapper.updateById(rel);
                
                // 如果状态更新为审核通过（'1'），则更新相关随访计划的患者数量
                if ("1".equals(status) && rel.getGroupId() != null) {
                    // 查询该分组下的所有随访计划，并更新其患者数量
                    LambdaQueryWrapper<FollowupPlan> planWrapper = new LambdaQueryWrapper<>();
                    planWrapper.eq(FollowupPlan::getGroupId, rel.getGroupId());
                    List<FollowupPlan> plans = followupPlanMapper.selectList(planWrapper);
                    
                    for (FollowupPlan plan : plans) {
                        followupPlanMapper.updatePatientCount(plan.getId());
                    }
                }
                
                return true;
            }
        } catch (Exception e) {
            // ignore
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdatePatientStatus(List<Long> ids, String status, Long reviewerId, String reviewerName) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        for (Long id : ids) {
            Patient patient = baseMapper.selectPatientById(id);
            if (patient != null) {
                patient.setStatus(status);
                patient.setReviewerId(reviewerId);
                patient.setReviewerName(reviewerName);
                patient.setReviewTime(now);
                patient.setUpdateTime(now);
                baseMapper.updateById(patient);
            }
        }
        
        // 如果状态更新为审核通过（'1'），则更新相关随访计划的患者数量
        if ("1".equals(status)) {
            // 查询所有相关分组的随访计划，并更新其患者数量
            for (Long id : ids) {
                LambdaQueryWrapper<PatientGroupRelation> relWrapper = new LambdaQueryWrapper<>();
                relWrapper.eq(PatientGroupRelation::getPatientId, id);
                PatientGroupRelation rel = relationMapper.selectOne(relWrapper);
                if (rel != null && rel.getGroupId() != null) {
                    LambdaQueryWrapper<FollowupPlan> planWrapper = new LambdaQueryWrapper<>();
                    planWrapper.eq(FollowupPlan::getGroupId, rel.getGroupId());
                    List<FollowupPlan> plans = followupPlanMapper.selectList(planWrapper);
                    
                    for (FollowupPlan plan : plans) {
                        followupPlanMapper.updatePatientCount(plan.getId());
                    }
                }
            }
        }
        
        return true;
    }
} 
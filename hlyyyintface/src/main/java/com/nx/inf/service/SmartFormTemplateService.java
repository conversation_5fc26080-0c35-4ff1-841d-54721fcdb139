package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nx.inf.model.dto.SmartFormQueryDTO;
import com.nx.inf.model.dto.SmartFormTemplateDTO;
import com.nx.inf.model.vo.QuestionnaireVO;
import com.nx.inf.model.vo.SmartFormTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * 智能表单模板Service接口
 */
public interface SmartFormTemplateService {
    
    /**
     * 分页查询表单模板列表
     */
    IPage<SmartFormTemplateVO> getFormTemplateList(SmartFormQueryDTO queryDTO);
    
    /**
     * 创建表单模板（简化版）
     */
    SmartFormTemplateVO createFormTemplate(SmartFormTemplateDTO templateDTO);
    
    /**
     * 更新表单模板（完整版）
     */
    SmartFormTemplateVO updateFormTemplate(SmartFormTemplateDTO templateDTO);
    
    /**
     * 根据ID获取表单模板详情
     */
    SmartFormTemplateVO getFormTemplateById(Long id);
    
    /**
     * 删除表单模板
     */
    boolean deleteFormTemplate(Long id);
    
    /**
     * 复制表单模板
     */
    SmartFormTemplateVO copyFormTemplate(Long sourceId, String newName);
    
    /**
     * 批量删除表单模板
     */
    boolean batchDeleteFormTemplates(List<Long> ids);
    
    /**
     * 更新表单模板状态
     */
    boolean updateFormTemplateStatus(Long id, String status);
    
    /**
     * 获取可用问卷列表（支持搜索）
     */
    List<QuestionnaireVO> getAvailableQuestionnaires(String keyword, String type, String status);
    
    /**
     * 添加表单问卷关联
     */
    boolean addFormQuestionnaire(Long formTemplateId, Long questionnaireId, Integer sortOrder, 
                                Boolean isRequired, Map<String, Object> displayConfig);
    
    /**
     * 更新表单问卷关联
     */
    boolean updateFormQuestionnaire(Long relId, Integer sortOrder, Boolean isRequired, 
                                   Map<String, Object> displayConfig);
    
    /**
     * 删除表单问卷关联
     */
    boolean removeFormQuestionnaire(Long relId);
    
    /**
     * 获取表单数据分析
     */
    Map<String, Object> getFormDataAnalysis(Long formTemplateId);
    
    /**
     * 获取表单统计分析
     */
    Map<String, Object> getFormStatistics(Long formTemplateId);
    
    /**
     * 获取表单填写页面数据
     */
    Map<String, Object> getFormForFilling(Long formTemplateId, String patientId);
    
    /**
     * 提交表单数据
     */
    Long submitFormData(Long formTemplateId, String patientId, Map<String, Object> formData);
    
    /**
     * 保存草稿
     */
    boolean saveFormDraft(Long formTemplateId, String patientId, Map<String, Object> draftData);
    
    /**
     * 获取草稿数据
     */
    Map<String, Object> getFormDraft(Long formTemplateId, String patientId);
    
    /**
     * 获取表单提交数据列表
     */
    Map<String, Object> getFormSubmissionList(Long formTemplateId, Integer pageNum, Integer pageSize, 
                                             String patientId, String status, String startTime, String endTime);
    
    /**
     * 获取表单提交详情
     */
    Map<String, Object> getFormSubmissionDetail(Long submissionId);
    
    /**
     * 清理重复的表单问卷关联数据
     * 用于解决数据库中可能存在的重复关联问题
     */
    void cleanupDuplicateQuestionnaireRelations();
    
    /**
     * 检查表单问卷关联的完整性
     * 用于诊断关联数据问题
     */
    Map<String, Object> checkQuestionnaireRelationIntegrity(Long formTemplateId);
    
    /**
     * 生成表单填写二维码
     * 
     * @param formTemplateId 表单模板ID
     * @return 二维码URL
     */
    String generateFormQRCode(Long formTemplateId);
    
    /**
     * 更新表单二维码
     * 
     * @param formTemplateId 表单模板ID
     * @param qrCodeUrl 二维码URL
     * @return 是否成功
     */
    boolean updateFormQRCode(Long formTemplateId, String qrCodeUrl);
    
    /**
     * 获取表单二维码
     * 
     * @param formTemplateId 表单模板ID
     * @return 二维码URL
     */
    String getFormQRCode(Long formTemplateId);
} 
package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.FormTemplateMapper;
import com.nx.inf.model.dto.FormTemplateDTO;
import com.nx.inf.model.entity.FormTemplate;
import com.nx.inf.service.FormTemplateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 表单模板服务实现
 */
@Service
public class FormTemplateServiceImpl extends ServiceImpl<FormTemplateMapper, FormTemplate> 
        implements FormTemplateService {

    @Override
    public List<FormTemplateDTO> getTemplateList(String name, String category) {
        List<FormTemplate> templateList = baseMapper.selectTemplateList(name, category);
        
        // 转换为DTO
        return templateList.stream().map(template -> {
            FormTemplateDTO dto = new FormTemplateDTO();
            BeanUtils.copyProperties(template, dto);
            return dto;
        }).collect(Collectors.toList());
    }
} 
package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.nx.inf.mapper.*;
import com.nx.inf.model.dto.*;
import com.nx.inf.model.entity.*;
import com.nx.inf.service.FollowupPlanService;
import com.nx.inf.utils.UserContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 随访计划服务实现
 */
@Service
public class FollowupPlanServiceImpl extends ServiceImpl<FollowupPlanMapper, FollowupPlan> implements FollowupPlanService {

    @Autowired
    private FollowupSubplanMapper followupSubplanMapper;
    
    @Autowired
    private FollowupSubplanItemMapper followupSubplanItemMapper;
    
    @Autowired
    private FollowupSubplanQuestionnaireMapper followupSubplanQuestionnaireMapper;
    
    @Autowired
    private FollowupTaskMapper followupTaskMapper;
    
    @Autowired
    private PatientGroupMapper patientGroupMapper;
    
    @Autowired
    private QuestionnaireMapper questionnaireMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveFollowupPlan(FollowupPlanDTO followupPlanDTO) {
        // 1. 保存计划基本信息
        FollowupPlan followupPlan = new FollowupPlan();
        BeanUtils.copyProperties(followupPlanDTO, followupPlan);
        followupPlan.setCreateTime(new Date());
        followupPlan.setUpdateTime(new Date());
        
        // 设置创建者信息
        followupPlan.setCreatorId(UserContext.getCurrentUserId());
        followupPlan.setCreatorName(UserContext.getCurrentUserName());
        
        // 生成二维码URL
        followupPlan.setQrCodeUrl(generateQRCodeUrl(followupPlanDTO.getGroupId()));
        
        baseMapper.insert(followupPlan);
        
        // 2. 保存子计划
        if (followupPlanDTO.getSubplans() != null) {
            for (FollowupSubplanDTO subplanDTO : followupPlanDTO.getSubplans()) {
                saveSubplan(followupPlan.getId(), subplanDTO);
            }
        }
        
        // 3. 更新计划统计信息（包括患者数量）
        updatePlanStatistics(followupPlan.getId());
        
        return followupPlan.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFollowupPlan(FollowupPlanDTO followupPlanDTO) {
        // 1. 更新计划基本信息
        FollowupPlan followupPlan = baseMapper.selectPlanById(followupPlanDTO.getId());
        if (followupPlan == null) {
            return false;
        }
        BeanUtils.copyProperties(followupPlanDTO, followupPlan);
        followupPlan.setUpdateTime(new Date());
        baseMapper.updateById(followupPlan);
        
        // 2. 删除旧的子计划数据
        deleteSubplansByPlanId(followupPlan.getId());
        
        // 3. 保存新的子计划
        if (followupPlanDTO.getSubplans() != null) {
            for (FollowupSubplanDTO subplanDTO : followupPlanDTO.getSubplans()) {
                saveSubplan(followupPlan.getId(), subplanDTO);
            }
        }
        
        // 4. 更新计划统计信息（包括患者数量）
        updatePlanStatistics(followupPlan.getId());
        
        return true;
    }

    @Override
    public IPage<FollowupPlanDTO> getFollowupPlanPage(int page, int size, String category, String status, String name) {
        Page<FollowupPlan> queryPage = new Page<>(page, size);
        IPage<FollowupPlan> planPage = baseMapper.selectPlanPage(queryPage, category, status, name);
        
        // 转换为DTO
        IPage<FollowupPlanDTO> resultPage = planPage.convert(followupPlan -> {
            FollowupPlanDTO dto = new FollowupPlanDTO();
            BeanUtils.copyProperties(followupPlan, dto);
            
            // 设置分组名称
            if (followupPlan.getGroupId() != null) {
                PatientGroup group = patientGroupMapper.selectById(followupPlan.getGroupId());
                if (group != null) {
                    dto.setGroupName(group.getName());
                }
            }
            
            // 设置统计信息（从数据库字段获取，如果为null则设为0）
            dto.setPatientCount(followupPlan.getPatientCount() != null ? followupPlan.getPatientCount() : 0);
            dto.setTaskCount(followupPlan.getTaskCount() != null ? followupPlan.getTaskCount() : 0);
            dto.setCompletedTaskCount(followupPlan.getCompletedTaskCount() != null ? followupPlan.getCompletedTaskCount() : 0);
            
            // 手动计算子计划数量
            LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
            subplanWrapper.eq(FollowupSubplan::getPlanId, followupPlan.getId());
            Long subplanCount = followupSubplanMapper.selectCount(subplanWrapper);
            dto.setSubplanCount(subplanCount.intValue());
            
            return dto;
        });
        
        return resultPage;
    }

    @Override
    public FollowupPlanDTO getFollowupPlanById(Long id) {
        System.out.println("=== 开始查询随访计划详情，ID: " + id + " ===");
        
        // 1. 查询计划基本信息
        FollowupPlan followupPlan = baseMapper.selectPlanById(id);
        if (followupPlan == null) {
            System.out.println("随访计划不存在，ID: " + id);
            return null;
        }
        System.out.println("查询到随访计划: " + followupPlan.getName());
        
        FollowupPlanDTO dto = new FollowupPlanDTO();
        BeanUtils.copyProperties(followupPlan, dto);
        
        // 2. 设置分组名称
        if (followupPlan.getGroupId() != null) {
            PatientGroup group = patientGroupMapper.selectById(followupPlan.getGroupId());
            if (group != null) {
                dto.setGroupName(group.getName());
            }
        }
        
        // 3. 查询并设置子计划
        List<FollowupSubplan> subplans = followupSubplanMapper.selectByPlanId(id);
        System.out.println("查询到子计划数量: " + (subplans != null ? subplans.size() : 0));
        
        if (subplans != null) {
            List<FollowupSubplanDTO> subplanDTOs = subplans.stream().map(subplan -> {
                System.out.println("处理子计划: " + subplan.getName() + ", ID: " + subplan.getId());
                
                FollowupSubplanDTO subplanDTO = new FollowupSubplanDTO();
                BeanUtils.copyProperties(subplan, subplanDTO);
                
                // 查询子计划项目
                List<FollowupSubplanItem> items = followupSubplanItemMapper.selectBySubplanId(subplan.getId());
                System.out.println("子计划 " + subplan.getId() + " 的项目数量: " + (items != null ? items.size() : 0));
                if (items != null) {
                    List<FollowupSubplanItemDTO> itemDTOs = items.stream().map(item -> {
                        FollowupSubplanItemDTO itemDTO = new FollowupSubplanItemDTO();
                        BeanUtils.copyProperties(item, itemDTO);
                        return itemDTO;
                    }).collect(Collectors.toList());
                    subplanDTO.setItems(itemDTOs);
                }
                
                // 查询子计划问卷
                List<FollowupSubplanQuestionnaire> questionnaires = followupSubplanQuestionnaireMapper.selectBySubplanId(subplan.getId());
                System.out.println("子计划ID: " + subplan.getId() + ", 查询到的问卷关联数据: " + (questionnaires != null ? questionnaires.size() : 0));
                if (questionnaires != null) {
                    List<FollowupSubplanQuestionnaireDTO> questionnaireDTOs = questionnaires.stream().map(q -> {
                        FollowupSubplanQuestionnaireDTO qDTO = new FollowupSubplanQuestionnaireDTO();
                        BeanUtils.copyProperties(q, qDTO);
                        
                        // 查询问卷详细信息
                        Questionnaire questionnaire = questionnaireMapper.selectById(q.getQuestionnaireId());
                        System.out.println("查询问卷ID: " + q.getQuestionnaireId() + ", 结果: " + (questionnaire != null ? questionnaire.getName() : "null"));
                        if (questionnaire != null) {
                            qDTO.setQuestionnaireName(questionnaire.getName());
                            // 问卷表没有description字段，设置为空字符串
                            qDTO.setQuestionnaireDescription("");
                        }
                        
                        return qDTO;
                    }).collect(Collectors.toList());
                    subplanDTO.setQuestionnaires(questionnaireDTOs);
                    System.out.println("子计划ID: " + subplan.getId() + ", 设置问卷DTO数量: " + questionnaireDTOs.size());
                } else {
                    System.out.println("子计划ID: " + subplan.getId() + ", 没有查询到问卷关联数据");
                }
                
                return subplanDTO;
            }).collect(Collectors.toList());
            dto.setSubplans(subplanDTOs);
            System.out.println("设置子计划DTO数量: " + subplanDTOs.size());
        }
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFollowupPlan(Long id) {
        // 1. 删除任务
        LambdaQueryWrapper<FollowupTask> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.eq(FollowupTask::getPlanId, id);
        followupTaskMapper.delete(taskWrapper);
        
        // 2. 删除子计划问卷关联
        List<FollowupSubplan> subplans = followupSubplanMapper.selectByPlanId(id);
        if (subplans != null) {
            for (FollowupSubplan subplan : subplans) {
                LambdaQueryWrapper<FollowupSubplanQuestionnaire> qWrapper = new LambdaQueryWrapper<>();
                qWrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplan.getId());
                followupSubplanQuestionnaireMapper.delete(qWrapper);
                
                // 删除子计划项目
                LambdaQueryWrapper<FollowupSubplanItem> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.eq(FollowupSubplanItem::getSubplanId, subplan.getId());
                followupSubplanItemMapper.delete(itemWrapper);
            }
        }
        
        // 3. 删除子计划
        LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
        subplanWrapper.eq(FollowupSubplan::getPlanId, id);
        followupSubplanMapper.delete(subplanWrapper);
        
        // 4. 删除计划基本信息
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public boolean updatePlanStatus(Long id, String status) {
        FollowupPlan followupPlan = baseMapper.selectPlanById(id);
        if (followupPlan == null) {
            return false;
        }
        
        followupPlan.setStatus(status);
        followupPlan.setUpdateTime(new Date());
        return baseMapper.updateById(followupPlan) > 0;
    }

    @Override
    public String generateQRCode(Long id) {
        FollowupPlan followupPlan = baseMapper.selectPlanById(id);
        if (followupPlan == null) {
            return null;
        }
        
        String qrCodeUrl = generateQRCodeUrl(followupPlan.getGroupId());
        followupPlan.setQrCodeUrl(qrCodeUrl);
        followupPlan.setUpdateTime(new Date());
        baseMapper.updateById(followupPlan);
        
        return qrCodeUrl;
    }

    @Override
    public void updatePlanStatistics(Long id) {
        baseMapper.updatePatientCount(id);
        baseMapper.updateTaskCount(id);
    }
    
    @Override
    public void updateAllPlanStatistics() {
        LambdaQueryWrapper<FollowupPlan> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(FollowupPlan::getId);
        List<FollowupPlan> plans = baseMapper.selectList(wrapper);
        for (FollowupPlan plan : plans) {
            updatePlanStatistics(plan.getId());
        }
    }
    
    /**
     * 保存子计划
     */
    private void saveSubplan(Long planId, FollowupSubplanDTO subplanDTO) {
        FollowupSubplan subplan = new FollowupSubplan();
        BeanUtils.copyProperties(subplanDTO, subplan);
        subplan.setPlanId(planId);
        subplan.setCreateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        subplan.setUpdateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        followupSubplanMapper.insert(subplan);
        
        // 保存子计划项目
        if (subplanDTO.getItems() != null) {
            for (FollowupSubplanItemDTO itemDTO : subplanDTO.getItems()) {
                FollowupSubplanItem item = new FollowupSubplanItem();
                BeanUtils.copyProperties(itemDTO, item);
                item.setSubplanId(subplan.getId());
                item.setCreateTime(new Date());
                item.setUpdateTime(new Date());
                followupSubplanItemMapper.insert(item);
            }
        }
        
        // 保存子计划问卷关联
        if (subplanDTO.getQuestionnaires() != null) {
            for (FollowupSubplanQuestionnaireDTO questionnaireDTO : subplanDTO.getQuestionnaires()) {
                FollowupSubplanQuestionnaire questionnaire = new FollowupSubplanQuestionnaire();
                BeanUtils.copyProperties(questionnaireDTO, questionnaire);
                questionnaire.setSubplanId(subplan.getId());
                questionnaire.setCreateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                followupSubplanQuestionnaireMapper.insert(questionnaire);
            }
        }
    }
    
    /**
     * 删除计划的子计划数据
     */
    private void deleteSubplansByPlanId(Long planId) {
        List<FollowupSubplan> subplans = followupSubplanMapper.selectByPlanId(planId);
        if (subplans != null) {
            for (FollowupSubplan subplan : subplans) {
                // 删除子计划问卷关联
                LambdaQueryWrapper<FollowupSubplanQuestionnaire> qWrapper = new LambdaQueryWrapper<>();
                qWrapper.eq(FollowupSubplanQuestionnaire::getSubplanId, subplan.getId());
                followupSubplanQuestionnaireMapper.delete(qWrapper);
                
                // 删除子计划项目
                LambdaQueryWrapper<FollowupSubplanItem> itemWrapper = new LambdaQueryWrapper<>();
                itemWrapper.eq(FollowupSubplanItem::getSubplanId, subplan.getId());
                followupSubplanItemMapper.delete(itemWrapper);
            }
        }
        
        // 删除子计划
        LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
        subplanWrapper.eq(FollowupSubplan::getPlanId, planId);
        followupSubplanMapper.delete(subplanWrapper);
    }
    
    /**
     * 生成二维码URL
     */
    private String generateQRCodeUrl(Long groupId) {
        // 这里应该调用二维码生成服务
        // 暂时返回一个示例URL
        return "http://example.com/qr/group/" + groupId;
    }

    @Override
    public Map<String, Object> exportPlanData(Long id) {
        Map<String, Object> exportData = new HashMap<>();
        
        // 获取计划基本信息
        FollowupPlanDTO plan = getFollowupPlanById(id);
        if (plan != null) {
            exportData.put("plan", plan);
            
            // 获取子计划信息
            List<FollowupSubplanDTO> subplans = followupSubplanMapper.selectByPlanId(id).stream()
                .map(subplan -> {
                    FollowupSubplanDTO dto = new FollowupSubplanDTO();
                    BeanUtils.copyProperties(subplan, dto);
                    return dto;
                })
                .collect(Collectors.toList());
            exportData.put("subplans", subplans);
            
            // 获取任务统计
            Map<String, Object> taskStats = new HashMap<>();
            LambdaQueryWrapper<FollowupTask> taskWrapper = new LambdaQueryWrapper<>();
            taskWrapper.eq(FollowupTask::getPlanId, id);
            long totalTasks = followupTaskMapper.selectCount(taskWrapper);
            
            taskWrapper.eq(FollowupTask::getStatus, "completed");
            long completedTasks = followupTaskMapper.selectCount(taskWrapper);
            
            taskStats.put("totalTasks", totalTasks);
            taskStats.put("completedTasks", completedTasks);
            taskStats.put("completionRate", totalTasks > 0 ? (double) completedTasks / totalTasks : 0.0);
            
            exportData.put("taskStats", taskStats);
        }
        
        return exportData;
    }

    // C端相关方法实现
    @Override
    public IPage<FollowupPlanDTO> getPatientPlans(Page<FollowupPlanDTO> page, Long patientId, String status) {
        // TODO: 实现患者参与的随访计划列表查询
        return new Page<>();
    }

    @Override
    public FollowupPlanDTO getPlanDetail(Long planId) {
        return getFollowupPlanById(planId);
    }

    @Override
    public IPage<Map<String, Object>> getPatientTasks(Page<Map<String, Object>> page, Long patientId, String status) {
        // TODO: 实现患者任务列表查询
        return new Page<>();
    }

    @Override
    public Map<String, Object> getPatientStats(Long patientId) {
        Map<String, Object> stats = new HashMap<>();
        // TODO: 实现患者统计信息查询
        return stats;
    }

    @Override
    public boolean validateQRCode(String phone, Long groupId, String groupName) {
        // TODO: 实现二维码参数验证
        return true;
    }

    // 医护端相关方法实现
    @Override
    public IPage<FollowupPlanDTO> getPlanPage(Page<FollowupPlan> page, String name, String category, String status) {
        IPage<FollowupPlan> planPage = baseMapper.selectPlanPage(page, category, status, name);
        return planPage.convert(followupPlan -> {
            FollowupPlanDTO dto = new FollowupPlanDTO();
            BeanUtils.copyProperties(followupPlan, dto);
            return dto;
        });
    }

    @Override
    public FollowupPlanDTO getPlanById(Long id) {
        return getFollowupPlanById(id);
    }

    @Override
    public FollowupPlan createPlan(FollowupPlanDTO planDTO) {
        Long planId = saveFollowupPlan(planDTO);
        return baseMapper.selectPlanById(planId);
    }

    @Override
    public FollowupPlan updatePlan(FollowupPlanDTO planDTO) {
        boolean success = updateFollowupPlan(planDTO);
        if (success) {
            return baseMapper.selectPlanById(planDTO.getId());
        }
        return null;
    }

    @Override
    public void deletePlan(Long id) {
        deleteFollowupPlan(id);
    }

    @Override
    public void batchDeletePlans(List<Long> ids) {
        // TODO: 实现批量删除计划
        for (Long id : ids) {
            deleteFollowupPlan(id);
        }
    }

    @Override
    public Map<String, Object> getPlanStats(Long id) {
        Map<String, Object> stats = new HashMap<>();
        // TODO: 实现计划统计信息查询
        return stats;
    }

    @Override
    public Map<String, Object> getPlanExecutionStats(Long planId) {
        Map<String, Object> stats = new HashMap<>();
        // TODO: 实现计划执行统计查询
        return stats;
    }

    @Override
    public FollowupPlan copyPlan(Long id) {
        // TODO: 实现计划复制功能
        FollowupPlan originalPlan = baseMapper.selectPlanById(id);
        if (originalPlan != null) {
            FollowupPlan copyPlan = new FollowupPlan();
            BeanUtils.copyProperties(originalPlan, copyPlan);
            copyPlan.setId(null);
            copyPlan.setName(originalPlan.getName() + "_副本");
            copyPlan.setCreateTime(new Date());
            copyPlan.setUpdateTime(new Date());
            
            // 设置新的创建者信息
            copyPlan.setCreatorId(UserContext.getCurrentUserId());
            copyPlan.setCreatorName(UserContext.getCurrentUserName());
            
            baseMapper.insert(copyPlan);
            return copyPlan;
        }
        return null;
    }
} 
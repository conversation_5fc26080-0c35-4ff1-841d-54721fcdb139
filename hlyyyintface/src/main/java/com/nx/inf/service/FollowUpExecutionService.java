package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.nx.inf.model.dto.FollowUpExecutionDTO;
import com.nx.inf.model.entity.FollowUpExecution;

import java.util.List;

/**
 * 随访执行记录服务接口
 */
public interface FollowUpExecutionService extends IService<FollowUpExecution> {
    
    /**
     * 创建随访执行记录
     * 
     * @param followUpExecutionDTO 执行记录DTO
     * @return 记录ID
     */
    Long createFollowUpExecution(FollowUpExecutionDTO followUpExecutionDTO);
    
    /**
     * 更新随访执行记录
     * 
     * @param followUpExecutionDTO 执行记录DTO
     * @return 是否成功
     */
    boolean updateFollowUpExecution(FollowUpExecutionDTO followUpExecutionDTO);
    
    /**
     * 根据ID获取随访执行记录
     * 
     * @param id 记录ID
     * @return 执行记录DTO
     */
    FollowUpExecutionDTO getFollowUpExecutionById(Long id);
    
    /**
     * 分页查询随访执行记录
     * 
     * @param page 页码
     * @param size 每页数量
     * @param planId 计划ID
     * @param patientName 患者姓名
     * @param warningFlag 是否有异常告警
     * @return 分页结果
     */
    IPage<FollowUpExecutionDTO> getExecutionPage(int page, int size, Long planId, 
                                               String patientName, Boolean warningFlag);
    
    /**
     * 根据计划ID查询执行记录
     * 
     * @param planId 计划ID
     * @return 执行记录DTO列表
     */
    List<FollowUpExecutionDTO> getExecutionsByPlanId(Long planId);
    
    /**
     * 根据患者ID查询执行记录
     * 
     * @param patientId 患者ID
     * @return 执行记录DTO列表
     */
    List<FollowUpExecutionDTO> getExecutionsByPatientId(Long patientId);
    
    /**
     * 根据计划ID查询患者列表（去重）
     * 
     * @param planId 计划ID
     * @return 患者DTO列表
     */
    List<FollowUpExecutionDTO> getPatientsByPlanId(Long planId);
    
    /**
     * 更新执行记录状态
     * 
     * @param id 记录ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateExecutionStatus(Long id, String status);
} 
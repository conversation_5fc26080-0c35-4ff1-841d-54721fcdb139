package com.nx.inf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.dto.FormTemplateDTO;
import com.nx.inf.model.entity.FormTemplate;

import java.util.List;

/**
 * 表单模板服务接口
 */
public interface FormTemplateService extends IService<FormTemplate> {
    
    /**
     * 查询表单模板列表
     * 
     * @param name 模板名称
     * @param category 分类
     * @return 模板列表
     */
    List<FormTemplateDTO> getTemplateList(String name, String category);
} 
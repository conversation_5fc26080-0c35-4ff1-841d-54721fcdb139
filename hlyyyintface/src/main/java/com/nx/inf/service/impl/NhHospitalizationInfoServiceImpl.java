package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.mapper.NhHospitalizationInfoMapper;
import com.nx.inf.model.entity.NhHospitalizationInfo;
import com.nx.inf.service.NhHospitalizationInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 住院信息服务实现类
 */
@Service
public class NhHospitalizationInfoServiceImpl implements NhHospitalizationInfoService {

    @Resource
    private NhHospitalizationInfoMapper nhHospitalizationInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(NhHospitalizationInfo nhHospitalizationInfo) {
        return nhHospitalizationInfoMapper.insert(nhHospitalizationInfo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(NhHospitalizationInfo nhHospitalizationInfo) {
        return nhHospitalizationInfoMapper.updateById(nhHospitalizationInfo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return nhHospitalizationInfoMapper.deleteById(id) > 0;
    }

    @Override
    public NhHospitalizationInfo getById(Long id) {
        return nhHospitalizationInfoMapper.selectById(id);
    }

    @Override
    public IPage<NhHospitalizationInfo> page(Page<NhHospitalizationInfo> page) {
        return nhHospitalizationInfoMapper.selectPage(page, null);
    }

    @Override
    public NhHospitalizationInfo getByPatientId(String patientId) {
        // 使用自定义的XML查询方法
        return nhHospitalizationInfoMapper.selectByPatientId(patientId);
    }

    @Override
    public List<Map<String, Object>> countByDepartment() {
        return nhHospitalizationInfoMapper.countByDepartment();
    }

    @Override
    public List<NhHospitalizationInfo> getByAdmissionDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return nhHospitalizationInfoMapper.selectByAdmissionDateRange(startDate, endDate);
    }

    @Override
    public String genInHospitalId() {
        return nhHospitalizationInfoMapper.genInHospitalId();
    }
}

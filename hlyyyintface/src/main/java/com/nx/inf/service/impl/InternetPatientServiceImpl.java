package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.mapper.InternetPatientMapper;
import com.nx.inf.model.entity.InternetPatient;
import com.nx.inf.model.vo.PageResponseVO;
import com.nx.inf.model.vo.PatientListRequestVO;
import com.nx.inf.service.InternetPatientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @Description: 患者服务实现
 * @Author: zhongx
 * @CreateDate: 2024/5/30
 * @Version: 1.0
 */
@Service
@Slf4j
public class InternetPatientServiceImpl implements InternetPatientService {

    @Autowired
    private InternetPatientMapper internetPatientMapper;

    /**
     * 分页查询患者列表
     * @param requestVO 查询参数
     * @return 分页患者列表
     */
    @Override
    public PageResponseVO<InternetPatient> getPatientList(PatientListRequestVO requestVO) {
        log.info("查询患者列表，参数：{}", requestVO);
        
        // 构建查询条件
        LambdaQueryWrapper<InternetPatient> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(requestVO.getPatientName())) {
            queryWrapper.like(InternetPatient::getPatientName, requestVO.getPatientName());
        }
        if (StringUtils.hasText(requestVO.getPhone())) {
            queryWrapper.eq(InternetPatient::getPhone, requestVO.getPhone());
        }
        
        // 添加排序条件
        queryWrapper.orderByDesc(InternetPatient::getCreateTime);
        
        // 执行分页查询
        Page<InternetPatient> page = new Page<>(requestVO.getPageNum(), requestVO.getPageSize());
        Page<InternetPatient> patientPage = internetPatientMapper.selectPage(page, queryWrapper);
        
        // 构建返回结果
        PageResponseVO<InternetPatient> result = new PageResponseVO<>();
        result.setTotal(patientPage.getTotal());
        result.setPageNum(requestVO.getPageNum());
        result.setPageSize(requestVO.getPageSize());
        result.setList(patientPage.getRecords());
        
        log.info("查询患者列表完成，共查到{}条记录", patientPage.getTotal());
        return result;
    }
} 
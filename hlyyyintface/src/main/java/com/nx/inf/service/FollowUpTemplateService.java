package com.nx.inf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.dto.FollowUpTemplateDTO;
import com.nx.inf.model.entity.FollowUpTemplate;

import java.util.List;

/**
 * 随访计划模板服务接口
 */
public interface FollowUpTemplateService extends IService<FollowUpTemplate> {
    
    /**
     * 查询随访计划模板列表
     * 
     * @param name 模板名称
     * @param category 分类
     * @return 模板列表
     */
    List<FollowUpTemplateDTO> getTemplateList(String name, String category);
} 
package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nx.inf.model.dto.SmartFollowUpTemplateDTO;
import com.nx.inf.model.vo.SmartFollowUpTemplateRequestVO;

/**
 * 智能随访模板服务接口
 */
public interface SmartFollowUpTemplateService {
    
    /**
     * 分页查询智能随访模板列表
     */
    IPage<SmartFollowUpTemplateDTO> getTemplatePage(SmartFollowUpTemplateRequestVO request);
    
    /**
     * 创建智能随访模板
     */
    Long createTemplate(SmartFollowUpTemplateDTO templateDTO);
    
    /**
     * 更新智能随访模板
     */
    boolean updateTemplate(SmartFollowUpTemplateDTO templateDTO);
    
    /**
     * 根据ID获取智能随访模板详情
     */
    SmartFollowUpTemplateDTO getTemplateById(Long id);
    
    /**
     * 删除智能随访模板
     */
    boolean deleteTemplate(Long id);
    
    /**
     * 更新智能随访模板状态
     */
    boolean updateTemplateStatus(Long id, Integer status);
} 
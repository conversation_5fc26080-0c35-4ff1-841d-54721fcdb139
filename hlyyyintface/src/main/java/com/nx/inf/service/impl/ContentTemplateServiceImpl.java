package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.ContentTemplateCategoryMapper;
import com.nx.inf.mapper.ContentTemplateMapper;
import com.nx.inf.model.dto.ContentTemplateCategoryDTO;
import com.nx.inf.model.dto.ContentTemplateDTO;
import com.nx.inf.model.entity.ContentTemplate;
import com.nx.inf.model.entity.ContentTemplateCategory;
import com.nx.inf.model.vo.ContentTemplateRequestVO;
import com.nx.inf.service.ContentTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容模板服务实现类
 */
@Service
@Slf4j
public class ContentTemplateServiceImpl extends ServiceImpl<ContentTemplateMapper, ContentTemplate> 
        implements ContentTemplateService {
    
    @Autowired
    private ContentTemplateCategoryMapper categoryMapper;
    
    // 模拟当前用户信息，实际项目中应该从SecurityContext或Token中获取
    private static final Long CURRENT_USER_ID = 1L;
    private static final String CURRENT_USER_NAME = "系统管理员";
    private static final Long CURRENT_DEPT_ID = 1L;
    private static final String CURRENT_DEPT_NAME = "内科";
    
    private static final SimpleDateFormat DATE_TIME_FORMATTER = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public IPage<ContentTemplateDTO> getTemplateList(ContentTemplateRequestVO request) {
        Page<ContentTemplate> page = new Page<>(request.getPage(), request.getPageSize());
        
        IPage<ContentTemplate> pageResult = baseMapper.selectTemplatePage(
            page, 
            request.getCategory(), 
            request.getKeyword(), 
            request.getPermission(), 
            request.getStatus(),
            CURRENT_USER_ID,
            CURRENT_DEPT_ID
        );
        
        return convertToDTO(pageResult);
    }
    
    @Override
    public ContentTemplateDTO getTemplateById(Long id) {
        ContentTemplate template = baseMapper.selectTemplateByIdWithPermission(
            id, CURRENT_USER_ID, CURRENT_DEPT_ID
        );
        
        if (template == null) {
            throw new RuntimeException("模板不存在或无权限访问");
        }
        
        return convertToDTO(template);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTemplate(ContentTemplateRequestVO request) {
        // 参数校验
        validateTemplateRequest(request, false);
        
        ContentTemplate template = new ContentTemplate();
        BeanUtils.copyProperties(request, template);
        
        // 设置创建者信息
        template.setCreatorId(CURRENT_USER_ID);
        template.setCreatorName(CURRENT_USER_NAME);
        template.setCreatorDeptId(CURRENT_DEPT_ID);
        template.setCreatorDeptName(CURRENT_DEPT_NAME);
        
        // 生成纯文本内容用于搜索
        if (StringUtils.hasText(request.getContent())) {
            template.setContentText(extractTextFromHtml(request.getContent()));
        }
        
        // 设置默认值
        template.setStatus("active");
        template.setUseCount(0);
        template.setIsDeleted(0);
        
        // 手动设置创建时间和更新时间
        Date now = new Date();
        template.setCreateTime(now);
        template.setUpdateTime(now);
        
        save(template);
        
        log.info("创建内容模板成功，ID: {}, 名称: {}, 创建人: {}", 
                template.getId(), template.getName(), CURRENT_USER_NAME);
        
        return template.getId();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplate(ContentTemplateRequestVO request) {
        if (request.getId() == null) {
            throw new RuntimeException("模板ID不能为空");
        }
        
        // 检查模板是否存在且有权限修改
        ContentTemplate existingTemplate = baseMapper.selectTemplateByIdWithPermission(
            request.getId(), CURRENT_USER_ID, CURRENT_DEPT_ID
        );
        
        if (existingTemplate == null) {
            throw new RuntimeException("模板不存在或无权限修改");
        }
        
        // 检查权限：只有创建者或全局权限模板的管理员可以修改
        if (!existingTemplate.getCreatorId().equals(CURRENT_USER_ID) && 
            !"all".equals(existingTemplate.getPermission())) {
            throw new RuntimeException("无权限修改此模板");
        }
        
        // 参数校验
        validateTemplateRequest(request, true);
        
        ContentTemplate template = new ContentTemplate();
        BeanUtils.copyProperties(request, template);
        
        // 生成纯文本内容用于搜索
        if (StringUtils.hasText(request.getContent())) {
            template.setContentText(extractTextFromHtml(request.getContent()));
        }
        
        // 设置更新时间
        template.setUpdateTime(new Date());
        
        boolean success = updateById(template);
        
        if (success) {
            log.info("更新内容模板成功，ID: {}, 名称: {}, 修改人: {}", 
                    template.getId(), template.getName(), CURRENT_USER_NAME);
        }
        
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long id) {
        // 检查模板是否存在且有权限删除
        ContentTemplate template = baseMapper.selectTemplateByIdWithPermission(
            id, CURRENT_USER_ID, CURRENT_DEPT_ID
        );
        
        if (template == null) {
            throw new RuntimeException("模板不存在或无权限删除");
        }
        
        // 检查权限：只有创建者可以删除
        if (!template.getCreatorId().equals(CURRENT_USER_ID)) {
            throw new RuntimeException("只有创建者可以删除模板");
        }
        
        // 逻辑删除
        UpdateWrapper<ContentTemplate> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                    .set("is_deleted", 1)
                    .set("update_time", new Date());
        
        boolean success = update(updateWrapper);
        
        if (success) {
            log.info("删除内容模板成功，ID: {}, 名称: {}, 删除人: {}", 
                    id, template.getName(), CURRENT_USER_NAME);
        }
        
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContentTemplateDTO copyTemplate(Long id) {
        // 检查源模板是否存在且有权限访问
        ContentTemplate sourceTemplate = baseMapper.selectTemplateByIdWithPermission(
            id, CURRENT_USER_ID, CURRENT_DEPT_ID
        );
        
        if (sourceTemplate == null) {
            throw new RuntimeException("源模板不存在或无权限访问");
        }
        
        // 创建副本
        ContentTemplate newTemplate = new ContentTemplate();
        BeanUtils.copyProperties(sourceTemplate, newTemplate);
        
        // 重置关键字段
        newTemplate.setId(null);
        newTemplate.setName(sourceTemplate.getName() + "_副本");
        newTemplate.setCreatorId(CURRENT_USER_ID);
        newTemplate.setCreatorName(CURRENT_USER_NAME);
        newTemplate.setCreatorDeptId(CURRENT_DEPT_ID);
        newTemplate.setCreatorDeptName(CURRENT_DEPT_NAME);
        newTemplate.setUseCount(0);
        newTemplate.setLastUsedTime(null);
        
        // 设置创建时间和更新时间
        Date now = new Date();
        newTemplate.setCreateTime(now);
        newTemplate.setUpdateTime(now);
        
        save(newTemplate);
        
        log.info("复制内容模板成功，源ID: {}, 新ID: {}, 新名称: {}, 操作人: {}", 
                id, newTemplate.getId(), newTemplate.getName(), CURRENT_USER_NAME);
        
        ContentTemplateDTO result = new ContentTemplateDTO();
        result.setId(newTemplate.getId());
        result.setName(newTemplate.getName());
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteTemplates(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("删除的模板ID列表不能为空");
        }
        
        // 使用自定义Mapper方法进行批量删除（带权限检查）
        int deletedCount = baseMapper.batchDeleteTemplatesWithPermission(
            ids, CURRENT_USER_ID, CURRENT_DEPT_ID
        );
        
        log.info("批量删除内容模板完成，请求删除数量: {}, 实际删除数量: {}, 操作人: {}", 
                ids.size(), deletedCount, CURRENT_USER_NAME);
        
        return deletedCount;
    }
    
    @Override
    public List<ContentTemplateCategoryDTO> getCategoryList() {
        List<ContentTemplateCategory> categories = categoryMapper.selectActiveCategoriesOrderBySort();
        
        return categories.stream().map(this::convertCategoryToDTO).collect(Collectors.toList());
    }
    
    @Override
    public IPage<ContentTemplateDTO> getTemplatesByCategory(ContentTemplateRequestVO request) {
        if (!StringUtils.hasText(request.getCategory())) {
            throw new RuntimeException("分类编码不能为空");
        }
        
        Page<ContentTemplate> page = new Page<>(request.getPage(), request.getPageSize());
        
        IPage<ContentTemplate> pageResult = baseMapper.selectTemplatesByCategory(
            page, 
            request.getCategory(), 
            request.getStatus(),
            CURRENT_USER_ID,
            CURRENT_DEPT_ID
        );
        
        return convertToDTO(pageResult);
    }
    
    @Override
    public IPage<ContentTemplateDTO> searchTemplates(ContentTemplateRequestVO request) {
        if (!StringUtils.hasText(request.getKeyword())) {
            throw new RuntimeException("搜索关键词不能为空");
        }
        
        Page<ContentTemplate> page = new Page<>(request.getPage(), request.getPageSize());
        
        IPage<ContentTemplate> pageResult = baseMapper.searchTemplates(
            page, 
            request.getKeyword(), 
            request.getCategory(), 
            request.getPermission(),
            CURRENT_USER_ID,
            CURRENT_DEPT_ID
        );
        
        return convertToDTO(pageResult);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplateStatus(Long id, String status) {
        if (!"active".equals(status) && !"inactive".equals(status)) {
            throw new RuntimeException("状态值不正确，只能是active或inactive");
        }
        
        // 检查模板是否存在且有权限修改
        ContentTemplate template = baseMapper.selectTemplateByIdWithPermission(
            id, CURRENT_USER_ID, CURRENT_DEPT_ID
        );
        
        if (template == null) {
            throw new RuntimeException("模板不存在或无权限修改");
        }
        
        // 检查权限：只有创建者或管理员可以修改状态
        if (!template.getCreatorId().equals(CURRENT_USER_ID)) {
            throw new RuntimeException("无权限修改此模板状态");
        }
        
        UpdateWrapper<ContentTemplate> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                    .set("status", status)
                    .set("update_time", new Date());
        
        boolean success = update(updateWrapper);
        
        if (success) {
            log.info("更新模板状态成功，ID: {}, 新状态: {}, 操作人: {}", 
                    id, status, CURRENT_USER_NAME);
        }
        
        return success;
    }
    
    // ========== 私有辅助方法 ==========
    
    /**
     * 转换分页结果为DTO
     */
    private IPage<ContentTemplateDTO> convertToDTO(IPage<ContentTemplate> pageResult) {
        Page<ContentTemplateDTO> dtoPage = new Page<>(pageResult.getCurrent(), pageResult.getSize());
        dtoPage.setTotal(pageResult.getTotal());
        
        List<ContentTemplateDTO> dtoList = pageResult.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }
    
    /**
     * 转换实体为DTO
     */
    private ContentTemplateDTO convertToDTO(ContentTemplate template) {
        ContentTemplateDTO dto = new ContentTemplateDTO();
        BeanUtils.copyProperties(template, dto);
        
        // 格式化时间字段
        if (template.getLastUsedTime() != null) {
            dto.setLastUsedTime(DATE_TIME_FORMATTER.format(template.getLastUsedTime()));
        }
        if (template.getCreateTime() != null) {
            dto.setCreateTime(DATE_TIME_FORMATTER.format(template.getCreateTime()));
        }
        if (template.getUpdateTime() != null) {
            dto.setUpdateTime(DATE_TIME_FORMATTER.format(template.getUpdateTime()));
        }
        
        return dto;
    }
    
    /**
     * 转换分类实体为DTO
     */
    private ContentTemplateCategoryDTO convertCategoryToDTO(ContentTemplateCategory category) {
        ContentTemplateCategoryDTO dto = new ContentTemplateCategoryDTO();
        BeanUtils.copyProperties(category, dto);
        return dto;
    }
    
    /**
     * 校验模板请求参数
     */
    private void validateTemplateRequest(ContentTemplateRequestVO request, boolean isUpdate) {
        if (!StringUtils.hasText(request.getName())) {
            throw new RuntimeException("模板名称不能为空");
        }
        if (request.getName().length() > 50) {
            throw new RuntimeException("模板名称不能超过50个字符");
        }
        
        if (!isUpdate) { // 创建时必填字段
            if (!StringUtils.hasText(request.getCategory())) {
                throw new RuntimeException("模板分类不能为空");
            }
            if (!StringUtils.hasText(request.getPermission())) {
                throw new RuntimeException("权限范围不能为空");
            }
            if (!StringUtils.hasText(request.getMessageType())) {
                throw new RuntimeException("消息类型不能为空");
            }
            if (!StringUtils.hasText(request.getTitle())) {
                throw new RuntimeException("内容标题不能为空");
            }
            if (!StringUtils.hasText(request.getContent())) {
                throw new RuntimeException("模板内容不能为空");
            }
        }
        
        if (StringUtils.hasText(request.getTitle()) && request.getTitle().length() > 200) {
            throw new RuntimeException("内容标题不能超过200个字符");
        }
        
        if (StringUtils.hasText(request.getDescription()) && request.getDescription().length() > 1000) {
            throw new RuntimeException("模板说明不能超过1000个字符");
        }
    }
    
    /**
     * 从HTML中提取纯文本用于搜索
     */
    private String extractTextFromHtml(String html) {
        if (!StringUtils.hasText(html)) {
            return "";
        }
        
        // 简单的HTML标签移除，实际项目中可以使用Jsoup等库
        return html.replaceAll("<[^>]+>", "")
                  .replaceAll("&nbsp;", " ")
                  .replaceAll("&amp;", "&")
                  .replaceAll("&lt;", "<")
                  .replaceAll("&gt;", ">")
                  .trim();
    }
} 
package com.nx.inf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.dto.NursingHomeInstitutionDTO;
import com.nx.inf.model.entity.NursingHomeInstitution;
import com.nx.inf.model.vo.NursingHomeRequestVO;
import com.nx.inf.model.vo.PageResponseVO;

import java.util.List;

/**
 * 养老院机构服务接口
 */
public interface NursingHomeInstitutionService extends IService<NursingHomeInstitution> {
    
    /**
     * 分页获取机构列表
     * 
     * @param request 请求参数
     * @return 分页结果
     */
    PageResponseVO<NursingHomeInstitutionDTO> getInstitutionList(NursingHomeRequestVO request);
    
    /**
     * 根据ID获取机构详情
     * 
     * @param id 机构ID
     * @return 机构详情
     */
    NursingHomeInstitutionDTO getInstitutionById(Long id);
    
    /**
     * 创建养老院机构
     * 
     * @param request 请求参数
     * @return 创建的机构ID
     */
    Long createInstitution(NursingHomeRequestVO request);
    
    /**
     * 更新养老院机构
     * 
     * @param request 请求参数
     * @return 是否成功
     */
    boolean updateInstitution(NursingHomeRequestVO request);
    
    /**
     * 删除养老院机构（逻辑删除）
     * 
     * @param id 机构ID
     * @return 是否成功
     */
    boolean deleteInstitution(Long id);
    
    /**
     * 批量删除机构（逻辑删除）
     * 
     * @param ids 机构ID列表
     * @return 删除的数量
     */
    int batchDeleteInstitutions(List<Long> ids);
    
    /**
     * 更新机构状态
     * 
     * @param id 机构ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateInstitutionStatus(Long id, Integer status);
    
    /**
     * 批量更新机构状态
     * 
     * @param ids 机构ID列表
     * @param status 状态值
     * @return 更新的数量
     */
    int batchUpdateStatus(List<Long> ids, Integer status);
    
    /**
     * 根据机构代码查询机构
     * 
     * @param code 机构代码
     * @return 机构信息
     */
    NursingHomeInstitution getByCode(String code);
    
    /**
     * 检查机构代码是否已存在
     * 
     * @param code 机构代码
     * @param excludeId 排除的机构ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isCodeExists(String code, Long excludeId);
    
    /**
     * 根据机构名称查询机构（模糊搜索）
     * 
     * @param name 机构名称
     * @return 机构列表
     */
    List<NursingHomeInstitution> getByNameLike(String name);
    
    /**
     * 获取所有启用状态的机构
     * 
     * @return 机构列表
     */
    List<NursingHomeInstitutionDTO> getAllActiveInstitutions();
    
    /**
     * 统计机构数量
     * 
     * @param status 状态过滤
     * @param level 等级过滤
     * @param province 省份过滤
     * @param city 城市过滤
     * @return 机构数量
     */
    Long countInstitutions(Integer status, String level, String province, String city);
    
    /**
     * 更新机构入住人数
     * 
     * @param id 机构ID
     * @param currentPatients 当前入住人数
     * @return 是否成功
     */
    boolean updateCurrentPatients(Long id, Integer currentPatients);
    
    /**
     * 根据地理位置查询附近机构
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param radius 半径（公里）
     * @param limit 返回数量限制
     * @return 附近机构列表
     */
    List<NursingHomeInstitutionDTO> getNearbyInstitutions(String longitude, String latitude, 
                                                          Double radius, Integer limit);
    
    /**
     * 复制机构信息
     * 
     * @param id 源机构ID
     * @return 新机构信息
     */
    NursingHomeInstitutionDTO copyInstitution(Long id);
    
    /**
     * 导入机构数据
     * 
     * @param institutionList 机构数据列表
     * @return 导入结果信息
     */
    String importInstitutions(List<NursingHomeRequestVO> institutionList);
    
    /**
     * 导出机构数据
     * 
     * @param request 查询条件
     * @return 机构数据列表
     */
    List<NursingHomeInstitutionDTO> exportInstitutions(NursingHomeRequestVO request);
} 
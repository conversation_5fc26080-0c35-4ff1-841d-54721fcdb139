package com.nx.inf.service;

import com.nx.inf.model.entity.Questionnaire;
import com.nx.inf.model.dto.QuestionnaireDTO;

import java.util.List;

public interface QuestionnaireService {
    Questionnaire createQuestionnaire(Questionnaire questionnaire);
    Questionnaire updateQuestionnaire(Questionnaire questionnaire);
    void deleteQuestionnaire(Long id);
    Questionnaire getQuestionnaireById(Long id);
    List<Questionnaire> getAllQuestionnaires();
    
    // DTO方法
    QuestionnaireDTO createQuestionnaireWithDetails(QuestionnaireDTO questionnaireDTO);
    QuestionnaireDTO updateQuestionnaireWithDetails(QuestionnaireDTO questionnaireDTO);
    QuestionnaireDTO getQuestionnaireWithDetails(Long id);
    List<QuestionnaireDTO> getAllQuestionnairesWithDetails();
}

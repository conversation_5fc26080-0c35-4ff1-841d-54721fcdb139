package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.FollowupCUserMapper;
import com.nx.inf.model.entity.FollowupCUser;
import com.nx.inf.service.FollowupCUserService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class FollowupCUserServiceImpl extends ServiceImpl<FollowupCUserMapper, FollowupCUser> implements FollowupCUserService {
    @Override
    public FollowupCUser register(FollowupCUser user) {
        QueryWrapper<FollowupCUser> wrapper = new QueryWrapper<>();
        wrapper.eq("phone", user.getPhone());
        if (this.getOne(wrapper) != null) {
            throw new RuntimeException("手机号已注册");
        }
        this.save(user);
        return user;
    }

    @Override
    public boolean updateBindStatus(String phone, Integer isBind) {
        QueryWrapper<FollowupCUser> wrapper = new QueryWrapper<>();
        wrapper.eq("phone", phone);
        FollowupCUser user = this.getOne(wrapper);
        if (user == null) return false;
        user.setIsBind(isBind);
        return this.updateById(user);
    }

    @Override
    public boolean updateAuthStatus(String phone, Integer isAuth) {
        QueryWrapper<FollowupCUser> wrapper = new QueryWrapper<>();
        wrapper.eq("phone", phone);
        FollowupCUser user = this.getOne(wrapper);
        if (user == null) return false;
        user.setIsAuth(isAuth);
        return this.updateById(user);
    }

    @Override
    public boolean updateLoginInfo(Long userId, String lastLoginTime, String lastLoginIp) {
        FollowupCUser user = this.getById(userId);
        if (user == null) return false;
        
        if (lastLoginTime != null && !lastLoginTime.isEmpty()) {
            try {
                // 这里可以根据实际的时间格式进行解析
                // 暂时使用当前时间
                user.setLastLoginTime(new Date());
            } catch (Exception e) {
                user.setLastLoginTime(new Date());
            }
        } else {
            user.setLastLoginTime(new Date());
        }
        
        user.setLastLoginIp(lastLoginIp);
        user.setUpdateTime(new Date());
        
        return this.updateById(user);
    }

    @Override
    public boolean updateUserInfo(FollowupCUser user) {
        if (user.getId() == null) return false;
        
        user.setUpdateTime(new Date());
        return this.updateById(user);
    }

    @Override
    public FollowupCUser getUserByPhone(String phone) {
        LambdaQueryWrapper<FollowupCUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FollowupCUser::getPhone, phone);
        return this.getOne(wrapper);
    }

    @Override
    public IPage<FollowupCUser> getUserPage(int page, int size, String name, String phone, 
                                          String bindStatus, String authStatus) {
        Page<FollowupCUser> pageable = new Page<>(Math.max(0, page), size);
        LambdaQueryWrapper<FollowupCUser> wrapper = new LambdaQueryWrapper<>();
        
        if (name != null && !name.isEmpty()) {
            wrapper.like(FollowupCUser::getName, name);
        }
        if (phone != null && !phone.isEmpty()) {
            wrapper.eq(FollowupCUser::getPhone, phone);
        }
        if (bindStatus != null && !bindStatus.isEmpty()) {
            wrapper.eq(FollowupCUser::getBindStatus, bindStatus);
        }
        if (authStatus != null && !authStatus.isEmpty()) {
            wrapper.eq(FollowupCUser::getAuthStatus, authStatus);
        }
        
        wrapper.orderByDesc(FollowupCUser::getCreateTime);
        
        return this.page(pageable, wrapper);
    }
} 
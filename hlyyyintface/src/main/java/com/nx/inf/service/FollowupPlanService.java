package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import com.nx.inf.model.dto.FollowupPlanDTO;
import com.nx.inf.model.entity.FollowupPlan;

import java.util.List;
import java.util.Map;

/**
 * 随访计划服务接口
 */
public interface FollowupPlanService extends IService<FollowupPlan> {
    
    /**
     * 保存随访计划（包含子计划、项目、问卷）
     * 
     * @param followupPlanDTO 随访计划DTO
     * @return 计划ID
     */
    Long saveFollowupPlan(FollowupPlanDTO followupPlanDTO);
    
    /**
     * 更新随访计划（包含子计划、项目、问卷）
     * 
     * @param followupPlanDTO 随访计划DTO
     * @return 是否成功
     */
    boolean updateFollowupPlan(FollowupPlanDTO followupPlanDTO);
    
    /**
     * 分页查询随访计划
     * 
     * @param page 页码
     * @param size 每页数量
     * @param category 分类
     * @param status 状态
     * @param name 名称
     * @return 分页结果
     */
    IPage<FollowupPlanDTO> getFollowupPlanPage(int page, int size, String category, String status, String name);
    
    /**
     * 根据ID获取随访计划（包含子计划、项目、问卷）
     * 
     * @param id 计划ID
     * @return 随访计划DTO
     */
    FollowupPlanDTO getFollowupPlanById(Long id);
    
    /**
     * 删除随访计划
     * 
     * @param id 计划ID
     * @return 是否成功
     */
    boolean deleteFollowupPlan(Long id);
    
    /**
     * 更新计划状态
     * 
     * @param id 计划ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updatePlanStatus(Long id, String status);
    
    /**
     * 生成入组申请二维码
     * 
     * @param id 计划ID
     * @return 二维码URL
     */
    String generateQRCode(Long id);
    
    /**
     * 更新计划统计信息
     * 
     * @param id 计划ID
     */
    void updatePlanStatistics(Long id);
    
    /**
     * 批量更新所有计划统计信息
     */
    void updateAllPlanStatistics();
    
    // C端相关方法
    
    /**
     * 获取患者参与的随访计划列表
     * 
     * @param page 分页参数
     * @param patientId 患者ID
     * @param status 状态筛选
     * @return 分页结果
     */
    IPage<FollowupPlanDTO> getPatientPlans(Page<FollowupPlanDTO> page, Long patientId, String status);
    
    /**
     * 获取随访计划详情（C端）
     * 
     * @param planId 计划ID
     * @return 计划详情
     */
    FollowupPlanDTO getPlanDetail(Long planId);
    
    /**
     * 获取患者任务列表
     * 
     * @param page 分页参数
     * @param patientId 患者ID
     * @param status 状态筛选
     * @return 分页结果
     */
    IPage<Map<String, Object>> getPatientTasks(Page<Map<String, Object>> page, Long patientId, String status);
    
    /**
     * 获取患者统计信息
     * 
     * @param patientId 患者ID
     * @return 统计信息
     */
    Map<String, Object> getPatientStats(Long patientId);
    
    /**
     * 验证二维码参数
     * 
     * @param phone 手机号
     * @param groupId 分组ID
     * @param groupName 分组名称
     * @return 是否有效
     */
    boolean validateQRCode(String phone, Long groupId, String groupName);

    // 医护端相关方法

    /**
     * 分页查询随访计划列表（医护端）
     */
    IPage<FollowupPlanDTO> getPlanPage(Page<FollowupPlan> page, String name, String category, String status);

    /**
     * 根据ID获取随访计划详情（医护端）
     */
    FollowupPlanDTO getPlanById(Long id);

    /**
     * 创建随访计划（医护端）
     */
    FollowupPlan createPlan(FollowupPlanDTO planDTO);

    /**
     * 更新随访计划（医护端）
     */
    FollowupPlan updatePlan(FollowupPlanDTO planDTO);

    /**
     * 删除随访计划（医护端）
     */
    void deletePlan(Long id);

    /**
     * 批量删除随访计划（医护端）
     */
    void batchDeletePlans(List<Long> ids);

    /**
     * 获取计划统计信息（医护端）
     */
    Map<String, Object> getPlanStats(Long id);

    /**
     * 获取计划执行统计（医护端）
     */
    Map<String, Object> getPlanExecutionStats(Long planId);

    /**
     * 复制随访计划（医护端）
     */
    FollowupPlan copyPlan(Long id);

    /**
     * 导出计划数据（医护端）
     */
    Map<String, Object> exportPlanData(Long id);
} 
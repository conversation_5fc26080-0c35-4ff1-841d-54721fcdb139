package com.nx.inf.service.impl;

import com.nx.inf.mapper.QuestionnaireMapper;
import com.nx.inf.model.entity.Questionnaire;
import com.nx.inf.model.entity.QuestionnaireQuestion;
import com.nx.inf.model.entity.QuestionnaireOption;
import com.nx.inf.model.entity.QuestionnaireRule;
import com.nx.inf.model.dto.QuestionnaireDTO;
import com.nx.inf.model.dto.QuestionDTO;
import com.nx.inf.model.dto.OptionDTO;
import com.nx.inf.model.dto.QuestionnaireRuleDTO;
import com.nx.inf.service.QuestionnaireService;
import com.nx.inf.service.QuestionnaireQuestionService;
import com.nx.inf.service.QuestionnaireOptionService;
import com.nx.inf.service.QuestionnaireRuleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;

@Service
public class QuestionnaireServiceImpl implements QuestionnaireService {

    @Autowired
    private QuestionnaireMapper questionnaireMapper;
    
    @Autowired
    private QuestionnaireQuestionService questionService;
    
    @Autowired
    private QuestionnaireOptionService optionService;
    
    @Autowired
    private QuestionnaireRuleService ruleService;

    @Override
    public Questionnaire createQuestionnaire(Questionnaire questionnaire) {
        questionnaireMapper.insert(questionnaire);
        return questionnaire;
    }

    @Override
    public Questionnaire updateQuestionnaire(Questionnaire questionnaire) {
        questionnaireMapper.updateById(questionnaire);
        return questionnaireMapper.selectById(questionnaire.getId());
    }

    @Override
    public void deleteQuestionnaire(Long id) {
        questionnaireMapper.deleteById(id);
    }

    @Override
    public Questionnaire getQuestionnaireById(Long id) {
        return questionnaireMapper.selectById(id);
    }

    @Override
    public List<Questionnaire> getAllQuestionnaires() {
        return questionnaireMapper.selectAll();
    }
    
    @Override
    @Transactional
    public QuestionnaireDTO createQuestionnaireWithDetails(QuestionnaireDTO questionnaireDTO) {
        try {
            // 保存问卷基本信息
            Questionnaire questionnaire = new Questionnaire();
            BeanUtils.copyProperties(questionnaireDTO, questionnaire);
            questionnaire.setCreateTime(new Date());
            questionnaire.setUpdateTime(new Date());
            questionnaireMapper.insert(questionnaire);
            
            // 保存题目和选项
            if (questionnaireDTO.getQuestions() != null) {
                for (int i = 0; i < questionnaireDTO.getQuestions().size(); i++) {
                    QuestionDTO questionDTO = questionnaireDTO.getQuestions().get(i);
                    QuestionnaireQuestion question = new QuestionnaireQuestion();
                    BeanUtils.copyProperties(questionDTO, question);
                    question.setQuestionnaireId(questionnaire.getId());
                    question.setSort(i + 1);
                    questionService.createQuestion(question);
                    
                    // 保存选项
                    if (questionDTO.getOptions() != null) {
                        for (int j = 0; j < questionDTO.getOptions().size(); j++) {
                            OptionDTO optionDTO = questionDTO.getOptions().get(j);
                            QuestionnaireOption option = new QuestionnaireOption();
                            BeanUtils.copyProperties(optionDTO, option);
                            option.setQuestionId(question.getId());
                            option.setSort(j + 1);
                            optionService.createOption(option);
                        }
                    }
                }
            }
            
            return getQuestionnaireWithDetails(questionnaire.getId());
        } catch (Exception e) {
            throw new RuntimeException("创建问卷失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public QuestionnaireDTO updateQuestionnaireWithDetails(QuestionnaireDTO questionnaireDTO) {
        try {
            // 更新问卷基本信息
            Questionnaire questionnaire = new Questionnaire();
            BeanUtils.copyProperties(questionnaireDTO, questionnaire);
            questionnaire.setUpdateTime(new Date());
            questionnaireMapper.updateById(questionnaire);
            
            // 删除原有题目和选项（使用批量删除提高效率）
            List<QuestionnaireQuestion> existingQuestions = questionService.getQuestionsByQuestionnaireId(questionnaire.getId());
            
            // 先删除所有选项
            for (QuestionnaireQuestion question : existingQuestions) {
                optionService.deleteOptionsByQuestionId(question.getId());
            }
            
            // 再删除所有题目
            questionService.deleteQuestionsByQuestionnaireId(questionnaire.getId());
            
            // 保存新的题目和选项
            if (questionnaireDTO.getQuestions() != null) {
                for (int i = 0; i < questionnaireDTO.getQuestions().size(); i++) {
                    QuestionDTO questionDTO = questionnaireDTO.getQuestions().get(i);
                    QuestionnaireQuestion question = new QuestionnaireQuestion();
                    BeanUtils.copyProperties(questionDTO, question);
                    question.setQuestionnaireId(questionnaire.getId());
                    question.setSort(i + 1);
                    questionService.createQuestion(question);
                    
                    // 保存选项
                    if (questionDTO.getOptions() != null) {
                        for (int j = 0; j < questionDTO.getOptions().size(); j++) {
                            OptionDTO optionDTO = questionDTO.getOptions().get(j);
                            QuestionnaireOption option = new QuestionnaireOption();
                            BeanUtils.copyProperties(optionDTO, option);
                            option.setQuestionId(question.getId());
                            option.setSort(j + 1);
                            optionService.createOption(option);
                        }
                    }
                }
            }
            
            return getQuestionnaireWithDetails(questionnaire.getId());
        } catch (Exception e) {
            throw new RuntimeException("更新问卷失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public QuestionnaireDTO getQuestionnaireWithDetails(Long id) {
        try {
            Questionnaire questionnaire = questionnaireMapper.selectById(id);
            if (questionnaire == null) {
                return null;
            }
            
            QuestionnaireDTO questionnaireDTO = new QuestionnaireDTO();
            BeanUtils.copyProperties(questionnaire, questionnaireDTO);
            
            // 获取题目列表
            List<QuestionnaireQuestion> questions = questionService.getQuestionsByQuestionnaireId(id);
            List<QuestionDTO> questionDTOs = questions.stream().map(question -> {
                QuestionDTO questionDTO = new QuestionDTO();
                BeanUtils.copyProperties(question, questionDTO);
                
                // 获取选项列表
                List<QuestionnaireOption> options = optionService.getOptionsByQuestionId(question.getId());
                List<OptionDTO> optionDTOs = options.stream().map(option -> {
                    OptionDTO optionDTO = new OptionDTO();
                    BeanUtils.copyProperties(option, optionDTO);
                    return optionDTO;
                }).collect(Collectors.toList());
                questionDTO.setOptions(optionDTOs);
                
                return questionDTO;
            }).collect(Collectors.toList());
            questionnaireDTO.setQuestions(questionDTOs);
            
            return questionnaireDTO;
        } catch (Exception e) {
            throw new RuntimeException("获取问卷详情失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<QuestionnaireDTO> getAllQuestionnairesWithDetails() {
        try {
            List<Questionnaire> questionnaires = questionnaireMapper.selectAll();
            return questionnaires.stream().map(questionnaire -> 
                getQuestionnaireWithDetails(questionnaire.getId())
            ).collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("获取问卷列表失败: " + e.getMessage(), e);
        }
    }
}

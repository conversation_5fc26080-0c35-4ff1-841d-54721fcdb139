package com.nx.inf.service;

import java.util.Map;

/**
 * 任务调度服务接口
 */
public interface TaskSchedulerService {

    /**
     * 为计划生成任务
     */
    void generateTasksForPlan(Long planId);

    /**
     * 发送任务提醒消息
     */
    int sendReminders(Long[] taskIds);

    /**
     * 更新任务状态
     */
    void updateTaskStatus(Long taskId, String status);

    /**
     * 批量更新任务状态
     */
    int batchUpdateTaskStatus(Long[] taskIds, String status);

    /**
     * 获取调度统计信息
     */
    Map<String, Object> getSchedulerStats();

    /**
     * 获取计划调度统计
     */
    Map<String, Object> getPlanSchedulerStats(Long planId);

    /**
     * 启动任务调度器
     */
    void startScheduler();

    /**
     * 停止任务调度器
     */
    void stopScheduler();

    /**
     * 获取调度器状态
     */
    Map<String, Object> getSchedulerStatus();

    /**
     * 重新调度任务
     */
    void rescheduleTask(Long taskId);

    /**
     * 清理过期任务
     */
    int cleanupExpiredTasks();

    /**
     * 获取任务执行日志
     */
    Map<String, Object> getTaskExecutionLogs(Integer page, Integer size, Long taskId, Long planId);
} 
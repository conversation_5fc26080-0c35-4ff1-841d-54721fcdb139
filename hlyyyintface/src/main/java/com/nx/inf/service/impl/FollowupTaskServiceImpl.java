package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.FollowupTaskMapper;
import com.nx.inf.model.entity.FollowupTask;
import com.nx.inf.model.entity.FollowupPlan;
import com.nx.inf.model.entity.FollowupSubplan;
import com.nx.inf.model.entity.PatientGroupRelation;
import com.nx.inf.service.FollowupTaskService;
import com.nx.inf.service.FollowupPlanService;
import com.nx.inf.service.FollowupSubplanService;
import com.nx.inf.service.PatientGroupService;
import com.nx.inf.mapper.PatientGroupRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 随访任务服务实现
 */
@Service
public class FollowupTaskServiceImpl extends ServiceImpl<FollowupTaskMapper, FollowupTask> implements FollowupTaskService {
    
    @Autowired
    private FollowupPlanService followupPlanService;
    
    @Autowired
    private FollowupSubplanService followupSubplanService;
    
    @Autowired
    private PatientGroupService patientGroupService;
    
    @Autowired
    private PatientGroupRelationMapper patientGroupRelationMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateTasks(Long planId) {
        try {
            // 获取计划信息
            FollowupPlan plan = followupPlanService.getById(planId);
            if (plan == null) {
                return false;
            }
            
            // 获取计划的子计划列表
            LambdaQueryWrapper<FollowupSubplan> subplanWrapper = new LambdaQueryWrapper<>();
            subplanWrapper.eq(FollowupSubplan::getPlanId, planId);
            List<FollowupSubplan> subplans = followupSubplanService.list(subplanWrapper);
            
            // 获取计划关联的患者分组（这里需要根据实际情况查询）
            // 暂时使用空列表，实际应该根据计划ID查询关联的患者
            List<PatientGroupRelation> relations = new ArrayList<>();
            
            // 为每个患者生成任务
            for (PatientGroupRelation relation : relations) {
                for (FollowupSubplan subplan : subplans) {
                    FollowupTask task = new FollowupTask();
                    task.setPlanId(planId);
                    task.setSubplanId(subplan.getId());
                    task.setPatientId(relation.getPatientId());
                    task.setStatus("pending");
                    task.setCreateTime(new Date());
                    task.setUpdateTime(new Date());
                    
                    // 根据子计划类型设置任务类型
                    task.setScheduledTime(new Date()); // 使用scheduledTime字段
                    
                    this.save(task);
                }
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskStatus(Long taskId, String status) {
        try {
            FollowupTask task = this.getById(taskId);
            if (task == null) {
                return false;
            }
            
            task.setStatus(status);
            task.setUpdateTime(new Date());
            
            return this.updateById(task);
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public Map<String, Object> getPlanExecutionStats(Long planId) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 查询该计划下的所有任务
            LambdaQueryWrapper<FollowupTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FollowupTask::getPlanId, planId);
            List<FollowupTask> tasks = this.list(wrapper);
            
            // 统计各种状态的任务数量
            long totalTasks = tasks.size();
            long pendingTasks = tasks.stream().filter(t -> "pending".equals(t.getStatus())).count();
            long completedTasks = tasks.stream().filter(t -> "completed".equals(t.getStatus())).count();
            long failedTasks = tasks.stream().filter(t -> "failed".equals(t.getStatus())).count();
            long inProgressTasks = tasks.stream().filter(t -> "executing".equals(t.getStatus())).count();
            
            stats.put("totalTasks", totalTasks);
            stats.put("pendingTasks", pendingTasks);
            stats.put("completedTasks", completedTasks);
            stats.put("failedTasks", failedTasks);
            stats.put("inProgressTasks", inProgressTasks);
            
            // 计算完成率
            double completionRate = totalTasks > 0 ? (double) completedTasks / totalTasks * 100 : 0;
            stats.put("completionRate", completionRate);
            
        } catch (Exception e) {
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
    
} 
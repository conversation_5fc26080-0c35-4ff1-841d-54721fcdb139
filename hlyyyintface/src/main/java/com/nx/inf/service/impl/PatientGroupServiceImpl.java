package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.nx.inf.mapper.PatientGroupMapper;
import com.nx.inf.mapper.PatientGroupRelationMapper;
import com.nx.inf.mapper.PatientMapper;
import com.nx.inf.mapper.FollowupCUserMapper;
import com.nx.inf.model.dto.PatientDTO;
import com.nx.inf.model.dto.PatientGroupDTO;
import com.nx.inf.model.entity.Patient;
import com.nx.inf.model.entity.PatientGroup;
import com.nx.inf.model.entity.PatientGroupRelation;
import com.nx.inf.model.entity.FollowupCUser;

import com.nx.inf.service.PatientGroupService;
import com.nx.inf.utils.UserContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;

/**
 * 患者分组服务实现
 */
@Service
public class PatientGroupServiceImpl extends ServiceImpl<PatientGroupMapper, PatientGroup> implements PatientGroupService {

    @Autowired
    private PatientMapper patientMapper;
    
    @Autowired
    private PatientGroupRelationMapper relationMapper;

    @Autowired
    private FollowupCUserMapper followupCUserMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPatientGroup(PatientGroupDTO patientGroupDTO) {
        PatientGroup patientGroup = new PatientGroup();
        BeanUtils.copyProperties(patientGroupDTO, patientGroup);
        
        // 从用户上下文获取创建者信息
        patientGroup.setCreatorId(UserContext.getCurrentUserId());
        patientGroup.setCreatorName(UserContext.getCurrentUserName());
        patientGroup.setPatientCount(0);
        patientGroup.setCreateTime(new Date());
        patientGroup.setUpdateTime(new Date());
        
        baseMapper.insert(patientGroup);
        return patientGroup.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePatientGroup(PatientGroupDTO patientGroupDTO) {
        PatientGroup patientGroup = baseMapper.selectById(patientGroupDTO.getId());
        if (patientGroup == null) {
            return false;
        }
        
        BeanUtils.copyProperties(patientGroupDTO, patientGroup);
        patientGroup.setUpdateTime(new Date());
        
        return baseMapper.updateById(patientGroup) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePatientGroup(Long id) {
        // 先删除关联关系
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getGroupId, id);
        relationMapper.delete(wrapper);
        
        // 再删除分组
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public PatientGroupDTO getPatientGroupById(Long id) {
        PatientGroup patientGroup = baseMapper.selectById(id);
        if (patientGroup == null) {
            return null;
        }
        
        PatientGroupDTO dto = new PatientGroupDTO();
        BeanUtils.copyProperties(patientGroup, dto);
        
        return dto;
    }

    @Override
    public IPage<PatientGroupDTO> getPatientGroupPage(int page, int size, String name) {
        // 创建分页对象，MyBatis-Plus的Page对象页码从0开始
        Page<PatientGroup> pageable = new Page<>(Math.max(0, page), size);
        
        // 调用Mapper查询分页数据
        IPage<PatientGroup> groupPage = baseMapper.selectGroupPage(pageable, name);
        
        // 将分页结果转换为DTO
        IPage<PatientGroupDTO> result = groupPage.convert(group -> {
            PatientGroupDTO dto = new PatientGroupDTO();
            BeanUtils.copyProperties(group, dto);
            return dto;
        });
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPatientsToGroup(Long groupId, List<Long> patientIds, String joinMethod) {
        PatientGroup patientGroup = baseMapper.selectById(groupId);
        if (patientGroup == null || patientIds == null || patientIds.isEmpty()) {
            return false;
        }
        
        // 查询已存在的关联
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getGroupId, groupId)
               .in(PatientGroupRelation::getPatientId, patientIds);
        List<PatientGroupRelation> existRelations = relationMapper.selectList(wrapper);
        
        // 提取已存在的患者ID
        List<Long> existPatientIds = existRelations.stream()
                .map(PatientGroupRelation::getPatientId)
                .collect(Collectors.toList());
        
        // 过滤出需要新增的患者ID
        List<Long> newPatientIds = patientIds.stream()
                .filter(id -> !existPatientIds.contains(id))
                .collect(Collectors.toList());
        
        // 新增关联
        for (Long patientId : newPatientIds) {
            PatientGroupRelation relation = new PatientGroupRelation();
            relation.setGroupId(groupId);
            relation.setPatientId(patientId);
            relation.setJoinTime(new Date());
            relation.setJoinMethod(joinMethod);
            // 设置创建者信息
            relation.setCreatorId(UserContext.getCurrentUserId());
            relation.setCreatorName(UserContext.getCurrentUserName());
            relationMapper.insert(relation);
        }
        
        // 更新分组的患者数量
        baseMapper.updatePatientCount(groupId);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removePatientsFromGroup(Long groupId, List<Long> patientIds) {
        if (groupId == null || patientIds == null || patientIds.isEmpty()) {
            return false;
        }
        
        // 批量删除关联
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getGroupId, groupId)
               .in(PatientGroupRelation::getPatientId, patientIds);
        relationMapper.delete(wrapper);
        
        // 更新分组的患者数量
        baseMapper.updatePatientCount(groupId);
        
        return true;
    }

    @Override
    public List<PatientGroupDTO> getPatientGroups(Long patientId) {
        // 查询患者关联的分组ID
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getPatientId, patientId);
        List<PatientGroupRelation> relations = relationMapper.selectList(wrapper);
        
        if (relations.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取分组ID列表
        List<Long> groupIds = relations.stream()
                .map(PatientGroupRelation::getGroupId)
                .collect(Collectors.toList());
        
        // 批量查询分组信息
        List<PatientGroup> patientGroups = baseMapper.selectBatchIds(groupIds);
        
        // 转换为DTO
        return patientGroups.stream().map(patientGroup -> {
            PatientGroupDTO dto = new PatientGroupDTO();
            BeanUtils.copyProperties(patientGroup, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public IPage<PatientDTO> getPatientsByGroupId(int page, int size, Long groupId, String status, String name, String idCard, String phone, String disease) {
        // 1. 查询分组下所有关联的C端用户ID
        Page<FollowupCUser> pageable = new Page<>(Math.max(0, page), size);
        LambdaQueryWrapper<PatientGroupRelation> relWrapper = new LambdaQueryWrapper<>();
        relWrapper.eq(PatientGroupRelation::getGroupId, groupId);
        List<PatientGroupRelation> relations = relationMapper.selectList(relWrapper);
        if (relations == null || relations.isEmpty()) {
            // 没有关联数据，直接返回空分页
            return new Page<>();
        }
        List<Long> userIds = relations.stream().map(PatientGroupRelation::getPatientId).collect(Collectors.toList());
        // 2. 构建C端用户查询条件
        LambdaQueryWrapper<FollowupCUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(FollowupCUser::getId, userIds);
        if (name != null && !name.isEmpty()) userWrapper.like(FollowupCUser::getName, name);
        if (phone != null && !phone.isEmpty()) userWrapper.eq(FollowupCUser::getPhone, phone);
        if (idCard != null && !idCard.isEmpty()) userWrapper.eq(FollowupCUser::getIdNumber, idCard);
        // 3. 分页查C端用户
        IPage<FollowupCUser> userPage = followupCUserMapper.selectPage(pageable, userWrapper);
        // 4. 转为PatientDTO
        IPage<PatientDTO> result = userPage.convert(user -> {
            PatientDTO dto = new PatientDTO();
            dto.setId(user.getId());
            dto.setName(user.getName());
            dto.setAge(user.getAge());
            dto.setGender(user.getGender());
            dto.setPhone(user.getPhone());
            dto.setIdCard(user.getIdNumber());
            dto.setRemark("C端用户");
            // 设置分组信息
            dto.setGroups(java.util.Collections.singletonList(new PatientDTO.PatientGroupSimpleDTO() {{
                setId(groupId);
                // 分组名称可查group表
            }}));
            // 设置入组方式和时间
            PatientGroupRelation rel = relations.stream().filter(r -> r.getPatientId().equals(user.getId())).findFirst().orElse(null);
            if (rel != null) {
                dto.setJoinMethod(rel.getJoinMethod());
                dto.setJoinTime(rel.getJoinTime());
                dto.setStatus(rel.getStatus());
            }
            return dto;
        });
        return result;
    }

    @Override
    public IPage<PatientDTO> getAllPatients(int page, int size, String status, String name, String idCard, String phone, String disease) {
        // 只查C端用户
        Page<FollowupCUser> pageable = new Page<>(Math.max(0, page), size);
        LambdaQueryWrapper<FollowupCUser> userWrapper = new LambdaQueryWrapper<>();
        if (name != null && !name.isEmpty()) userWrapper.like(FollowupCUser::getName, name);
        if (phone != null && !phone.isEmpty()) userWrapper.eq(FollowupCUser::getPhone, phone);
        if (idCard != null && !idCard.isEmpty()) userWrapper.eq(FollowupCUser::getIdNumber, idCard);
        IPage<FollowupCUser> userPage = followupCUserMapper.selectPage(pageable, userWrapper);
        IPage<PatientDTO> result = userPage.convert(user -> {
            PatientDTO dto = new PatientDTO();
            dto.setId(user.getId());
            dto.setName(user.getName());
            dto.setAge(user.getAge());
            dto.setGender(user.getGender());
            dto.setPhone(user.getPhone());
            dto.setIdCard(user.getIdNumber());
            dto.setRemark("C端用户");
            return dto;
        });
        return result;
    }

    @Override
    public void updatePatientCount(Long groupId) {
        baseMapper.updatePatientCount(groupId);
    }

    @Override
    public List<PatientGroupDTO> getAllGroupsWithJoinStatus(Long patientId) {
        // 查询所有分组
        List<PatientGroup> allGroups = baseMapper.selectList(null);
        // 查询已入组分组
        LambdaQueryWrapper<PatientGroupRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PatientGroupRelation::getPatientId, patientId);
        List<PatientGroupRelation> relations = relationMapper.selectList(wrapper);
        Map<Long, PatientGroupRelation> joinedMap = relations.stream()
            .collect(Collectors.toMap(PatientGroupRelation::getGroupId, r -> r));
        // 合并结果
        List<PatientGroupDTO> result = new ArrayList<>();
        for (PatientGroup group : allGroups) {
            PatientGroupDTO dto = new PatientGroupDTO();
            BeanUtils.copyProperties(group, dto);
            PatientGroupRelation rel = joinedMap.get(group.getId());
            if (rel != null) {
                dto.setJoined(true);
                dto.setJoinTime(rel.getJoinTime());
                dto.setStatus(rel.getStatus());
            } else {
                dto.setJoined(false);
            }
            result.add(dto);
        }
        return result;
    }

    @Override
    public IPage<PatientDTO> getAllJoinedPatients(int page, int size, String status, String name, String idCard, String phone, String disease) {
        // 查询所有有分组关联的患者ID
        Page<FollowupCUser> pageable = new Page<>(Math.max(0, page), size);
        
        // 先根据状态过滤关联关系
        LambdaQueryWrapper<PatientGroupRelation> relWrapper = new LambdaQueryWrapper<>();
        if (status != null && !status.isEmpty()) {
            relWrapper.eq(PatientGroupRelation::getStatus, status);
        }
        List<PatientGroupRelation> relations = relationMapper.selectList(relWrapper);
        
        if (relations == null || relations.isEmpty()) {
            return new Page<>();
        }
        
        // 用 patientId 做映射，方便后续查找 joinTime
        Map<Long, PatientGroupRelation> relMap = relations.stream().collect(Collectors.toMap(PatientGroupRelation::getPatientId, r -> r, (a, b) -> a));
        List<Long> userIds = relations.stream().map(PatientGroupRelation::getPatientId).distinct().collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return new Page<>();
        }
        
        LambdaQueryWrapper<FollowupCUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.in(FollowupCUser::getId, userIds);
        if (name != null && !name.isEmpty()) userWrapper.like(FollowupCUser::getName, name);
        if (phone != null && !phone.isEmpty()) userWrapper.eq(FollowupCUser::getPhone, phone);
        if (idCard != null && !idCard.isEmpty()) userWrapper.eq(FollowupCUser::getIdNumber, idCard);
        
        IPage<FollowupCUser> userPage = followupCUserMapper.selectPage(pageable, userWrapper);
        IPage<PatientDTO> result = userPage.convert(user -> {
            PatientDTO dto = new PatientDTO();
            dto.setId(user.getId());
            dto.setName(user.getName());
            dto.setAge(user.getAge());
            dto.setGender(user.getGender());
            dto.setPhone(user.getPhone());
            dto.setIdCard(user.getIdNumber());
            dto.setRemark("C端用户");
            // 设置入组时间
            PatientGroupRelation rel = relMap.get(user.getId());
            if (rel != null) {
                dto.setJoinTime(rel.getJoinTime());
                dto.setStatus(rel.getStatus());
                dto.setJoinMethod(rel.getJoinMethod());
            }
            return dto;
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditPatientJoinRequest(Long relationId, String auditResult, Long auditorId, 
                                         String auditorName, String auditRemark) {
        PatientGroupRelation relation = relationMapper.selectById(relationId);
        if (relation == null) {
            return false;
        }
        
        // 更新审核相关字段
        relation.setAuditorId(auditorId);
        relation.setAuditorName(auditorName);
        relation.setAuditRemark(auditRemark);
        relation.setAuditTime(new Date());
        
        // 根据审核结果设置状态
        if ("approved".equals(auditResult)) {
            relation.setStatus("approved");
            relation.setUserAgree(true);
            relation.setDoctorAgree(true);
            relation.setAgreeTime(new Date());
        } else if ("rejected".equals(auditResult)) {
            relation.setStatus("rejected");
            relation.setUserAgree(false);
            relation.setDoctorAgree(false);
        }
        
        relation.setUpdateTime(new Date());
        
        return relationMapper.updateById(relation) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAuditPatientJoinRequest(List<Long> relationIds, String auditResult, Long auditorId,
                                          String auditorName, String auditRemark) {
        if (relationIds == null || relationIds.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (Long relationId : relationIds) {
            if (auditPatientJoinRequest(relationId, auditResult, auditorId, auditorName, auditRemark)) {
                successCount++;
            }
        }
        
        return successCount;
    }
} 
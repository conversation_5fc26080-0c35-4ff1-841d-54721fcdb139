package com.nx.inf.service;

import com.nx.inf.model.vo.MedicalRequestVO;
import com.nx.inf.model.vo.HealthMeasurementVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 医疗API服务接口
 * @Author: zhongx
 * @CreateDate: 2024/3/28
 * @Version: 1.0
 */
public interface MedicalApiService {

    /**
     * 获取患者检查报告
     * @param request 请求参数（患者ID、开始时间、结束时间）
     * @return 检查报告数据
     */
    Map<String, Object> getPatientExaminationReports(MedicalRequestVO request);

    /**
     * 获取患者检验报告
     * @param request 请求参数（患者ID、开始时间、结束时间）
     * @return 检验报告数据
     */
    Map<String, Object> getPatientTestReports(MedicalRequestVO request);

    /**
     * 获取患者门诊病历
     * @param request 请求参数（患者ID、开始时间、结束时间）
     * @return 门诊病历数据
     */
    Map<String, Object> getOutpatientRecords(MedicalRequestVO request);

    /**
     * 获取患者住院记录
     * @param request 请求参数（患者ID、开始时间、结束时间）
     * @return 住院记录数据
     */
    Map<String, Object> getInpatientRecords(MedicalRequestVO request);

    /**
     * 根据身份证号查询电话号码
     * @param idCard 身份证号
     * @return 电话号码
     */
    String getPhoneByIdCard(String idCard);

    /**
     * 根据身份证号获取患者卡信息
     * @param sfzh 身份证号
     * @return 患者卡信息
     */
    Map<String, Object> getPatientCardByIdCard(String sfzh);

    /**
     * 新增患者健康测量记录
     * @param measurement 健康测量数据
     * @return 是否添加成功
     */
    boolean addHealthMeasurement(HealthMeasurementVO measurement);

    /**
     * 获取患者健康测量记录
     * @param sfzh 身份证号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 健康测量记录列表
     */
    List<HealthMeasurementVO> getHealthRecords(String sfzh, String startTime, String endTime);

    /**
     * 删除患者健康测量记录
     * @param id 记录ID
     * @param sfzh 身份证号
     * @return 是否删除成功
     */
    boolean deleteHealthRecord(Long id, String sfzh);

    /**
     * 生成第三方医疗影像系统跳转URL
     * @param sfzh 身份证号
     * @param hsCode 医院编码
     * @param accessKey 接口访问密钥
     * @param accessSecret 接口访问密钥秘钥
     * @param aesKey AES加密密钥
     * @param aesIv AES加密偏移量
     * @return 跳转URL
     */
    String generateImagingRedirectUrl(String sfzh, String hsCode, String accessKey, 
                                     String accessSecret, String aesKey, String aesIv);
} 
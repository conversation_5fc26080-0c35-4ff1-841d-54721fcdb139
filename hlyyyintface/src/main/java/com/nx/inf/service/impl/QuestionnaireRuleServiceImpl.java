package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.QuestionnaireRuleMapper;
import com.nx.inf.model.entity.QuestionnaireRule;
import com.nx.inf.service.QuestionnaireRuleService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class QuestionnaireRuleServiceImpl extends ServiceImpl<QuestionnaireRuleMapper, QuestionnaireRule>
    implements QuestionnaireRuleService {

    @Override
    public List<QuestionnaireRule> getRulesByQuestionId(Long questionId) {
        QueryWrapper<QuestionnaireRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("question_id", questionId);
        return this.list(queryWrapper);
    }
}

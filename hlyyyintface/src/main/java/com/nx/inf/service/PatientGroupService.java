package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import com.nx.inf.model.dto.PatientDTO;
import com.nx.inf.model.dto.PatientGroupDTO;
import com.nx.inf.model.entity.PatientGroup;

import java.util.List;

/**
 * 患者分组服务接口
 */
public interface PatientGroupService extends IService<PatientGroup> {
    
    /**
     * 创建患者分组
     * 
     * @param patientGroupDTO 患者分组DTO
     * @return 分组ID
     */
    Long createPatientGroup(PatientGroupDTO patientGroupDTO);
    
    /**
     * 更新患者分组
     * 
     * @param patientGroupDTO 患者分组DTO
     * @return 是否成功
     */
    boolean updatePatientGroup(PatientGroupDTO patientGroupDTO);
    
    /**
     * 删除患者分组
     * 
     * @param id 分组ID
     * @return 是否成功
     */
    boolean deletePatientGroup(Long id);
    
    /**
     * 根据ID获取患者分组
     * 
     * @param id 分组ID
     * @return 患者分组DTO
     */
    PatientGroupDTO getPatientGroupById(Long id);
    
    /**
     * 分页查询患者分组
     * 
     * @param page 页码
     * @param size 每页数量
     * @param name 组名
     * @return 分页结果
     */
    IPage<PatientGroupDTO> getPatientGroupPage(int page, int size, String name);
    
    /**
     * 向分组添加患者
     * 
     * @param groupId 分组ID
     * @param patientIds 患者ID列表
     * @param joinMethod 加入方式
     * @return 是否成功
     */
    boolean addPatientsToGroup(Long groupId, List<Long> patientIds, String joinMethod);
    
    /**
     * 从分组移除患者
     * 
     * @param groupId 分组ID
     * @param patientIds 患者ID列表
     * @return 是否成功
     */
    boolean removePatientsFromGroup(Long groupId, List<Long> patientIds);
    
    /**
     * 查询患者所属的分组列表
     * 
     * @param patientId 患者ID
     * @return 分组列表
     */
    List<PatientGroupDTO> getPatientGroups(Long patientId);
    
    /**
     * 分页查询某个分组下的患者
     * 
     * @param page 页码
     * @param size 每页数量
     * @param groupId 分组ID
     * @param status 状态
     * @param name 姓名
     * @param idCard 证件号码
     * @param phone 手机号
     * @param disease 疾病
     * @return 分页结果
     */
    IPage<PatientDTO> getPatientsByGroupId(int page, int size, Long groupId, String status,
                                           String name, String idCard, String phone, String disease);
    
    /**
     * 分页查询所有患者（不指定分组）
     * 
     * @param page 页码
     * @param size 每页数量
     * @param status 状态
     * @param name 姓名
     * @param idCard 证件号码
     * @param phone 手机号
     * @param disease 疾病
     * @return 分页结果
     */
    IPage<PatientDTO> getAllPatients(int page, int size, String status,
                                    String name, String idCard, String phone, String disease);
    
    /**
     * 分页查询所有已入组的患者
     * 
     * @param page 页码
     * @param size 每页数量
     * @param status 状态
     * @param name 姓名
     * @param idCard 证件号码
     * @param phone 手机号
     * @param disease 疾病
     * @return 分页结果
     */
    IPage<PatientDTO> getAllJoinedPatients(int page, int size, String status,
                                          String name, String idCard, String phone, String disease);
    
    /**
     * 审核患者入组申请
     * 
     * @param relationId 关联记录ID
     * @param auditResult 审核结果：approved-通过，rejected-拒绝
     * @param auditorId 审核人ID
     * @param auditorName 审核人姓名
     * @param auditRemark 审核备注
     * @return 是否成功
     */
    boolean auditPatientJoinRequest(Long relationId, String auditResult, Long auditorId, 
                                   String auditorName, String auditRemark);
    
    /**
     * 批量审核患者入组申请
     * 
     * @param relationIds 关联记录ID列表
     * @param auditResult 审核结果：approved-通过，rejected-拒绝
     * @param auditorId 审核人ID
     * @param auditorName 审核人姓名
     * @param auditRemark 审核备注
     * @return 成功审核的数量
     */
    int batchAuditPatientJoinRequest(List<Long> relationIds, String auditResult, Long auditorId,
                                    String auditorName, String auditRemark);

    /**
     * 更新分组的患者数量
     */
    void updatePatientCount(Long groupId);

    /**
     * 查询患者所有参与的分组列表及其加入状态
     * 
     * @param patientId 患者ID
     * @return 分组列表及其加入状态
     */
    List<PatientGroupDTO> getAllGroupsWithJoinStatus(Long patientId);

} 
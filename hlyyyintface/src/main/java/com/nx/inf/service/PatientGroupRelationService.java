package com.nx.inf.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.entity.PatientGroupRelation;

import java.util.List;
import java.util.Map;

/**
 * 患者分组关系服务接口
 */
public interface PatientGroupRelationService extends IService<PatientGroupRelation> {
    
    /**
     * 根据患者ID获取分组关系
     */
    List<PatientGroupRelation> getByPatientId(Long patientId);
    
    /**
     * 根据分组ID获取患者列表
     */
    List<PatientGroupRelation> getByGroupId(Long groupId);
    
    /**
     * 检查患者是否在分组中
     */
    boolean isPatientInGroup(Long patientId, Long groupId);
    
    /**
     * 审核患者入组申请
     */
    boolean auditPatientJoinRequest(Long patientId, Long groupId, String status, String auditRemark);
    
    /**
     * 批量审核患者入组申请
     */
    boolean batchAuditPatientJoinRequest(List<Map<String, Object>> auditList);
} 
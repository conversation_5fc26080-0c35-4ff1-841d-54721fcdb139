package com.nx.inf.service;

import com.nx.inf.model.entity.QuestionnaireOption;
import java.util.List;

public interface QuestionnaireOptionService {
    QuestionnaireOption createOption(QuestionnaireOption option);
    QuestionnaireOption updateOption(QuestionnaireOption option);
    void deleteOption(Long id);
    void deleteOptionsByQuestionId(Long questionId);
    QuestionnaireOption getOptionById(Long id);
    List<QuestionnaireOption> getOptionsByQuestionId(Long questionId);
    List<QuestionnaireOption> getAllOptions();
}

package com.nx.inf.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.nx.inf.mapper.FollowUpExecutionMapper;
import com.nx.inf.mapper.FollowupPlanMapper;
import com.nx.inf.mapper.PatientMapper;
import com.nx.inf.model.dto.FollowUpExecutionDTO;
import com.nx.inf.model.entity.FollowUpExecution;
import com.nx.inf.model.entity.FollowupPlan;
import com.nx.inf.model.entity.Patient;

import com.nx.inf.service.FollowUpExecutionService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 随访执行记录服务实现
 */
@Service
public class FollowUpExecutionServiceImpl extends ServiceImpl<FollowUpExecutionMapper, FollowUpExecution> implements FollowUpExecutionService {

    @Autowired
    private FollowupPlanMapper followupPlanMapper;
    
    @Autowired
    private PatientMapper patientMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFollowUpExecution(FollowUpExecutionDTO followUpExecutionDTO) {
        FollowUpExecution execution = new FollowUpExecution();
        BeanUtils.copyProperties(followUpExecutionDTO, execution);
        
        // 设置默认值
        execution.setCreateTime(LocalDateTime.now());
        execution.setUpdateTime(LocalDateTime.now());
        
        // 如果只有患者ID，尝试获取患者信息
        if (execution.getPatientId() != null && (execution.getPatientName() == null || execution.getPatientGender() == null)) {
            Patient patient = patientMapper.selectById(execution.getPatientId());
            if (patient != null) {
                execution.setPatientName(patient.getName());
                execution.setPatientAge(patient.getAge());
                execution.setPatientGender(patient.getGender());
            }
        }
        
        baseMapper.insert(execution);
        
        return execution.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFollowUpExecution(FollowUpExecutionDTO followUpExecutionDTO) {
        FollowUpExecution execution = baseMapper.selectById(followUpExecutionDTO.getId());
        if (execution == null) {
            return false;
        }
        
        BeanUtils.copyProperties(followUpExecutionDTO, execution);
        execution.setUpdateTime(LocalDateTime.now());
        
        return baseMapper.updateById(execution) > 0;
    }

    @Override
    public FollowUpExecutionDTO getFollowUpExecutionById(Long id) {
        FollowUpExecution execution = baseMapper.selectById(id);
        if (execution == null) {
            return null;
        }
        
        FollowUpExecutionDTO dto = new FollowUpExecutionDTO();
        BeanUtils.copyProperties(execution, dto);
        
        // 查询并设置计划标题
        FollowupPlan plan = followupPlanMapper.selectPlanById(execution.getPlanId());
        if (plan != null) {
            dto.setPlanTitle(plan.getName());
        }
        
        return dto;
    }

    @Override
    public IPage<FollowUpExecutionDTO> getExecutionPage(int page, int size, Long planId, String patientName, Boolean warningFlag) {
        Page<FollowUpExecution> queryPage = new Page<>(page, size);
        IPage<FollowUpExecution> executionPage = baseMapper.selectExecutionPage(queryPage, planId, patientName, warningFlag);
        
        // 转换为DTO
        IPage<FollowUpExecutionDTO> resultPage = executionPage.convert(execution -> {
            FollowUpExecutionDTO dto = new FollowUpExecutionDTO();
            BeanUtils.copyProperties(execution, dto);
            
            // 查询并设置计划标题
            FollowupPlan plan = followupPlanMapper.selectPlanById(execution.getPlanId());
            if (plan != null) {
                dto.setPlanTitle(plan.getName());
            }
            
            return dto;
        });
        
        return resultPage;
    }

    @Override
    public List<FollowUpExecutionDTO> getExecutionsByPlanId(Long planId) {
        List<FollowUpExecution> executions = baseMapper.selectByPlanId(planId);
        
        // 转换为DTO
        return executions.stream().map(execution -> {
            FollowUpExecutionDTO dto = new FollowUpExecutionDTO();
            BeanUtils.copyProperties(execution, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FollowUpExecutionDTO> getExecutionsByPatientId(Long patientId) {
        List<FollowUpExecution> executions = baseMapper.selectByPatientId(patientId);
        
        // 转换为DTO
        return executions.stream().map(execution -> {
            FollowUpExecutionDTO dto = new FollowUpExecutionDTO();
            BeanUtils.copyProperties(execution, dto);
            
            // 查询并设置计划标题
            FollowupPlan plan = followupPlanMapper.selectPlanById(execution.getPlanId());
            if (plan != null) {
                dto.setPlanTitle(plan.getName());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FollowUpExecutionDTO> getPatientsByPlanId(Long planId) {
        List<FollowUpExecution> patients = baseMapper.selectPatientsByPlanId(planId);
        
        // 转换为DTO
        return patients.stream().map(execution -> {
            FollowUpExecutionDTO dto = new FollowUpExecutionDTO();
            BeanUtils.copyProperties(execution, dto);
            
            // 查询并设置计划标题
            FollowupPlan plan = followupPlanMapper.selectPlanById(execution.getPlanId());
            if (plan != null) {
                dto.setPlanTitle(plan.getName());
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExecutionStatus(Long id, String status) {
        FollowUpExecution execution = baseMapper.selectById(id);
        if (execution == null) {
            return false;
        }
        
        execution.setStatus(status);
        execution.setUpdateTime(LocalDateTime.now());
        
        return baseMapper.updateById(execution) > 0;
    }
} 
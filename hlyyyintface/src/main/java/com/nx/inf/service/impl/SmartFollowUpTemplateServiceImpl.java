package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.SmartFollowUpTemplateMapper;
import com.nx.inf.model.dto.SmartFollowUpTemplateDTO;
import com.nx.inf.model.entity.SmartFollowUpTemplate;
import com.nx.inf.model.vo.SmartFollowUpTemplateRequestVO;
import com.nx.inf.service.SmartFollowUpTemplateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能随访模板服务实现
 */
@Service
public class SmartFollowUpTemplateServiceImpl extends ServiceImpl<SmartFollowUpTemplateMapper, SmartFollowUpTemplate> 
        implements SmartFollowUpTemplateService {

    @Override
    public IPage<SmartFollowUpTemplateDTO> getTemplatePage(SmartFollowUpTemplateRequestVO request) {
        // 构建查询条件
        QueryWrapper<SmartFollowUpTemplate> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(request.getName())) {
            queryWrapper.like("name", request.getName());
        }
        
        if (StringUtils.hasText(request.getCategory())) {
            queryWrapper.eq("category", request.getCategory());
        }
        
        if (request.getStatus() != null) {
            queryWrapper.eq("status", request.getStatus());
        }
        
        if (request.getCreatorId() != null) {
            queryWrapper.eq("creator_id", request.getCreatorId());
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc("create_time");
        
        // 分页查询
        Page<SmartFollowUpTemplate> page = new Page<>(request.getPage(), request.getSize());
        IPage<SmartFollowUpTemplate> templatePage = this.page(page, queryWrapper);
        
        // 转换为DTO
        List<SmartFollowUpTemplateDTO> dtoList = templatePage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        // 构建返回结果
        Page<SmartFollowUpTemplateDTO> resultPage = new Page<>(request.getPage(), request.getSize());
        resultPage.setRecords(dtoList);
        resultPage.setTotal(templatePage.getTotal());
        resultPage.setCurrent(templatePage.getCurrent());
        resultPage.setSize(templatePage.getSize());
        
        return resultPage;
    }

    @Override
    public Long createTemplate(SmartFollowUpTemplateDTO templateDTO) {
        SmartFollowUpTemplate template = new SmartFollowUpTemplate();
        BeanUtils.copyProperties(templateDTO, template);
        
        // 设置默认值
        if (template.getStatus() == null) {
            template.setStatus(1); // 默认启用
        }
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        
        // 保存到数据库
        this.save(template);
        
        return template.getId();
    }

    @Override
    public boolean updateTemplate(SmartFollowUpTemplateDTO templateDTO) {
        SmartFollowUpTemplate template = this.getById(templateDTO.getId());
        if (template == null) {
            return false;
        }
        
        BeanUtils.copyProperties(templateDTO, template);
        template.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(template);
    }

    @Override
    public SmartFollowUpTemplateDTO getTemplateById(Long id) {
        SmartFollowUpTemplate template = this.getById(id);
        if (template == null) {
            return null;
        }
        
        return convertToDTO(template);
    }

    @Override
    public boolean deleteTemplate(Long id) {
        SmartFollowUpTemplate template = this.getById(id);
        if (template == null) {
            return false;
        }
        
        // 软删除：设置状态为删除
        template.setStatus(0);
        template.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(template);
    }

    @Override
    public boolean updateTemplateStatus(Long id, Integer status) {
        SmartFollowUpTemplate template = this.getById(id);
        if (template == null) {
            return false;
        }
        
        template.setStatus(status);
        template.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(template);
    }
    
    /**
     * 转换为DTO
     */
    private SmartFollowUpTemplateDTO convertToDTO(SmartFollowUpTemplate template) {
        SmartFollowUpTemplateDTO dto = new SmartFollowUpTemplateDTO();
        BeanUtils.copyProperties(template, dto);
        
        // 设置分类名称
        if ("1".equals(template.getCategory())) {
            dto.setCategoryName("术后随访");
        } else if ("2".equals(template.getCategory())) {
            dto.setCategoryName("慢病管理");
        } else if ("3".equals(template.getCategory())) {
            dto.setCategoryName("健康体检");
        } else {
            dto.setCategoryName("其他");
        }
        
        return dto;
    }
} 
package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nx.inf.model.dto.ImageTextRecordDTO;
import com.nx.inf.model.entity.ImageTextRecord;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图片文字识别记录服务接口
 */
public interface ImageTextRecordService extends IService<ImageTextRecord> {
    
    /**
     * 上传图片并进行文字识别
     * 
     * @param file 上传的图片文件
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @return 记录ID
     */
    Long uploadAndRecognize(MultipartFile file, Long creatorId, String creatorName);
    
    /**
     * 根据ID获取记录详情
     * 
     * @param id 记录ID
     * @return 记录DTO
     */
    ImageTextRecordDTO getRecordById(Long id);
    
    /**
     * 分页查询记录列表
     * 
     * @param page 页码
     * @param size 每页数量
     * @param status 处理状态
     * @param creatorId 创建人ID
     * @param originalFilename 原始文件名
     * @return 分页结果
     */
    IPage<ImageTextRecordDTO> getRecordPage(int page, int size, String status, 
                                          Long creatorId, String originalFilename);
    
    /**
     * 根据创建人ID查询记录列表
     * 
     * @param creatorId 创建人ID
     * @return 记录列表
     */
    List<ImageTextRecordDTO> getRecordsByCreatorId(Long creatorId);
    
    /**
     * 根据状态查询记录列表
     * 
     * @param status 处理状态
     * @return 记录列表
     */
    List<ImageTextRecordDTO> getRecordsByStatus(String status);
    
    /**
     * 删除记录（逻辑删除）
     * 
     * @param id 记录ID
     * @return 是否成功
     */
    boolean deleteRecord(Long id);
    
    /**
     * 重新识别图片文字
     * 
     * @param id 记录ID
     * @return 是否成功
     */
    boolean reRecognize(Long id);
}

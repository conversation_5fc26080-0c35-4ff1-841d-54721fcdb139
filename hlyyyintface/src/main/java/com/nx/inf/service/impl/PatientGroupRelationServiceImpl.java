package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.PatientGroupRelationMapper;
import com.nx.inf.model.entity.PatientGroupRelation;
import com.nx.inf.service.PatientGroupRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 患者分组关系服务实现类
 */
@Service
public class PatientGroupRelationServiceImpl extends ServiceImpl<PatientGroupRelationMapper, PatientGroupRelation> implements PatientGroupRelationService {

    @Override
    public List<PatientGroupRelation> getByPatientId(Long patientId) {
        QueryWrapper<PatientGroupRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("patient_id", patientId);
        return list(queryWrapper);
    }

    @Override
    public List<PatientGroupRelation> getByGroupId(Long groupId) {
        QueryWrapper<PatientGroupRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", groupId);
        return list(queryWrapper);
    }

    @Override
    public boolean isPatientInGroup(Long patientId, Long groupId) {
        QueryWrapper<PatientGroupRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("patient_id", patientId)
                   .eq("group_id", groupId)
                   .eq("status", "approved");
        return count(queryWrapper) > 0;
    }

    @Override
    @Transactional
    public boolean auditPatientJoinRequest(Long patientId, Long groupId, String status, String auditRemark) {
        QueryWrapper<PatientGroupRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("patient_id", patientId)
                   .eq("group_id", groupId);
        
        PatientGroupRelation relation = getOne(queryWrapper);
        if (relation == null) {
            return false;
        }
        
        relation.setStatus(status);
        relation.setAuditRemark(auditRemark);
        relation.setAuditTime(new Date());
        
        return updateById(relation);
    }

    @Override
    @Transactional
    public boolean batchAuditPatientJoinRequest(List<Map<String, Object>> auditList) {
        for (Map<String, Object> audit : auditList) {
            Long patientId = Long.valueOf(audit.get("patientId").toString());
            Long groupId = Long.valueOf(audit.get("groupId").toString());
            String status = (String) audit.get("status");
            String auditRemark = (String) audit.get("auditRemark");
            
            if (!auditPatientJoinRequest(patientId, groupId, status, auditRemark)) {
                return false;
            }
        }
        return true;
    }
} 
package com.nx.inf.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.NhHospitalizationInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 住院信息服务接口
 */
public interface NhHospitalizationInfoService {

    /**
     * 保存住院信息
     *
     * @param nhHospitalizationInfo 住院信息
     * @return 是否保存成功
     */
    boolean save(NhHospitalizationInfo nhHospitalizationInfo);

    /**
     * 根据ID更新住院信息
     *
     * @param nhHospitalizationInfo 住院信息
     * @return 是否更新成功
     */
    boolean updateById(NhHospitalizationInfo nhHospitalizationInfo);

    /**
     * 根据ID删除住院信息
     *
     * @param id 主键ID
     * @return 是否删除成功
     */
    boolean removeById(Long id);

    /**
     * 根据ID获取住院信息
     *
     * @param id 主键ID
     * @return 住院信息
     */
    NhHospitalizationInfo getById(Long id);

    /**
     * 分页查询住院信息
     *
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<NhHospitalizationInfo> page(Page<NhHospitalizationInfo> page);

    /**
     * 根据病人ID获取住院信息
     *
     * @param patientId 病人ID
     * @return 住院信息
     */
    NhHospitalizationInfo getByPatientId(String patientId);

    /**
     * 根据科室统计在院病人数量
     *
     * @return 科室及对应的病人数量
     */
    List<Map<String, Object>> countByDepartment();

    /**
     * 查询指定日期范围内的入院记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 住院信息列表
     */
    List<NhHospitalizationInfo> getByAdmissionDateRange(LocalDateTime startDate, LocalDateTime endDate);

    String genInHospitalId();
}
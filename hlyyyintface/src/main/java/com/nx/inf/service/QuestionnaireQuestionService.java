package com.nx.inf.service;

import com.nx.inf.model.entity.QuestionnaireQuestion;
import java.util.List;

public interface QuestionnaireQuestionService {
    QuestionnaireQuestion createQuestion(QuestionnaireQuestion question);
    QuestionnaireQuestion updateQuestion(QuestionnaireQuestion question);
    void deleteQuestion(Long id);
    void deleteQuestionsByQuestionnaireId(Long questionnaireId);
    QuestionnaireQuestion getQuestionById(Long id);
    List<QuestionnaireQuestion> getQuestionsByQuestionnaireId(Long questionnaireId);
    List<QuestionnaireQuestion> getAllQuestions();
}

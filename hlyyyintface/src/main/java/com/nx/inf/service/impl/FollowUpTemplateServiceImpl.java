package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.FollowUpTemplateMapper;
import com.nx.inf.model.dto.FollowUpTemplateDTO;
import com.nx.inf.model.entity.FollowUpTemplate;
import com.nx.inf.service.FollowUpTemplateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 随访计划模板服务实现
 */
@Service
public class FollowUpTemplateServiceImpl extends ServiceImpl<FollowUpTemplateMapper, FollowUpTemplate> 
        implements FollowUpTemplateService {

    @Override
    public List<FollowUpTemplateDTO> getTemplateList(String name, String category) {
        List<FollowUpTemplate> templateList = baseMapper.selectTemplateList(name, category);
        
        // 转换为DTO
        return templateList.stream().map(template -> {
            FollowUpTemplateDTO dto = new FollowUpTemplateDTO();
            BeanUtils.copyProperties(template, dto);
            return dto;
        }).collect(Collectors.toList());
    }
} 
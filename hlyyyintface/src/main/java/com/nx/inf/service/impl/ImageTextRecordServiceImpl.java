package com.nx.inf.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nx.inf.mapper.ImageTextRecordMapper;
import com.nx.inf.model.dto.ImageTextRecordDTO;
import com.nx.inf.model.entity.ImageTextRecord;
import com.nx.inf.service.ImageTextRecordService;
import com.nx.inf.utils.FileUploadUtil;
import com.nx.inf.utils.BaiduOCRUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 图片文字识别记录服务实现
 */
@Service
public class ImageTextRecordServiceImpl extends ServiceImpl<ImageTextRecordMapper, ImageTextRecord>
        implements ImageTextRecordService {

    private static final Logger logger = LoggerFactory.getLogger(ImageTextRecordServiceImpl.class);

    @Autowired
    private FileUploadUtil fileUploadUtil;

    // 不再需要注入OCRUtil，直接使用静态方法
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long uploadAndRecognize(MultipartFile file, Long creatorId, String creatorName) {
        logger.info("开始处理图片上传和文字识别，文件名: {}, 创建人: {}", 
                   file.getOriginalFilename(), creatorName);
        
        // 创建记录
        ImageTextRecord record = new ImageTextRecord();
        record.setOriginalFilename(file.getOriginalFilename());
        record.setFileSize(file.getSize());
        record.setFileType(file.getContentType());
        record.setStatus("processing");
        record.setCreatorId(creatorId);
        record.setCreatorName(creatorName);
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        record.setIsDeleted(0);
        
        try {
            // 1. 上传文件
            String filePath = FileUploadUtil.uploadImage(file, null);
            record.setFilePath(filePath);
            
            // 2. 获取图片尺寸
            try {
                java.awt.image.BufferedImage image = javax.imageio.ImageIO.read(new java.io.File(filePath));
                if (image != null) {
                    record.setImageWidth(image.getWidth());
                    record.setImageHeight(image.getHeight());
                } else {
                    record.setImageWidth(0);
                    record.setImageHeight(0);
                }
            } catch (Exception e) {
                logger.warn("获取图片尺寸失败: {}", e.getMessage());
                record.setImageWidth(0);
                record.setImageHeight(0);
            }
            
            // 先保存记录
            baseMapper.insert(record);
            
            // 3. 异步进行OCR识别
            performOCRRecognition(record);
            
            logger.info("图片上传成功，记录ID: {}, 文件路径: {}", record.getId(), filePath);
            return record.getId();
            
        } catch (Exception e) {
            logger.error("图片上传和识别失败: {}", e.getMessage(), e);
            
            // 更新记录状态为失败
            record.setStatus("failed");
            record.setErrorMessage(e.getMessage());
            record.setUpdateTime(LocalDateTime.now());
            
            if (record.getId() != null) {
                baseMapper.updateById(record);
            } else {
                baseMapper.insert(record);
            }
            
            throw new RuntimeException("图片上传和识别失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行百度OCR识别
     */
    private void performOCRRecognition(ImageTextRecord record) {
        try {
            logger.info("开始百度OCR识别，记录ID: {}, 文件路径: {}", record.getId(), record.getFilePath());

            // 读取图片文件
            byte[] imageBytes = java.nio.file.Files.readAllBytes(java.nio.file.Paths.get(record.getFilePath()));

            // 执行百度OCR识别
            BaiduOCRUtil.OCRResult ocrResult = BaiduOCRUtil.recognizeText(imageBytes);

            // 更新记录
            record.setRecognizedText(ocrResult.getRecognizedText());
            record.setOcrEngine("百度OCR");
            record.setProcessingTime((int) ocrResult.getProcessingTime());
            record.setUpdateTime(LocalDateTime.now());

            if (ocrResult.isSuccess()) {
                record.setStatus("success");
                record.setConfidence(new java.math.BigDecimal("0.95")); // 百度OCR通常有较高置信度
                record.setErrorMessage(null);
            } else {
                record.setStatus("failed");
                record.setConfidence(new java.math.BigDecimal("0.00"));
                record.setErrorMessage(ocrResult.getErrorMessage());
            }

            baseMapper.updateById(record);

            logger.info("百度OCR识别完成，记录ID: {}, 状态: {}, 识别文字长度: {}",
                       record.getId(), record.getStatus(),
                       record.getRecognizedText() != null ? record.getRecognizedText().length() : 0);

        } catch (Exception e) {
            logger.error("百度OCR识别异常，记录ID: {}, 错误: {}", record.getId(), e.getMessage(), e);

            record.setStatus("failed");
            record.setErrorMessage("百度OCR识别异常: " + e.getMessage());
            record.setConfidence(new java.math.BigDecimal("0.00"));
            record.setUpdateTime(LocalDateTime.now());
            baseMapper.updateById(record);
        }
    }
    
    @Override
    public ImageTextRecordDTO getRecordById(Long id) {
        ImageTextRecord record = baseMapper.selectRecordById(id);
        if (record == null) {
            return null;
        }
        
        ImageTextRecordDTO dto = new ImageTextRecordDTO();
        BeanUtils.copyProperties(record, dto);
        return dto;
    }
    
    @Override
    public IPage<ImageTextRecordDTO> getRecordPage(int page, int size, String status, 
                                                 Long creatorId, String originalFilename) {
        Page<ImageTextRecord> queryPage = new Page<>(page, size);
        IPage<ImageTextRecord> recordPage = baseMapper.selectRecordPage(
                queryPage, status, creatorId, originalFilename);
        
        // 转换为DTO
        IPage<ImageTextRecordDTO> dtoPage = new Page<>(page, size, recordPage.getTotal());
        List<ImageTextRecordDTO> dtoList = recordPage.getRecords().stream()
                .map(record -> {
                    ImageTextRecordDTO dto = new ImageTextRecordDTO();
                    BeanUtils.copyProperties(record, dto);
                    return dto;
                })
                .collect(Collectors.toList());
        
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }
    
    @Override
    public List<ImageTextRecordDTO> getRecordsByCreatorId(Long creatorId) {
        List<ImageTextRecord> records = baseMapper.selectByCreatorId(creatorId);
        return records.stream()
                .map(record -> {
                    ImageTextRecordDTO dto = new ImageTextRecordDTO();
                    BeanUtils.copyProperties(record, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ImageTextRecordDTO> getRecordsByStatus(String status) {
        List<ImageTextRecord> records = baseMapper.selectByStatus(status);
        return records.stream()
                .map(record -> {
                    ImageTextRecordDTO dto = new ImageTextRecordDTO();
                    BeanUtils.copyProperties(record, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRecord(Long id) {
        ImageTextRecord record = baseMapper.selectRecordById(id);
        if (record == null) {
            return false;
        }
        
        // 逻辑删除
        record.setIsDeleted(1);
        record.setUpdateTime(LocalDateTime.now());
        
        // 删除文件
        if (record.getFilePath() != null) {
            FileUploadUtil.deleteFile(record.getFilePath());
        }
        
        return baseMapper.updateById(record) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reRecognize(Long id) {
        ImageTextRecord record = baseMapper.selectRecordById(id);
        if (record == null) {
            return false;
        }
        
        // 重置状态
        record.setStatus("processing");
        record.setRecognizedText(null);
        record.setConfidence(null);
        record.setProcessingTime(null);
        record.setErrorMessage(null);
        record.setUpdateTime(LocalDateTime.now());
        
        baseMapper.updateById(record);
        
        // 重新执行OCR识别
        performOCRRecognition(record);
        
        return true;
    }
}

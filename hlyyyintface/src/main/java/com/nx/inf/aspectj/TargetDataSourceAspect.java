package com.nx.inf.aspectj;

import com.nx.inf.annotation.TargetDataSource;
import com.nx.inf.annotation.DataSourceManagement;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @Description: 数据源切面
 * @Author: JinShiBo
 * @CreateDate: 2023/7/14 0:06
 * @Version: 1.0
 */
@Component
@Aspect
@Slf4j
public class TargetDataSourceAspect {

    @Before("@within(com.nx.inf.annotation.TargetDataSource) || @annotation(com.nx.inf.annotation.TargetDataSource)")
    public void beforeNoticeUpdateDataSource(JoinPoint joinPoint) {
        TargetDataSource annotation = null;
        Class<? extends Object> target = joinPoint.getTarget().getClass();
        if (target.isAnnotationPresent(TargetDataSource.class)) {
            // 判断类上是否标注着注解
            annotation = target.getAnnotation(TargetDataSource.class);
            log.info("类上标注了注解");
        } else {
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            if (method.isAnnotationPresent(TargetDataSource.class)) {
                // 判断方法上是否标注着注解，如果类和方法上都没有标注，则报错
                annotation = method.getAnnotation(TargetDataSource.class);
                log.info("方法上标注了注解");
            } else {
                throw new RuntimeException("@TargetDataSource注解只能用于类或者方法上, 错误出现在:[" +
                        target + " " + method + "];");
            }
        }
        // 切换数据源
        DataSourceManagement.flag.set(annotation.value().name());
    }
}

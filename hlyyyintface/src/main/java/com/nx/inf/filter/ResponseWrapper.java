package com.nx.inf.filter;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.*;

/**
 * @Description: 响应包装
 * @Author: JinShiBo
 * @CreateDate: 2023/9/10 22:56
 * @Version: 1.0
 */
public class ResponseWrapper extends HttpServletResponseWrapper {

    private ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    private PrintWriter printWriter = new PrintWriter(outputStream);
    public ResponseWrapper(HttpServletResponse response) {
        super(response);
    }
    @Override
    public PrintWriter getWriter() throws IOException {
        return printWriter;
    }
    @Override
    public ServletOutputStream getOutputStream() {
        return new ServletOutputStream() {
            @Override
            public boolean isReady() {
                return false;
            }
            @Override
            public void setWriteListener(WriteListener writeListener) {
            }
            @Override
            public void write(int b) throws IOException {
                outputStream.write(b);
            }
        };
    }
    public void flush() {
        try {
            printWriter.flush();
            printWriter.close();
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public ByteArrayOutputStream getByteArrayOutputStream(){
        return outputStream;
    }
    public String getTextContent() throws UnsupportedEncodingException {
        flush();
        return outputStream.toString("UTF-8");
    }
}

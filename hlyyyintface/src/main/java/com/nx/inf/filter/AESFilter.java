package com.nx.inf.filter;

import com.alibaba.fastjson.JSONObject;
import com.nx.inf.utils.encrypt.NXAESUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: AES加解密过滤器
 * @Author: JinShiBo
 * @CreateDate: 2023/9/10 22:09
 * @Version: 1.0
 */
@Slf4j
public class AESFilter implements Filter {

    /**
     * 需要过滤的地址
     */
    private static final List<String> URL_LIST = Arrays.asList("");

    /**
     * 是否需要过滤
     *
     * @param requestUrl 请求的url
     * @return
     */
    public boolean isPast(String requestUrl) {
        for (String url : URL_LIST) {
            if (requestUrl.equals(url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        // 获取请求地址
        String url = ((HttpServletRequest) request).getRequestURI().substring(((HttpServletRequest) request).getContextPath().length());
        if (!isPast(url)) {
            log.info("\nURL：{}, 进行入参解密", url);
            RequestWrapper requestWrapper = new RequestWrapper((HttpServletRequest) request, response);
            ResponseWrapper responseWrapper = new ResponseWrapper((HttpServletResponse) response);
            // 读取请求内容
            BufferedReader br = requestWrapper.getReader();
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            // 将json字符串转换为json对象
            JSONObject jsonObject = JSONObject.parseObject(sb.toString());
            String json = NXAESUtil.decrypt(jsonObject.get("param").toString());
            requestWrapper.setBody(json.getBytes(StandardCharsets.UTF_8));
            // 放行
            chain.doFilter(requestWrapper, responseWrapper);
            log.info("\nURL：{}, 进行出参加密", url);
            // 获取接口返回内容
            String resp = responseWrapper.getTextContent();
            // 加密处理返回
            response.getOutputStream().write(NXAESUtil.encrypt(resp).getBytes(StandardCharsets.UTF_8));
        } else {
            chain.doFilter(request, response);
        }
    }
}

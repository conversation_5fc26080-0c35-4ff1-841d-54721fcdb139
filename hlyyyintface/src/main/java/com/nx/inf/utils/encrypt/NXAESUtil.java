package com.nx.inf.utils.encrypt;


import cn.hutool.core.util.StrUtil;

public class NXAESUtil {

    /**
     * 掌上家医加密密钥
     */
    private static final String CRYPTO_KEY = "PY3SIEnDEUdHVKDL";

    /**
     * 数据解密（掌上家医）
     *
     * @param data
     * @return
     */
    public static String decrypt(String data) {
        return decrypt(data, CRYPTO_KEY);
    }

    /**
     * 数据加密（掌上家医）
     *
     * @param data
     * @return
     */
    public static String encrypt(String data) {
        return encrypt(data, CRYPTO_KEY);
    }

    /**
     * 数据加密
     *
     * @param data
     * @return
     */
    public static String encrypt(String data, String secret) {
        if (StrUtil.isEmpty(data)) {
            return null;
        }
        // 先解密，如果解密成功则是已经加密了，防止重复加密
        String decrypt = decrypt(data, secret);
        if (StrUtil.isNotEmpty(decrypt)) {
            return data;
        }
        String encrypt = AES.getInstance().encryptHex(data, secret);
        return encrypt.toUpperCase();
    }

    /**
     * 数据解密
     *
     * @param data
     * @param secret
     * @return
     */
    public static String decrypt(String data, String secret) {
        if (StrUtil.isEmpty(data)) {
            return null;
        }
        String encrypt = AES.getInstance().decryptHex(data, secret);
        return encrypt;
    }

    public static void main(String[] args) throws Exception {


        String en = NXAESUtil.encrypt("{\n" +
                "    \"idCard\": \"321025197506202238\"\n" +
                "}", "PY3SIEnDEUdHVKDL");
        String de = NXAESUtil.decrypt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nDEUdHVKDL");
        System.out.println(en);
        System.out.println(de);
    }

    /**
     * 运行结果
     * 7D0951358E4F208A6B6109C164C2BA9DA927170E56732FF88DF2E546289415D915CA39EB93A7FFCA08604285BD8D938D7699CF2D3A02A44B75599309444B050A227AAF26830FAB37B7087FA3ABFB977694670E541E69748C22062B8E88108E565088DFB7AF68F7075ACDC3C31D3797F0FDA1F0E1CB8E1C40D73DDD222C0F3468C80AA5A9073E49E4AA718C1BB0544810168658E007E5C5B07E214D4EAA9C852A50ECD138637614EC1E8D73B90C083A7F2432E5C907B675071DD075615C10B6F350ECD138637614EC1E8D73B90C083A7FD8176897D8633F753583D8F3749E8EFD08D7CE116F96240EDF1E7AA416D0DBA987F2DD84142954DB58D9B3BD22155AC905711EAC8015E67E61D70BE4778CF1A25240112C507E09204CF3F0B547C88B9FCE96129AD52D64545DD377FB56CFA127A6EF2F18D78359395284289FB2CF131F2318445C09677BA4B1EDD50EEF71566EFED2186BAB81D53DBE051C5FAAA6FBB3A78C0E886FB4CDA182A4D48C8A42257D4124393DC14612F98D6DBFBB06FEA3809E494EDC947DB8B35F85E48747D6A98203ED702813567F7BEACBE06874A6D68F89EE5890E911B864DB138E27A18C662B495F3011AB1749969303E1E49163E3E4664A699E9CCD4F948A59D4566143E2D2C13336310C0A7ABE429A664CD72B6323E13E9AA8E889808BEF9FB6EE9E924B0FCE466DCA2F2CD827B75BC01B5AE5A8814AE5A72DF2BA9AE04B3A4DA09572EFA7516B566622653486F91D845010E9CC773E5A7805E915DA22D06EF8986183CFB5455BB824E5F20D6D0667E0E8A7EE3A62286744E0223E672842EFC1106B94FEA6E54134377062D0C4D3FFAFF929B43461C13D750D7851570FBB37D35031FD9A74FA182094B33667453ECFCAE8C1040C80A14D5D40E74236C231F46E8604F8284D
     * {
     *             "scheduleDate": "20230930",
     *             "scheduleInterval": "下午",
     *             "timeInterval": "15:58   ",
     *             "communitySite": "中心",
     *             "deptCode": "1008",
     *             "deptName": "中医针灸",
     *             "doctorCode": "789   ",
     *             "doctorName": "诸君"
     *         },
     *         {
     *             "scheduleDate": "20230930",
     *             "scheduleInterval": "下午",
     *             "timeInterval": "15:59   ",
     *             "communitySite": "中心",
     *             "deptCode": "1008",
     *             "deptName": "中医针灸",
     *             "doctorCode": "789   ",
     *             "doctorName": "诸君"
     *         }
     * */

}

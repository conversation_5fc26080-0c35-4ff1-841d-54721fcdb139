package com.nx.inf.utils.exception;

import com.alibaba.fastjson.JSONException;
import com.nx.inf.utils.encrypt.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.json.JsonParseException;
import org.springframework.dao.DataAccessException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

/**
 * @Description: 全局异常捕获处理
 * @Author: JinShiBo
 * @CreateDate: 2023/6/27 9:55
 * @Version: 1.0
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(value = JSONException.class)
    public Result alibabaJsonExceptionHandler(JSONException e) {
        log.error("GlobalExceptionHandler| alibabaJsonExceptionHandler| alibabaJson异常", e);
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = JsonParseException.class)
    @ResponseBody
    public Result jsonParseExceptionHandler(JsonParseException e) {
        log.error("GlobalExceptionHandler| jsonParseExceptionHandler| json解析异常", e);
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result exceptionHandler(Exception e) {
        log.error("GlobalExceptionHandler| exceptionHandler| 其他异常", e);
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public Result methodArgumentTypeMismatchExceptionHandle(MethodArgumentTypeMismatchException e) {
        log.error("GlobalExceptionHandler| methodArgumentTypeMismatchExceptionHandle| 方法参数缺失异常", e);
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    @ResponseBody
    public Result illegalArgumentExceptionHandler(IllegalArgumentException e) {
        log.error("GlobalExceptionHandler| illegalArgumentExceptionHandler| 不合法参数异常", e);
        return Result.error(e.getMessage());
    }

    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public Result businessExceptionHandler(BusinessException e) {
        log.error("GlobalExceptionHandler| businessExceptionHandler| 业务异常", e);
        return Result.error(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.error("GlobalExceptionHandler| methodArgumentNotValidExceptionHandler| 方法参数不合法异常", e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return Result.error(message);
    }

    @ExceptionHandler(value = DataAccessException.class)
    @ResponseBody
    public Result dataAccessExceptionHandler(DataAccessException e) {
        log.error("GlobalExceptionHandler| dataAccessExceptionHandler| 数据库访问异常", e);
        // 打印详细错误信息到控制台
        System.err.println("数据库访问异常: " + e.getMessage());
        e.printStackTrace();
        return Result.error("数据库操作失败: " + e.getMessage());
    }
}

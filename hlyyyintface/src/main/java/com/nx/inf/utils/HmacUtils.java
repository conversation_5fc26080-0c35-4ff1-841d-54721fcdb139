package com.nx.inf.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.UUID;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Description: HMAC签名工具类
 * @Author: zhongx
 * @CreateDate: 2024/3/28
 * @Version: 1.0
 */
@Slf4j
public class HmacUtils {

    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    /**
     * 生成HMAC-SHA1签名
     * @param data 要签名的数据
     * @param key 密钥
     * @return Base64编码的签名
     */
    public static String hmacSHA1(String data, String key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA1_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(rawHmac);
        } catch (Exception e) {
            log.error("生成HMAC-SHA1签名出错", e);
            throw new RuntimeException("生成HMAC-SHA1签名出错: " + e.getMessage());
        }
    }

    /**
     * 生成UUID
     * @return UUID字符串
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 格式化当前时间为YYYYMMDDHHmm
     * @return 格式化后的时间字符串
     */
    public static String formatDateTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
    }

    /**
     * 编码签名字符串
     * @param appId 应用ID
     * @param nonce 随机数
     * @param sigTime 签名时间
     * @param patientIds 患者ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param hospitalBranch 医院分支
     * @return 签名字符串
     */
    public static String encodeSign(String appId, String nonce, String sigTime, 
                                    String patientIds, String startTime, String endTime,
                                    String hospitalBranch) {
        // 根据Node.js中xl=3/4/5/6的情况编码签名
        return String.format("appId=%s&nonce=%s&sigTime=%s&patientIds=%s&startTime=%s&endTime=%s&hospitalBranch=%s",
                appId, nonce, sigTime, patientIds, startTime, endTime, hospitalBranch);
    }
} 
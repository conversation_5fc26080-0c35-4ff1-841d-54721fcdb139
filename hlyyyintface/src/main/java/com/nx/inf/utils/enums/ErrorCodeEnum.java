package com.nx.inf.utils.enums;

/**
 * @Description: 错误代码枚举
 * @Author: jinshibo
 * @CreateTime: 2023-05-26
 * @Version: 1.0
 */
public enum ErrorCodeEnum {

    /**
     * 注册错误码 A0001-用户注册错误
     */
    SC_SERVER_ERROR_A0001("A0001", "用户注册错误"),
    /**
     * 注册错误码 A0100-用户端错误
     */
    SC_SERVER_ERROR_A0100("A0100", "用户端错误"),
    /**
     * 注册错误码 A0100-用户端错误
     */
    SC_SERVER_ERROR_A0101("A0101", "用户未同意隐私协议"),
    /**
     * 注册错误码 A0102-注册国家或地区受限
     */
    SC_SERVER_ERROR_A0102("A0102", "注册国家或地区受限"),


    /**
     * 用户错误码 A0210-用户名校验失败
     */
    SC_SERVER_ERROR_A0412("A0412", "用户名校验失败"),
    /**
     * 用户错误码 A0211-用户名已存在
     */
    SC_SERVER_ERROR_A0211("A0211", "用户名已存在"),
    /**
     * 用户错误码 A0212-用户名包含敏感词
     */
    SC_SERVER_ERROR_A0212("A0212", "用户名包含敏感词"),
    /**
     * 用户错误码 A0213-用户名包含特殊字符
     */
    SC_SERVER_ERROR_A0213("A0213", "用户名包含特殊字符"),
    /**
     * 用户错误码 A0214-用户不存在
     */
    SC_SERVER_ERROR_A0214("A0214", "用户不存在"),
    /**
     * 用户错误码 A0215-用户已锁定
     */
    SC_SERVER_ERROR_A0215("A0215", "用户已锁定"),


    /**
     * 密码错误码 A0220-密码校验失败
     */
    SC_SERVER_ERROR_A0220("A0220", "密码校验失败"),
    /**
     * 密码错误码 A0221-密码长度不够
     */
    SC_SERVER_ERROR_A0221("A0221", "密码长度不够"),
    /**
     * 密码错误码 A0222-密码强度不够
     */
    SC_SERVER_ERROR_A0222("A0222", "密码强度不够"),


    /**
     * 密码错误码 A0230-密码校验失败
     */
    SC_SERVER_ERROR_A0230("A0230", "密码校验失败"),
    /**
     * 密码错误码 A0231-密码校验失败
     */
    SC_SERVER_ERROR_A0231("A0231", "密码校验失败"),
    /**
     * 密码错误码 A0232-短信验证码输入错误
     */
    SC_SERVER_ERROR_A0232("A0232", "短信验证码输入错误"),
    /**
     * 密码错误码 A0233-邮箱验证码输入错误
     */
    SC_SERVER_ERROR_A0233("A0233", "邮箱验证码输入错误"),


    /**
     * 系统响应码 A0400-系统错误
     */
    SC_SERVER_ERROR_A0400("A0400", "系统错误"),
    /**
     * 系统响应码 A0401-无权限访问
     */
    SC_SERVER_ERROR_A0401("A0401", "无权限访问"),
    /**
     * 系统响应码 A0402-参数不能为空
     */
    SC_SERVER_ERROR_A0402("A0402", "参数不能为空"),
    /**
     * 系统响应码 A0403-参数格式有误
     */
    SC_SERVER_ERROR_A0403("A0403", "参数格式有误"),


    /**
     * SQL响应码 B0001-mapper操作数据库异常
     */
    SC_SERVER_ERROR_B0001("B0001", "Mapper操作数据库异常"),


    ;
    String errorCode;

    String description;

    ErrorCodeEnum(String errorCode, String description) {
        this.errorCode = errorCode;
        this.description = description;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getDescription() {
        return description;
    }

}

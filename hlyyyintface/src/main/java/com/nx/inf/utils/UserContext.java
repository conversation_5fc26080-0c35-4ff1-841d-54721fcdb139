package com.nx.inf.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户信息
 */
@Slf4j
public class UserContext {
    
    /**
     * 获取当前用户ID
     * @return 用户ID，如果获取失败返回默认值1L
     */
    public static Long getCurrentUserId() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                // 优先从请求头获取用户ID
                String userId = request.getHeader("X-User-Id");
                if (userId != null && !userId.isEmpty()) {
                    // 检查是否为UUID格式，如果是则返回默认值
                    if (userId.contains("-")) {
                        log.info("检测到UUID格式的用户ID: {}, 使用默认用户ID", userId);
                        return 1L;
                    }
                    return Long.parseLong(userId);
                }
                
                // 从accessToken中获取用户信息（这里可以根据实际token格式解析）
                String accessToken = request.getHeader("X-Access-Token");
                if (accessToken != null && !accessToken.isEmpty()) {
                    // 这里可以根据token解析用户ID，暂时使用默认值
                    // 实际项目中应该通过token解析或查询数据库获取用户ID
                    return 1L; // 默认用户ID
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户ID失败", e);
        }
        return 1L; // 默认用户ID
    }
    
    /**
     * 获取当前用户姓名
     * @return 用户姓名，如果获取失败返回默认值
     */
    public static String getCurrentUserName() {
        try {
            HttpServletRequest request = getRequest();
            if (request != null) {
                // 优先从请求头获取用户姓名
                String userName = request.getHeader("X-User-Name");
                if (userName != null && !userName.isEmpty()) {
                    try {
                        // 解码URL编码的用户名
                        return java.net.URLDecoder.decode(userName, "UTF-8");
                    } catch (Exception e) {
                        log.warn("解码用户名失败: {}", userName, e);
                        return userName; // 如果解码失败，返回原始值
                    }
                }
                
                // 从accessToken中获取用户信息（这里可以根据实际token格式解析）
                String accessToken = request.getHeader("X-Access-Token");
                if (accessToken != null && !accessToken.isEmpty()) {
                    // 这里可以根据token解析用户姓名，暂时使用默认值
                    // 实际项目中应该通过token解析或查询数据库获取用户姓名
                    return "系统管理员"; // 默认用户姓名
                }
            }
        } catch (Exception e) {
            log.warn("获取当前用户姓名失败", e);
        }
        return "系统管理员"; // 默认用户姓名
    }
    
    /**
     * 获取当前HTTP请求
     * @return HttpServletRequest
     */
    private static HttpServletRequest getRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.warn("获取HTTP请求失败", e);
            return null;
        }
    }
} 
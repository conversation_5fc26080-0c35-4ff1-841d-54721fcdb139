package com.nx.inf.utils.encrypt;

import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 *  SM2 加密工具类
 */
@Component
public class SM2Utils {

    /**
     * 获取公钥私钥
     * get key pair
     */
    public static Map<String, String> createKeyPair() throws NoSuchAlgorithmException {
        return SecretCommon.createKeyPair();
    }

    public static void main(String[] args) throws InvalidCipherTextException, IOException {

        String json = "[{\"applyDate\":\"2024-05-31\",\"channelCode\":\"2\",\"createDate\":\"2024-05-31 09:54:29\",\"deptCode\":\"103\",\"deptName\":\"内科\",\"endTime\":\"10:10\",\"handleStatus\":0,\"hosName\":\"上海助医互联网医院\",\"hosOrgCode\":\"310107202\",\"isAdd\":\"0\",\"numSourceDetailId\":\"61042465\",\"numSourceFrom\":\"4\",\"numSourceId\":\"61042465\",\"oneDeptName\":\"内科\",\"orderId\":\"499964\",\"orderNumType\":\"102\",\"orderStatus\":\"3\",\"orderTime\":\"2024-05-31\",\"orderType\":\"1\",\"patientType\":\"1\",\"payMode\":\"1\",\"payState\":\"3\",\"platformType\":\"9\",\"resourceCode\":\"**********\",\"resourceName\":\"杨洪杰\",\"scheduleId\":\"61042465\",\"startTime\":\"10:01\",\"timeRange\":\"1\",\"updateInsertStatus\":2,\"userBd\":\"1969-03-15\",\"userCardId\":\"232102196903151919\",\"userCardType\":\"01\",\"userName\":\"张亚春\",\"userPhone\":\"18717739352\",\"userSex\":\"1\",\"visitCost\":\"1.00\"}]";
        System.out.println("加密前>>>>>>>>>>>>>>> \n" + json);

        String encryptJson = encrypt(json, "04bcac99e0309140d588216e7707103534c1f81158ef1c8aca4fe30d6a443f16e79012680ef0e2d65848c738cb837de8675cbaefca290346a9a70b04c85179dac0");
        System.out.println("加密后>>>>>>>>>>>>>>> \n" + encryptJson);

        System.out.println("解密后>>>>>>>>>>>>>>> ");
        System.out.println(decrypt(encryptJson, "00bc303e3b0bf71da9e301aa147ce9c828e0f306be15b81fa6ace642e5d7ec7524"));

    }

    /**
     * encrypt
     * @param plainText 需加密的明文字符串
     * @param publicKey 公钥
     */
    public static String encrypt(String plainText, String publicKey) throws IOException, InvalidCipherTextException {
        return encrypt(plainText, publicKey, ModeTypeConstant.BASE);
    }

    /**
     * encrypt
     * @param plainText 需加密的明文字符串
     * @param publicKey 公钥
     * @param modeType base:标准；bc：BC模式
     */
    public static String encrypt(String plainText, String publicKey, String modeType) throws IOException, InvalidCipherTextException {
        return SecretCommon.encrypt(plainText, publicKey, ModeTypeConstant.getMode(modeType));
    }

    /**
     * decrypt
     * @param cipherText 需加密的字符串
     * @param privateKey 私钥
     */
    public static String decrypt(String cipherText, String privateKey) throws InvalidCipherTextException, UnsupportedEncodingException {
        return decrypt(cipherText, privateKey, ModeTypeConstant.BASE);
    }

    /**
     * decrypt
     * @param cipherText 需加密的字符串
     * @param privateKey 私钥
     * @param modeType base:标准；bc：BC模式
     */
    public static String decrypt(String cipherText, String privateKey, String modeType) throws InvalidCipherTextException, UnsupportedEncodingException {
        return SecretCommon.decrypt(cipherText, privateKey, ModeTypeConstant.getMode(modeType));
    }

}

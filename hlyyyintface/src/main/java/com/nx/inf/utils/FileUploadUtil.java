package com.nx.inf.utils;

import com.nx.inf.config.FileUploadConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件上传工具类
 */
@Component
public class FileUploadUtil {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    /**
     * 默认上传路径
     */
    private static String DEFAULT_UPLOAD_PATH = "uploads";

    /**
     * 图片文件夹
     */
    private static String IMAGE_FOLDER = "images";

    /**
     * 允许的图片文件类型
     */
    private static String[] ALLOWED_IMAGE_TYPES = {
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
    };

    /**
     * 最大文件大小 (10MB)
     */
    private static long MAX_FILE_SIZE = 10 * 1024 * 1024;

    @PostConstruct
    public void init() {
        if (fileUploadConfig != null) {
            DEFAULT_UPLOAD_PATH = fileUploadConfig.getPath();
            IMAGE_FOLDER = fileUploadConfig.getImageFolder();
            MAX_FILE_SIZE = fileUploadConfig.getMaxSize();
            ALLOWED_IMAGE_TYPES = fileUploadConfig.getAllowedTypesArray();
        }
    }
    
    /**
     * 上传图片文件
     * 
     * @param file 上传的文件
     * @param uploadPath 上传路径（可选，为空则使用默认路径）
     * @return 文件保存路径
     * @throws IOException 文件操作异常
     */
    public static String uploadImage(MultipartFile file, String uploadPath) throws IOException {
        // 验证文件
        validateImageFile(file);
        
        // 使用默认路径如果未指定
        if (uploadPath == null || uploadPath.trim().isEmpty()) {
            uploadPath = DEFAULT_UPLOAD_PATH;
        }
        
        // 创建目录结构：uploads/images/yyyy/MM/dd/
        String dateFolder = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String fullPath = uploadPath + File.separator + IMAGE_FOLDER + File.separator + dateFolder;
        
        // 创建目录
        Path directory = Paths.get(fullPath);
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String newFilename = UUID.randomUUID().toString() + fileExtension;
        
        // 保存文件
        Path filePath = directory.resolve(newFilename);
        Files.copy(file.getInputStream(), filePath);
        
        // 返回相对路径
        return fullPath + File.separator + newFilename;
    }
    
    /**
     * 验证图片文件
     * 
     * @param file 上传的文件
     * @throws IllegalArgumentException 文件验证失败
     */
    public static void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !isAllowedImageType(contentType)) {
            throw new IllegalArgumentException("不支持的文件类型，仅支持图片文件");
        }
        
        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
    }
    
    /**
     * 检查是否为允许的图片类型
     * 
     * @param contentType 文件类型
     * @return 是否允许
     */
    private static boolean isAllowedImageType(String contentType) {
        for (String allowedType : ALLOWED_IMAGE_TYPES) {
            if (allowedType.equalsIgnoreCase(contentType)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名（包含点号）
     */
    private static String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        
        return filename.substring(lastDotIndex);
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        
        try {
            Path path = Paths.get(filePath);
            return Files.deleteIfExists(path);
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean fileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }
        
        Path path = Paths.get(filePath);
        return Files.exists(path);
    }
}

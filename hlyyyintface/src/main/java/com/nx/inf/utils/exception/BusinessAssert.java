package com.nx.inf.utils.exception;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 业务自定义断言
 * @Author: JinShiBo
 * @CreateDate: 2023/6/27 9:42
 * @Version: 1.0
 */
public class BusinessAssert extends Assert {

    private static final int ZERO = 0;

    /**
     * <p>
     * 服务调用异常
     *
     * </p>
     *
     * @param message 消息
     */
    public static void serviceInvokerError(String message) {
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 服务调用判断是否合法
     * 若expression 为true则合法
     * 反之不合法
     * </p>
     *
     * @param expression 表达式
     * @param message    消息
     */
    public static void serviceInvokerValid(boolean expression, String message) {
        if (expression) {
            return;
        }
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 服务调用判断不合法
     * 若expression 为true 则 不合法
     * 反正合法
     * </p>
     *
     * @param expression 表达式
     * @param message    消息
     */
    public static void serviceInvokerInvalid(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 表达是是否为true
     * 若表达式为true 则合法
     * 反之不合法
     * </p>
     *
     * @param expression 表达式
     * @param message    消息
     */
    public static void isTrue(boolean expression, String message) {
        if (expression) {
            return;
        }
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 表达是是否为false
     * 若表达式为false 则合法
     * 反之不合法
     * </p>
     *
     * @param expression 表达式
     * @param message    消息
     */
    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断集合是否为空
     * 若为不为空 则不合法
     * </p>
     *
     * @param collection 集合
     * @param message    消息
     */
    public static void isEmpty(Collection<?> collection, String message) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 判断map是否为空
     * 若为不为空 则不合法
     * </p>
     *
     * @param map     集合
     * @param message 消息
     */
    public static void isEmpty(Map<?, ?> map, String message) {
        if (CollUtil.isEmpty(map)) {
            return;
        }
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 判断集合是否不为空
     * 若为空则不合法
     * </p>
     *
     * @param collection 集合
     * @param message    消息
     */
    public static void isNotEmpty(Collection<?> collection, String message) {
        if (CollUtil.isEmpty(collection)) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断map是否不为空
     * 若为空则不合法
     * </p>
     *
     * @param map 集合
     * @param message    消息
     */
    public static void isNotEmpty(Map<?, ?> map, String message) {
        if (CollUtil.isEmpty(map)) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断对象是否为空
     * 若不为空则不合法
     * </p>
     *
     * @param obj     对象
     * @param message 消息
     */
    public static void isNull(Object obj, String message) {
        if (Objects.isNull(obj)) {
            return;
        }
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 判断对象是否不为空
     * 若为空则不合法
     * </p>
     *
     * @param obj     对象
     * @param message 消息
     */
    public static void isNotNull(Object obj, String message) {
        if (Objects.isNull(obj)) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断字符串是否为空
     * 不为空则不合法
     * </p>
     *
     * @param obj     字符窜
     * @param message 消息
     */
    public static void isEmpty(String obj, String message) {
        if (StrUtil.isEmpty(obj)) {
            return;
        }
        throw new BusinessException(message);
    }

    /**
     * <p>
     * 判断字符串是否不为空
     * 为空则不合法
     * </p>
     *
     * @param obj     对象
     * @param message 消息
     */
    public static void isNotEmpty(String obj, String message) {
        if (StrUtil.isEmpty(obj)) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断数字是否大于0
     * 若小于等于0 则不合法
     * </p>
     *
     * @param number  数字
     * @param message 消息
     */
    public static void gtZero(int number, String message) {
        if (BusinessAssert.ZERO >= number) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断数字是否大于等于0
     * 若小于0 则不合法
     * </p>
     *
     * @param number  数字
     * @param message 消息
     */
    public static void geZero(int number, String message) {
        if (BusinessAssert.ZERO > number) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断数字是否小于0
     * 若大于等于0 则不合法
     * </p>
     *
     * @param number  数字
     * @param message 消息
     */
    public static void ltZero(int number, String message) {
        if (number >= BusinessAssert.ZERO) {
            throw new BusinessException(message);
        }
    }

    /**
     * <p>
     * 判断数字是否小于等于0
     * 若大于0 则不合法
     * </p>
     *
     * @param number  数字
     * @param message 消息
     */
    public static void leZero(int number, String message) {
        if (number > BusinessAssert.ZERO) {
            throw new BusinessException(message);
        }
    }
}

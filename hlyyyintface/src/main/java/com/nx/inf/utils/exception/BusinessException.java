package com.nx.inf.utils.exception;

/**
 * @Description: 业务自定义异常
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2023/6/26 22:06
 * @Version: 1.0
 */
public class BusinessException extends RuntimeException {

    private String code = "E200001";

    public BusinessException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public String getCode() {
        return this.code;
    }
}

package com.nx.inf.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.List;
import java.util.ArrayList;

/**
 * 百度OCR工具类
 */
public class BaiduOCRUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(BaiduOCRUtil.class);
    
    // 百度OCR API配置
    private static final String ACCESS_TOKEN = "24.5b5cbe6d2ae745568b070c1801ee0918.2592000.**********.282335-119570890";
    private static final String API_URL_GENERAL = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic";
    private static final String API_URL_MEDICAL = "https://aip.baidubce.com/rest/2.0/ocr/v1/medical_report_detection";
    
    /**
     * OCR识别结果
     */
    public static class OCRResult {
        private boolean success;
        private String recognizedText;
        private String errorMessage;
        private int wordsCount;
        private long processingTime;
        
        public OCRResult() {}
        
        public OCRResult(boolean success, String recognizedText) {
            this.success = success;
            this.recognizedText = recognizedText;
        }
        
        public OCRResult(boolean success, String errorMessage, boolean isError) {
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getRecognizedText() { return recognizedText; }
        public void setRecognizedText(String recognizedText) { this.recognizedText = recognizedText; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public int getWordsCount() { return wordsCount; }
        public void setWordsCount(int wordsCount) { this.wordsCount = wordsCount; }
        
        public long getProcessingTime() { return processingTime; }
        public void setProcessingTime(long processingTime) { this.processingTime = processingTime; }
    }

    /**
     * 医疗检验报告单识别结果
     */
    public static class MedicalReportResult {
        private boolean success;
        private String errorMessage;
        private long processingTime;

        // 报告基本信息
        private String patientName;        // 患者姓名
        private String patientId;          // 患者ID
        private String gender;             // 性别
        private String age;                // 年龄
        private String reportDate;         // 报告日期
        private String hospitalName;       // 医院名称
        private String departmentName;     // 科室名称

        // 检验项目列表
        private List<MedicalTestItem> testItems;

        // 原始识别文本
        private String rawText;

        public MedicalReportResult() {
            this.testItems = new ArrayList<>();
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public long getProcessingTime() { return processingTime; }
        public void setProcessingTime(long processingTime) { this.processingTime = processingTime; }

        public String getPatientName() { return patientName; }
        public void setPatientName(String patientName) { this.patientName = patientName; }

        public String getPatientId() { return patientId; }
        public void setPatientId(String patientId) { this.patientId = patientId; }

        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }

        public String getAge() { return age; }
        public void setAge(String age) { this.age = age; }

        public String getReportDate() { return reportDate; }
        public void setReportDate(String reportDate) { this.reportDate = reportDate; }

        public String getHospitalName() { return hospitalName; }
        public void setHospitalName(String hospitalName) { this.hospitalName = hospitalName; }

        public String getDepartmentName() { return departmentName; }
        public void setDepartmentName(String departmentName) { this.departmentName = departmentName; }

        public List<MedicalTestItem> getTestItems() { return testItems; }
        public void setTestItems(List<MedicalTestItem> testItems) { this.testItems = testItems; }

        public String getRawText() { return rawText; }
        public void setRawText(String rawText) { this.rawText = rawText; }
    }

    /**
     * 医疗检验项目
     */
    public static class MedicalTestItem {
        private String itemName;           // 检验项目名称
        private String result;             // 检验结果
        private String unit;               // 单位
        private String referenceRange;     // 参考范围
        private String abnormalFlag;       // 异常标识（如：↑、↓、H、L等）

        public MedicalTestItem() {}

        public MedicalTestItem(String itemName, String result, String unit, String referenceRange, String abnormalFlag) {
            this.itemName = itemName;
            this.result = result;
            this.unit = unit;
            this.referenceRange = referenceRange;
            this.abnormalFlag = abnormalFlag;
        }

        // Getters and Setters
        public String getItemName() { return itemName; }
        public void setItemName(String itemName) { this.itemName = itemName; }

        public String getResult() { return result; }
        public void setResult(String result) { this.result = result; }

        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }

        public String getReferenceRange() { return referenceRange; }
        public void setReferenceRange(String referenceRange) { this.referenceRange = referenceRange; }

        public String getAbnormalFlag() { return abnormalFlag; }
        public void setAbnormalFlag(String abnormalFlag) { this.abnormalFlag = abnormalFlag; }
    }
    
    /**
     * 识别图片中的文字
     * @param imageBytes 图片字节数组
     * @return OCR识别结果
     */
    public static OCRResult recognizeText(byte[] imageBytes) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 将图片转换为Base64
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            
            logger.info("开始百度OCR识别，图片大小: {} bytes, Base64长度: {}", 
                       imageBytes.length, base64Image.length());
            
            // 调用百度OCR API
            String apiResponse = callBaiduOCRAPI(base64Image);
            
            // 解析响应
            OCRResult result = parseOCRResponse(apiResponse);
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            
            logger.info("百度OCR识别完成，耗时: {}ms, 成功: {}, 文字长度: {}", 
                       result.getProcessingTime(), result.isSuccess(), 
                       result.getRecognizedText() != null ? result.getRecognizedText().length() : 0);
            
            return result;
            
        } catch (Exception e) {
            logger.error("百度OCR识别失败: {}", e.getMessage(), e);
            OCRResult result = new OCRResult(false, "OCR识别失败: " + e.getMessage(), true);
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            return result;
        }
    }
    
    /**
     * 调用百度OCR API
     */
    private static String callBaiduOCRAPI(String base64Image) throws Exception {
        // 构建请求参数
        StringBuilder params = new StringBuilder();
        params.append("access_token=").append(URLEncoder.encode(ACCESS_TOKEN, "UTF-8"));
        params.append("&image=").append(URLEncoder.encode(base64Image, "UTF-8"));
        params.append("&detect_direction=true");  // 检测并矫正图片方向
        params.append("&probability=true");       // 返回置信度
        
        // 创建HTTP连接
        URL url = new URL(API_URL_GENERAL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // 设置请求方法和头部
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("User-Agent", "Java-OCR-Client/1.0");
        connection.setDoOutput(true);
        connection.setConnectTimeout(30000); // 30秒连接超时
        connection.setReadTimeout(60000);    // 60秒读取超时
        
        // 发送请求数据
        try (DataOutputStream out = new DataOutputStream(connection.getOutputStream())) {
            out.writeBytes(params.toString());
            out.flush();
        }
        
        // 读取响应
        int responseCode = connection.getResponseCode();
        logger.debug("百度OCR API响应码: {}", responseCode);
        
        StringBuilder response = new StringBuilder();
        
        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 成功响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = in.readLine()) != null) {
                    response.append(line);
                }
            }
        } else {
            // 错误响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"))) {
                String line;
                while ((line = in.readLine()) != null) {
                    response.append(line);
                }
            }
        }
        
        connection.disconnect();
        
        String responseText = response.toString();
        logger.debug("百度OCR API响应: {}", responseText);
        
        return responseText;
    }
    
    /**
     * 解析百度OCR API响应
     */
    private static OCRResult parseOCRResponse(String apiResponse) {
        OCRResult result = new OCRResult();
        
        try {
            if (apiResponse.contains("\"words_result\"")) {
                // 成功响应
                result.setSuccess(true);
                
                // 提取识别的文字
                StringBuilder recognizedText = new StringBuilder();
                String[] lines = apiResponse.split("\"words\":");
                for (int i = 1; i < lines.length; i++) {
                    String line = lines[i];
                    int start = line.indexOf("\"") + 1;
                    int end = line.indexOf("\"", start);
                    if (start > 0 && end > start) {
                        String text = line.substring(start, end);
                        recognizedText.append(text).append("\n");
                    }
                }
                
                String finalText = recognizedText.toString().trim();
                result.setRecognizedText(finalText);
                
                // 提取words_result_num
                if (apiResponse.contains("\"words_result_num\":")) {
                    try {
                        String numStr = apiResponse.substring(apiResponse.indexOf("\"words_result_num\":") + 19);
                        numStr = numStr.substring(0, numStr.indexOf(",")).trim();
                        result.setWordsCount(Integer.parseInt(numStr));
                    } catch (Exception e) {
                        logger.debug("解析words_result_num失败: {}", e.getMessage());
                    }
                }
                
            } else if (apiResponse.contains("\"error_code\"")) {
                // 错误响应
                result.setSuccess(false);
                
                // 提取错误信息
                String errorMessage = "未知错误";
                if (apiResponse.contains("\"error_msg\":")) {
                    try {
                        String errorMsg = apiResponse.substring(apiResponse.indexOf("\"error_msg\":\"") + 13);
                        errorMsg = errorMsg.substring(0, errorMsg.indexOf("\""));
                        errorMessage = errorMsg;
                    } catch (Exception e) {
                        logger.debug("解析错误信息失败: {}", e.getMessage());
                    }
                }
                
                result.setErrorMessage(errorMessage);
                result.setRecognizedText("");
                
            } else {
                // 未知响应格式
                result.setSuccess(false);
                result.setErrorMessage("未知的API响应格式");
                result.setRecognizedText("");
            }
            
        } catch (Exception e) {
            logger.error("解析百度OCR响应失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage("响应解析失败: " + e.getMessage());
            result.setRecognizedText("");
        }
        
        return result;
    }

    /**
     * 调用百度医疗分析OCR API
     */
    private static String callBaiduMedicalOCRAPI(String base64Image) throws Exception {
        // 构建请求参数
        StringBuilder params = new StringBuilder();
        params.append("access_token=").append(URLEncoder.encode(ACCESS_TOKEN, "UTF-8"));
        params.append("&image=").append(URLEncoder.encode(base64Image, "UTF-8"));
        params.append("&detect_direction=true");  // 检测并矫正图片方向

        // 创建HTTP连接
        URL url = new URL(API_URL_MEDICAL);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // 设置请求方法和头部
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("User-Agent", "Java-Medical-OCR-Client/1.0");
        connection.setDoOutput(true);
        connection.setConnectTimeout(30000); // 30秒连接超时
        connection.setReadTimeout(60000);    // 60秒读取超时

        // 发送请求数据
        try (DataOutputStream out = new DataOutputStream(connection.getOutputStream())) {
            out.writeBytes(params.toString());
            out.flush();
        }

        // 读取响应
        int responseCode = connection.getResponseCode();
        logger.debug("百度医疗OCR API响应码: {}", responseCode);

        StringBuilder response = new StringBuilder();

        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 成功响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                String line;
                while ((line = in.readLine()) != null) {
                    response.append(line);
                }
            }
        } else {
            // 错误响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"))) {
                String line;
                while ((line = in.readLine()) != null) {
                    response.append(line);
                }
            }
        }

        connection.disconnect();

        String responseText = response.toString();
        logger.debug("百度医疗OCR API响应: {}", responseText);

        return responseText;
    }

    /**
     * 识别医疗检验报告单
     * @param imageBytes 图片字节数组
     * @return 医疗检验报告单识别结果
     */
    public static MedicalReportResult recognizeMedicalReport(byte[] imageBytes) {
        long startTime = System.currentTimeMillis();

        try {
            // 将图片转换为Base64
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            logger.info("开始医疗检验报告单识别，图片大小: {} bytes, Base64长度: {}",
                       imageBytes.length, base64Image.length());

            // 调用百度医疗分析OCR API
            String apiResponse = callBaiduMedicalOCRAPI(base64Image);

            // 解析响应
            MedicalReportResult result = parseMedicalOCRResponse(apiResponse);
            result.setProcessingTime(System.currentTimeMillis() - startTime);

            logger.info("医疗检验报告单识别完成，耗时: {}ms, 成功: {}, 检验项目数: {}",
                       result.getProcessingTime(), result.isSuccess(),
                       result.getTestItems() != null ? result.getTestItems().size() : 0);

            return result;

        } catch (Exception e) {
            logger.error("医疗检验报告单识别失败: {}", e.getMessage(), e);
            MedicalReportResult result = new MedicalReportResult();
            result.setSuccess(false);
            result.setErrorMessage("医疗检验报告单识别失败: " + e.getMessage());
            result.setProcessingTime(System.currentTimeMillis() - startTime);
            return result;
        }
    }
    
    /**
     * 测试百度OCR服务是否可用
     */
    public static boolean testService() {
        try {
            // 创建一个1x1像素的白色图片进行测试
            byte[] testImage = createTestImage();
            OCRResult result = recognizeText(testImage);
            
            logger.info("百度OCR服务测试结果: {}", result.isSuccess() ? "可用" : "不可用");
            return result.isSuccess();
            
        } catch (Exception e) {
            logger.error("百度OCR服务测试失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 创建测试用的小图片
     */
    private static byte[] createTestImage() {
        // 返回一个最小的PNG图片字节数组（1x1白色像素）
        return new byte[]{
            (byte)0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
            0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
            0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
            0x08, 0x02, 0x00, 0x00, 0x00, (byte)0x90, (byte)0x77, 0x53, (byte)0xDE,
            0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54,
            0x08, (byte)0xD7, 0x63, (byte)0xF8, (byte)0xFF, (byte)0xFF, (byte)0xFF, 0x00,
            0x00, 0x00, 0x04, 0x00, 0x01, (byte)0xDD, (byte)0x8D, (byte)0xB4, 0x1C,
            0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44,
            (byte)0xAE, 0x42, 0x60, (byte)0x82
        };
    }

    /**
     * 解析百度医疗OCR API响应
     */
    private static MedicalReportResult parseMedicalOCRResponse(String apiResponse) {
        MedicalReportResult result = new MedicalReportResult();

        try {
            // 保存原始文本
            result.setRawText(apiResponse);

            if (apiResponse.contains("\"words_result\"")) {
                // 成功响应 - 医疗报告检测API返回结构化数据
                result.setSuccess(true);

                // 解析结构化的医疗报告数据
                parseStructuredMedicalData(result, apiResponse);

            } else if (apiResponse.contains("\"error_code\"")) {
                // 错误响应
                result.setSuccess(false);

                // 提取错误信息
                String errorMessage = "未知错误";
                if (apiResponse.contains("\"error_msg\":")) {
                    try {
                        String errorMsg = apiResponse.substring(apiResponse.indexOf("\"error_msg\":\"") + 13);
                        errorMsg = errorMsg.substring(0, errorMsg.indexOf("\""));
                        errorMessage = errorMsg;
                    } catch (Exception e) {
                        logger.debug("解析错误信息失败: {}", e.getMessage());
                    }
                }

                result.setErrorMessage(errorMessage);

            } else {
                // 未知响应格式
                result.setSuccess(false);
                result.setErrorMessage("未知的API响应格式");
            }

        } catch (Exception e) {
            logger.error("解析百度医疗OCR响应失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage("响应解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析结构化的医疗报告数据
     */
    private static void parseStructuredMedicalData(MedicalReportResult result, String apiResponse) {
        try {
            // 解析CommonData（基本信息）
            if (apiResponse.contains("\"CommonData\"")) {
                parseCommonData(result, apiResponse);
            }

            // 解析Item（检验项目）
            if (apiResponse.contains("\"Item\"")) {
                parseTestItems(result, apiResponse);
            }

        } catch (Exception e) {
            logger.warn("解析结构化医疗数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析CommonData基本信息
     */
    private static void parseCommonData(MedicalReportResult result, String apiResponse) {
        try {
            // 提取CommonData部分
            int commonDataStart = apiResponse.indexOf("\"CommonData\":[");
            if (commonDataStart == -1) return;

            int commonDataEnd = apiResponse.indexOf("],\"Item\"", commonDataStart);
            if (commonDataEnd == -1) {
                commonDataEnd = apiResponse.indexOf("}],", commonDataStart);
            }
            if (commonDataEnd == -1) return;

            String commonDataSection = apiResponse.substring(commonDataStart, commonDataEnd);

            // 解析各个字段
            result.setPatientName(extractFieldValue(commonDataSection, "姓名"));
            result.setGender(extractFieldValue(commonDataSection, "性别"));
            result.setAge(extractFieldValue(commonDataSection, "年龄"));
            result.setHospitalName(extractFieldValue(commonDataSection, "报告单名称"));
            result.setDepartmentName(extractFieldValue(commonDataSection, "科室"));
            result.setReportDate(extractFieldValue(commonDataSection, "时间"));

            // 如果医院名称为空，尝试从报告单名称中提取
            if (result.getHospitalName() != null && result.getHospitalName().contains("医院")) {
                String hospitalName = result.getHospitalName();
                if (hospitalName.contains("检验报告单")) {
                    hospitalName = hospitalName.replace("检验报告单", "").trim();
                }
                result.setHospitalName(hospitalName);
            }

        } catch (Exception e) {
            logger.warn("解析CommonData失败: {}", e.getMessage());
        }
    }

    /**
     * 解析检验项目
     */
    private static void parseTestItems(MedicalReportResult result, String apiResponse) {
        try {
            // 提取Item部分
            int itemStart = apiResponse.indexOf("\"Item\":[[");
            if (itemStart == -1) return;

            int itemEnd = apiResponse.indexOf("]]", itemStart);
            if (itemEnd == -1) return;

            String itemSection = apiResponse.substring(itemStart + 8, itemEnd + 2);

            // 分割各个检验项目
            String[] itemGroups = itemSection.split("\\],\\[");

            for (String itemGroup : itemGroups) {
                MedicalTestItem testItem = parseTestItem(itemGroup);
                if (testItem != null && testItem.getItemName() != null && !testItem.getItemName().trim().isEmpty()) {
                    result.getTestItems().add(testItem);
                }
            }

        } catch (Exception e) {
            logger.warn("解析检验项目失败: {}", e.getMessage());
        }
    }

    /**
     * 解析单个检验项目
     */
    private static MedicalTestItem parseTestItem(String itemData) {
        try {
            MedicalTestItem item = new MedicalTestItem();

            item.setItemName(extractFieldValue(itemData, "项目名称"));
            item.setResult(extractFieldValue(itemData, "结果"));
            item.setUnit(extractFieldValue(itemData, "单位"));
            item.setReferenceRange(extractFieldValue(itemData, "参考区间"));
            item.setAbnormalFlag(extractFieldValue(itemData, "结果提示"));

            return item;

        } catch (Exception e) {
            logger.debug("解析单个检验项目失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从JSON字符串中提取指定字段的值
     */
    private static String extractFieldValue(String jsonSection, String fieldName) {
        try {
            String pattern = "\"word_name\":\"" + fieldName + "\",\"word\":\"";
            int start = jsonSection.indexOf(pattern);
            if (start == -1) return null;

            start += pattern.length();
            int end = jsonSection.indexOf("\"", start);
            if (end == -1) return null;

            String value = jsonSection.substring(start, end);
            return value.trim().isEmpty() ? null : value;

        } catch (Exception e) {
            logger.debug("提取字段值失败: {}, 字段: {}", e.getMessage(), fieldName);
            return null;
        }
    }

    /**
     * 解析医疗报告信息（旧方法，保留用于兼容）
     */
    private static void parseMedicalReportInfo(MedicalReportResult result, String fullText) {
        try {
            String[] lines = fullText.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 解析患者姓名
                if (line.contains("姓名") || line.contains("患者")) {
                    String name = extractValue(line, new String[]{"姓名", "患者"});
                    if (name != null && !name.isEmpty()) {
                        result.setPatientName(name);
                    }
                }

                // 解析性别
                if (line.contains("性别")) {
                    String gender = extractValue(line, new String[]{"性别"});
                    if (gender != null && !gender.isEmpty()) {
                        result.setGender(gender);
                    }
                }

                // 解析年龄
                if (line.contains("年龄")) {
                    String age = extractValue(line, new String[]{"年龄"});
                    if (age != null && !age.isEmpty()) {
                        result.setAge(age);
                    }
                }

                // 解析报告日期
                if (line.contains("日期") || line.contains("时间")) {
                    String date = extractDateFromLine(line);
                    if (date != null && !date.isEmpty()) {
                        result.setReportDate(date);
                    }
                }

                // 解析医院名称
                if (line.contains("医院") || line.contains("中心")) {
                    String hospital = extractHospitalName(line);
                    if (hospital != null && !hospital.isEmpty()) {
                        result.setHospitalName(hospital);
                    }
                }

                // 解析科室
                if (line.contains("科") && !line.contains("检验科")) {
                    String department = extractValue(line, new String[]{"科室", "科"});
                    if (department != null && !department.isEmpty()) {
                        result.setDepartmentName(department);
                    }
                }

                // 解析检验项目（简单的表格行识别）
                if (isTestItemLine(line)) {
                    MedicalTestItem item = parseTestItem(line);
                    if (item != null) {
                        result.getTestItems().add(item);
                    }
                }
            }

        } catch (Exception e) {
            logger.warn("解析医疗报告信息失败: {}", e.getMessage());
        }
    }

    /**
     * 从行中提取值
     */
    private static String extractValue(String line, String[] keywords) {
        for (String keyword : keywords) {
            int index = line.indexOf(keyword);
            if (index >= 0) {
                String remaining = line.substring(index + keyword.length()).trim();
                // 移除常见的分隔符
                remaining = remaining.replaceFirst("^[：:：\\s]+", "");
                // 提取第一个有意义的词
                String[] parts = remaining.split("[\\s,，；;]+");
                if (parts.length > 0 && !parts[0].trim().isEmpty()) {
                    return parts[0].trim();
                }
            }
        }
        return null;
    }

    /**
     * 从行中提取日期
     */
    private static String extractDateFromLine(String line) {
        // 简单的日期匹配模式
        java.util.regex.Pattern datePattern = java.util.regex.Pattern.compile(
            "(\\d{4}[-/年]\\d{1,2}[-/月]\\d{1,2}[日]?)|" +
            "(\\d{1,2}[-/]\\d{1,2}[-/]\\d{4})|" +
            "(\\d{4}\\.\\d{1,2}\\.\\d{1,2})"
        );

        java.util.regex.Matcher matcher = datePattern.matcher(line);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    /**
     * 提取医院名称
     */
    private static String extractHospitalName(String line) {
        // 查找包含"医院"或"中心"的完整名称
        if (line.contains("医院")) {
            int endIndex = line.indexOf("医院") + 2;
            int startIndex = Math.max(0, endIndex - 20); // 向前查找最多20个字符
            String candidate = line.substring(startIndex, endIndex);
            // 清理前缀
            candidate = candidate.replaceAll("^[^\\u4e00-\\u9fa5]*", "");
            return candidate;
        }
        return null;
    }

    /**
     * 判断是否为检验项目行
     */
    private static boolean isTestItemLine(String line) {
        // 简单的启发式规则：包含数字和单位，或者包含异常标识
        return line.matches(".*\\d+.*") &&
               (line.contains("mmol") || line.contains("mg") || line.contains("g/L") ||
                line.contains("↑") || line.contains("↓") || line.contains("H") || line.contains("L") ||
                line.contains("正常") || line.contains("异常"));
    }

}

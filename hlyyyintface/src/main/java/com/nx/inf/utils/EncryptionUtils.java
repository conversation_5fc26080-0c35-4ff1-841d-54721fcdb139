package com.nx.inf.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @Description: 加密工具类，用于AES加密和MD5签名
 * @Author: zhongx
 * @CreateDate: 2024/5/15
 * @Version: 1.0
 */
public class EncryptionUtils {
    
    private static final Logger log = LoggerFactory.getLogger(EncryptionUtils.class);
    
    /**
     * 计算MD5签名并转为大写
     * @param text 待签名字符串
     * @return MD5签名（大写）
     */
    public static String md5Hex(String text) {
        try {
            return DigestUtils.md5Hex(text).toUpperCase();
        } catch (Exception e) {
            log.error("MD5加密失败", e);
            throw new RuntimeException("MD5加密失败", e);
        }
    }
    
    /**
     * AES加密（CBC模式，PKCS5Padding填充，128位数据块）
     * @param content 待加密内容
     * @param key 密钥
     * @param iv 偏移量
     * @return 加密后的字符串（HEX格式，大写）
     */
    public static String aesEncrypt(String content, String key, String iv) {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            
            // 转为HEX并大写
            return bytesToHex(encrypted).toUpperCase();
        } catch (Exception e) {
            log.error("AES加密失败", e);
            throw new RuntimeException("AES加密失败", e);
        }
    }
    
    /**
     * 将字节数组转为HEX字符串
     * @param bytes 字节数组
     * @return HEX字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                sb.append('0');
            }
            sb.append(hex);
        }
        return sb.toString();
    }
    
    /**
     * 生成毫秒级时间戳
     * @return 毫秒级时间戳字符串
     */
    public static String generateTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }
} 
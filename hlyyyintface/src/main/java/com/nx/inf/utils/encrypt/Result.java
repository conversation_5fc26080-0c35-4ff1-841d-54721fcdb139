package com.nx.inf.utils.encrypt;

import com.nx.inf.utils.enums.ErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 系统统一返回对象
 * @Author: Jin<PERSON>hiBo
 * @CreateDate: 2023/6/27 9:55
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String DEFAULT_MESSAGE = "操作成功!";

    private static final Boolean DEFAULT_FLAG = true;

    private static final String SUCCESS_CODE = "00";

    /**
     * 成功标志(true-成功、false-失败)
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应代码(00-成功、其他-失败)
     */
    private String code = "00";

    /**
     * 响应结果
     */
    private T result;

    /**
     * 时间戳(返回时间戳)
     */
    private long timestamp = System.currentTimeMillis();

    public Result<T> success(String message) {
        this.message = message;
        this.code = SUCCESS_CODE;
        this.success = DEFAULT_FLAG;
        return this;
    }

    public static <T> Result<T> OK() {
        return OK(DEFAULT_MESSAGE, null);
    }

    public static <T> Result<T> OK(String msg) {
        return OK(msg, null);
    }

    public static <T> Result<T> OK(T data) {
        return OK(DEFAULT_MESSAGE, data);
    }

    public static <T> Result<T> OK(String msg, T data) {
        Result<T> r = new Result<>();
        r.setSuccess(DEFAULT_FLAG);
        r.setCode(SUCCESS_CODE);
        r.setMessage(msg);
        r.setResult(data);
        return r;
    }

    public static <T> Result<T> error(String msg, T data) {
        return error(ErrorCodeEnum.SC_SERVER_ERROR_A0400.getErrorCode(), msg, data);
    }

    public static <T> Result<T> error(ErrorCodeEnum ErrorCodeEnumEnum) {
        return error(ErrorCodeEnumEnum.getErrorCode(), ErrorCodeEnumEnum.getDescription(), null);
    }

    public static Result<Object> error(String msg) {
        return error(ErrorCodeEnum.SC_SERVER_ERROR_A0400.getErrorCode(), msg);
    }

    public static Result error(String code, String msg) {
        return error(code, msg, null);
    }

    public static <T> Result<T> error(String code, String msg, T data) {
        Result<T> r = new Result<>();
        r.setCode(code);
        r.setMessage(msg);
        r.setResult(data);
        r.setSuccess(false);
        return r;
    }

    public Result<T> error500(String message) {
        this.message = message;
        this.code = ErrorCodeEnum.SC_SERVER_ERROR_A0400.getErrorCode();
        this.success = false;
        return this;
    }

    /**
     * 400-参数校验失败
     *
     * @param message --- 错误消息
     * @return
     */
    public static Result<Object> error400(String message) {
        return error(ErrorCodeEnum.SC_SERVER_ERROR_A0403.getErrorCode(), message);
    }

    /**
     * 401-需要身份验证(-登录)
     *
     * @param message --- 错误消息
     * @return
     */
    public static Result<Object> error401(String message) {
        return error(ErrorCodeEnum.SC_SERVER_ERROR_A0400.getErrorCode(), message);
    }

    /**
     * 410-非法数据(数据不存在,或者禁止使用)
     *
     * @param message --- 错误消息
     * @return
     */
    public static Result<Object> error410(String message) {
        return error(ErrorCodeEnum.SC_SERVER_ERROR_A0400.getErrorCode(), message);
    }

}

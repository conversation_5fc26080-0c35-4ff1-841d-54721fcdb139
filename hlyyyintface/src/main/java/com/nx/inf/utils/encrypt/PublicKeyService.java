package com.nx.inf.utils.encrypt;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

public class PublicKeyService {

    private static final String API_URL = "http://61.129.134.236:8001/shihsp/test/outRequest/getPublicKey";

    /**
     * 获取 hosKeyPublic
     *
     * @param hosOrgCode 医院组织代码
     * @param hosName    医院名称
     * @return hosKeyPublic 字符串或 null（如果获取失败）
     */
    public static String getHosKeyPublic(String hosOrgCode, String hosName) {
        try {
            // 构建请求体的JSON字符串
            String jsonBody = JSONUtil.toJsonStr(new JSONObject()
                    .put("hosOrgCode", hosOrgCode)
                    .put("hosName", hosName));

            // 发送POST请求并接收响应
            HttpResponse response = HttpRequest.post(API_URL)
                    .header("Content-Type", "application/json")
                    .body(jsonBody)
                    .execute();

            // 检查响应状态码
            if (response.isOk()) {
                // 解析JSON响应
                JSONObject jsonResponse = JSONUtil.parseObj(response.body());

                // 提取hosKeyPublic字段
                return jsonResponse.getJSONObject("data").getStr("hosKeyPublic");
            } else {
                System.err.println("请求失败，状态码：" + response.getStatus() + "，消息：" + response.body());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null; // 如果获取失败返回null
    }
}
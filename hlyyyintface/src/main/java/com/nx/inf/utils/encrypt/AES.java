package com.nx.inf.utils.encrypt;

import cn.hutool.crypto.SecureUtil;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Random;

public class AES {
    private static final String ALGORITHM = "AES";
    private static final byte[] SECRET = new byte[]{'2', '0', '1', '5', '0', '2', '0', '1', '2', '0', '2', '0', '0', '1', '3', '1'};
    //Cipher负责完成加密或解密工作
    private Cipher encryptor;
    private Cipher decryptor;
    private volatile static AES instance = null;

    private volatile static AES instanceSecret = null;

    private static final Object mutex = new Object();

    public static AES getInstance() {
        if (instance == null) {
            synchronized (mutex) {
                if (instance == null) {
                    instance = new AES();
                }
            }
        }
        return instance;
    }

    public static AES getInstance(byte[] SECRET) {
        if (instanceSecret == null) {
            synchronized (mutex) {
                if (instanceSecret == null) {
                    instanceSecret = new AES(SECRET);
                }
            }
        }
        return instanceSecret;
    }

    private Charset charset;


    private AES() {
        try {
            this.charset = StandardCharsets.UTF_8;
            //密钥
            SecretKeySpec key = new SecretKeySpec(SECRET, ALGORITHM);
            //生成Cipher对象,指定其支持的AES算法；根据密钥，对Cipher对象进行初始化，ENCRYPT_MODE表示加密模式
            this.encryptor = Cipher.getInstance(ALGORITHM);
            this.encryptor.init(Cipher.ENCRYPT_MODE, key);
            this.decryptor = Cipher.getInstance(ALGORITHM);
            this.decryptor.init(Cipher.DECRYPT_MODE, key);
        } catch (Exception e) {
        }
    }

    public AES(byte[] secret) {
        try {
            this.charset = StandardCharsets.UTF_8;
            //密钥
            SecretKeySpec key = new SecretKeySpec(secret, ALGORITHM);
            //生成Cipher对象,指定其支持的AES算法；根据密钥，对Cipher对象进行初始化，ENCRYPT_MODE表示加密模式
            this.encryptor = Cipher.getInstance(ALGORITHM);
            this.encryptor.init(Cipher.ENCRYPT_MODE, key);
            this.decryptor = Cipher.getInstance(ALGORITHM);
            this.decryptor.init(Cipher.DECRYPT_MODE, key);
        } catch (Exception e) {
        }
    }

    public String encrypt(String content) throws Exception {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] buffer = this.encryptor.doFinal(content.getBytes(this.charset));
        byte[] encode = Base64.encode(buffer);
        return new String(encode, this.charset);
    }

    public String decrypt(String content) throws Exception {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] stt = Base64.decode(content);
        byte[] buffer = this.decryptor.doFinal(stt);
        return new String(buffer, this.charset);
    }


    public String encrypt(String content, String password) {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] buffer = new byte[0];
        try {
            Cipher instance = Cipher.getInstance(ALGORITHM);
            instance.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(password.getBytes(), ALGORITHM));
            buffer = instance.doFinal(content.getBytes(this.charset));
        } catch (Exception e) {
        }
        byte[] encode = Base64.encode(buffer);
        return new String(encode, this.charset);
    }


    public String decrypt(String content, String password) {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] buffer = new byte[0];
        try {
            Cipher instance = Cipher.getInstance(ALGORITHM);
            byte[] bytes = password.getBytes();
            SecretKeySpec key = new SecretKeySpec(bytes, ALGORITHM);
            instance.init(Cipher.DECRYPT_MODE, key);
            buffer = instance.doFinal(Base64.decode(content));
        } catch (Exception e) {
        }
        return new String(buffer);
    }

    public String encryptHex(String content, String password) {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] buffer = new byte[0];
        try {
            Cipher instance = Cipher.getInstance(ALGORITHM);
            instance.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(password.getBytes(), ALGORITHM));
            buffer = instance.doFinal(content.getBytes(this.charset));
        } catch (Exception e) {
        }
        byte[] encode = Hex.encode(buffer);
        return new String(encode, this.charset);
    }

    public String decryptHex(String content, String password) {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] buffer = new byte[0];
        try {
            Cipher instance = Cipher.getInstance(ALGORITHM);
            byte[] bytes = password.getBytes();
            SecretKeySpec key = new SecretKeySpec(bytes, ALGORITHM);
            instance.init(Cipher.DECRYPT_MODE, key);
            byte[] decode = Hex.decode(content);
            buffer = instance.doFinal(decode);
        } catch (Exception e) {
        }
        return new String(buffer, this.charset);
    }

    public static void main(String[] args) {
        try {
            Random r = new Random();
            for (int i = 0; i < 5; i++) {
                int ran1 = r.nextInt(100);
                System.out.println(ran1);
            }

            String param = "{" + "\"pageNum\":1," + "\"pageSize\":10," + "\"name\":\"\"" + "}";
            System.out.println("加密后" + AES.getInstance().encrypt(param));

            String decryptStr = AES.getInstance().decrypt("I0L8AM0yWBo1Rq9f1LtXrhhZY7utKyRi3lWWleEvh49ij2gnlm5jCJcbAeMxFAV+ej1jDqHGHxRKnqiffba+MVXwxbGdjJKaVxPve0aM8oX6jJ24tfwy7cZGJgLKAOVyY2UvtVBItRpouo1nhHzBlp+hbeXKEmBdQQQsXYnxvkqApG/Ky3NfgFx2WjMRz0J9Tc2dk3PZeI9qBO7JEg5z3cM2rwo9IM/bsuQzWoxNa/wk9LEgL5QPKC+NfpcTW0Qnqy1u5kQgLSMzHD5lr9yxp7Lc8SfdIWjCm0nUS4xU6KvT3F82hcK9G+3pq6UlRu7hPNyfQUW6Z855435q0b0EzHdvt52L/UU638OEu0wlkFWypJg0/RKTKIiPjiHq9+NRRDHspCQ96utfiLB1hCg55Hm0+wLaDOcxavFJbPAi7/wjHYpcTQIB9+vyKld9NJLDjyH9TMKHP2fYkRScM37obU5h3EG2ybVQtBh6Gk3h7TaoacnAuNUv7YKuj0UMHRrS9cyqxFJm2x9KXfXmDhJbt5NfeLZEEnnOfrHbdD+vj3yuxQ8og+/B5Kh76LSqXsXewzavCj0gz9uy5DNajE1r/LhzEBCHPjBuUgbDWjPCGbqhuCV8nkjUyMHuE0Yuonwi8kWM/Ew8R/nwl8VWwnYDE9UZYQBNOSH5ktX6MsZbO6nOoDzi+zQEs9RR5xVJIK9ZmL+p/hvefWerRao9PGA6EBWMFAeiQ9KKZY8hv4x0yKg4IWznWfHk/8fAVItj8Sqlg5afH1ELC+bhks33V7vaB+CNR7jlzhFyfxJUPF5uFVraDfl0qGzrkcxkvAMHeMla+gi95hmg1rc9PNLrilrVZiiAK59wzeU9FAH8kZAo3RyWaoaFreSZKRUtGqMOFKe820SVmhvT+A65gViHEPeQhAOq16oiAf0ryQI1LUSEIY2WJcbpUi/2tTKU3LAUEV4d9vvI7XA9E4vIs5HfWrRjUeAvYFnkgs8GiQ3ADGHh/vK5622PofXZJAOsRpnjxHAABn+ZxcahlHpNssGRxGlfl6pAT4fJsgLaXXeENK3YIsHxuN5M1aBt+3igfQIf5ncsKHTF6518Es2lNbxLu+48wPrtowrngydj09VTgdp88ZXoKDAgd//cZoNe4X9CrPjJrRpjghApbgoc/XQcyZCK2D7wFQeyxc2gbvDGrc5kM4hATOzvGzXkwgulijSEP3/4o7CXlfD05XoEInNKIAGoLwgR7OeHh10fH7X7e5dQBE8VehVt+xVntdvKAY5hDMW5wgfexYqsXdus74Zd5MNpCTPm1qd9weZMC2WOXmSsEJzUlQ5L6spEODa6sZBW1Phm+kpnq7NElJ8yE1vFyYa06mcH5SFvwMiWgBEvmY5+zB1+oc0jZ4iPCulqEnz53gKSVs6FGVSQJ/MPYaNgiFfS+8WLAL2sNl9v/B73G3GKGv+vos1D8SMU+V7iR+OYA7vxLKZpT0Avz4vGPJITcwDwMw==");

            System.out.println("解密后：" + decryptStr);

            String md5 = SecureUtil.md5(decryptStr).toLowerCase();

            System.out.println(md5);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

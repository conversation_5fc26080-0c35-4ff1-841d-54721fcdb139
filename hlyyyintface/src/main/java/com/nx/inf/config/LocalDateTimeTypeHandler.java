package com.nx.inf.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.time.LocalDateTime;

/**
 * 处理Java 8 LocalDateTime与MySQL 5.x日期时间类型的转换
 */
@MappedTypes(LocalDateTime.class)
public class LocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        ps.setTimestamp(i, Timestamp.valueOf(parameter));
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            // 首先尝试使用 getTimestamp
            Timestamp timestamp = rs.getTimestamp(columnName);
            return getLocalDateTime(timestamp);
        } catch (SQLFeatureNotSupportedException e) {
            // 如果 getTimestamp 不支持，尝试使用 getString 然后解析
            String dateStr = rs.getString(columnName);
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                try {
                    return LocalDateTime.parse(dateStr.replace(' ', 'T'));
                } catch (Exception parseEx) {
                    // 如果解析失败，返回 null
                    return null;
                }
            }
            return null;
        }
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            // 首先尝试使用 getTimestamp
            Timestamp timestamp = rs.getTimestamp(columnIndex);
            return getLocalDateTime(timestamp);
        } catch (SQLFeatureNotSupportedException e) {
            // 如果 getTimestamp 不支持，尝试使用 getString 然后解析
            String dateStr = rs.getString(columnIndex);
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                try {
                    return LocalDateTime.parse(dateStr.replace(' ', 'T'));
                } catch (Exception parseEx) {
                    // 如果解析失败，返回 null
                    return null;
                }
            }
            return null;
        }
    }

    @Override
    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            // 首先尝试使用 getTimestamp
            Timestamp timestamp = cs.getTimestamp(columnIndex);
            return getLocalDateTime(timestamp);
        } catch (SQLFeatureNotSupportedException e) {
            // 如果 getTimestamp 不支持，尝试使用 getString 然后解析
            String dateStr = cs.getString(columnIndex);
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                try {
                    return LocalDateTime.parse(dateStr.replace(' ', 'T'));
                } catch (Exception parseEx) {
                    // 如果解析失败，返回 null
                    return null;
                }
            }
            return null;
        }
    }

    private static LocalDateTime getLocalDateTime(Timestamp timestamp) {
        if (timestamp != null) {
            return timestamp.toLocalDateTime();
        }
        return null;
    }
} 
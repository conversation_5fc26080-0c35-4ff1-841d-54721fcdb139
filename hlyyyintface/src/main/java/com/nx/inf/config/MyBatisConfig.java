package com.nx.inf.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;


import java.util.Date;
import java.time.LocalDateTime;

/**
 * MyBatis配置类
 */
@Configuration
@MapperScan("com.nx.inf.mapper")
public class MyBatisConfig {
    
    /**
     * 注册自定义全局配置
     */
    @Bean
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setMetaObjectHandler(new MyMetaObjectHandler());
        
        // 注册类型处理器
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        globalConfig.setDbConfig(dbConfig);
        
        return globalConfig;
    }
    

    
    /**
     * 自定义元对象处理器，用于自动填充创建时间、更新时间等
     */
    public static class MyMetaObjectHandler implements com.baomidou.mybatisplus.core.handlers.MetaObjectHandler {
        @Override
        public void insertFill(org.apache.ibatis.reflection.MetaObject metaObject) {
            // 创建时间和更新时间自动填充
            // 根据字段类型自动选择合适的时间类型
            Object createTime = metaObject.getValue("createTime");
            Object updateTime = metaObject.getValue("updateTime");
            
            if (createTime == null) {
                if (metaObject.getSetterType("createTime") == LocalDateTime.class) {
                    this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
                } else {
                    this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
                }
            }
            
            if (updateTime == null) {
                if (metaObject.getSetterType("updateTime") == LocalDateTime.class) {
                    this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                } else {
                    this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
                }
            }
        }

        @Override
        public void updateFill(org.apache.ibatis.reflection.MetaObject metaObject) {
            // 更新时间自动填充
            Object updateTime = metaObject.getValue("updateTime");
            if (updateTime == null) {
                if (metaObject.getSetterType("updateTime") == LocalDateTime.class) {
                    this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
                } else {
                    this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
                }
            }
        }
    }
} 
package com.nx.inf.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置类
 */
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {
    
    /**
     * 文件上传根路径
     */
    private String path = "./uploads";
    
    /**
     * 图片文件夹
     */
    private String imageFolder = "images";
    
    /**
     * 最大文件大小 (字节)
     */
    private Long maxSize = 10485760L; // 10MB
    
    /**
     * 允许的图片类型
     */
    private String allowedTypes = "image/jpeg,image/jpg,image/png,image/gif,image/bmp,image/webp";
    
    // Getters and Setters
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getImageFolder() {
        return imageFolder;
    }
    
    public void setImageFolder(String imageFolder) {
        this.imageFolder = imageFolder;
    }
    
    public Long getMaxSize() {
        return maxSize;
    }
    
    public void setMaxSize(Long maxSize) {
        this.maxSize = maxSize;
    }
    
    public String getAllowedTypes() {
        return allowedTypes;
    }
    
    public void setAllowedTypes(String allowedTypes) {
        this.allowedTypes = allowedTypes;
    }
    
    /**
     * 获取允许的图片类型数组
     */
    public String[] getAllowedTypesArray() {
        if (allowedTypes == null || allowedTypes.trim().isEmpty()) {
            return new String[0];
        }
        return allowedTypes.split(",");
    }
}

package com.nx.inf.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;


/**
 * @Description: 定义多个数据源
 * @Author: JinShiBo
 * @CreateDate: 2023/7/13 23:41
 * @Version: 1.0
 */
@Configuration
public class DataSourceConfig {

    @Bean(name = "mysqlDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.mysql-datasource")
    public DataSource dataSource1(){
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        return build;
    }
    @Bean(name = "sqlserverDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.sqlserver-datasource")
    public DataSource dataSource2(){
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        return build;
    }
}

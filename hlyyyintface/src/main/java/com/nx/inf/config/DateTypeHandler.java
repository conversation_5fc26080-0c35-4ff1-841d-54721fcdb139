package com.nx.inf.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.Date;

/**
 * 处理Java Date与MySQL日期时间类型的转换
 */
@MappedTypes(Date.class)
public class DateTypeHandler extends BaseTypeHandler<Date> {
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Date parameter, JdbcType jdbcType) throws SQLException {
        ps.setTimestamp(i, new Timestamp(parameter.getTime()));
    }

    @Override
    public Date getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            // 首先尝试使用 getTimestamp
            Timestamp timestamp = rs.getTimestamp(columnName);
            return getDate(timestamp);
        } catch (SQLFeatureNotSupportedException e) {
            // 如果 getTimestamp 不支持，尝试使用 getString 然后解析
            String dateStr = rs.getString(columnName);
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                try {
                    // 尝试解析ISO 8601格式
                    if (dateStr.contains("T")) {
                        return new Date(java.time.LocalDateTime.parse(dateStr.replace(' ', 'T')).atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
                    } else {
                        // 尝试解析标准格式
                        return new Date(java.sql.Timestamp.valueOf(dateStr).getTime());
                    }
                } catch (Exception parseEx) {
                    // 如果解析失败，返回 null
                    return null;
                }
            }
            return null;
        }
    }

    @Override
    public Date getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            // 首先尝试使用 getTimestamp
            Timestamp timestamp = rs.getTimestamp(columnIndex);
            return getDate(timestamp);
        } catch (SQLFeatureNotSupportedException e) {
            // 如果 getTimestamp 不支持，尝试使用 getString 然后解析
            String dateStr = rs.getString(columnIndex);
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                try {
                    // 尝试解析ISO 8601格式
                    if (dateStr.contains("T")) {
                        return new Date(java.time.LocalDateTime.parse(dateStr.replace(' ', 'T')).atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
                    } else {
                        // 尝试解析标准格式
                        return new Date(java.sql.Timestamp.valueOf(dateStr).getTime());
                    }
                } catch (Exception parseEx) {
                    // 如果解析失败，返回 null
                    return null;
                }
            }
            return null;
        }
    }

    @Override
    public Date getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            // 首先尝试使用 getTimestamp
            Timestamp timestamp = cs.getTimestamp(columnIndex);
            return getDate(timestamp);
        } catch (SQLFeatureNotSupportedException e) {
            // 如果 getTimestamp 不支持，尝试使用 getString 然后解析
            String dateStr = cs.getString(columnIndex);
            if (dateStr != null && !dateStr.trim().isEmpty()) {
                try {
                    // 尝试解析ISO 8601格式
                    if (dateStr.contains("T")) {
                        return new Date(java.time.LocalDateTime.parse(dateStr.replace(' ', 'T')).atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
                    } else {
                        // 尝试解析标准格式
                        return new Date(java.sql.Timestamp.valueOf(dateStr).getTime());
                    }
                } catch (Exception parseEx) {
                    // 如果解析失败，返回 null
                    return null;
                }
            }
            return null;
        }
    }

    private static Date getDate(Timestamp timestamp) {
        if (timestamp != null) {
            return new Date(timestamp.getTime());
        }
        return null;
    }
} 
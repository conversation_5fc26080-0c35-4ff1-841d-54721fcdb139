package com.nx.inf.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 百度OCR配置
 */
@Configuration
@ConfigurationProperties(prefix = "baidu.ocr")
public class BaiduOCRConfig {
    
    /**
     * 百度OCR API访问令牌
     */
    private String accessToken = "24.5b5cbe6d2ae745568b070c1801ee0918.2592000.1755706804.282335-119570890";
    
    /**
     * 百度OCR API地址
     */
    private String apiUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic";
    
    /**
     * 是否启用图片方向检测
     */
    private boolean detectDirection = true;
    
    /**
     * 是否返回置信度
     */
    private boolean probability = true;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;
    
    // Getters and Setters
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public String getApiUrl() {
        return apiUrl;
    }
    
    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
    
    public boolean isDetectDirection() {
        return detectDirection;
    }
    
    public void setDetectDirection(boolean detectDirection) {
        this.detectDirection = detectDirection;
    }
    
    public boolean isProbability() {
        return probability;
    }
    
    public void setProbability(boolean probability) {
        this.probability = probability;
    }
    
    public int getConnectTimeout() {
        return connectTimeout;
    }
    
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
}

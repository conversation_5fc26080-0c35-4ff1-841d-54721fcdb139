package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.ContentTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容模板Mapper接口
 */
@Mapper
public interface ContentTemplateMapper extends BaseMapper<ContentTemplate> {
    
    /**
     * 分页查询模板列表
     * 
     * @param page 分页参数
     * @param category 分类过滤
     * @param keyword 关键词搜索
     * @param permission 权限过滤
     * @param status 状态过滤
     * @param currentUserId 当前用户ID（用于权限过滤）
     * @param currentDeptId 当前用户科室ID（用于权限过滤）
     * @return 分页结果
     */
    IPage<ContentTemplate> selectTemplatePage(Page<ContentTemplate> page,
                                             @Param("category") String category,
                                             @Param("keyword") String keyword,
                                             @Param("permission") String permission,
                                             @Param("status") String status,
                                             @Param("currentUserId") Long currentUserId,
                                             @Param("currentDeptId") Long currentDeptId);
    
    /**
     * 根据分类获取模板列表
     * 
     * @param page 分页参数
     * @param category 分类编码
     * @param status 状态
     * @param currentUserId 当前用户ID
     * @param currentDeptId 当前用户科室ID
     * @return 分页结果
     */
    IPage<ContentTemplate> selectTemplatesByCategory(Page<ContentTemplate> page,
                                                    @Param("category") String category,
                                                    @Param("status") String status,
                                                    @Param("currentUserId") Long currentUserId,
                                                    @Param("currentDeptId") Long currentDeptId);
    
    /**
     * 搜索模板
     * 
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param category 限定分类
     * @param permission 权限过滤
     * @param currentUserId 当前用户ID
     * @param currentDeptId 当前用户科室ID
     * @return 分页结果
     */
    IPage<ContentTemplate> searchTemplates(Page<ContentTemplate> page,
                                          @Param("keyword") String keyword,
                                          @Param("category") String category,
                                          @Param("permission") String permission,
                                          @Param("currentUserId") Long currentUserId,
                                          @Param("currentDeptId") Long currentDeptId);
    
    /**
     * 根据ID查询模板详情（包含权限检查）
     * 
     * @param id 模板ID
     * @param currentUserId 当前用户ID
     * @param currentDeptId 当前用户科室ID
     * @return 模板信息
     */
    ContentTemplate selectTemplateByIdWithPermission(@Param("id") Long id,
                                                    @Param("currentUserId") Long currentUserId,
                                                    @Param("currentDeptId") Long currentDeptId);
    
    /**
     * 更新模板使用次数
     * 
     * @param id 模板ID
     * @return 更新行数
     */
    int updateUseCount(@Param("id") Long id);
    
    /**
     * 批量删除模板（逻辑删除）
     * 
     * @param ids 模板ID列表
     * @return 删除行数
     */
    int batchDeleteTemplates(@Param("ids") List<Long> ids);
    
    /**
     * 批量删除模板（物理删除，仅限权限检查通过的）
     * 
     * @param ids 模板ID列表
     * @param currentUserId 当前用户ID
     * @param currentDeptId 当前用户科室ID
     * @return 删除行数
     */
    int batchDeleteTemplatesWithPermission(@Param("ids") List<Long> ids,
                                          @Param("currentUserId") Long currentUserId,
                                          @Param("currentDeptId") Long currentDeptId);
} 
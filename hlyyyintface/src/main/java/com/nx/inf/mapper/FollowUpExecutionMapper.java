package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.FollowUpExecution;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随访执行记录Mapper接口
 */
@Mapper
public interface FollowUpExecutionMapper extends BaseMapper<FollowUpExecution> {
    
    /**
     * 分页查询随访执行记录
     * 
     * @param page 分页参数
     * @param planId 计划ID
     * @param patientName 患者姓名
     * @param warningFlag 是否有异常告警
     * @return 分页结果
     */
    IPage<FollowUpExecution> selectExecutionPage(Page<FollowUpExecution> page, 
                                               @Param("planId") Long planId,
                                               @Param("patientName") String patientName,
                                               @Param("warningFlag") Boolean warningFlag);
    
    /**
     * 根据计划ID查询执行记录
     * 
     * @param planId 计划ID
     * @return 执行记录列表
     */
    List<FollowUpExecution> selectByPlanId(@Param("planId") Long planId);
    
    /**
     * 根据患者ID查询执行记录
     * 
     * @param patientId 患者ID
     * @return 执行记录列表
     */
    List<FollowUpExecution> selectByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 根据计划ID和患者ID查询执行记录
     * 
     * @param planId 计划ID
     * @param patientId 患者ID
     * @return 执行记录列表
     */
    List<FollowUpExecution> selectByPlanIdAndPatientId(@Param("planId") Long planId, 
                                                     @Param("patientId") Long patientId);
    
    /**
     * 根据计划ID查询患者列表（去重）
     * 
     * @param planId 计划ID
     * @return 患者信息列表
     */
    List<FollowUpExecution> selectPatientsByPlanId(@Param("planId") Long planId);
} 
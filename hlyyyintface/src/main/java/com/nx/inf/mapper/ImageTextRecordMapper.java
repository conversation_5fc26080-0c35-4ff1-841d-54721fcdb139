package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.ImageTextRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片文字识别记录Mapper接口
 */
@Mapper
public interface ImageTextRecordMapper extends BaseMapper<ImageTextRecord> {
    
    /**
     * 根据ID查询记录（使用自定义ResultMap）
     * 
     * @param id 记录ID
     * @return 记录信息
     */
    ImageTextRecord selectRecordById(@Param("id") Long id);
    
    /**
     * 分页查询记录列表
     * 
     * @param page 分页参数
     * @param status 处理状态
     * @param creatorId 创建人ID
     * @param originalFilename 原始文件名
     * @return 分页结果
     */
    IPage<ImageTextRecord> selectRecordPage(Page<ImageTextRecord> page, 
                                          @Param("status") String status, 
                                          @Param("creatorId") Long creatorId, 
                                          @Param("originalFilename") String originalFilename);
    
    /**
     * 根据创建人ID查询记录列表
     * 
     * @param creatorId 创建人ID
     * @return 记录列表
     */
    List<ImageTextRecord> selectByCreatorId(@Param("creatorId") Long creatorId);
    
    /**
     * 根据状态查询记录列表
     * 
     * @param status 处理状态
     * @return 记录列表
     */
    List<ImageTextRecord> selectByStatus(@Param("status") String status);
}

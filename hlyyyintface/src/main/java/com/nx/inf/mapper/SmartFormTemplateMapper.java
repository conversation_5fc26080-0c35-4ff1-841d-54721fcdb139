package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.SmartFormTemplate;
import com.nx.inf.model.vo.SmartFormTemplateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 智能表单模板Mapper接口
 */
@Mapper
public interface SmartFormTemplateMapper extends BaseMapper<SmartFormTemplate> {
    
    /**
     * 分页查询表单模板列表
     */
    IPage<SmartFormTemplateVO> selectFormTemplatePage(Page<SmartFormTemplateVO> page, @Param("params") Map<String, Object> params);
    
    /**
     * 根据ID查询表单模板详情（包含问卷关联和其他数据）
     */
    SmartFormTemplateVO selectFormTemplateDetailById(@Param("id") Long id);
    
    /**
     * 查询表单模板的问卷关联详情
     */
    List<Map<String, Object>> selectFormQuestionnaireRelations(@Param("formTemplateId") Long formTemplateId);
    
    /**
     * 查询表单模板的其他数据配置
     */
    List<Map<String, Object>> selectFormOtherData(@Param("formTemplateId") Long formTemplateId);
    
    /**
     * 查询表单模板统计信息
     */
    Map<String, Object> selectFormTemplateStatistics(@Param("id") Long id);
    
    /**
     * 批量删除表单模板
     */
    int batchDeleteFormTemplates(@Param("ids") List<Long> ids);
    
    /**
     * 复制表单模板
     */
    int copyFormTemplate(@Param("sourceId") Long sourceId, @Param("newTemplate") SmartFormTemplate newTemplate);
} 
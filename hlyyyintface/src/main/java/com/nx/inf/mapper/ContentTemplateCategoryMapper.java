package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.ContentTemplateCategory;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 内容模板分类Mapper接口
 */
@Mapper
public interface ContentTemplateCategoryMapper extends BaseMapper<ContentTemplateCategory> {
    
    /**
     * 获取所有启用的分类列表，按排序号排序
     * 
     * @return 分类列表
     */
    List<ContentTemplateCategory> selectActiveCategoriesOrderBySort();
} 
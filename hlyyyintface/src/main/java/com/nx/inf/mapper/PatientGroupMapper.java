package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.PatientGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 患者分组Mapper接口
 */
@Mapper
public interface PatientGroupMapper extends BaseMapper<PatientGroup> {
    
    /**
     * 分页查询患者分组列表
     * 
     * @param page 分页参数
     * @param name 组名
     * @return 分页结果
     */
    IPage<PatientGroup> selectGroupPage(Page<PatientGroup> page, @Param("name") String name);
    
    /**
     * 更新患者数量
     * 
     * @param groupId 分组ID
     * @return 更新行数
     */
    int updatePatientCount(@Param("groupId") Long groupId);
} 
package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.FollowUpPlanDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 随访计划详情Mapper接口
 */
@Mapper
public interface FollowUpPlanDetailMapper extends BaseMapper<FollowUpPlanDetail> {
    
    /**
     * 根据计划ID查询详情
     * 
     * @param planId 计划ID
     * @return 计划详情
     */
    FollowUpPlanDetail selectByPlanId(@Param("planId") Long planId);
} 
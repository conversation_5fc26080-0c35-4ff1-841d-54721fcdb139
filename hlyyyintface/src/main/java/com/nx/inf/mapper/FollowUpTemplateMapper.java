package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.FollowUpTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随访计划模板Mapper接口
 */
@Mapper
public interface FollowUpTemplateMapper extends BaseMapper<FollowUpTemplate> {
    
    /**
     * 查询随访计划模板列表
     * 
     * @param name 模板名称
     * @param category 分类
     * @return 模板列表
     */
    List<FollowUpTemplate> selectTemplateList(@Param("name") String name, 
                                             @Param("category") String category);
} 
package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.NursingHomeInstitution;
import com.nx.inf.model.dto.NursingHomeInstitutionDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 养老院机构Mapper接口
 */
@Mapper
public interface NursingHomeInstitutionMapper extends BaseMapper<NursingHomeInstitution> {
    
    /**
     * 分页查询机构列表
     * 
     * @param page 分页参数
     * @param name 机构名称（模糊搜索）
     * @param code 机构代码
     * @param status 状态过滤
     * @param level 机构等级
     * @param province 省份
     * @param city 城市
     * @param district 区县
     * @param keyword 关键词搜索（机构名称、地址、联系人）
     * @param startTime 创建时间开始
     * @param endTime 创建时间结束
     * @return 分页结果
     */
    IPage<NursingHomeInstitutionDTO> selectInstitutionPage(Page<NursingHomeInstitutionDTO> page,
                                                          @Param("name") String name,
                                                          @Param("code") String code,
                                                          @Param("status") Integer status,
                                                          @Param("level") String level,
                                                          @Param("province") String province,
                                                          @Param("city") String city,
                                                          @Param("district") String district,
                                                          @Param("keyword") String keyword,
                                                          @Param("startTime") String startTime,
                                                          @Param("endTime") String endTime);
    
    /**
     * 根据ID查询机构详情
     * 
     * @param id 机构ID
     * @return 机构信息
     */
    NursingHomeInstitutionDTO selectInstitutionById(@Param("id") Long id);
    
    /**
     * 根据机构代码查询机构
     * 
     * @param code 机构代码
     * @return 机构信息
     */
    NursingHomeInstitution selectByCode(@Param("code") String code);
    
    /**
     * 根据机构名称查询机构（模糊搜索）
     * 
     * @param name 机构名称
     * @return 机构列表
     */
    List<NursingHomeInstitution> selectByNameLike(@Param("name") String name);
    
    /**
     * 获取所有启用状态的机构
     * 
     * @return 机构列表
     */
    List<NursingHomeInstitutionDTO> selectAllActive();
    
    /**
     * 批量更新机构状态
     * 
     * @param ids 机构ID列表
     * @param status 状态值
     * @param updateBy 更新人
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") Integer status,
                         @Param("updateBy") String updateBy);
    
    /**
     * 批量删除机构（逻辑删除）
     * 
     * @param ids 机构ID列表
     * @param updateBy 更新人
     * @return 删除行数
     */
    int batchDelete(@Param("ids") List<Long> ids,
                   @Param("updateBy") String updateBy);
    
    /**
     * 统计机构数量
     * 
     * @param status 状态过滤
     * @param level 等级过滤
     * @param province 省份过滤
     * @param city 城市过滤
     * @return 机构数量
     */
    Long countInstitutions(@Param("status") Integer status,
                          @Param("level") String level,
                          @Param("province") String province,
                          @Param("city") String city);
    
    /**
     * 更新机构入住人数
     * 
     * @param id 机构ID
     * @param currentPatients 当前入住人数
     * @return 更新行数
     */
    int updateCurrentPatients(@Param("id") Long id,
                             @Param("currentPatients") Integer currentPatients);
    
    /**
     * 根据地理位置查询附近机构
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param radius 半径（公里）
     * @param limit 返回数量限制
     * @return 附近机构列表
     */
    List<NursingHomeInstitutionDTO> selectNearbyInstitutions(@Param("longitude") String longitude,
                                                            @Param("latitude") String latitude,
                                                            @Param("radius") Double radius,
                                                            @Param("limit") Integer limit);
} 
package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.FollowupPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 随访计划Mapper接口
 */
@Mapper
public interface FollowupPlanMapper extends BaseMapper<FollowupPlan> {
    
    /**
     * 分页查询随访计划列表
     * 
     * @param page 分页参数
     * @param category 分类
     * @param status 状态
     * @param name 名称
     * @return 分页结果
     */
    IPage<FollowupPlan> selectPlanPage(Page<FollowupPlan> page, 
                                      @Param("category") String category,
                                      @Param("status") String status,
                                      @Param("name") String name);
    
    /**
     * 更新患者计数
     * 
     * @param planId 计划ID
     * @return 更新行数
     */
    int updatePatientCount(@Param("planId") Long planId);
    
    /**
     * 更新任务计数
     * 
     * @param planId 计划ID
     * @return 更新行数
     */
    int updateTaskCount(@Param("planId") Long planId);
    
    /**
     * 更新随访次数
     * 
     * @param planId 计划ID
     * @return 更新行数
     */
    int updateVisitCount(@Param("planId") Long planId);
    
    /**
     * 根据ID查询随访计划详情（不包含subplanCount字段）
     * 
     * @param id 计划ID
     * @return 随访计划
     */
    FollowupPlan selectPlanById(@Param("id") Long id);
} 
package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.SmartFormQuestionnaireRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 表单问卷关联Mapper接口
 */
@Mapper
public interface SmartFormQuestionnaireRelMapper extends BaseMapper<SmartFormQuestionnaireRel> {
    
    /**
     * 根据表单模板ID查询问卷关联列表
     */
    List<Map<String, Object>> selectQuestionnaireRelsByFormId(@Param("formTemplateId") Long formTemplateId);
    
    /**
     * 批量插入问卷关联
     */
    int batchInsertQuestionnaireRels(@Param("rels") List<SmartFormQuestionnaireRel> rels);
    
    /**
     * 根据表单模板ID删除问卷关联
     */
    int deleteByFormTemplateId(@Param("formTemplateId") Long formTemplateId);
    
    /**
     * 根据表单模板ID彻底删除已软删除的问卷关联
     */
    int deleteByFormTemplateIdAndDeleted(@Param("formTemplateId") Long formTemplateId);
} 
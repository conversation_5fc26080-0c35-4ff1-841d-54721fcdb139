package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.FollowupSubplanItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随访子计划项目Mapper接口
 */
@Mapper
public interface FollowupSubplanItemMapper extends BaseMapper<FollowupSubplanItem> {
    
    /**
     * 根据子计划ID查询项目列表
     * 
     * @param subplanId 子计划ID
     * @return 项目列表
     */
    List<FollowupSubplanItem> selectBySubplanId(@Param("subplanId") Long subplanId);
} 
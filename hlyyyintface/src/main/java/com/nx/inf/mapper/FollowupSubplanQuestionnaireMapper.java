package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.FollowupSubplanQuestionnaire;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随访子计划问卷关联Mapper接口
 */
@Mapper
public interface FollowupSubplanQuestionnaireMapper extends BaseMapper<FollowupSubplanQuestionnaire> {
    
    /**
     * 根据子计划ID查询问卷列表
     * 
     * @param subplanId 子计划ID
     * @return 问卷列表
     */
    List<FollowupSubplanQuestionnaire> selectBySubplanId(@Param("subplanId") Long subplanId);
} 
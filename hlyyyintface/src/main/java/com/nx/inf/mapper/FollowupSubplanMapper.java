package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.FollowupSubplan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随访子计划Mapper接口
 */
@Mapper
public interface FollowupSubplanMapper extends BaseMapper<FollowupSubplan> {
    
    /**
     * 根据计划ID查询子计划列表
     * 
     * @param planId 计划ID
     * @return 子计划列表
     */
    List<FollowupSubplan> selectByPlanId(@Param("planId") Long planId);
} 
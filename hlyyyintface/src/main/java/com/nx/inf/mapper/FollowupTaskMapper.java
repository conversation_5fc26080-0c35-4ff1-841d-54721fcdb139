package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.FollowupTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 随访任务Mapper接口
 */
@Mapper
public interface FollowupTaskMapper extends BaseMapper<FollowupTask> {
    
    /**
     * 分页查询随访任务列表
     * 
     * @param page 分页参数
     * @param planId 计划ID
     * @param subplanId 子计划ID
     * @param patientId 患者ID
     * @param status 状态
     * @return 分页结果
     */
    IPage<FollowupTask> selectTaskPage(Page<FollowupTask> page,
                                      @Param("planId") Long planId,
                                      @Param("subplanId") Long subplanId,
                                      @Param("patientId") Long patientId,
                                      @Param("status") String status);
    
    /**
     * 根据计划ID查询任务列表
     * 
     * @param planId 计划ID
     * @return 任务列表
     */
    List<FollowupTask> selectByPlanId(@Param("planId") Long planId);
    
    /**
     * 根据患者ID查询任务列表
     * 
     * @param patientId 患者ID
     * @return 任务列表
     */
    List<FollowupTask> selectByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 统计计划任务数量
     * 
     * @param planId 计划ID
     * @return 任务数量
     */
    int countByPlanId(@Param("planId") Long planId);
    
    /**
     * 统计计划已完成任务数量
     * 
     * @param planId 计划ID
     * @return 已完成任务数量
     */
    int countCompletedByPlanId(@Param("planId") Long planId);
} 
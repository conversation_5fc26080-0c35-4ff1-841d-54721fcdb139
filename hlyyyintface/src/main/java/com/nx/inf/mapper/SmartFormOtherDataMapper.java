package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.SmartFormOtherData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单其他数据Mapper接口
 */
@Mapper
public interface SmartFormOtherDataMapper extends BaseMapper<SmartFormOtherData> {
    
    /**
     * 根据表单模板ID查询其他数据列表
     */
    List<SmartFormOtherData> selectOtherDataByFormId(@Param("formTemplateId") Long formTemplateId);
    
    /**
     * 批量插入其他数据
     */
    int batchInsertOtherData(@Param("dataList") List<SmartFormOtherData> dataList);
    
    /**
     * 根据表单模板ID删除其他数据
     */
    int deleteByFormTemplateId(@Param("formTemplateId") Long formTemplateId);
} 
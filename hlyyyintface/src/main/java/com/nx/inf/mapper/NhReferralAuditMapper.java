package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.NhReferralAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 转介审核记录Mapper接口
 */
@Mapper
public interface NhReferralAuditMapper extends BaseMapper<NhReferralAudit> {
    
    /**
     * 获取转介审核记录列表（含机构信息）
     * 
     * @param record 查询条件
     * @return 转介审核记录列表
     */
    List<NhReferralAudit> getReferralAuditList(@Param("record") NhReferralAudit record);
    
    /**
     * 根据ID获取转介审核记录详情（含机构信息）
     * 
     * @param id 记录ID
     * @return 转介审核记录详情
     */
    NhReferralAudit getReferralAuditWithInstitution(@Param("id") Long id);
    
    /**
     * 更新审核状态
     * 
     * @param id 记录ID
     * @param auditStatus 审核状态
     * @param auditorId 审核人ID
     * @param auditorName 审核人姓名
     * @param auditRemarks 审核备注
     * @return 影响行数
     */
    int updateAuditStatus(@Param("id") Long id, 
                         @Param("auditStatus") String auditStatus,
                         @Param("auditorId") Long auditorId,
                         @Param("auditorName") String auditorName,
                         @Param("auditRemarks") String auditRemarks);
} 
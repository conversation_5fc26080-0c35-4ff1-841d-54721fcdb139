package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.Patient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 患者Mapper接口
 */
@Mapper
public interface PatientMapper extends BaseMapper<Patient> {
    
    /**
     * 根据ID查询患者（使用自定义ResultMap）
     * 
     * @param id 患者ID
     * @return 患者信息
     */
    Patient selectPatientById(@Param("id") Long id);
    
    /**
     * 分页查询患者列表
     * 
     * @param page 分页参数
     * @param status 状态
     * @param name 姓名
     * @param idCard 证件号码
     * @param phone 手机号
     * @param disease 疾病
     * @return 分页结果
     */
    IPage<Patient> selectPatientPage(Page<Patient> page, 
                                    @Param("status") String status, 
                                    @Param("name") String name, 
                                    @Param("idCard") String idCard,
                                    @Param("phone") String phone,
                                    @Param("disease") String disease);
    
    /**
     * 查询某个分组下的患者
     * 
     * @param groupId 分组ID
     * @return 患者列表
     */
    List<Patient> selectByGroupId(@Param("groupId") Long groupId);
    
    /**
     * 分页查询某个分组下的患者
     * 
     * @param page 分页参数
     * @param groupId 分组ID
     * @param status 状态
     * @param name 姓名
     * @param idCard 证件号码
     * @param phone 手机号
     * @param disease 疾病
     * @return 分页结果
     */
    IPage<Patient> selectPatientPageByGroupId(Page<Patient> page, 
                                            @Param("groupId") Long groupId,
                                            @Param("status") String status, 
                                            @Param("name") String name, 
                                            @Param("idCard") String idCard,
                                            @Param("phone") String phone,
                                            @Param("disease") String disease);
} 
package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.NhHospitalizationInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 住院信息Mapper接口
 */
@Mapper
public interface NhHospitalizationInfoMapper extends BaseMapper<NhHospitalizationInfo> {
    
    /**
     * 根据病人ID查询住院信息
     *
     * @param patientId 病人ID
     * @return 住院信息
     */
    NhHospitalizationInfo selectByPatientId(@Param("patientId") String patientId);

    /**
     * 根据科室统计在院病人数量
     *
     * @return 科室及对应的病人数量
     */
    List<Map<String, Object>> countByDepartment();

    /**
     * 查询指定日期范围内的入院记录
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 住院信息列表
     */
    List<NhHospitalizationInfo> selectByAdmissionDateRange(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    String genInHospitalId();
}

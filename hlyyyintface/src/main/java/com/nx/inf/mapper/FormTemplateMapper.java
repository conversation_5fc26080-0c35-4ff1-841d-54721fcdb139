package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.FormTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单模板Mapper接口
 */
@Mapper
public interface FormTemplateMapper extends BaseMapper<FormTemplate> {
    
    /**
     * 查询表单模板列表
     * 
     * @param name 模板名称
     * @param category 分类
     * @return 模板列表
     */
    List<FormTemplate> selectTemplateList(@Param("name") String name, 
                                         @Param("category") String category);
} 
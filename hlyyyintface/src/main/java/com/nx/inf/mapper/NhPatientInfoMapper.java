package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nx.inf.model.entity.NhPatientInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface NhPatientInfoMapper extends BaseMapper<NhPatientInfo> {

    /**
     * 获取病人信息列表
     *
     * @return 病人信息列表
     */
    List<NhPatientInfo> getNhPatientInfoList(NhPatientInfo nhPatientInfo);

    /**
     * 更新病人信息
     *
     * @param nhPatientInfo 病人信息
     * @return 更新结果
     */
    int updateNhPatientInfo(NhPatientInfo nhPatientInfo);
}

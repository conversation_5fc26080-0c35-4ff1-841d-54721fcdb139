package com.nx.inf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nx.inf.model.entity.QuestionnaireAnswer;
import com.nx.inf.model.dto.QuestionnaireAnswerDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Date;

/**
 * 问卷答案Mapper接口
 */
@Mapper
public interface QuestionnaireAnswerMapper extends BaseMapper<QuestionnaireAnswer> {
    
    /**
     * 分页查询问卷答案
     */
    IPage<QuestionnaireAnswerDTO> selectAnswerPage(Page<QuestionnaireAnswerDTO> page, 
                                                  @Param("questionnaireId") Long questionnaireId,
                                                  @Param("patientId") Long patientId,
                                                  @Param("taskId") Long taskId,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);
    
    /**
     * 根据任务ID查询问卷答案
     */
    List<QuestionnaireAnswerDTO> selectAnswersByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 根据患者ID和问卷ID查询答案
     */
    List<QuestionnaireAnswerDTO> selectAnswersByPatientAndQuestionnaire(@Param("patientId") Long patientId, 
                                                                       @Param("questionnaireId") Long questionnaireId);
    
    /**
     * 根据患者ID查询所有答案
     */
    List<QuestionnaireAnswerDTO> selectAnswersByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 统计患者问卷完成情况
     */
    List<QuestionnaireAnswerDTO> selectPatientQuestionnaireStats(@Param("patientId") Long patientId);
    
    /**
     * 批量插入问卷答案
     */
    int batchInsert(@Param("answers") List<QuestionnaireAnswer> answers);
    
    /**
     * 根据条件删除答案
     */
    int deleteByCondition(@Param("questionnaireId") Long questionnaireId,
                         @Param("patientId") Long patientId,
                         @Param("taskId") Long taskId);
} 
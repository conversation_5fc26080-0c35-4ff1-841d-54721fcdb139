package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者实体类
 */
@Data
@TableName("patient")
public class Patient {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 证件号码
     */
    private String idCard;
    
    /**
     * 病历号
     */
    private String medicalNo;
    
    /**
     * 出生日期
     */
    private LocalDate birthDate;
    
    /**
     * 所患疾病
     */
    private String disease;
    
    /**
     * 申请日期
     */
    private LocalDate applyDate;
    
    /**
     * 状态: 未审核, 审核通过, 已拒绝
     */
    private String status;
    
    /**
     * 审核人ID
     */
    private Long reviewerId;
    
    /**
     * 审核人姓名
     */
    private String reviewerName;
    
    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
} 
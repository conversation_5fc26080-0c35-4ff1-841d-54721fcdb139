package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 患者分组关联实体类
 */
@Data
@TableName("patient_group_relation")
public class PatientGroupRelation {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 分组ID
     */
    private Long groupId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者手机号（新增字段）
     */
    private String patientPhone;
    
    /**
     * 入组方式（新增字段）
     */
    private String joinType;
    
    /**
     * 用户是否同意（新增字段）
     */
    private Boolean userAgree;
    
    /**
     * 医生是否同意（新增字段）
     */
    private Boolean doctorAgree;
    
    /**
     * 加入时间
     */
    private Date joinTime;
    
    /**
     * 同意时间（新增字段）
     */
    private Date agreeTime;
    
    /**
     * 审核时间（新增字段）
     */
    private Date auditTime;
    
    /**
     * 审核人ID（新增字段）
     */
    private Long auditorId;
    
    /**
     * 审核人姓名（新增字段）
     */
    private String auditorName;
    
    /**
     * 审核备注（新增字段）
     */
    private String auditRemark;
    
    /**
     * 状态（新增字段）
     */
    private String status;
    
    /**
     * 加入方式: 院内渠道, 院外渠道
     */
    private String joinMethod;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者姓名
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
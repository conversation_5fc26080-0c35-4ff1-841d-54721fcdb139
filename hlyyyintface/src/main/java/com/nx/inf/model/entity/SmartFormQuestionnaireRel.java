package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 表单问卷关联实体类
 */
@Data
@TableName("smart_form_questionnaire_rel")
public class SmartFormQuestionnaireRel {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单模板ID
     */
    private Long formTemplateId;
    
    /**
     * 问卷ID
     */
    private Long questionnaireId;
    
    /**
     * 问卷名称（冗余字段，便于搜索）
     */
    private String questionnaireName;
    
    /**
     * 问卷类型：symptom-症状评估，scale-量表评估，side_effect-不良反应，custom-自定义
     */
    private String questionnaireType;
    
    /**
     * 题目数量（冗余字段）
     */
    private Integer questionCount;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 是否必填：0-非必填，1-必填
     */
    private Integer isRequired;
    
    /**
     * 显示配置（JSON格式）
     */
    private String displayConfig;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
} 
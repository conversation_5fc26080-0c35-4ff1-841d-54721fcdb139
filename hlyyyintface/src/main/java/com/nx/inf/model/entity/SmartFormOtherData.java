package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 表单其他数据实体类
 */
@Data
@TableName("smart_form_other_data")
public class SmartFormOtherData {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单模板ID
     */
    private Long formTemplateId;
    
    /**
     * 数据类型：patient_info-患者信息，medical_record-病历信息，lab_result-检验结果，imaging_result-影像结果，medication_info-用药信息，vital_signs-生命体征，other-其他
     */
    private String dataType;
    
    /**
     * 数据名称
     */
    private String dataName;
    
    /**
     * 数据描述
     */
    private String dataDescription;
    
    /**
     * 数据配置（JSON格式）
     */
    private String dataConfig;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 是否必填：0-非必填，1-必填
     */
    private Integer isRequired;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
} 
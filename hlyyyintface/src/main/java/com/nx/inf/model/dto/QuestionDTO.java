package com.nx.inf.model.dto;

import lombok.Data;
import java.util.List;

@Data
public class QuestionDTO {
    private Long id;
    private Long questionnaireId;
    private String title;
    private String type;
    private Boolean required;
    private String remark;
    private String instruction;
    private String content;
    private String textType;
    private Integer minValue;
    private Integer maxValue;
    private Integer sliderMin;
    private Integer sliderMax;
    private Integer sort;
    private List<OptionDTO> options;
} 
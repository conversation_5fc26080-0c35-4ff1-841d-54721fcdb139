package com.nx.inf.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 养老院机构请求VO
 */
@Data
public class NursingHomeRequestVO {
    
    // ============ 通用请求参数 ============
    
    /**
     * 机构ID
     */
    private Long id;
    
    /**
     * 机构ID数组（批量操作用）
     */
    private List<Long> ids;
    
    // ============ 分页查询参数 ============
    
    /**
     * 当前页码，默认为1
     */
    private Integer page = 1;
    
    /**
     * 每页大小，默认为10
     */
    private Integer pageSize = 10;
    
    // ============ 查询条件参数 ============
    
    /**
     * 机构名称（模糊搜索）
     */
    private String name;
    
    /**
     * 机构代码
     */
    private String code;
    
    /**
     * 机构状态（0-禁用, 1-启用）
     */
    private Integer status;
    
    /**
     * 机构等级
     */
    private String level;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String district;
    
    /**
     * 关键词搜索（机构名称、地址、联系人）
     */
    private String keyword;
    
    /**
     * 创建时间开始
     */
    private String startTime;
    
    /**
     * 创建时间结束
     */
    private String endTime;
    
    // ============ 新增/更新参数 ============
    
    /**
     * 机构地址
     */
    private String address;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 传真号码
     */
    private String fax;
    
    /**
     * 邮箱地址
     */
    private String email;
    
    /**
     * 床位数量
     */
    private Integer capacity;
    
    /**
     * 当前入住人数
     */
    private Integer currentPatients;
    
    /**
     * 执业许可证号
     */
    private String licenseNumber;
    
    /**
     * 法人代表
     */
    private String legalPerson;
    
    /**
     * 院长姓名
     */
    private String director;
    
    /**
     * 院长联系电话
     */
    private String directorPhone;
    
    /**
     * 联系人姓名
     */
    private String contactPerson;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 机构描述
     */
    private String description;
    
    /**
     * 服务类型(JSON格式)
     */
    private String serviceTypes;
    
    /**
     * 成立日期
     */
    private Date establishDate;
    
    /**
     * 合作开始日期
     */
    private Date cooperationStartDate;
    
    /**
     * 合作结束日期
     */
    private Date cooperationEndDate;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 纬度
     */
    private BigDecimal latitude;
} 
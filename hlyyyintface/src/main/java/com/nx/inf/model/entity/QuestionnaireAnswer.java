package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 问卷答案实体类
 */
@Data
@TableName("questionnaire_answer")
public class QuestionnaireAnswer {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 问卷ID
     */
    private Long questionnaireId;
    
    /**
     * 问题ID
     */
    private Long questionId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 答案类型：single-单选，multiple-多选，text-文本，number-数字，date-日期，slider-滑动
     */
    private String answerType;
    
    /**
     * 答案文本内容
     */
    private String answerText;
    
    /**
     * 答案值（选项ID、数字值等）
     */
    private String answerValue;
    
    /**
     * 答案得分
     */
    private BigDecimal answerScore;
    
    /**
     * 答题时间
     */
    private Date answerTime;
    
    /**
     * 答题耗时（秒）
     */
    private Integer timeSpent;
    
    /**
     * 任务ID（关联随访任务）
     */
    private Long taskId;
    
    /**
     * 子计划ID
     */
    private Long subplanId;
    
    /**
     * 计划ID
     */
    private Long planId;
    
    /**
     * 答案来源：manual-手动填写，auto-自动填充，import-导入
     */
    private String answerSource;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
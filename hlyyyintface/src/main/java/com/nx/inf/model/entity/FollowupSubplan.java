package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 随访子计划实体类
 */
@Data
@TableName("followup_subplan")
public class FollowupSubplan {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 所属计划ID
     */
    private Long planId;
    
    /**
     * 子计划名称
     */
    private String name;
    
    /**
     * 子计划描述
     */
    private String description;
    
    /**
     * 子计划类型：patient-患者填写，medical-仅医护填写
     */
    private String type;
    
    /**
     * 执行时间类型：all-全部时段，custom-自定义
     */
    private String executionTimeType;
    
    /**
     * 自定义开始天数
     */
    private Integer customStartDay;
    
    /**
     * 自定义结束天数
     */
    private Integer customEndDay;
    
    /**
     * 提醒时间（精确到分钟，格式：HH:mm:ss）
     */
    private String reminderTime;
    
    /**
     * 计划类型：cycle-周期循环，custom-自定义
     */
    private String planType;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String createTime;
    
    /**
     * 更新时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String updateTime;
} 
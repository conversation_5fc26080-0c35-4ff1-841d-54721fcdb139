package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("questionnaire_rule")
public class QuestionnaireRule {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long questionId;
    private String ruleType;
    private String ruleValue;
    private String description;
    private String evaluationName;
    private Integer score;
    private Boolean reminderEnabled;
}

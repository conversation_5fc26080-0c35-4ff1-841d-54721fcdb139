package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 表单分享配置实体类
 */
@Data
@TableName("smart_form_share_config")
public class SmartFormShareConfig {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单模板ID
     */
    private Long formTemplateId;
    
    /**
     * 分享类型：link-链接分享，qrcode-二维码分享，embed-嵌入分享
     */
    private String shareType;
    
    /**
     * 分享码/链接码
     */
    private String shareCode;
    
    /**
     * 分享链接
     */
    private String shareUrl;
    
    /**
     * 是否公开：0-非公开，1-公开
     */
    private Integer isPublic;
    
    /**
     * 是否需要密码：0-不需要，1-需要
     */
    private Integer passwordRequired;
    
    /**
     * 分享密码
     */
    private String sharePassword;
    
    /**
     * 最大提交次数限制
     */
    private Integer maxSubmissions;
    
    /**
     * 当前提交次数
     */
    private Integer currentSubmissions;
    
    /**
     * 有效期类型：permanent-永久，date-指定日期，duration-时长
     */
    private String expireType;
    
    /**
     * 过期时间
     */
    private Date expireTime;
    
    /**
     * 是否激活：0-未激活，1-激活
     */
    private Integer isActive;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
} 
package com.nx.inf.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 图片文字识别记录DTO
 */
@Data
public class ImageTextRecordDTO {
    
    private Long id;
    
    /**
     * 原始文件名
     */
    private String originalFilename;
    
    /**
     * 文件存储路径
     */
    private String filePath;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 文件类型(MIME类型)
     */
    private String fileType;
    
    /**
     * 图片宽度
     */
    private Integer imageWidth;
    
    /**
     * 图片高度
     */
    private Integer imageHeight;
    
    /**
     * 识别出的文字内容
     */
    private String recognizedText;
    
    /**
     * 识别置信度(0-100)
     */
    private BigDecimal confidence;
    
    /**
     * OCR引擎类型
     */
    private String ocrEngine;
    
    /**
     * 处理耗时(毫秒)
     */
    private Integer processingTime;
    
    /**
     * 处理状态: processing-处理中, success-成功, failed-失败
     */
    private String status;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

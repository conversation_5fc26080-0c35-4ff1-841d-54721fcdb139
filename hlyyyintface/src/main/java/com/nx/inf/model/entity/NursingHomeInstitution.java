package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 养老院机构基础信息实体类
 */
@Data
@TableName("nursing_home_institution")
public class NursingHomeInstitution {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 机构名称
     */
    private String name;
    
    /**
     * 机构代码
     */
    private String code;
    
    /**
     * 机构地址
     */
    private String address;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 传真号码
     */
    private String fax;
    
    /**
     * 邮箱地址
     */
    private String email;
    
    /**
     * 床位数量
     */
    private Integer capacity;
    
    /**
     * 当前入住人数
     */
    private Integer currentPatients;
    
    /**
     * 执业许可证号
     */
    private String licenseNumber;
    
    /**
     * 法人代表
     */
    private String legalPerson;
    
    /**
     * 院长姓名
     */
    private String director;
    
    /**
     * 院长联系电话
     */
    private String directorPhone;
    
    /**
     * 联系人姓名
     */
    private String contactPerson;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 机构描述
     */
    private String description;
    
    /**
     * 机构等级: STANDARD-标准, PREMIUM-高级, LUXURY-豪华
     */
    private String level;
    
    /**
     * 服务类型(JSON格式): 养老服务、医疗服务、康复服务等
     */
    private String serviceTypes;
    
    /**
     * 成立日期
     */
    private Date establishDate;
    
    /**
     * 合作开始日期
     */
    private Date cooperationStartDate;
    
    /**
     * 合作结束日期
     */
    private Date cooperationEndDate;
    
    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String district;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 纬度
     */
    private BigDecimal latitude;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    private Integer isDeleted;
} 
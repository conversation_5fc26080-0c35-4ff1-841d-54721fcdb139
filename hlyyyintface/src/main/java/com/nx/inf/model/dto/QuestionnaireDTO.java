package com.nx.inf.model.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class QuestionnaireDTO {
    private Long id;
    private String name;
    private String type;
    private String status;
    private Boolean isScoring;
    private Boolean isGuidance;
    private Boolean isTrigger;
    private Date createTime;
    private Date updateTime;
    private List<QuestionDTO> questions;
    private List<QuestionnaireRuleDTO> rules;
} 
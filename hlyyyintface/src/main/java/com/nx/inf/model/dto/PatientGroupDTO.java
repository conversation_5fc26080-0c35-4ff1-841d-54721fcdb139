package com.nx.inf.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 患者分组DTO
 */
@Data
public class PatientGroupDTO {
    
    private Long id;
    
    private String name;
    
    private String description;
    
    /**
     * 分组分类（新增字段）
     */
    private String category;
    
    private Integer patientCount;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    private Date createTime;
    
    private Date updateTime;
    
    private <PERSON><PERSON><PERSON> joined;
    private Date joinTime;
    private String status; // 入组状态：pending-待审核，approved-已通过，rejected-已拒绝
    
    /**
     * 分组下的患者
     */
    private List<PatientDTO> patients;
} 
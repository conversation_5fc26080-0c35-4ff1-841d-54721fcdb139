package com.nx.inf.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 内容模板响应VO
 */
@Data
public class ContentTemplateResponseVO {
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 返回消息
     */
    private String message;
    
    /**
     * 数据内容
     */
    private Object data;
    
    /**
     * 构造成功响应
     */
    public static ContentTemplateResponseVO success(String message, Object data) {
        ContentTemplateResponseVO response = new ContentTemplateResponseVO();
        response.setSuccess(true);
        response.setMessage(message);
        response.setData(data);
        return response;
    }
    
    /**
     * 构造成功响应（无数据）
     */
    public static ContentTemplateResponseVO success(String message) {
        return success(message, null);
    }
    
    /**
     * 构造失败响应
     */
    public static ContentTemplateResponseVO error(String message) {
        ContentTemplateResponseVO response = new ContentTemplateResponseVO();
        response.setSuccess(false);
        response.setMessage(message);
        response.setData(null);
        return response;
    }
    
    /**
     * 分页数据包装类
     */
    @Data
    public static class PageData {
        private List<?> list;
        private Long total;
        
        public PageData(List<?> list, Long total) {
            this.list = list;
            this.total = total;
        }
    }
    
    /**
     * 创建/复制操作的数据包装类
     */
    @Data
    public static class CreateData {
        private Long id;
        private String name;
        
        public CreateData(Long id) {
            this.id = id;
        }
        
        public CreateData(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }
    
    /**
     * 批量删除操作的数据包装类
     */
    @Data
    public static class BatchDeleteData {
        private Integer deletedCount;
        
        public BatchDeleteData(Integer deletedCount) {
            this.deletedCount = deletedCount;
        }
    }
    
    /**
     * 状态更新操作的数据包装类
     */
    @Data
    public static class StatusUpdateData {
        private Long id;
        private String status;
        
        public StatusUpdateData(Long id, String status) {
            this.id = id;
            this.status = status;
        }
    }
} 
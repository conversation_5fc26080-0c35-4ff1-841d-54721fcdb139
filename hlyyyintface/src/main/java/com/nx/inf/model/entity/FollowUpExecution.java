package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 随访执行记录实体类
 */
@Data
@TableName("follow_up_execution")
public class FollowUpExecution {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 随访计划ID
     */
    private Long planId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    
    /**
     * 用时(秒)
     */
    private Integer duration;
    
    /**
     * 表单收集数
     */
    private Integer formCount;
    
    /**
     * 报告数
     */
    private Integer reportCount;
    
    /**
     * 异常指标数
     */
    private Integer warningCount;
    
    /**
     * 状态: 待确认, 已执行, 已终止, 已过期
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
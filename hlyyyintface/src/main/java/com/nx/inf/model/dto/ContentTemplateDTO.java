package com.nx.inf.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容模板DTO
 */
@Data
public class ContentTemplateDTO {
    
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 模板说明
     */
    private String description;
    
    /**
     * 模板分类
     */
    private String category;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 权限范围
     */
    private String permission;
    
    /**
     * 内容标题
     */
    private String title;
    
    /**
     * 模板内容（富文本HTML）
     */
    private String content;
    
    /**
     * 模板内容（纯文本）
     */
    private String contentText;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 使用次数
     */
    private Integer useCount;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建人科室ID
     */
    private Long creatorDeptId;
    
    /**
     * 创建人科室名称
     */
    private String creatorDeptName;
    
    /**
     * 最后使用时间
     */
    private String lastUsedTime;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
} 
package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 健康记录删除请求VO
 * @Author: zhongx
 * @CreateDate: 2024/5/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HealthDeleteRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空")
    private Long id;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    private String sfzh;
} 
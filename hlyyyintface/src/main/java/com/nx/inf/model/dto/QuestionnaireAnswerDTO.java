package com.nx.inf.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 问卷答案DTO类
 */
@Data
public class QuestionnaireAnswerDTO {
    
    private Long id;
    private Long questionnaireId;
    private Long questionId;
    private Long patientId;
    private String patientName;
    private String answerType;
    private String answerText;
    private String answerValue;
    private BigDecimal answerScore;
    private Date answerTime;
    private Integer timeSpent;
    private Long taskId;
    private Long subplanId;
    private Long planId;
    private String answerSource;
    private Date createTime;
    private Date updateTime;
    
    // 关联信息
    private String questionnaireName;
    private String questionTitle;
    private String questionType;
    private Boolean questionRequired;
    private List<String> selectedOptions; // 选中的选项文本
} 
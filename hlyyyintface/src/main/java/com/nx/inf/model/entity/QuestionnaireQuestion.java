package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("questionnaire_question")
public class QuestionnaireQuestion {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long questionnaireId;
    private String title;
    private String type;
    private Boolean required;
    private String remark;
    private String instruction;
    private String content;
    private String textType;
    private Integer minValue;
    private Integer maxValue;
    private Integer sliderMin;
    private Integer sliderMax;
    private Integer sort;
}

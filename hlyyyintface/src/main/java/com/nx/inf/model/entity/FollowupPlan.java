package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 随访计划主表实体类
 */
@Data
@TableName("followup_plan")
public class FollowupPlan {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 计划名称
     */
    private String name;
    
    /**
     * 计划描述
     */
    private String description;
    
    /**
     * 计划分类：postoperative-术后随访，chronic_disease-慢病管理，health_assessment-健康评估，rehabilitation-康复管理
     */
    private String category;
    
    /**
     * 状态：draft-草稿，active-启用，paused-暂停，completed-已完成
     */
    private String status;
    
    /**
     * 结束时间类型：day-天，week-周，month-月，year-年，unlimited-长期
     */
    private String endTimeType;
    
    /**
     * 结束时间值
     */
    private Integer endTimeValue;
    
    /**
     * 关联的患者分组ID
     */
    private Long groupId;
    
    /**
     * 入组申请二维码URL
     */
    private String qrCodeUrl;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 患者数量
     */
    private Integer patientCount;
    
    /**
     * 任务数量
     */
    private Integer taskCount;
    
    /**
     * 已完成任务数量
     */
    private Integer completedTaskCount;
    
    // 注意：subplanCount字段已移除，改为实时计算
    // 如果需要子计划数量，请通过关联查询获取
} 
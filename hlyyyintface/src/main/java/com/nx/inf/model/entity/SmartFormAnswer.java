package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 表单答案实体类
 */
@Data
@TableName("smart_form_answer")
public class SmartFormAnswer {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 提交记录ID
     */
    private Long submissionId;
    
    /**
     * 问卷ID
     */
    private Long questionnaireId;
    
    /**
     * 问题ID
     */
    private Long questionId;
    
    /**
     * 答案类型：single-单选，multiple-多选，text-文本，number-数字，date-日期，file-文件
     */
    private String answerType;
    
    /**
     * 答案文本内容
     */
    private String answerText;
    
    /**
     * 答案值（选项ID、数字值等）
     */
    private String answerValue;
    
    /**
     * 答案得分
     */
    private BigDecimal answerScore;
    
    /**
     * 文件答案（JSON格式存储文件信息）
     */
    private String answerFiles;
    
    /**
     * 答题时间
     */
    private Date answerTime;
    
    /**
     * 答题耗时（秒）
     */
    private Integer timeSpent;
    
    /**
     * 答案来源：manual-手动填写，auto-自动填充，import-导入
     */
    private String answerSource;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
} 
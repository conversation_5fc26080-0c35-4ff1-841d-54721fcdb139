package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 转介审核记录实体类
 */
@Data
@TableName("nh_referral_audit")
public class NhReferralAudit {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 患者ID
     */
    private String patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 出生日期
     */
    private Date birthDate;
    
    /**
     * 出院日期
     */
    private Date dischargeDate;
    
    /**
     * 出院科室
     */
    private String dischargeDepartment;
    
    /**
     * 主要诊断
     */
    private String primaryDiagnosis;
    
    /**
     * 其他诊断
     */
    private String otherDiagnosis;
    
    /**
     * 转介机构代码
     */
    private String referralInstitutionCode;
    
    /**
     * 转介机构名称（冗余字段）
     */
    private String referralInstitutionName;
    
    /**
     * 转介类型
     */
    private String referralType;
    
    /**
     * 紧急联系人
     */
    private String emergencyContact;
    
    /**
     * 紧急联系人电话
     */
    private String emergencyPhone;
    
    /**
     * 联系人关系
     */
    private String contactRelation;
    
    /**
     * 审核状态：0-未审核，1-已审核，2-已拒绝
     */
    private String auditStatus;
    
    /**
     * 审核时间
     */
    private Date auditTime;
    
    /**
     * 审核人ID
     */
    private Long auditorId;
    
    /**
     * 审核人姓名
     */
    private String auditorName;
    
    /**
     * 审核备注
     */
    private String auditRemarks;
    
    /**
     * 转介信详细内容（JSON格式）
     */
    private String referralLetterContent;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 是否删除
     */
    private Integer isDeleted;
    
    // 关联查询字段（非数据库字段）
    
    /**
     * 机构名称（关联查询字段）
     */
    @TableField(exist = false)
    private String institutionName;
    
    /**
     * 机构地址（关联查询字段）
     */
    @TableField(exist = false)
    private String institutionAddress;
    
    /**
     * 机构联系电话（关联查询字段）
     */
    @TableField(exist = false)
    private String institutionPhone;
    
    /**
     * 机构联系人（关联查询字段）
     */
    @TableField(exist = false)
    private String institutionContact;
    
    /**
     * 机构联系人电话（关联查询字段）
     */
    @TableField(exist = false)
    private String institutionContactPhone;
    
    /**
     * 是否为临时记录（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isTempRecord;
} 
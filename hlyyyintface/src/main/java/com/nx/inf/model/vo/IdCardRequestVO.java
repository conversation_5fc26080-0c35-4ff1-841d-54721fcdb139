package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 身份证号请求VO
 * @Author: zhongx
 * @CreateDate: 2024/4/20
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdCardRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    private String idCard;
} 
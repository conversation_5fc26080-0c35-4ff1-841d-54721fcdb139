package com.nx.inf.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 随访子计划DTO类
 */
@Data
public class FollowupSubplanDTO {

    private Long id;
    private Long planId;
    private String name;
    private String description;
    private String type;
    private String executionTimeType;
    private Integer customStartDay;
    private Integer customEndDay;
    private String reminderTime;
    private String planType;
    private Integer sortOrder;
    private Date createTime;
    private Date updateTime;

    // 关联信息
    private String planName;
    private List<FollowupSubplanItemDTO> items;
    private List<FollowupSubplanQuestionnaireDTO> questionnaires;
} 
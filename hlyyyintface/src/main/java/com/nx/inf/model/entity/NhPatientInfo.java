package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.time.LocalDate;
import java.math.BigDecimal;

@Data
@TableName("nh_patient_info")
public class NhPatientInfo {
    /**
     * 病人ID
     */
    private String patientId;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 电话
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 地址
     */
    private String address;

    /**
     * 病史
     */
    private String medicalHistory;

    /**
     * 用药信息
     */
    private String medications;

    /**
     * 血压
     */
    private String bloodPressure;

    /**
     * 心率
     */
    private Integer heartRate;

    /**
     * 血糖
     */
    private BigDecimal bloodSugar;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 所属养老机构代码
     */
    private String nursingHomeCode;

    /**
     * 养老机构名称（关联查询字段，非数据库字段）
     */
    @TableField(exist = false)
    private String nursingHomeName;
    
    /**
     * 年龄（计算字段，非数据库字段）
     */
    @TableField(exist = false)
    private Integer age;
    
    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date admissionDate;
    
    /**
     * 床位号
     */
    private String bedNumber;
    
    /**
     * 护理等级：1-自理，2-半自理，3-全护理，4-特护
     */
    private String careLevel;
    
    /**
     * 健康状态：1-良好，2-一般，3-较差，4-危重
     */
    private String healthStatus;
    
    /**
     * 紧急联系人
     */
    private String emergencyContact;
    
    /**
     * 紧急联系人电话
     */
    private String emergencyPhone;
    
    /**
     * 备注
     */
    private String remark;
}

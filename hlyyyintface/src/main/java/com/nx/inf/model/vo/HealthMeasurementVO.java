package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 健康测量数据VO
 * @Author: zhongx
 * @CreateDate: 2024/5/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HealthMeasurementVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 测量时间（精确到秒）
     */
    @NotBlank(message = "测量时间不能为空")
    private String date;

    /**
     * 收缩压
     */
    private String ssy;

    /**
     * 舒张压
     */
    private String szy;

    /**
     * 体重
     */
    private String tz;

    /**
     * 脉搏
     */
    private String tb;

    /**
     * 血糖
     */
    private String xt;

    /**
     * 血氧
     */
    private String xy;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    private String sfzh;
} 
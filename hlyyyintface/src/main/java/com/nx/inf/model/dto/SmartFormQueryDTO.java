package com.nx.inf.model.dto;

import lombok.Data;

/**
 * 智能表单查询DTO
 */
@Data
public class SmartFormQueryDTO {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 表单名称（模糊查询）
     */
    private String name;
    
    /**
     * 表单类型
     */
    private String formType;
    
    /**
     * 表单分类
     */
    private String category;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
} 
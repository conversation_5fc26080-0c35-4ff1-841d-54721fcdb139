package com.nx.inf.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 智能表单模板DTO
 */
@Data
public class SmartFormTemplateDTO {
    
    /**
     * 表单ID（编辑时使用）
     */
    private Long id;
    
    /**
     * 表单名称（新增时必填，最大50字符）
     */
    private String name;
    
    /**
     * 表单描述（新增时必填，最大1000字符）
     */
    private String description;
    
    /**
     * 表单类型：one_time|repeatable（编辑时配置）
     */
    private String formType;
    
    /**
     * 表单分类（新增时必填）
     */
    private String category;
    
    /**
     * 状态：active|inactive
     */
    private String status;
    
    /**
     * 权限范围：self|department|all（新增时必填）
     */
    private String permission;
    
    /**
     * 表单配置（编辑时配置）
     */
    private Map<String, Object> config;
    
    /**
     * 重复配置（编辑时配置）
     */
    private Map<String, Object> repeatConfig;
    
    /**
     * 备注说明（编辑时配置，最大50字符）
     */
    private String remarkText;
    
    /**
     * 知情同意书配置（编辑时配置）
     */
    private Map<String, Object> consentConfig;
    
    /**
     * 其他数据配置（编辑时配置）
     */
    private List<Map<String, Object>> otherData;
    
    /**
     * 问卷关联列表（编辑时配置）
     */
    private List<Map<String, Object>> questionnaires;
} 
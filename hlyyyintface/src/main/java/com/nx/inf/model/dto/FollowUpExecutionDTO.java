package com.nx.inf.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 随访执行记录DTO
 */
@Data
public class FollowUpExecutionDTO {
    
    private Long id;
    
    private Long planId;
    
    /**
     * 计划标题
     */
    private String planTitle;
    
    private Long patientId;
    
    private String patientName;
    
    private Integer patientAge;
    
    private String patientGender;
    
    private LocalDateTime submitTime;
    
    private Integer duration;
    
    private Integer formCount;
    
    private Integer reportCount;
    
    private Integer warningCount;
    
    private String status;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 表单模板实体类
 */
@Data
@TableName("form_template")
public class FormTemplate {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 所属医院
     */
    private String hospital;
    
    /**
     * 分类
     */
    private String category;
    
    /**
     * 模板内容
     */
    private String content;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 是否系统模板
     */
    private Boolean isSystem;
} 
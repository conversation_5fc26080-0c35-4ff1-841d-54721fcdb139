package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 患者分组实体类
 */
@Data
@TableName("patient_group")
public class PatientGroup {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 组名
     */
    private String name;
    
    /**
     * 备注
     */
    private String description;
    
    /**
     * 分组分类（新增字段）
     */
    private String category;
    
    /**
     * 患者数量
     */
    private Integer patientCount;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 模板使用记录实体类
 */
@Data
@TableName("content_template_usage_log")
public class ContentTemplateUsageLog {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 使用人ID
     */
    private Long userId;
    
    /**
     * 使用人姓名
     */
    private String userName;
    
    /**
     * 使用人科室ID
     */
    private Long deptId;
    
    /**
     * 使用人科室名称
     */
    private String deptName;
    
    /**
     * 使用场景（follow_up:随访, consultation:咨询, notification:通知）
     */
    private String usageScene;
    
    /**
     * 患者ID（如果适用）
     */
    private Long patientId;
    
    /**
     * 患者姓名（如果适用）
     */
    private String patientName;
    
    /**
     * 使用时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date useTime;
} 
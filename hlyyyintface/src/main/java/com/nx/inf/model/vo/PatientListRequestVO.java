package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 患者列表查询请求VO
 * @Author: zhongx
 * @CreateDate: 2024/5/30
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PatientListRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者姓名，支持模糊查询
     */
    private String patientName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 页码，从1开始
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    private Integer pageSize;
} 
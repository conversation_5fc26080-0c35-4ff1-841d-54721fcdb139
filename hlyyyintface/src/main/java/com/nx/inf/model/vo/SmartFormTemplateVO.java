package com.nx.inf.model.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 智能表单模板VO
 */
@Data
public class SmartFormTemplateVO {
    
    /**
     * 表单ID
     */
    private Long id;
    
    /**
     * 表单名称
     */
    private String name;
    
    /**
     * 表单描述
     */
    private String description;
    
    /**
     * 表单类型
     */
    private String formType;
    
    /**
     * 表单分类
     */
    private String category;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 权限范围
     */
    private String permission;
    
    /**
     * 表单配置
     */
    private Map<String, Object> config;
    
    /**
     * 重复配置
     */
    private Map<String, Object> repeatConfig;
    
    /**
     * 备注说明
     */
    private String remarkText;
    
    /**
     * 知情同意书配置
     */
    private Map<String, Object> consentConfig;
    
    /**
     * 其他数据配置
     */
    private List<Map<String, Object>> otherData;
    
    /**
     * 问卷关联列表
     */
    private List<Map<String, Object>> questionnaires;
    
    /**
     * 创建者姓名
     */
    private String creatorName;
    
    /**
     * 创建者科室名称
     */
    private String creatorDeptName;
    
    /**
     * 提交次数
     */
    private Integer submissionCount;
    
    /**
     * 问卷数量
     */
    private Integer questionnaireCount;
    
    /**
     * 最后提交时间
     */
    private Date lastSubmittedTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 统计信息
     */
    private Map<String, Object> statistics;
} 
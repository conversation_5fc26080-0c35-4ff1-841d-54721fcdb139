package com.nx.inf.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 智能随访模板DTO
 */
@Data
public class SmartFollowUpTemplateDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 模板描述
     */
    private String description;
    
    /**
     * 模板分类
     */
    private String category;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 科室名称
     */
    private String departmentName;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 模板配置（JSON格式）
     */
    private String config;
    
    /**
     * 备注
     */
    private String remark;
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 随访子计划项目实体类
 */
@Data
@TableName("followup_subplan_item")
public class FollowupSubplanItem {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 所属子计划ID
     */
    private Long subplanId;
    
    /**
     * 项目名称
     */
    private String itemName;
    
    /**
     * 周期天数（周期循环时使用）
     */
    private Integer cycleDays;
    
    /**
     * 加入计划第几天（自定义时使用）
     */
    private Integer joinDay;
    
    /**
     * 事件类型：instant_message-即时消息
     */
    private String eventType;
    
    /**
     * 内容模板ID（即时消息时使用）
     */
    private Long contentTemplateId;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 随访任务实体类
 */
@Data
@TableName("followup_task")
public class FollowupTask {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 计划ID
     */
    private Long planId;
    
    /**
     * 子计划ID
     */
    private Long subplanId;
    
    /**
     * 子计划项目ID
     */
    private Long subplanItemId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 计划执行时间
     */
    private Date scheduledTime;
    
    /**
     * 状态：pending-待执行，executing-执行中，completed-已完成，failed-执行失败
     */
    private String status;
    
    /**
     * 消息是否已发送
     */
    private Boolean messageSent;
    
    /**
     * 消息发送时间
     */
    private Date messageSentTime;
    
    /**
     * 问卷是否已完成
     */
    private Boolean questionnaireCompleted;
    
    /**
     * 问卷完成时间
     */
    private Date questionnaireCompletedTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 
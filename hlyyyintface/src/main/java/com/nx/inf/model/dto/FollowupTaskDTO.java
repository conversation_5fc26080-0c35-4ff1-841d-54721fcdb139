package com.nx.inf.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 随访任务DTO类
 */
@Data
public class FollowupTaskDTO {

    private Long id;
    private Long planId;
    private Long subplanId;
    private Long patientId;
    private String patientName;
    private String taskType;
    private String taskName;
    private String taskDescription;
    private String status;
    private LocalDateTime scheduledTime;
    private LocalDateTime actualTime;
    private LocalDateTime completedTime;
    private String completionNote;
    private String delayReason;
    private Integer delayDays;
    private String assigneeId;
    private String assigneeName;
    private Integer priority;
    private String reminderType;
    private LocalDateTime reminderTime;
    private Boolean isReminded;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    // 关联信息
    private String planName;
    private String subplanName;
    private String patientPhone;
    private String patientGender;
    private Integer patientAge;
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("questionnaire_option")
public class QuestionnaireOption {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long questionId;
    private String optionText;
    private String remark;
    private String instruction;
    private String content;
    private Integer score;
    private Integer sort;
}

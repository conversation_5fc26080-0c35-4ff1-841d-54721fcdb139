package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: API响应VO，与Node.js格式保持一致
 * @Author: zhongx
 * @CreateDate: 2024/3/28
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ApiResponseVO<T> {
    
    /**
     * 响应代码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应数据
     */
    private T body;
    
    /**
     * 成功响应
     * @param msg 成功消息
     * @param body 响应数据
     * @return 成功响应对象
     */
    public static <T> ApiResponseVO<T> success(String msg, T body) {
        return ApiResponseVO.<T>builder()
                .code(200)
                .msg(msg)
                .body(body)
                .build();
    }
    
    /**
     * 错误响应
     * @param code 错误代码
     * @param msg 错误消息
     * @return 错误响应对象
     */
    public static <T> ApiResponseVO<T> error(Integer code, String msg) {
        return ApiResponseVO.<T>builder()
                .code(code)
                .msg(msg)
                .body(null)
                .build();
    }
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 随访计划详情实体类
 */
@Data
@TableName("follow_up_plan_detail")
public class FollowUpPlanDetail {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联的随访计划ID
     */
    private Long planId;
    
    /**
     * 结束时间
     */
    private Integer duration;
    
    /**
     * 时间单位: day, week, month, year, unlimited
     */
    private String timeUnit;
    
    /**
     * 计划类型: all, custom, etc.
     */
    private String planType;
    
    /**
     * 执行时间类型: all, custom
     */
    private String executionTimeType;
    
    /**
     * 自定义执行时间
     */
    private String customTimeValue;
    
    /**
     * 计划类型详情: random, custom
     */
    private String planTypeDetail;
} 
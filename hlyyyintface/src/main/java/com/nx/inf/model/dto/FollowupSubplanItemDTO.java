package com.nx.inf.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * 随访子计划项目DTO类
 */
@Data
public class FollowupSubplanItemDTO {

    private Long id;
    private Long subplanId;
    private String itemType;
    private String itemName;
    private String itemDescription;
    private String itemContent;
    private Integer cycleDays;
    private Integer joinDay;
    private String eventType;
    private Long contentTemplateId;
    private Integer sortOrder;
    private Boolean isRequired;
    private String config;
    private Date createTime;
    private Date updateTime;
} 
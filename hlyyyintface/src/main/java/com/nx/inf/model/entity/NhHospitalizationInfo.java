package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 住院信息实体类
 */
@Data
@TableName("nh_hospitalization_info")
public class NhHospitalizationInfo {

    @TableId(type = IdType.AUTO)
    private Long id;

    // 基本信息
    private String patientId;
    private String patientName;        // 姓名
    private String gender;             // 性别
    private String birthDate;          // 出生年月
    private String maritalStatus;      // 婚姻状况
    private String nationality;        // 国籍
    private String ethnicity;          // 民族
    private String birthPlace;         // 出生地
    private String nativePlace;        // 籍贯
    private String idCardNumber;       // 身份证号
    private String mobilePhone;        // 手机号码
    private String contactPhone;       // 联系电话

    // 现住址信息
    private String currentAddressProvince;  // 现住址省
    private String currentAddressCity;      // 现住址市
    private String currentAddressCounty;    // 现住址县
    private String currentAddress;          // 现住址
    private String currentAddressPostcode;  // 现住址邮编

    // 户口地址信息
    private String householdAddress;        // 户口地址
    private String householdPostcode;       // 户口邮编

    // 单位信息
    private String workAddress;             // 单位地址
    private String workPhone;               // 单位电话
    private String workPostcode;            // 单位邮编

    // 联系人信息
    private String contactPersonName;       // 联系人姓名
    private String contactPersonPhone;      // 联系人电话
    private String contactPersonRelation;   // 联系人关系
    private String contactPersonAddress;    // 联系人地址
    private String contactPersonPostcode;   // 联系人邮编

    // 监护人信息
    private String guardianName;            // 监护人姓名
    private String guardianPhone;           // 监护人电话
    private String guardianRelation;        // 监护人关系

    // 住院信息
    private String inHospitalId;
    private String diagnosis;               // 入院诊断
    private String nursingHome;             // 养老机构
    private String bedNumber;               // 床号
    private Timestamp orderDate;            // 开单日期

    // 其他原有字段
    private String cardNo;
    private Integer age;
    private String department;
    private String nurseLevel;
    private String responsibleNurse;
    private String attendingDoctor;
    private Timestamp admissionDate;
    private Timestamp dischargeDate;
    private Timestamp createTime;
    private Timestamp updateTime;
}

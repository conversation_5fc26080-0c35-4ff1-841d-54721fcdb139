package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 表单提交记录实体类
 */
@Data
@TableName("smart_form_submission")
public class SmartFormSubmission {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单模板ID
     */
    private Long formTemplateId;
    
    /**
     * 提交编号
     */
    private String submissionNo;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者手机号
     */
    private String patientPhone;
    
    /**
     * 患者身份证号
     */
    private String patientIdCard;
    
    /**
     * 提交者ID
     */
    private Long submitterId;
    
    /**
     * 提交者姓名
     */
    private String submitterName;
    
    /**
     * 提交者类型：patient-患者，doctor-医生，nurse-护士
     */
    private String submitterType;
    
    /**
     * 状态：submitted-已提交，reviewed-已审核，completed-已完成
     */
    private String status;
    
    /**
     * 完成进度（百分比）
     */
    private BigDecimal progress;
    
    /**
     * 总分数
     */
    private BigDecimal totalScore;
    
    /**
     * 提交来源：web-网页，app-APP，mini-小程序
     */
    private String submitSource;
    
    /**
     * 提交设备信息
     */
    private String submitDevice;
    
    /**
     * 提交IP地址
     */
    private String submitIp;
    
    /**
     * 提交时间
     */
    private Date submitTime;
    
    /**
     * 审核时间
     */
    private Date reviewTime;
    
    /**
     * 完成时间
     */
    private Date completeTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
} 
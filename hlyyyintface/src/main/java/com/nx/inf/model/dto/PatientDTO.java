package com.nx.inf.model.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 患者DTO
 */
@Data
public class PatientDTO {
    
    private Long id;
    
    private String name;
    
    private Integer age;
    
    private String gender;
    
    private String phone;
    
    private String idCard;
    
    private String medicalNo;
    
    private LocalDate birthDate;
    
    private String disease;
    
    private LocalDate applyDate;
    
    private String reviewerName;
    
    private Date reviewTime;
    
    private String remark;
    
    /**
     * 患者所属分组
     */
    private List<PatientGroupSimpleDTO> groups;
    
    /**
     * 加入方式
     */
    private String joinMethod;
    
    /**
     * 加入时间
     */
    private Date joinTime;
    
    private String status; // 入组状态：pending-待审核，approved-已通过，rejected-已拒绝
    
    @Data
    public static class PatientGroupSimpleDTO {
        private Long id;
        private String name;
    }
} 
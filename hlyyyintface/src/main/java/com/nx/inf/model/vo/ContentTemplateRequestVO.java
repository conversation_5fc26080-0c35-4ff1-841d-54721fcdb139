package com.nx.inf.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 内容模板请求VO
 */
@Data
public class ContentTemplateRequestVO {
    
    // ============ 通用请求参数 ============
    
    /**
     * 模板ID
     */
    private Long id;
    
    /**
     * 模板ID数组（批量操作用）
     */
    private List<Long> ids;
    
    // ============ 分页查询参数 ============
    
    /**
     * 当前页码，默认为1
     */
    private Integer page = 1;
    
    /**
     * 每页大小，默认为10
     */
    private Integer pageSize = 10;
    
    /**
     * 模板分类过滤
     */
    private String category;
    
    /**
     * 关键词搜索（模板名称、标题、内容）
     */
    private String keyword;
    
    /**
     * 权限过滤（all|self|department）
     */
    private String permission;
    
    /**
     * 状态过滤（active|inactive）
     */
    private String status;
    
    // ============ 创建/更新模板参数 ============
    
    /**
     * 模板名称（最大50字符）
     */
    private String name;
    
    /**
     * 模板说明（最大1000字符）
     */
    private String description;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 内容标题（最大200字符）
     */
    private String title;
    
    /**
     * 模板内容（富文本HTML）
     */
    private String content;
} 
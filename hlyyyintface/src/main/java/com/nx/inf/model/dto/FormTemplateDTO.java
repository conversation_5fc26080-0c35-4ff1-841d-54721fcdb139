package com.nx.inf.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 表单模板DTO
 */
@Data
public class FormTemplateDTO {
    
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 所属医院
     */
    private String hospital;
    
    /**
     * 分类
     */
    private String category;
    
    /**
     * 模板内容
     */
    private String content;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 是否系统模板
     */
    private Boolean isSystem;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
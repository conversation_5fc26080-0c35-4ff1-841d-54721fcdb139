package com.nx.inf.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 随访计划DTO
 */
@Data
public class FollowupPlanDTO {
    
    private Long id;
    
    /**
     * 计划名称
     */
    private String name;
    
    /**
     * 计划描述
     */
    private String description;
    
    /**
     * 计划分类：postoperative-术后随访，chronic_disease-慢病管理，health_assessment-健康评估，rehabilitation-康复管理
     */
    private String category;
    
    /**
     * 状态：draft-草稿，active-启用，paused-暂停，completed-已完成
     */
    private String status;
    
    /**
     * 结束时间类型：day-天，week-周，month-月，year-年，unlimited-长期
     */
    private String endTimeType;
    
    /**
     * 结束时间值
     */
    private Integer endTimeValue;
    
    /**
     * 关联的患者分组ID
     */
    private Long groupId;
    
    /**
     * 患者分组名称
     */
    private String groupName;
    
    /**
     * 入组申请二维码URL
     */
    private String qrCodeUrl;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 子计划列表
     */
    private List<FollowupSubplanDTO> subplans;
    
    /**
     * 患者数量
     */
    private Integer patientCount;
    
    /**
     * 任务数量
     */
    private Integer taskCount;
    
    /**
     * 已完成任务数量
     */
    private Integer completedTaskCount;
    
    /**
     * 子计划数量
     */
    private Integer subplanCount;
    
    // 手动添加setter方法，确保MyBatis能正确设置属性
    public void setSubplanCount(Integer subplanCount) {
        this.subplanCount = subplanCount;
    }
    
    public Integer getSubplanCount() {
        return this.subplanCount;
    }
} 
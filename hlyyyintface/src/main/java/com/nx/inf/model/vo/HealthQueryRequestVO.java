package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 健康记录查询请求VO
 * @Author: zhongx
 * @CreateDate: 2024/5/15
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HealthQueryRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    private String sfzh;

    /**
     * 开始时间，格式: YYYY-MM-DD HH:MM:SS
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间，格式: YYYY-MM-DD HH:MM:SS
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;
} 
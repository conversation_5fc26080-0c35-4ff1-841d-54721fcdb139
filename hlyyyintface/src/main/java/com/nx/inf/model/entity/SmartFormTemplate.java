package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 智能表单模板实体类
 */
@Data
@TableName("smart_form_template")
public class SmartFormTemplate {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单名称
     */
    private String name;
    
    /**
     * 表单描述
     */
    private String description;
    
    /**
     * 表单类型：one_time-一次性问卷，repeatable-重复性问卷
     */
    private String formType;
    
    /**
     * 表单分类
     */
    private String category;
    
    /**
     * 状态：active-启用，inactive-停用
     */
    private String status;
    
    /**
     * 权限范围：self-仅本人查看，department-本科室查看，all-全院查看
     */
    private String permission;
    
    /**
     * 表单配置信息（JSON格式）
     */
    private String config;
    
    /**
     * 重复配置（JSON格式）
     */
    private String repeatConfig;
    
    /**
     * 备注说明
     */
    private String remarkText;
    
    /**
     * 知情同意书配置（JSON格式）
     */
    private String consentConfig;
    
    /**
     * 关联问卷ID
     */
    private Long questionnaireId;
    
    /**
     * 填写二维码URL（新增字段）
     */
    private String qrCodeUrl;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 创建者姓名
     */
    private String creatorName;
    
    /**
     * 创建者科室ID
     */
    private Long creatorDeptId;
    
    /**
     * 创建者科室名称
     */
    private String creatorDeptName;
    
    /**
     * 提交次数
     */
    private Integer submissionCount;
    
    /**
     * 最后提交时间
     */
    private Date lastSubmittedTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDeleted;
} 
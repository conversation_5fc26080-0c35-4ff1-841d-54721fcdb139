package com.nx.inf.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 医疗API请求参数VO
 * @Author: zhongx
 * @CreateDate: 2024/3/28
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MedicalRequestVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者ID列表，多个ID用逗号分隔
     */
    @NotBlank(message = "患者ID不能为空")
    private String patientIds;

    /**
     * 开始时间，格式: YYYY-MM-DD HH:MM:SS
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    /**
     * 结束时间，格式: YYYY-MM-DD HH:MM:SS
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;
} 
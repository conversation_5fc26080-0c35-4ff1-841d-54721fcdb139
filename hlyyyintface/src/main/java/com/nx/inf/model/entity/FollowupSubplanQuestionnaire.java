package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 随访子计划问卷关联实体类
 */
@Data
@TableName("followup_subplan_questionnaire")
public class FollowupSubplanQuestionnaire {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 子计划ID
     */
    private Long subplanId;
    
    /**
     * 问卷ID
     */
    private Long questionnaireId;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 是否必填
     */
    private Boolean isRequired;
    
    /**
     * 创建时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String createTime;
} 
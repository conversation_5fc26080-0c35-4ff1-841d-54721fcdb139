package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 模板分类字典实体类
 */
@Data
@TableName("content_template_category")
public class ContentTemplateCategory {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 分类编码
     */
    private String code;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类说明
     */
    private String description;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 状态（active:启用, inactive:停用）
     */
    private String status;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 是否删除（0:未删除, 1:已删除）
     */
    private Integer isDeleted;
} 
package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 内容模板实体类
 */
@Data
@TableName("content_template")
public class ContentTemplate {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 模板说明
     */
    private String description;
    
    /**
     * 模板分类（health_education:健康教育, medication_guide:用药指导, nursing_guide:护理指导, rehabilitation_guide:康复指导, diet_guide:饮食指导, other:其他）
     */
    private String category;
    
    /**
     * 消息类型（patientEducation:患教内容）
     */
    private String messageType;
    
    /**
     * 权限范围（all:全部用户, self:仅本人, department:同科室）
     */
    private String permission;
    
    /**
     * 内容标题
     */
    private String title;
    
    /**
     * 模板内容（富文本HTML）
     */
    private String content;
    
    /**
     * 模板内容（纯文本，用于搜索）
     */
    private String contentText;
    
    /**
     * 状态（active:启用, inactive:停用）
     */
    private String status;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 创建人姓名
     */
    private String creatorName;
    
    /**
     * 创建人科室ID
     */
    private Long creatorDeptId;
    
    /**
     * 创建人科室名称
     */
    private String creatorDeptName;
    
    /**
     * 使用次数
     */
    private Integer useCount;
    
    /**
     * 最后使用时间
     */
    private Date lastUsedTime;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
    /**
     * 是否删除（0:未删除, 1:已删除）
     */
    private Integer isDeleted;
} 
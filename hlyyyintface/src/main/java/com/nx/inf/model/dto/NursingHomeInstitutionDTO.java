package com.nx.inf.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 养老院机构DTO
 */
@Data
public class NursingHomeInstitutionDTO {
    
    private Long id;
    
    /**
     * 机构名称
     */
    private String name;
    
    /**
     * 机构代码
     */
    private String code;
    
    /**
     * 机构地址
     */
    private String address;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 传真号码
     */
    private String fax;
    
    /**
     * 邮箱地址
     */
    private String email;
    
    /**
     * 床位数量
     */
    private Integer capacity;
    
    /**
     * 当前入住人数
     */
    private Integer currentPatients;
    
    /**
     * 入住率
     */
    private BigDecimal occupancyRate;
    
    /**
     * 执业许可证号
     */
    private String licenseNumber;
    
    /**
     * 法人代表
     */
    private String legalPerson;
    
    /**
     * 院长姓名
     */
    private String director;
    
    /**
     * 院长联系电话
     */
    private String directorPhone;
    
    /**
     * 联系人姓名
     */
    private String contactPerson;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 机构描述
     */
    private String description;
    
    /**
     * 机构等级
     */
    private String level;
    
    /**
     * 机构等级描述
     */
    private String levelText;
    
    /**
     * 服务类型(JSON格式)
     */
    private String serviceTypes;
    
    /**
     * 成立日期
     */
    private String establishDate;
    
    /**
     * 合作开始日期
     */
    private String cooperationStartDate;
    
    /**
     * 合作结束日期
     */
    private String cooperationEndDate;
    
    /**
     * 状态: 0-禁用, 1-启用
     */
    private Integer status;
    
    /**
     * 状态文本
     */
    private String statusText;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String district;
    
    /**
     * 完整地址
     */
    private String fullAddress;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 纬度
     */
    private BigDecimal latitude;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
} 
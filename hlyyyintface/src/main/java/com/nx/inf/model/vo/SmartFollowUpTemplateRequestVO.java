package com.nx.inf.model.vo;

import lombok.Data;

/**
 * 智能随访模板请求VO
 */
@Data
public class SmartFollowUpTemplateRequestVO {
    
    /**
     * 页码（从0开始）
     */
    private Integer page = 0;
    
    /**
     * 每页大小
     */
    private Integer size = 20;
    
    /**
     * 模板名称（模糊查询）
     */
    private String name;
    
    /**
     * 模板分类
     */
    private String category;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 创建人ID
     */
    private Long creatorId;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
} 
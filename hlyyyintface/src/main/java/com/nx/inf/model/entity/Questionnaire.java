package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("questionnaire")
public class Questionnaire {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    private String type;
    private String status;
    private Boolean isScoring;
    private Boolean isGuidance;
    private Boolean isTrigger;
    private Date createTime;
    private Date updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsScoring() {
        return isScoring;
    }

    public void setIsScoring(Boolean isScoring) {
        this.isScoring = isScoring;
    }

    public Boolean getIsGuidance() {
        return isGuidance;
    }

    public void setIsGuidance(Boolean isGuidance) {
        this.isGuidance = isGuidance;
    }

    public Boolean getIsTrigger() {
        return isTrigger;
    }

    public void setIsTrigger(Boolean isTrigger) {
        this.isTrigger = isTrigger;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

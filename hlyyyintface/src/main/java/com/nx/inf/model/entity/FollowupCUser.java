package com.nx.inf.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("followup_c_user")
public class FollowupCUser {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 手机号（新增字段）
     */
    private String phone;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;
    
    /**
     * 出生日期（新增字段）
     */
    @TableField("birth_date")
    private Date birthDate;
    
    /**
     * 身份证号（新增字段）
     */
    @TableField("id_card")
    private String idCard;
    
    /**
     * 地址（新增字段）
     */
    private String address;
    
    /**
     * 紧急联系人（新增字段）
     */
    @TableField("emergency_contact")
    private String emergencyContact;
    
    /**
     * 紧急联系人电话（新增字段）
     */
    @TableField("emergency_phone")
    private String emergencyPhone;
    
    /**
     * 绑定状态（新增字段）
     */
    @TableField("bind_status")
    private String bindStatus;
    
    /**
     * 授权状态（新增字段）
     */
    @TableField("auth_status")
    private String authStatus;
    
    /**
     * 最后登录时间（新增字段）
     */
    @TableField("last_login_time")
    private Date lastLoginTime;
    
    /**
     * 最后登录IP（新增字段）
     */
    @TableField("last_login_ip")
    private String lastLoginIp;
    
    /**
     * 证件类型
     */
    @TableField("id_type")
    private String idType;
    
    /**
     * 关系
     */
    private String relationship;
    
    /**
     * 证件号码
     */
    @TableField("id_number")
    private String idNumber;
    
    /**
     * 监护人证件类型
     */
    @TableField("jhr_id_type")
    private String jhrIdType;
    
    /**
     * 监护人证件号码
     */
    @TableField("jhr_id_number")
    private String jhrIdNumber;
    
    /**
     * 监护人姓名
     */
    @TableField("jhr_name")
    private String jhrName;
    
    /**
     * 监护人电话
     */
    @TableField("jhr_phone")
    private String jhrPhone;
    
    /**
     * 是否绑定
     */
    @TableField("is_bind")
    private Integer isBind;
    
    /**
     * 是否授权
     */
    @TableField("is_auth")
    private Integer isAuth;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
} 
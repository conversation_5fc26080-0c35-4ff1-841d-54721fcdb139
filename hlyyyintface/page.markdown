# 页面文档

## 模板管理页面 (templateManage.vue)

### 页面信息
- **文件路径**: `hlwyy/src/components/followUp/templateManage.vue`
- **路由路径**: `/followUp/templateManage`
- **页面类型**: 模板管理功能页面
- **最后更新**: 2025-07-24 21:30
- **版本**: v1.3.2

### 页面功能

#### 核心功能
1. **模板管理**
   - 智能表单模板管理
   - 问卷模板管理
   - 内容模板管理
   - 智能随访模块（暂时屏蔽）

2. **标签页导航**
   - 多模块标签页切换
   - 默认显示智能表单模块
   - 模块间独立运行

#### 页面布局

##### 页面结构
- **头部区域**: 页面标题"模板管理"
- **标签页区域**: 四个功能模块的标签页导航
- **内容区域**: 各模块的具体功能界面

##### 功能模块

###### 智能表单模块 (smartForm)
- **状态**: 正常运行
- **功能**: 智能表单模板的创建、编辑、管理
- **组件**: SmartForm.vue

###### 问卷模板模块 (questionnaire)
- **状态**: 正常运行
- **功能**: 问卷模板的设计和管理
- **组件**: QuestionnaireTemplate.vue

###### 内容模板模块 (content)
- **状态**: 正常运行
- **功能**: 内容模板的创建和编辑
- **组件**: ContentTemplate.vue

###### 智能随访模块 (smartFollowUp)
- **状态**: 暂时屏蔽
- **功能**: 智能随访模板管理
- **组件**: SmartFollowUp.vue
- **屏蔽原因**: 功能调整需要
- **恢复方式**: 取消注释相关代码

### UI组件详情

#### 标签页组件
```vue
<el-tabs v-model="activeTab" class="template-tabs">
    <!-- 智能随访模块暂时屏蔽 -->
    <!-- <el-tab-pane label="智能随访" name="smartFollowUp">
        <smart-follow-up ref="smartFollowUp"></smart-follow-up>
    </el-tab-pane> -->
    <el-tab-pane label="智能表单" name="smartForm">
        <smart-form ref="smartForm"></smart-form>
    </el-tab-pane>
    <!-- 其他模块... -->
</el-tabs>
```

**特性**:
- Element UI标签页组件
- 响应式标签页切换
- 模块化组件加载
- 统一的样式设计

### 技术实现

#### 组件结构
```vue
<template>
    <div class="template-manage">
        <div class="header">
            <h2>模板管理</h2>
        </div>
        <el-tabs v-model="activeTab" class="template-tabs">
            <!-- 各功能模块标签页 -->
        </el-tabs>
    </div>
</template>
```

#### 数据绑定
- `activeTab`: 当前活跃的标签页名称
- 默认值: 'smartForm' (智能表单)

#### 方法函数
- `refreshCurrentTab()`: 刷新当前活跃标签页的组件

#### 组件导入
```javascript
// 智能随访模块暂时屏蔽
// import SmartFollowUp from './children/SmartFollowUp.vue'
import SmartForm from './children/SmartForm.vue'
import QuestionnaireTemplate from './children/QuestionnaireTemplate.vue'
import ContentTemplate from './children/ContentTemplate.vue'
```

### 样式系统

#### 页面样式
- **背景色**: #f5f7fa
- **内边距**: 20px
- **最小高度**: 100vh

#### 标签页样式
- **背景色**: #fff
- **圆角**: 4px
- **阴影**: 0 2px 12px 0 rgba(0, 0, 0, 0.1)
- **标签高度**: 50px
- **字体大小**: 14px

### 功能状态

#### 正常运行模块
1. ✅ 智能表单模块
2. ✅ 问卷模板模块
3. ✅ 内容模板模块

#### 暂时屏蔽模块
1. 🔄 智能随访模块
   - 屏蔽时间: 2024-12-19 16:45
   - 屏蔽方式: 代码注释
   - 恢复方式: 取消注释

### 维护说明

#### 智能随访模块恢复步骤
1. 取消注释标签页组件
2. 恢复组件导入语句
3. 重新注册组件
4. 测试功能正常性

#### 代码位置
```javascript
// 1. 恢复导入
import SmartFollowUp from './children/SmartFollowUp.vue'

// 2. 恢复组件注册
components: {
    SmartFollowUp,
    // 其他组件...
}

// 3. 恢复标签页
<el-tab-pane label="智能随访" name="smartFollowUp">
    <smart-follow-up ref="smartFollowUp"></smart-follow-up>
</el-tab-pane>
```

---

## 登录页面 (login.vue)

### 页面信息
- **文件路径**: `hlwyy/src/components/special/login.vue`
- **路由路径**: `/login`
- **页面类型**: 登录认证页面
- **最后更新**: 2024-12-19 16:30
- **版本**: v1.3.0

### 页面功能

#### 核心功能
1. **用户登录认证**
   - 账号密码验证
   - 记住密码功能
   - 忘记密码跳转
   - 登录状态管理

2. **表单验证**
   - 账号输入验证
   - 密码输入验证
   - 实时表单状态反馈
   - 登录按钮状态控制

3. **用户体验优化**
   - 输入框聚焦动画
   - 按钮悬停效果
   - 加载状态显示
   - 错误信息提示

#### 页面布局

##### 左侧插图区域
- **背景**: 医疗主题渐变背景 (#52a3fc → #3f87ea)
- **内容**: 
  - 三个医生角色动画插图
  - 医疗主题标语
  - 装饰性网格背景
- **动画**: 医生角色浮动动画，错位播放

##### 右侧登录表单区域
- **布局**: 卡片式设计，居中显示
- **组件**:
  - Logo图标 (医疗十字图标)
  - 系统标题和副标题
  - 账号输入框 (带用户图标)
  - 密码输入框 (带锁图标)
  - 记住密码复选框
  - 忘记密码链接
  - 登录按钮
  - 版权信息

### UI组件详情

#### 输入框组件
```vue
<div class="input-wrapper">
    <i class="input-icon el-icon-user"></i>
    <input class="form-input" v-model="ruleForm.userName" />
    <div class="input-line"></div>
</div>
```

**特性**:
- 圆角设计 (25px)
- 图标前缀
- 聚焦时底部线条动画
- 背景色变化反馈
- 占位符文本

#### 登录按钮
```vue
<el-button class="login-button" @click="submitForm" :loading="loading">
    立即登录
</el-button>
```

**特性**:
- 渐变背景色
- 悬停时上浮效果
- 加载状态显示
- 禁用状态控制
- 阴影效果

#### 医生插图组件
```vue
<div class="doctor doctor-1">
    <div class="doctor-avatar"></div>
    <div class="doctor-body"></div>
    <div class="stethoscope"></div>
    <div class="clipboard"></div>
</div>
```

**特性**:
- 纯CSS绘制
- 浮动动画效果
- 错位播放时间
- 医疗主题元素

### 样式系统

#### 颜色方案
- **主色调**: #52a3fc (蓝色)
- **辅助色**: #3f87ea (深蓝色)
- **背景色**: 渐变背景 (#667eea → #764ba2)
- **文字色**: #333 (主要), #666 (次要), #999 (辅助)
- **边框色**: #e4e4e4

#### 字体系统
- **标题**: 24px, 600 weight
- **副标题**: 14px, normal weight
- **输入框**: 16px, normal weight
- **按钮**: 16px, 600 weight
- **辅助文字**: 12px, normal weight

#### 间距系统
- **容器内边距**: 40px
- **组件间距**: 25px
- **按钮高度**: 50px
- **输入框高度**: 50px
- **圆角半径**: 25px

### 响应式设计

#### 桌面端 (>768px)
- 左右分栏布局
- 插图区域和表单区域各占50%
- 最大宽度1200px
- 高度600px

#### 移动端 (≤768px)
- 上下堆叠布局
- 插图区域最小高度200px
- 表单区域自适应
- 边距调整为20px

### 动画效果

#### CSS动画
1. **浮动动画** (医生插图)
   ```scss
   @keyframes doctorFloat {
       0%, 100% { transform: translateY(0px); }
       50% { transform: translateY(-10px); }
   }
   ```

2. **背景装饰动画**
   ```scss
   @keyframes float {
       0%, 100% { transform: translateY(0px); }
       50% { transform: translateY(-20px); }
   }
   ```

3. **输入框聚焦动画**
   ```scss
   .input-line {
       transform: scaleX(0);
       transition: transform 0.3s ease;
   }
   ```

#### 交互反馈
- 输入框聚焦时背景色变化
- 按钮悬停时上浮效果
- 加载状态旋转动画
- 错误信息淡入效果

### 技术实现

#### 组件结构
```vue
<template>
    <div class="login-wrap">
        <!-- 背景装饰 -->
        <div class="background-decoration">...</div>
        
        <!-- 主要内容区域 -->
        <div class="login-container">
            <!-- 左侧插图区域 -->
            <div class="illustration-section">...</div>
            
            <!-- 右侧登录表单区域 -->
            <div class="login-form-section">...</div>
        </div>
    </div>
</template>
```

#### 数据绑定
- `ruleForm.userName`: 用户账号
- `ruleForm.passWord`: 用户密码
- `checked`: 记住密码状态
- `loading`: 登录加载状态

#### 方法函数
- `submitForm()`: 提交登录表单
- `misspwd()`: 跳转到忘记密码页面
- `isUpdatePassWordFn()`: 检查密码更新状态
- `getImToken()`: 获取网易云信token

### 浏览器兼容性

#### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

#### CSS特性支持
- CSS Grid
- Flexbox
- CSS3 渐变
- CSS3 动画
- backdrop-filter (部分支持)

### 性能优化

#### 已实现的优化
- CSS动画使用transform属性
- 图片使用CSS绘制，减少HTTP请求
- 响应式图片加载
- 代码分割和懒加载

#### 建议的优化
- 图片懒加载
- 代码压缩
- CDN加速
- 缓存策略

### 测试用例

#### 功能测试
1. 正常登录流程
2. 记住密码功能
3. 忘记密码跳转
4. 表单验证
5. 错误处理

#### 兼容性测试
1. 不同浏览器测试
2. 不同屏幕尺寸测试
3. 不同设备测试
4. 网络状况测试

#### 性能测试
1. 页面加载速度
2. 动画流畅度
3. 内存使用情况
4. 响应时间

### 维护说明

#### 日常维护
- 定期检查浏览器兼容性
- 监控页面性能指标
- 收集用户反馈
- 更新依赖包

#### 版本更新
- 记录所有变更内容
- 更新版本号
- 测试新功能
- 更新文档

---

*最后更新时间: 2024-12-19 16:30* 
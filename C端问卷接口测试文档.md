# C端问卷接口测试文档

**文档版本**: v1.0.0  
**创建时间**: 2025-07-25  
**接口修改**: 基于统一数据结构的三个核心接口

## 修改总结

### 1. 问卷列表获取接口 (`/emr/followup/customer/questionnaire-list`)

**主要修改**:
- 支持可选的`subplanId`参数
- 返回标准的问卷结构（不含`result`字段）
- 增加用户权限验证
- 优化数据结构，支持按子计划分组

**新增功能**:
- `validateUserPlanAccess()` - 验证用户访问权限
- `buildQuestionnaireList()` - 构建问卷列表
- `buildQuestionList()` - 构建题目列表
- `buildOptionList()` - 构建选项列表

### 2. 问卷填报接口 (`/emr/followup/customer/questionnaire-submit`)

**主要修改**:
- 完全重写接口逻辑，支持新的数据结构
- 入参改为包含`questionnaires`数组的完整结构
- 支持多问卷同时提交
- 增加`result`字段解析逻辑
- 自动计算得分和统计信息

**新增功能**:
- `validateQuestionnaireInSubplan()` - 验证问卷归属
- `findOptionByValue()` - 根据值查找选项
- `calculateMaxQuestionScore()` - 计算题目最大分数
- 支持单选题、多选题、文本题的不同处理逻辑

### 3. 问卷答案查看接口 (`/emr/followup/customer/questionnaire-answers`)

**主要修改**:
- 完全重写接口逻辑
- 支持可选的`subplanId`和`questionnaireId`参数
- 返回与填报接口相同的数据结构（含`result`字段）
- 支持按计划、子计划、问卷的层级查询

**新增功能**:
- `buildSubplanQuestionnaireAnswers()` - 构建子计划问卷答案
- `buildQuestionnaireAnswers()` - 构建问卷答案
- `buildQuestionAnswers()` - 构建题目答案
- `getQuestionAnswer()` - 获取题目答案
- `getLatestSubmitTime()` - 获取最新提交时间
- `calculateTotalScore()` - 计算总分

## 接口测试用例

### 测试用例1: 问卷列表获取

**请求**:
```json
POST /emr/followup/customer/questionnaire-list
{
    "phone": "13800138000",
    "planId": "1",
    "subplanId": "2"
}
```

**预期响应**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 1,
        "planName": "膝关节置换术后随访",
        "subplanId": 2,
        "subplanName": "康复节点评估",
        "questionnaires": [
            {
                "id": 3,
                "name": "膝关节功能评估",
                "description": "评估膝关节功能恢复情况",
                "sortOrder": 1,
                "isRequired": true,
                "questions": [
                    {
                        "id": 10,
                        "questionText": "您目前的疼痛程度如何？",
                        "questionType": "radio",
                        "isRequired": true,
                        "sortOrder": 1,
                        "options": [
                            {
                                "id": 30,
                                "optionText": "无疼痛",
                                "optionValue": "30",
                                "score": 0,
                                "sortOrder": 1
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

### 测试用例2: 问卷填报

**请求**:
```json
POST /emr/followup/customer/questionnaire-submit
{
    "phone": "13800138000",
    "planId": 1,
    "subplanId": 2,
    "questionnaires": [
        {
            "id": 3,
            "name": "膝关节功能评估",
            "questions": [
                {
                    "id": 10,
                    "questionText": "您目前的疼痛程度如何？",
                    "questionType": "radio",
                    "result": "30",
                    "options": [...]
                },
                {
                    "id": 11,
                    "questionText": "您目前有哪些症状？",
                    "questionType": "checkbox",
                    "result": ["34", "35"],
                    "options": [...]
                }
            ]
        }
    ]
}
```

**预期响应**:
```json
{
    "code": 200,
    "message": "问卷提交成功",
    "data": {
        "planId": 1,
        "subplanId": 2,
        "submittedQuestionnaires": [
            {
                "questionnaireId": 3,
                "questionnaireName": "膝关节功能评估",
                "totalScore": 2,
                "maxScore": 10,
                "submittedQuestions": 2,
                "totalQuestions": 2
            }
        ],
        "totalScore": 2,
        "submitTime": "2025-07-25 15:30:00"
    }
}
```

### 测试用例3: 问卷答案查看

**请求**:
```json
POST /emr/followup/customer/questionnaire-answers
{
    "phone": "13800138000",
    "planId": "1",
    "subplanId": "2",
    "questionnaireId": "3"
}
```

**预期响应**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 1,
        "planName": "膝关节置换术后随访",
        "subplanId": 2,
        "subplanName": "康复节点评估",
        "submitTime": "2025-07-25 15:30:00",
        "totalScore": 2,
        "questionnaires": [
            {
                "id": 3,
                "name": "膝关节功能评估",
                "questions": [
                    {
                        "id": 10,
                        "questionText": "您目前的疼痛程度如何？",
                        "questionType": "radio",
                        "result": "30",
                        "options": [...]
                    },
                    {
                        "id": 11,
                        "questionText": "您目前有哪些症状？",
                        "questionType": "checkbox",
                        "result": "34,35",
                        "options": [...]
                    }
                ]
            }
        ]
    }
}
```

## 注意事项

### 1. 数据类型处理
- `result`字段在单选题和文本题中为字符串
- `result`字段在多选题中为字符串数组，但存储时转为逗号分隔的字符串
- 所有ID字段统一使用字符串格式传输，内部转换为Long类型

### 2. 权限验证
- 所有接口都增加了用户权限验证
- 验证用户是否有权限访问指定的随访计划
- 验证问卷是否属于指定的子计划

### 3. 错误处理
- 统一的错误响应格式
- 详细的错误信息提示
- 异常情况的日志记录

### 4. 性能优化
- 批量查询减少数据库访问
- 合理的数据结构避免N+1问题
- 适当的缓存机制（可后续添加）

## 兼容性说明

### 向后兼容
- 保持原有接口路径不变
- 错误响应格式保持一致
- 核心业务逻辑保持兼容

### 数据结构变更
- 问卷列表接口：数据结构完全重构，更加规范
- 问卷填报接口：入参结构完全变更，支持批量提交
- 问卷答案接口：出参结构完全变更，与填报接口保持一致

## 部署建议

### 1. 测试验证
- 在测试环境充分验证所有接口功能
- 测试各种边界情况和异常情况
- 验证数据一致性和完整性

### 2. 数据迁移
- 如有必要，准备数据迁移脚本
- 确保现有数据的兼容性
- 备份重要数据

### 3. 监控告警
- 添加接口调用监控
- 设置异常告警机制
- 监控接口性能指标

这次修改实现了C端问卷接口的统一数据结构设计，大大提升了前端开发的便利性和代码复用性。

# 随访系统详细流程文档

**文档版本**: v2.0.0  
**创建时间**: 2025-07-25  
**基于代码**: 前端Vue.js + 后端Spring Boot  
**数据库**: ylydb.sql

## 系统架构概述

### 核心表结构关系
```
followup_plan (随访计划) 1:1 patient_group (患者分组)
    ↓ 1:N
followup_subplan (随访子计划)
    ↓ 1:N                    ↓ N:N (通过followup_subplan_questionnaire关联)
followup_subplan_item       questionnaire (问卷模板)
(子计划项目)                    ↓ 1:N
    ↓ 1:N                   questionnaire_question (问卷题目)
followup_task (随访任务)         ↓ 1:N
                            questionnaire_option (题目选项)

patient_group (患者分组)
    ↓ 1:N
patient_group_relation (患者分组关联)
    ↓ N:1
followup_c_user (C端用户)

questionnaire_answer (问卷答案) - 关联到随访计划和问卷
    ↓ 关联字段
    - plan_id (随访计划ID)
    - subplan_id (子计划ID)
    - questionnaire_id (问卷ID)
    - patient_id (患者ID)
```

### 业务逻辑关系说明

**1. 随访计划与患者分组关系**
- 一个随访计划对应一个患者分组 (1:1)
- 患者通过扫描分组二维码申请入组
- 入组审核通过后，患者自动参与该分组对应的随访计划

**2. 随访计划与子计划关系**
- 一个随访计划包含多个子计划 (1:N)
- 每个子计划有不同的执行时间和内容配置
- 子计划支持周期循环和自定义两种模式

**3. 子计划执行模式详解**

**3.1 周期循环模式**
- **原理**: 每隔【周期天数】进行一次提醒
- **数据存储**: `followup_subplan_item`表中只维护一条记录
- **执行规律**: 加入计划当天开始，按周期天数循环执行
- **示例**: 周期天数=3，则在当天、当天+3、当天+6、当天+9...执行

**3.2 自定义模式**
- **原理**: 指定加入计划后的第几天执行
- **数据存储**: `followup_subplan_item`表中可维护多条记录
- **执行规律**: 按指定的天数执行，如第1天、第5天、第10天
- **示例**: 可设置第1天术后指导、第5天康复评估、第10天复查提醒

**3. 子计划与问卷关系**
- 一个子计划可以关联多个问卷 (N:N)
- 通过`followup_subplan_questionnaire`表建立关联
- 每个关联可以设置排序顺序和是否必填

**4. 子计划表单收集业务逻辑**
- **不同阶段需要不同问卷**: 术后第1周疼痛评估，第2周康复进度评估
- **时间节点差异化**: 每个子计划有独立的执行时间和提醒设置
- **内容针对性**: 不同子计划针对不同康复阶段，配置相应评估工具
- **灵活性**: 医生可根据患者情况为不同子计划配置不同问卷组合

## 一、管理端随访计划管理流程

### 1.1 随访计划列表页面
**页面路径**: `/followUp/plan`  
**组件文件**: `hlwyy/src/components/followUp/followUpPlan.vue`  
**API接口**: `hlwyy/src/api/followupPlan.js`

#### 页面功能按钮及接口调用：

**1. 新增随访计划按钮**
- **按钮位置**: 顶部工具栏
- **点击事件**: `showAddPlanDialog()`
- **调用接口**: `POST /emr/followup/plan`
- **后端控制器**: `FollowupPlanController.createPlan()`

**2. 搜索功能**
- **搜索条件**: 计划名称、计划分类、计划状态
- **点击事件**: `searchPlans()`
- **调用接口**: `GET /emr/followup/plan/page`
- **后端控制器**: `FollowupPlanController.getPlanPage()`

**3. 计划卡片操作**
- **编辑按钮**: `handleCardOperation('edit', plan)`
- **暂停/启用按钮**: `handleCardOperation('pause/activate', plan)`
  - **调用接口**: `PUT /emr/followup/plan/{id}/status`
  - **后端控制器**: `FollowupPlanController.updatePlanStatus()`
- **删除按钮**: `handleCardOperation('delete', plan)`
  - **调用接口**: `DELETE /emr/followup/plan/{id}`
  - **后端控制器**: `FollowupPlanController.deletePlan()`

### 1.2 随访计划详情页面
**页面切换**: 点击计划卡片 → `loadPlanDetail(plan.id)`

#### 详情页面标签页：

**1. 患者管理标签**
- **功能**: 查看和管理计划下的患者
- **数据来源**: 通过`patient_group_relation`表关联

**2. 数据分析标签**
- **功能**: 查看随访数据统计
- **组件**: `DataAnalysisTab.vue`

**3. 统计分析标签**
- **功能**: 查看计划执行统计
- **组件**: `DataStatistics.vue`

**4. 项目设置标签**
- **功能**: 配置随访子计划和项目
- **核心功能**: 子计划管理、问卷关联、任务配置

### 1.3 智能随访计划管理
**页面路径**: `/followUp/smart-plan`  
**组件文件**: `hlwyy/src/components/followUp/children/SmartFollowUp.vue`

#### 主要功能按钮：

**1. 新增模板按钮**
- **点击事件**: `handleAdd()`
- **打开组件**: `SmartFollowUpEdit.vue`

**2. 模板操作按钮**
- **查看**: `handleView(row)` - 只读模式查看模板
- **编辑**: `handleEdit(row)` - 编辑模式
- **复制**: `handleCopy(row)` - 复制模板创建新模板
- **启用/禁用**: `handleToggleStatus(row)` - 切换模板状态
- **删除**: `handleDelete(row)` - 删除模板

## 二、患者分组管理流程

### 2.1 分组列表管理
**页面路径**: `/followUp/groupList`  
**组件文件**: `hlwyy/src/components/followUp/groupList.vue`

#### 核心功能按钮：

**1. 新增分组按钮**
- **点击事件**: `addNewGroup()`
- **调用接口**: `POST /emr/patient/group`
- **后端控制器**: `PatientGroupController.createGroup()`

**2. 分组操作按钮**
- **编辑**: `editGroup(row)`
  - **调用接口**: `PUT /emr/patient/group/{id}`
  - **后端控制器**: `PatientGroupController.updateGroup()`
- **查看**: `viewGroup(row)` - 查看分组详情和患者列表
- **删除**: `deleteGroup(row)`
  - **调用接口**: `DELETE /emr/patient/group/{id}`
  - **后端控制器**: `PatientGroupController.deleteGroup()`

### 2.2 患者入组管理
**页面路径**: `/followUp/patientGroup`  
**组件文件**: `hlwyy/src/components/followUp/patientGroup.vue`

#### 入组方式：

**1. 二维码入组**
- **生成二维码**: 每个分组自动生成入组二维码
- **二维码内容**: 包含分组ID和相关参数
- **扫码流程**: 患者扫码 → 填写信息 → 提交申请 → 医生审核

**2. 手动导入**
- **批量导入**: 支持Excel批量导入患者
- **单个添加**: 手动输入患者信息直接入组

## 三、C端用户管理流程

### 3.1 C端用户注册流程

#### 3.1.1 用户注册接口
- **API接口**: `POST /emr/followup/cuser/register`
- **后端控制器**: `FollowupCUserController.register()`
- **功能**: C端用户注册，创建用户账号

**请求参数**:
```json
{
    "phone": "13800138000",
    "name": "张三",
    "gender": "男",
    "age": 45,
    "idType": "身份证",
    "idCard": "110101199001011234",
    "guardianName": "李四",
    "guardianPhone": "13900139000"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "userId": 1,
        "phone": "13800138000",
        "name": "张三",
        "status": "registered"
    }
}
```

#### 注册步骤：
1. **填写基本信息**: 姓名、性别、年龄、证件类型、证件号码、手机号
2. **监护人信息**: 如果是监护人代理，填写监护人信息
3. **提交注册**: 系统创建C端用户记录
4. **数据存储**: 保存到`followup_c_user`表

### 3.2 用户绑定和授权流程

#### 3.2.1 确认绑定接口
- **API接口**: `POST /emr/followup/cuser/bind`
- **后端控制器**: `FollowupCUserController.bind()`
- **功能**: 绑定患者信息，关联医院患者档案

**请求参数**:
```json
{
    "userId": 1,
    "patientId": 12345,
    "hospitalId": 1,
    "confirmCode": "ABC123"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "绑定成功",
    "data": {
        "bindId": 1,
        "status": "bound",
        "bindTime": "2025-07-25 10:00:00"
    }
}
```

#### 3.2.2 确认授权接口
- **API接口**: `POST /emr/followup/cuser/auth`
- **后端控制器**: `FollowupCUserController.auth()`
- **功能**: 授权访问随访计划和相关数据

**请求参数**:
```json
{
    "userId": 1,
    "authScope": ["followup", "questionnaire"],
    "authConfirm": true
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "授权成功",
    "data": {
        "authId": 1,
        "authScope": ["followup", "questionnaire"],
        "authTime": "2025-07-25 10:00:00"
    }
}
```

### 3.3 患者入组申请流程

#### 3.3.1 患者确认入组接口
- **API接口**: `POST /emr/followup/cuser/confirm-group`
- **后端控制器**: `FollowupCUserController.confirmGroup()`
- **功能**: 患者确认加入随访分组

**请求参数**:
```json
{
    "phone": "13800138000",
    "groupId": 1,
    "qrCode": "GROUP_QR_CODE_123",
    "patientInfo": {
        "name": "张三",
        "age": 45,
        "gender": "男"
    }
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "入组申请提交成功",
    "data": {
        "applicationId": 1,
        "status": "pending",
        "groupName": "高血压随访分组",
        "submitTime": "2025-07-25 10:00:00"
    }
}
```

#### 入组流程：
1. **扫描二维码**: 获取分组信息
2. **确认入组**: 提交入组申请
3. **创建关联记录**: 在`patient_group_relation`表中创建记录，状态为"0"（待审核）
4. **等待审核**: 医生在管理端审核入组申请
5. **审核结果**: 通过后状态变为"1"（已入组），拒绝后状态变为"2"（已拒绝）

## 四、C端随访功能流程

### 4.1 随访计划列表接口
- **API接口**: `POST /emr/followup/customer/plan-list`
- **后端控制器**: `CustomerFollowupController.getFollowupPlanList()`
- **功能**: 获取用户参与的随访计划列表

**请求参数**:
```json
{
    "phone": "13800138000",
    "state": "0",
    "pageNo": 1,
    "pageSize": 10
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "planId": 34,
            "subplanId": 44,
            "name": "高血压随访计划",
            "number": "第1次计划",
            "date": "07月24日",
            "desc": "",
            "status": "0"
        },
        {
            "planId": 34,
            "subplanId": 45,
            "name": "高血压随访计划",
            "number": "第2次计划",
            "date": "07月27日",
            "desc": "",
            "status": "0"
        }
    ]
}
```

#### 查看流程：
1. **获取计划列表**: 根据手机号获取用户参与的随访计划
2. **状态筛选**: 支持按状态筛选（全部、已完成、已过期、即将开始、进行中）
3. **分页显示**: 支持分页查询
4. **子计划展示**: 每个子计划显示为一次计划，包含subplanId

### 4.2 随访计划详情接口
- **API接口**: `POST /emr/followup/customer/plan-detail`
- **后端控制器**: `CustomerFollowupController.getPlanDetail()`
- **功能**: 获取随访计划的详细信息

**请求参数**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 1
}
```

**响应数据（特定子计划模式）**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "subplanId": 44,
        "name": "高血压随访计划",
        "number": "第1次计划",
        "date": "07月24日",
        "personName": "患者",
        "desc": "",
        "projectItems": [
            {
                "title": "问卷调查",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            }
        ]
    }
}
```

**响应数据（汇总模式，不传number参数）**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "subplanIds": [44, 45],
        "name": "高血压随访计划",
        "number": "全部计划（共2次）",
        "date": "07月24日",
        "personName": "患者",
        "desc": "",
        "projectItems": [
            {
                "title": "问卷调查 - 第1次计划",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            },
            {
                "title": "问卷调查 - 第2次计划",
                "subQuestions": ["NRS动态疼痛评估（共1题）"]
            }
        ]
    }
}
```

#### 详情内容：
1. **计划基本信息**: 计划名称、执行时间、描述
2. **项目列表**: 显示需要完成的项目（问卷调查、内容查看等）
3. **执行状态**: 显示当前执行进度
4. **子计划支持**: 支持查看特定子计划或所有子计划汇总

### 4.3 获取用户信息接口
- **API接口**: `POST /emr/followup/customer/user-info`
- **后端控制器**: `CustomerFollowupController.getUserInfo()`
- **功能**: 获取C端用户的基本信息

**请求参数**:
```json
{
    "phone": "13800138000"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "userId": 1,
        "phone": "13800138000",
        "name": "张三",
        "gender": "男",
        "age": 45,
        "idCard": "110101199001011234",
        "status": "active",
        "registerTime": "2025-07-25 10:00:00"
    }
}
```

### 4.4 问卷填报流程

#### 4.4.1 问卷列表获取接口
- **API接口**: `POST /emr/followup/customer/questionnaire-list`
- **后端控制器**: `CustomerFollowupController.getQuestionnaireList()`
- **功能**: 获取患者可填写的问卷列表（基础结构，不含答案）

**请求参数**:
```json
{
    "phone": "13800138000",
    "planId": "34",
    "subplanId": "44"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "planName": "高血压随访计划",
        "subplanId": 44,
        "subplanName": "第一阶段评估",
        "questionnaires": [
            {
                "id": 1,
                "name": "疼痛相关不适评估",
                "description": "评估疼痛相关症状",
                "sortOrder": 1,
                "isRequired": true,
                "questions": [
                    {
                        "id": 1,
                        "questionText": "请您选择您最近1周内是否出现以下不适症状",
                        "questionType": "checkbox",
                        "isRequired": true,
                        "sortOrder": 1,
                        "options": [
                            {
                                "id": 1,
                                "optionText": "天气下雨或阴天",
                                "optionValue": "1",
                                "score": 5,
                                "sortOrder": 1
                            },
                            {
                                "id": 2,
                                "optionText": "疼痛",
                                "optionValue": "2",
                                "score": 2,
                                "sortOrder": 2
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

**业务逻辑**:
- 根据随访计划ID查询所有子计划
- 通过`followup_subplan_questionnaire`表获取每个子计划关联的问卷
- 按子计划的执行时间和问卷排序返回
- 返回标准的问卷结构（不含result字段）

#### 4.4.2 问卷填报接口
- **API接口**: `POST /emr/followup/customer/questionnaire-submit`
- **后端控制器**: `CustomerFollowupController.submitQuestionnaire()`
- **功能**: 提交问卷答案
- **入参设计**: 基于问卷列表接口的出参，在每道题目上添加`result`字段

**请求参数**:
```json
{
    "phone": "13800138000",
    "planId": 34,
    "subplanId": 44,
    "questionnaires": [
        {
            "id": 1,
            "name": "疼痛相关不适评估",
            "questions": [
                {
                    "id": 1,
                    "questionText": "请您选择您最近1周内是否出现以下不适症状",
                    "questionType": "checkbox",
                    "result": ["2", "3", "5"],
                    "options": [...]
                }
            ]
        }
    ]
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "提交成功",
    "data": {
        "planId": 34,
        "subplanId": 44,
        "submittedQuestionnaires": [
            {
                "questionnaireId": 1,
                "questionnaireName": "疼痛相关不适评估",
                "totalScore": 9,
                "maxScore": 20,
                "submittedQuestions": 1,
                "totalQuestions": 1
            }
        ],
        "totalScore": 9,
        "submitTime": "2025-07-25 15:30:00"
    }
}
```

**result字段规则**:
- **单选题(radio)**: `result`为字符串，值为选中选项的`optionValue`
- **多选题(checkbox)**: `result`为字符串数组，值为选中选项的`optionValue`数组
- **文本题(input)**: `result`为字符串，值为用户输入的文本内容

**业务逻辑**:
- 验证患者是否有权限填写该问卷
- 检查问卷是否属于患者参与的随访计划的子计划
- 解析题目的`result`字段，计算问卷得分
- 数据存储: 保存到`questionnaire_answer`表，包含plan_id、subplan_id等关联信息

#### 4.4.3 问卷答案查看接口
- **API接口**: `POST /emr/followup/customer/questionnaire-answers`
- **后端控制器**: `CustomerFollowupController.getQuestionnaireAnswers()`
- **功能**: 查看已提交的问卷答案
- **出参设计**: 与问卷填报接口的入参结构完全一致（包含`result`字段）

**请求参数**:
```json
{
    "phone": "13800138000",
    "planId": "34",
    "subplanId": "44",
    "questionnaireId": "1"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "planName": "高血压随访计划",
        "subplanId": 44,
        "subplanName": "第一阶段评估",
        "submitTime": "2025-07-25 15:30:00",
        "totalScore": 9,
        "questionnaires": [
            {
                "id": 1,
                "name": "疼痛相关不适评估",
                "questions": [
                    {
                        "id": 1,
                        "questionText": "请您选择您最近1周内是否出现以下不适症状",
                        "questionType": "checkbox",
                        "result": ["2", "3", "5"],
                        "options": [
                            {
                                "id": 2,
                                "optionText": "疼痛",
                                "optionValue": "2",
                                "score": 2
                            },
                            {
                                "id": 3,
                                "optionText": "麻木",
                                "optionValue": "3",
                                "score": 2
                            },
                            {
                                "id": 5,
                                "optionText": "咳嗽",
                                "optionValue": "5",
                                "score": 2
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

**功能特点**:
- 查看已提交的问卷答案和得分
- 支持按子计划分组显示
- 返回与填报接口相同的数据结构，便于前端复用组件

**4. 子计划问卷执行逻辑**
- **时间驱动**: 系统根据子计划的执行时间自动推送问卷提醒
- **阶段性评估**: 不同子计划的问卷针对不同康复阶段
- **个性化配置**: 医生可为每个子计划配置不同的问卷组合
- **数据关联**: 问卷答案与具体的子计划和执行时间关联，便于追踪患者在不同阶段的状态变化

## 五、问卷系统流程

### 5.1 问卷模板管理
**组件文件**: `hlwyy/src/components/followUp/children/QuestionnaireTemplate.vue`

#### 问卷类型支持：
- **单选题**: type="radio"
- **多选题**: type="checkbox"  
- **文本题**: type="input"
- **数字题**: type="number"
- **日期题**: type="date"

### 5.2 问卷评分系统
- **评分规则**: 每个选项可设置分值
- **自动计算**: 系统自动计算问卷总分
- **结果展示**: 显示得分和评价建议

## 六、内容模板管理流程

### 6.1 内容模板分类
**数据表**: `content_template`

#### 模板分类：
- **健康教育**: health_education
- **用药指导**: medication_guide  
- **护理指导**: nursing_guide
- **康复指导**: rehabilitation_guide
- **饮食指导**: diet_guide
- **其他**: other

### 6.2 模板权限管理
- **仅本人**: self - 只有创建者可见
- **本科室**: department - 同科室用户可见
- **全院**: all - 所有用户可见

## 七、任务调度系统

### 7.1 任务生成机制
**数据表**: `followup_task`

#### 任务生成规则详解：

**1. 周期循环任务生成**
- **触发条件**: 子计划类型为"cycle"，且设置了周期天数
- **生成逻辑**:
  - 患者入组当天作为起始时间
  - 按周期天数（如3天）循环生成：当天、当天+3、当天+6、当天+9...
  - 直到随访计划结束时间为止
- **数据来源**: `followup_subplan_item`表中的单条记录
- **执行时间**: 结合子计划的执行时间和提醒时间计算具体执行时刻

**2. 自定义任务生成**
- **触发条件**: 子计划类型为"custom"，设置了多个执行天数
- **生成逻辑**:
  - 患者入组当天作为起始时间
  - 按指定天数生成任务：第1天、第5天、第10天等
  - 每个指定天数对应一个任务
- **数据来源**: `followup_subplan_item`表中的多条记录
- **执行时间**: 入组时间 + 指定天数 + 提醒时间

**3. 任务内容构成**
- **提醒内容**: 来自`followup_subplan_item`中关联的内容模板
- **问卷填报**: 来自子计划关联的问卷列表（`followup_subplan_questionnaire`）
- **执行时间**: 综合考虑执行时间、提醒时间、计划结束时间

### 7.2 任务执行状态
- **pending**: 待执行
- **executing**: 执行中
- **completed**: 已完成
- **failed**: 执行失败

## 八、数据统计分析

### 8.1 计划统计信息
- **患者数量**: 参与计划的患者总数
- **任务数量**: 生成的任务总数
- **完成数量**: 已完成的任务数量
- **子计划数量**: 计划下的子计划数量

### 8.2 问卷统计分析
- **填写率**: 问卷填写完成率
- **平均得分**: 问卷平均分数
- **异常指标**: 超出正常范围的指标数量

## 九、系统集成接口

### 9.1 第三方系统集成
- **HIS系统**: 患者基本信息同步
- **EMR系统**: 病历信息获取
- **消息推送**: 短信、微信通知

### 9.2 移动端支持
- **二维码扫描**: 支持入组和表单填写
- **响应式设计**: 适配移动设备
- **离线缓存**: 支持离线填写和同步

## 十、详细技术实现

### 10.1 前端组件详细分析

#### SmartFollowUpEdit.vue 核心功能实现

**1. 表单数据结构**
```javascript
form: {
    planName: '',           // 计划名称
    treatmentTime: '',      // 治疗时间
    timeUnit: 'day',        // 时间单位
    planStatus: 'draft',    // 计划状态
    subPlans: [             // 子计划数组
        {
            name: '',       // 子计划名称
            settings: {     // 子计划设置
                type: 'patient',              // 类型：patient/medical
                executionTime: 'allTime',     // 执行时间：allTime/custom
                startDay: '',                 // 开始天数
                endDay: '',                   // 结束天数
                reminderTime: '',             // 提醒时间
                planType: 'cycle'             // 计划类型：cycle/custom
            },
            details: [],    // 周期循环详情
            customDetails: [], // 自定义详情
            questionnaires: [] // 关联问卷
        }
    ]
}
```

**2. 关键方法实现**
```javascript
// 添加子计划
addSubPlan() {
    const newSubPlan = {
        name: `子计划${this.form.subPlans.length + 1}`,
        settings: {
            type: 'patient',
            executionTime: 'allTime',
            startDay: '',
            endDay: '',
            reminderTime: new Date(),
            planType: 'cycle'
        },
        details: [],
        customDetails: [],
        questionnaires: []
    };
    this.form.subPlans.push(newSubPlan);
}

// 添加问卷到子计划
addQuestionnaireToSubplan(subplanIndex) {
    // 调用接口: GET /emr/questionnaire/list
    // 显示问卷选择对话框
    this.showQuestionnaireDialog = true;
    this.currentSubplanIndex = subplanIndex;

    // 获取可用问卷列表
    this.loadAvailableQuestionnaires();
},

// 加载可用问卷列表
async loadAvailableQuestionnaires() {
    try {
        const response = await getQuestionnaireList({
            status: 'active',
            pageSize: 100
        });
        if (response.data.code === 200) {
            this.availableQuestionnaires = response.data.data.records;
        }
    } catch (error) {
        this.$message.error('获取问卷列表失败');
    }
},

// 确认选择问卷
confirmQuestionnaireSelection(selectedQuestionnaires) {
    const subplan = this.form.subPlans[this.currentSubplanIndex];

    // 添加选中的问卷到子计划
    selectedQuestionnaires.forEach((questionnaire, index) => {
        const questionnaireItem = {
            id: questionnaire.id,
            name: questionnaire.name,
            description: questionnaire.description,
            sortOrder: subplan.questionnaires.length + index + 1,
            isRequired: true
        };
        subplan.questionnaires.push(questionnaireItem);
    });

    this.showQuestionnaireDialog = false;
    this.$message.success(`已添加${selectedQuestionnaires.length}个问卷`);
},

// 移除子计划中的问卷
removeQuestionnaireFromSubplan(subplanIndex, questionnaireIndex) {
    this.form.subPlans[subplanIndex].questionnaires.splice(questionnaireIndex, 1);

    // 重新排序
    this.form.subPlans[subplanIndex].questionnaires.forEach((q, index) => {
        q.sortOrder = index + 1;
    });
}

// 保存计划
handleSave() {
    this.$refs.templateForm.validate((valid) => {
        if (valid) {
            // 调用接口: POST /emr/followup/plan
            this.saveFollowupPlan();
        }
    });
}
```

#### followUpPlan.vue 详细实现

**1. 计划列表数据结构**
```javascript
plans: [
    {
        id: 34,
        name: '高血压随访计划',
        category: 'chronic_disease',
        status: 'active',
        endTimeType: 'unlimited',
        endTimeValue: null,
        groupId: 2,
        groupName: '高血压随访组',
        patientCount: 5,
        taskCount: 12,
        completedTaskCount: 8,
        subplanCount: 2,
        createTime: '2025-07-24 14:40:30'
    }
]
```

**2. 状态管理方法**
```javascript
// 获取状态样式类
getStatusClass(status) {
    const statusMap = {
        'draft': 'status-draft',
        'active': 'status-active',
        'paused': 'status-paused',
        'completed': 'status-completed'
    };
    return statusMap[status] || 'status-default';
}

// 获取状态文本
getStatusText(status) {
    const statusMap = {
        'draft': '草稿',
        'active': '启用中',
        'paused': '已暂停',
        'completed': '已完成'
    };
    return statusMap[status] || '未知';
}

// 获取分类文本
getCategoryText(category) {
    const categoryMap = {
        'postoperative': '术后随访',
        'chronic_disease': '慢病管理',
        'health_assessment': '健康评估',
        'rehabilitation': '康复管理'
    };
    return categoryMap[category] || '其他';
}
```

### 10.2 后端控制器详细实现

#### FollowupPlanController.java 核心方法

**1. 创建随访计划**
```java
@PostMapping
public Result<Object> createPlan(@RequestBody FollowupPlanDTO planDTO) {
    try {
        // 1. 验证数据完整性
        validatePlanData(planDTO);

        // 2. 创建主计划
        FollowupPlan plan = new FollowupPlan();
        BeanUtils.copyProperties(planDTO, plan);
        plan.setCreateTime(new Date());
        plan.setStatus("draft");
        followupPlanMapper.insert(plan);

        // 3. 创建子计划
        if (planDTO.getSubPlans() != null) {
            for (FollowupSubplanDTO subplanDTO : planDTO.getSubPlans()) {
                createSubplan(plan.getId(), subplanDTO);
            }
        }

        // 4. 生成二维码
        String qrCodeUrl = generateQRCode(plan.getId());
        plan.setQrCodeUrl(qrCodeUrl);
        followupPlanMapper.updateById(plan);

        return Result.OK(plan);
    } catch (Exception e) {
        return Result.error("创建随访计划失败: " + e.getMessage());
    }
}

// 创建子计划
private void createSubplan(Long planId, FollowupSubplanDTO subplanDTO) {
    // 1. 创建子计划记录
    FollowupSubplan subplan = new FollowupSubplan();
    BeanUtils.copyProperties(subplanDTO, subplan);
    subplan.setPlanId(planId);
    followupSubplanMapper.insert(subplan);

    // 2. 创建子计划项目
    if (subplanDTO.getItems() != null) {
        for (FollowupSubplanItemDTO itemDTO : subplanDTO.getItems()) {
            FollowupSubplanItem item = new FollowupSubplanItem();
            BeanUtils.copyProperties(itemDTO, item);
            item.setSubplanId(subplan.getId());
            followupSubplanItemMapper.insert(item);
        }
    }

    // 3. 关联问卷
    if (subplanDTO.getQuestionnaires() != null) {
        for (int i = 0; i < subplanDTO.getQuestionnaires().size(); i++) {
            QuestionnaireDTO questionnaireDTO = subplanDTO.getQuestionnaires().get(i);

            FollowupSubplanQuestionnaire relation = new FollowupSubplanQuestionnaire();
            relation.setSubplanId(subplan.getId());
            relation.setQuestionnaireId(questionnaireDTO.getId());
            relation.setSortOrder(questionnaireDTO.getSortOrder() != null ?
                                questionnaireDTO.getSortOrder() : i + 1);
            relation.setIsRequired(questionnaireDTO.getIsRequired() != null ?
                                 questionnaireDTO.getIsRequired() : true);
            relation.setCreateTime(new Date());

            followupSubplanQuestionnaireMapper.insert(relation);

            logger.info("子计划问卷关联创建成功: 子计划ID={}, 问卷ID={}, 排序={}",
                       subplan.getId(), questionnaireDTO.getId(), relation.getSortOrder());
        }
    }
}
```

**2. 更新计划状态**
```java
@PutMapping("/{id}/status")
public Result<Object> updatePlanStatus(@PathVariable Long id, @RequestBody Map<String, String> request) {
    try {
        String status = request.get("status");

        // 1. 验证状态值
        if (!Arrays.asList("draft", "active", "paused", "completed").contains(status)) {
            return Result.error("无效的状态值");
        }

        // 2. 更新计划状态
        FollowupPlan plan = followupPlanMapper.selectById(id);
        if (plan == null) {
            return Result.error("随访计划不存在");
        }

        plan.setStatus(status);
        plan.setUpdateTime(new Date());
        followupPlanMapper.updateById(plan);

        // 3. 如果启用计划，生成任务
        if ("active".equals(status)) {
            generateTasksForPlan(id);
        }

        return Result.OK("状态更新成功");
    } catch (Exception e) {
        return Result.error("更新计划状态失败: " + e.getMessage());
    }
}

// 为计划生成任务
private void generateTasksForPlan(Long planId) {
    // 1. 获取计划下的所有患者
    List<PatientGroupRelation> relations = getPatientsByPlanId(planId);

    // 2. 获取计划的子计划
    List<FollowupSubplan> subplans = followupSubplanMapper.selectByPlanId(planId);

    // 3. 为每个患者生成任务
    for (PatientGroupRelation relation : relations) {
        for (FollowupSubplan subplan : subplans) {
            generateTasksForPatientSubplan(planId, subplan, relation.getPatientId());
        }
    }
}
```

#### CustomerFollowupController.java C端接口实现

**1. 获取随访计划列表**
```java
@PostMapping("/plan-list")
public ResponseEntity<Map<String, Object>> getFollowupPlanList(@RequestBody Map<String, Object> request) {
    Map<String, Object> result = new HashMap<>();

    try {
        String phone = (String) request.get("phone");
        String state = (String) request.get("state");
        Integer pageNo = (Integer) request.get("pageNo");
        Integer pageSize = (Integer) request.get("pageSize");

        // 1. 根据手机号查找C端用户
        FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
        if (cUser == null) {
            result.put("code", 404);
            result.put("message", "用户不存在");
            return ResponseEntity.ok(result);
        }

        // 2. 查找用户参与的分组
        List<PatientGroupRelation> relations = patientGroupRelationMapper.selectList(
            new LambdaQueryWrapper<PatientGroupRelation>()
                .eq(PatientGroupRelation::getPatientId, cUser.getId())
                .eq(PatientGroupRelation::getStatus, "1") // 已入组
        );

        if (relations.isEmpty()) {
            result.put("code", 200);
            result.put("data", new ArrayList<>());
            result.put("message", "暂无随访计划");
            return ResponseEntity.ok(result);
        }

        // 3. 获取分组对应的随访计划
        List<Long> groupIds = relations.stream()
            .map(PatientGroupRelation::getGroupId)
            .collect(Collectors.toList());

        List<FollowupPlan> plans = followupPlanMapper.selectList(
            new LambdaQueryWrapper<FollowupPlan>()
                .in(FollowupPlan::getGroupId, groupIds)
                .eq(FollowupPlan::getStatus, "active")
        );

        // 4. 转换为C端格式
        List<Map<String, Object>> planList = new ArrayList<>();
        for (FollowupPlan plan : plans) {
            Map<String, Object> planInfo = convertToCPlanFormat(plan, cUser.getId());
            planList.add(planInfo);
        }

        result.put("code", 200);
        result.put("data", planList);
        result.put("message", "获取成功");
        return ResponseEntity.ok(result);

    } catch (Exception e) {
        result.put("code", 500);
        result.put("message", "获取失败: " + e.getMessage());
        return ResponseEntity.ok(result);
    }
}

// 转换为C端计划格式
private Map<String, Object> convertToCPlanFormat(FollowupPlan plan, Long patientId) {
    Map<String, Object> planInfo = new HashMap<>();

    // 1. 基本信息
    planInfo.put("planId", plan.getId());
    planInfo.put("name", plan.getName());
    planInfo.put("desc", plan.getDescription());

    // 2. 计算执行时间和状态
    Date now = new Date();
    String dateStr = new SimpleDateFormat("MM月dd日").format(now);
    planInfo.put("date", dateStr);

    // 3. 计算是第几次计划
    int executionCount = getExecutionCount(plan.getId(), patientId);
    planInfo.put("number", "第" + executionCount + "次计划");

    // 4. 计算状态
    String status = calculatePlanStatus(plan, patientId);
    planInfo.put("status", status);

    return planInfo;
}

// 获取子计划问卷列表（按子计划分组）
@PostMapping("/subplan-questionnaires")
public ResponseEntity<Map<String, Object>> getSubplanQuestionnaires(@RequestBody Map<String, Object> request) {
    Map<String, Object> result = new HashMap<>();

    try {
        String phone = (String) request.get("phone");
        String planIdStr = (String) request.get("planId");

        Long planId = Long.parseLong(planIdStr);

        // 1. 验证用户权限
        FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
        if (cUser == null || !validateUserPlanAccess(cUser.getId(), planId)) {
            result.put("code", 403);
            result.put("message", "无权限访问");
            return ResponseEntity.ok(result);
        }

        // 2. 获取计划的子计划（按执行时间排序）
        List<FollowupSubplan> subplans = followupSubplanMapper.selectList(
            new LambdaQueryWrapper<FollowupSubplan>()
                .eq(FollowupSubplan::getPlanId, planId)
                .orderByAsc(FollowupSubplan::getExecutionTime)
        );

        // 3. 构建子计划问卷数据
        List<Map<String, Object>> subplanList = new ArrayList<>();
        for (FollowupSubplan subplan : subplans) {
            Map<String, Object> subplanData = buildSubplanQuestionnaireData(subplan, cUser.getId());
            subplanList.add(subplanData);
        }

        result.put("code", 200);
        result.put("data", subplanList);
        result.put("message", "获取成功");
        return ResponseEntity.ok(result);

    } catch (Exception e) {
        logger.error("获取子计划问卷失败", e);
        result.put("code", 500);
        result.put("message", "获取失败: " + e.getMessage());
        return ResponseEntity.ok(result);
    }
}

// 构建子计划问卷数据
private Map<String, Object> buildSubplanQuestionnaireData(FollowupSubplan subplan, Long patientId) {
    Map<String, Object> subplanData = new HashMap<>();

    // 子计划基本信息
    subplanData.put("subplanId", subplan.getId());
    subplanData.put("subplanName", subplan.getName());
    subplanData.put("executionTime", subplan.getExecutionTime());
    subplanData.put("reminderTime", subplan.getReminderTime());
    subplanData.put("planType", subplan.getPlanType());

    // 获取关联的问卷
    List<FollowupSubplanQuestionnaire> relations = followupSubplanQuestionnaireMapper.selectList(
        new LambdaQueryWrapper<FollowupSubplanQuestionnaire>()
            .eq(FollowupSubplanQuestionnaire::getSubplanId, subplan.getId())
            .orderByAsc(FollowupSubplanQuestionnaire::getSortOrder)
    );

    List<Map<String, Object>> questionnaires = new ArrayList<>();
    int completedCount = 0;

    for (FollowupSubplanQuestionnaire relation : relations) {
        Questionnaire questionnaire = questionnaireMapper.selectById(relation.getQuestionnaireId());
        if (questionnaire != null) {
            Map<String, Object> questionnaireData = new HashMap<>();
            questionnaireData.put("id", questionnaire.getId());
            questionnaireData.put("name", questionnaire.getName());
            questionnaireData.put("description", questionnaire.getDescription());
            questionnaireData.put("sortOrder", relation.getSortOrder());
            questionnaireData.put("isRequired", relation.getIsRequired());

            // 检查完成状态
            boolean isCompleted = checkQuestionnaireCompleted(questionnaire.getId(), patientId, subplan.getId());
            questionnaireData.put("isCompleted", isCompleted);
            if (isCompleted) {
                completedCount++;
            }

            // 获取最后提交时间
            Date lastSubmitTime = getLastSubmitTime(questionnaire.getId(), patientId, subplan.getId());
            questionnaireData.put("lastSubmitTime", lastSubmitTime);

            questionnaires.add(questionnaireData);
        }
    }

    subplanData.put("questionnaires", questionnaires);
    subplanData.put("totalCount", questionnaires.size());
    subplanData.put("completedCount", completedCount);
    subplanData.put("completionRate", questionnaires.size() > 0 ?
                   (double) completedCount / questionnaires.size() : 0.0);

    return subplanData;
}
```

**2. 问卷提交处理**
```java
@PostMapping("/questionnaire-submit")
public ResponseEntity<Map<String, Object>> submitQuestionnaire(@RequestBody Map<String, Object> request) {
    Map<String, Object> result = new HashMap<>();

    try {
        String phone = (String) request.get("phone");
        String planIdStr = (String) request.get("planId");
        String examIdStr = (String) request.get("examId");
        String examType = (String) request.get("examType");
        List<Map<String, Object>> answers = (List<Map<String, Object>>) request.get("answers");

        Long planId = Long.parseLong(planIdStr);
        Long examId = Long.parseLong(examIdStr);

        // 1. 验证用户和计划
        FollowupCUser cUser = followupCUserService.getUserByPhone(phone);
        if (cUser == null) {
            result.put("code", 404);
            result.put("message", "用户不存在");
            return ResponseEntity.ok(result);
        }

        // 2. 获取子计划ID（从问卷关联中查找）
        String subplanIdStr = (String) request.get("subplanId");
        Long subplanId = null;
        if (subplanIdStr != null) {
            subplanId = Long.parseLong(subplanIdStr);
        } else {
            // 如果没有直接传入子计划ID，从问卷关联中查找
            FollowupSubplanQuestionnaire relation = followupSubplanQuestionnaireMapper.selectOne(
                new LambdaQueryWrapper<FollowupSubplanQuestionnaire>()
                    .eq(FollowupSubplanQuestionnaire::getQuestionnaireId, examId)
                    .last("LIMIT 1")
            );
            if (relation != null) {
                subplanId = relation.getSubplanId();
            }
        }

        // 3. 验证问卷是否属于该计划的子计划
        if (subplanId != null) {
            FollowupSubplan subplan = followupSubplanMapper.selectById(subplanId);
            if (subplan == null || !subplan.getPlanId().equals(planId)) {
                result.put("code", 400);
                result.put("message", "问卷不属于该随访计划");
                return ResponseEntity.ok(result);
            }
        }

        // 4. 处理每个答案
        Date submitTime = new Date();
        int submittedCount = 0;

        for (Map<String, Object> answerData : answers) {
            QuestionnaireAnswer answer = new QuestionnaireAnswer();
            answer.setQuestionnaireId(examId);
            answer.setQuestionId(Long.parseLong(answerData.get("qId").toString()));
            answer.setPatientId(cUser.getId());
            answer.setPatientName(cUser.getName());
            answer.setQuestionType((String) answerData.get("type"));
            answer.setAnswerText((String) answerData.get("text"));
            answer.setAnswerValue((String) answerData.get("result"));
            answer.setPlanId(planId);
            answer.setSubplanId(subplanId); // 关联子计划ID
            answer.setAnswerTime(submitTime);
            answer.setSubmissionType("manual");

            // 5. 计算得分
            BigDecimal score = calculateQuestionScore(answer);
            answer.setScore(score);

            // 6. 保存答案
            questionnaireAnswerMapper.insert(answer);
            submittedCount++;
        }

        // 5. 返回结果
        Map<String, Object> data = new HashMap<>();
        data.put("planId", planId);
        data.put("examId", examId);
        data.put("submittedCount", submittedCount);
        data.put("submitTime", submitTime);

        result.put("code", 200);
        result.put("data", data);
        result.put("message", "问卷提交成功");
        return ResponseEntity.ok(result);

    } catch (Exception e) {
        result.put("code", 500);
        result.put("message", "提交失败: " + e.getMessage());
        return ResponseEntity.ok(result);
    }
}

// 计算问题得分
private BigDecimal calculateQuestionScore(QuestionnaireAnswer answer) {
    if ("checkbox".equals(answer.getQuestionType())) {
        // 多选题：累加所有选项分数
        String[] optionIds = answer.getAnswerValue().split(",");
        BigDecimal totalScore = BigDecimal.ZERO;

        for (String optionId : optionIds) {
            QuestionnaireOption option = questionnaireOptionMapper.selectById(Long.parseLong(optionId.trim()));
            if (option != null && option.getScore() != null) {
                totalScore = totalScore.add(option.getScore());
            }
        }
        return totalScore;

    } else if ("radio".equals(answer.getQuestionType())) {
        // 单选题：获取选项分数
        QuestionnaireOption option = questionnaireOptionMapper.selectById(Long.parseLong(answer.getAnswerValue()));
        return option != null && option.getScore() != null ? option.getScore() : BigDecimal.ZERO;

    } else {
        // 文本题等不计分
        return BigDecimal.ZERO;
    }
}
```

### 10.3 数据库操作详细实现

#### 关键SQL查询

**1. 获取用户随访计划**
```sql
SELECT
    fp.id as plan_id,
    fp.name as plan_name,
    fp.description,
    fp.status,
    pg.name as group_name,
    COUNT(DISTINCT pgr.patient_id) as patient_count
FROM followup_plan fp
LEFT JOIN patient_group pg ON fp.group_id = pg.id
LEFT JOIN patient_group_relation pgr ON pg.id = pgr.group_id AND pgr.status = '1'
WHERE fp.status = 'active'
AND fp.group_id IN (
    SELECT group_id FROM patient_group_relation
    WHERE patient_id = ? AND status = '1'
)
GROUP BY fp.id
ORDER BY fp.create_time DESC
```

**2. 获取子计划问卷信息**
```sql
SELECT
    fs.id as subplan_id,
    fs.name as subplan_name,
    fs.execution_time,
    fs.reminder_time,
    fsq.sort_order as questionnaire_order,
    fsq.is_required as questionnaire_required,
    q.id as questionnaire_id,
    q.name as questionnaire_name,
    q.description,
    qq.id as question_id,
    qq.question_text,
    qq.question_type,
    qq.is_required as question_required,
    qq.sort_order as question_order,
    qo.id as option_id,
    qo.option_text,
    qo.option_value,
    qo.score
FROM followup_subplan fs
LEFT JOIN followup_subplan_questionnaire fsq ON fs.id = fsq.subplan_id
LEFT JOIN questionnaire q ON fsq.questionnaire_id = q.id
LEFT JOIN questionnaire_question qq ON q.id = qq.questionnaire_id
LEFT JOIN questionnaire_option qo ON qq.id = qo.question_id
WHERE fs.plan_id = ?
ORDER BY fs.execution_time, fsq.sort_order, qq.sort_order, qo.sort_order
```

**3. 统计计划执行情况**
```sql
SELECT
    fp.id,
    fp.name,
    COUNT(DISTINCT pgr.patient_id) as patient_count,
    COUNT(DISTINCT ft.id) as task_count,
    COUNT(DISTINCT CASE WHEN ft.status = 'completed' THEN ft.id END) as completed_count,
    COUNT(DISTINCT fs.id) as subplan_count
FROM followup_plan fp
LEFT JOIN patient_group_relation pgr ON fp.group_id = pgr.group_id AND pgr.status = '1'
LEFT JOIN followup_subplan fs ON fp.id = fs.plan_id
LEFT JOIN followup_task ft ON fs.id = ft.subplan_id
WHERE fp.id = ?
GROUP BY fp.id
```

### 10.4 任务调度系统详细实现

#### 定时任务生成机制

**1. 任务生成服务类**
```java
@Service
public class FollowupTaskGeneratorService {

    @Autowired
    private FollowupTaskMapper followupTaskMapper;

    @Autowired
    private FollowupSubplanMapper followupSubplanMapper;

    @Autowired
    private FollowupSubplanItemMapper followupSubplanItemMapper;

    /**
     * 为患者生成子计划任务
     */
    public void generateTasksForPatientSubplan(Long planId, FollowupSubplan subplan, Long patientId) {
        List<FollowupSubplanItem> items = followupSubplanItemMapper.selectList(
            new LambdaQueryWrapper<FollowupSubplanItem>()
                .eq(FollowupSubplanItem::getSubplanId, subplan.getId())
        );

        for (FollowupSubplanItem item : items) {
            if ("cycle".equals(subplan.getPlanType())) {
                // 周期循环模式
                generateCycleTasks(planId, subplan, item, patientId);
            } else if ("custom".equals(subplan.getPlanType())) {
                // 自定义模式
                generateCustomTasks(planId, subplan, item, patientId);
            }
        }
    }

    /**
     * 生成周期循环任务
     */
    private void generateCycleTasks(Long planId, FollowupSubplan subplan,
                                   FollowupSubplanItem item, Long patientId) {
        // 1. 获取患者入组时间
        Date joinTime = getPatientJoinTime(planId, patientId);

        // 2. 获取随访计划结束时间
        FollowupPlan plan = followupPlanMapper.selectById(planId);
        Date planEndTime = calculatePlanEndTime(plan, joinTime);

        // 3. 根据周期天数生成任务
        Integer cycleDays = item.getCycleDays();
        if (cycleDays != null && cycleDays > 0) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(joinTime);

            // 从入组当天开始，按周期循环生成任务
            int cycleCount = 0;
            while (true) {
                // 计算当前周期的执行日期
                Calendar taskCalendar = Calendar.getInstance();
                taskCalendar.setTime(joinTime);
                taskCalendar.add(Calendar.DAY_OF_MONTH, cycleDays * cycleCount);

                // 检查是否超过计划结束时间
                if (taskCalendar.getTime().after(planEndTime)) {
                    break;
                }

                // 结合提醒时间计算具体执行时刻
                Date scheduledTime = combineDateTime(taskCalendar.getTime(), subplan.getReminderTime());

                // 创建任务
                createTask(planId, subplan.getId(), item.getId(), patientId, scheduledTime, item.getContentTemplateId());

                cycleCount++;

                // 防止无限循环，最多生成100个任务
                if (cycleCount > 100) {
                    logger.warn("周期任务生成数量超过限制，计划ID: {}, 患者ID: {}", planId, patientId);
                    break;
                }
            }

            logger.info("周期循环任务生成完成: 计划ID={}, 患者ID={}, 周期天数={}, 生成任务数={}",
                       planId, patientId, cycleDays, cycleCount);
        }
    }

    /**
     * 生成自定义任务
     */
    private void generateCustomTasks(Long planId, FollowupSubplan subplan,
                                    FollowupSubplanItem item, Long patientId) {
        // 1. 获取患者入组时间
        Date joinTime = getPatientJoinTime(planId, patientId);

        // 2. 获取随访计划结束时间
        FollowupPlan plan = followupPlanMapper.selectById(planId);
        Date planEndTime = calculatePlanEndTime(plan, joinTime);

        // 3. 计算任务执行时间（入组时间 + 指定天数）
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(joinTime);
        calendar.add(Calendar.DAY_OF_MONTH, item.getJoinDay());

        // 4. 检查是否在计划有效期内
        if (calendar.getTime().after(planEndTime)) {
            logger.warn("自定义任务超出计划结束时间，跳过创建: 计划ID={}, 患者ID={}, 执行天数={}",
                       planId, patientId, item.getJoinDay());
            return;
        }

        // 5. 结合提醒时间计算具体执行时刻
        Date scheduledTime = combineDateTime(calendar.getTime(), subplan.getReminderTime());

        // 6. 创建任务
        createTask(planId, subplan.getId(), item.getId(), patientId, scheduledTime, item.getContentTemplateId());

        logger.info("自定义任务创建成功: 计划ID={}, 患者ID={}, 执行天数={}, 执行时间={}",
                   planId, patientId, item.getJoinDay(), scheduledTime);
    }

    /**
     * 创建任务记录
     */
    private void createTask(Long planId, Long subplanId, Long itemId, Long patientId,
                           Date scheduledTime, Long contentTemplateId) {
        // 1. 获取患者信息
        FollowupCUser patient = followupCUserMapper.selectById(patientId);
        if (patient == null) {
            logger.error("患者不存在，无法创建任务: 患者ID={}", patientId);
            return;
        }

        // 2. 创建任务记录
        FollowupTask task = new FollowupTask();
        task.setPlanId(planId);
        task.setSubplanId(subplanId);
        task.setSubplanItemId(itemId);
        task.setPatientId(patientId);
        task.setPatientName(patient.getName());
        task.setScheduledTime(scheduledTime);
        task.setStatus("pending");
        task.setMessageSent(false);
        task.setCreateTime(new Date());

        // 3. 关联内容模板（用于后续消息推送）
        if (contentTemplateId != null) {
            task.setContentTemplateId(contentTemplateId);
        }

        followupTaskMapper.insert(task);

        logger.debug("任务创建成功: 任务ID={}, 计划ID={}, 患者={}, 执行时间={}",
                    task.getId(), planId, patient.getName(), scheduledTime);
    }

    /**
     * 计算计划结束时间
     */
    private Date calculatePlanEndTime(FollowupPlan plan, Date joinTime) {
        if ("unlimited".equals(plan.getEndTimeType())) {
            // 无限期计划，默认1年
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(joinTime);
            calendar.add(Calendar.YEAR, 1);
            return calendar.getTime();
        } else if ("days".equals(plan.getEndTimeType()) && plan.getEndTimeValue() != null) {
            // 按天数计算
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(joinTime);
            calendar.add(Calendar.DAY_OF_MONTH, plan.getEndTimeValue());
            return calendar.getTime();
        } else if ("date".equals(plan.getEndTimeType()) && plan.getEndDate() != null) {
            // 固定结束日期
            return plan.getEndDate();
        }

        // 默认30天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(joinTime);
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        return calendar.getTime();
    }

    /**
     * 合并日期和时间
     */
    private Date combineDateTime(Date date, Date time) {
        if (time == null) {
            return date;
        }

        Calendar dateCalendar = Calendar.getInstance();
        dateCalendar.setTime(date);

        Calendar timeCalendar = Calendar.getInstance();
        timeCalendar.setTime(time);

        dateCalendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
        dateCalendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
        dateCalendar.set(Calendar.SECOND, timeCalendar.get(Calendar.SECOND));

        return dateCalendar.getTime();
    }
}
```

**2. 定时任务执行器**
```java
@Component
public class FollowupTaskScheduler {

    @Autowired
    private FollowupTaskMapper followupTaskMapper;

    @Autowired
    private MessageService messageService;

    /**
     * 每分钟检查待执行的任务
     */
    @Scheduled(cron = "0 * * * * ?")
    public void checkPendingTasks() {
        Date now = new Date();

        // 查询需要执行的任务
        List<FollowupTask> pendingTasks = followupTaskMapper.selectList(
            new LambdaQueryWrapper<FollowupTask>()
                .eq(FollowupTask::getStatus, "pending")
                .le(FollowupTask::getScheduledTime, now)
                .eq(FollowupTask::getMessageSent, false)
        );

        for (FollowupTask task : pendingTasks) {
            try {
                executeTask(task);
            } catch (Exception e) {
                // 标记任务失败
                task.setStatus("failed");
                task.setUpdateTime(new Date());
                followupTaskMapper.updateById(task);
            }
        }
    }

    /**
     * 执行具体任务
     */
    private void executeTask(FollowupTask task) {
        try {
            // 1. 获取任务相关信息
            FollowupSubplanItem item = followupSubplanItemMapper.selectById(task.getSubplanItemId());
            FollowupSubplan subplan = followupSubplanMapper.selectById(task.getSubplanId());

            if (item == null || subplan == null) {
                logger.error("任务相关数据不存在，任务ID: {}", task.getId());
                task.setStatus("failed");
                task.setUpdateTime(new Date());
                followupTaskMapper.updateById(task);
                return;
            }

            // 2. 发送内容模板消息（如果有配置）
            if (item.getContentTemplateId() != null) {
                sendContentTemplateMessage(task, item);
            }

            // 3. 发送子计划关联的问卷提醒
            sendSubplanQuestionnaireReminder(task, subplan);

            // 4. 更新任务状态
            task.setStatus("executing");
            task.setMessageSent(true);
            task.setMessageSentTime(new Date());
            task.setUpdateTime(new Date());
            followupTaskMapper.updateById(task);

            logger.info("任务执行成功: 任务ID={}, 患者={}, 子计划={}",
                       task.getId(), task.getPatientName(), subplan.getName());

        } catch (Exception e) {
            logger.error("任务执行失败: 任务ID=" + task.getId(), e);
            task.setStatus("failed");
            task.setUpdateTime(new Date());
            followupTaskMapper.updateById(task);
        }
    }

    /**
     * 发送内容模板消息
     */
    private void sendContentTemplateMessage(FollowupTask task, FollowupSubplanItem item) {
        try {
            ContentTemplate template = contentTemplateMapper.selectById(item.getContentTemplateId());
            if (template != null) {
                // 个性化处理模板内容（可以替换患者姓名等变量）
                String personalizedContent = personalizeTemplateContent(template.getContent(), task.getPatientName());

                // 发送消息给患者
                messageService.sendMessage(
                    task.getPatientId(),
                    template.getTitle(),
                    personalizedContent,
                    "followup_content"
                );

                logger.info("内容模板消息发送成功: 患者={}, 模板={}", task.getPatientName(), template.getTitle());
            }
        } catch (Exception e) {
            logger.error("发送内容模板消息失败: 任务ID=" + task.getId(), e);
        }
    }

    /**
     * 发送子计划问卷提醒
     */
    private void sendSubplanQuestionnaireReminder(FollowupTask task, FollowupSubplan subplan) {
        try {
            // 1. 获取子计划关联的问卷
            List<FollowupSubplanQuestionnaire> questionnaireRelations = followupSubplanQuestionnaireMapper.selectList(
                new LambdaQueryWrapper<FollowupSubplanQuestionnaire>()
                    .eq(FollowupSubplanQuestionnaire::getSubplanId, task.getSubplanId())
                    .orderByAsc(FollowupSubplanQuestionnaire::getSortOrder)
            );

            if (questionnaireRelations.isEmpty()) {
                logger.info("子计划无关联问卷，跳过问卷提醒: 子计划ID={}", task.getSubplanId());
                return;
            }

            // 2. 构建问卷提醒消息
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("您好，").append(task.getPatientName()).append("！\n\n");
            messageBuilder.append("您有新的随访问卷需要填写：\n");

            List<String> questionnaireNames = new ArrayList<>();
            for (FollowupSubplanQuestionnaire relation : questionnaireRelations) {
                Questionnaire questionnaire = questionnaireMapper.selectById(relation.getQuestionnaireId());
                if (questionnaire != null) {
                    questionnaireNames.add(questionnaire.getName());
                    messageBuilder.append("• ").append(questionnaire.getName());
                    if (relation.getIsRequired()) {
                        messageBuilder.append("（必填）");
                    }
                    messageBuilder.append("\n");
                }
            }

            messageBuilder.append("\n请及时完成填写，谢谢配合！");

            // 3. 发送问卷提醒消息
            messageService.sendMessage(
                task.getPatientId(),
                "随访问卷提醒 - " + subplan.getName(),
                messageBuilder.toString(),
                "followup_questionnaire"
            );

            logger.info("问卷提醒发送成功: 患者={}, 子计划={}, 问卷数量={}",
                       task.getPatientName(), subplan.getName(), questionnaireNames.size());

        } catch (Exception e) {
            logger.error("发送问卷提醒失败: 任务ID=" + task.getId(), e);
        }
    }

    /**
     * 个性化模板内容
     */
    private String personalizeTemplateContent(String content, String patientName) {
        if (content == null) {
            return "";
        }

        // 替换常用变量
        return content
            .replace("{{patientName}}", patientName != null ? patientName : "")
            .replace("{{currentDate}}", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()))
            .replace("{{currentTime}}", new SimpleDateFormat("HH:mm").format(new Date()));
    }
}
```

### 10.5 前端状态管理详细实现

#### Vuex Store 结构

**1. 随访计划状态管理**
```javascript
// store/modules/followup.js
const followupStore = {
    namespaced: true,
    state: {
        // 计划列表
        planList: [],
        currentPlan: null,
        planLoading: false,

        // 分页信息
        pagination: {
            current: 1,
            size: 15,
            total: 0
        },

        // 搜索条件
        searchParams: {
            name: '',
            category: '',
            status: ''
        },

        // 子计划数据
        subplans: [],
        currentSubplan: null,

        // 问卷数据
        questionnaires: [],
        currentQuestionnaire: null
    },

    mutations: {
        SET_PLAN_LIST(state, planList) {
            state.planList = planList;
        },

        SET_CURRENT_PLAN(state, plan) {
            state.currentPlan = plan;
        },

        SET_PLAN_LOADING(state, loading) {
            state.planLoading = loading;
        },

        SET_PAGINATION(state, pagination) {
            state.pagination = { ...state.pagination, ...pagination };
        },

        SET_SEARCH_PARAMS(state, params) {
            state.searchParams = { ...state.searchParams, ...params };
        },

        ADD_SUBPLAN(state, subplan) {
            state.subplans.push(subplan);
        },

        UPDATE_SUBPLAN(state, { index, subplan }) {
            state.subplans.splice(index, 1, subplan);
        },

        REMOVE_SUBPLAN(state, index) {
            state.subplans.splice(index, 1);
        }
    },

    actions: {
        // 获取计划列表
        async fetchPlanList({ commit, state }) {
            commit('SET_PLAN_LOADING', true);
            try {
                const response = await getFollowupPlanPage({
                    current: state.pagination.current,
                    size: state.pagination.size,
                    ...state.searchParams
                });

                if (response.data.code === 200) {
                    commit('SET_PLAN_LIST', response.data.data.records);
                    commit('SET_PAGINATION', {
                        total: response.data.data.total,
                        current: response.data.data.current
                    });
                }
            } catch (error) {
                console.error('获取计划列表失败:', error);
            } finally {
                commit('SET_PLAN_LOADING', false);
            }
        },

        // 创建计划
        async createPlan({ dispatch }, planData) {
            try {
                const response = await saveFollowupPlan(planData);
                if (response.data.code === 200) {
                    // 重新获取列表
                    await dispatch('fetchPlanList');
                    return { success: true, data: response.data.data };
                } else {
                    return { success: false, message: response.data.message };
                }
            } catch (error) {
                return { success: false, message: '创建失败' };
            }
        },

        // 更新计划状态
        async updatePlanStatus({ dispatch }, { id, status }) {
            try {
                const response = await updatePlanStatus(id, status);
                if (response.data.code === 200) {
                    await dispatch('fetchPlanList');
                    return { success: true };
                } else {
                    return { success: false, message: response.data.message };
                }
            } catch (error) {
                return { success: false, message: '状态更新失败' };
            }
        }
    },

    getters: {
        // 获取当前页计划列表
        currentPagePlans: state => state.planList,

        // 获取计划总数
        totalPlans: state => state.pagination.total,

        // 获取活跃计划数量
        activePlansCount: state => {
            return state.planList.filter(plan => plan.status === 'active').length;
        },

        // 根据分类筛选计划
        plansByCategory: state => category => {
            if (!category) return state.planList;
            return state.planList.filter(plan => plan.category === category);
        }
    }
};

export default followupStore;
```

**2. 组件中的状态使用**
```javascript
// followUpPlan.vue 中的状态管理
export default {
    computed: {
        ...mapState('followup', ['planList', 'planLoading', 'pagination', 'searchParams']),
        ...mapGetters('followup', ['currentPagePlans', 'totalPlans', 'activePlansCount'])
    },

    methods: {
        ...mapActions('followup', ['fetchPlanList', 'createPlan', 'updatePlanStatus']),

        // 搜索计划
        async searchPlans() {
            await this.$store.commit('followup/SET_SEARCH_PARAMS', {
                name: this.searchForm.name,
                category: this.searchForm.category,
                status: this.searchForm.status
            });
            await this.fetchPlanList();
        },

        // 重置搜索
        async resetSearch() {
            this.searchForm = {
                name: '',
                category: '',
                status: ''
            };
            await this.$store.commit('followup/SET_SEARCH_PARAMS', this.searchForm);
            await this.fetchPlanList();
        },

        // 处理分页变化
        async handleCurrentChange(page) {
            await this.$store.commit('followup/SET_PAGINATION', { current: page });
            await this.fetchPlanList();
        },

        // 处理页面大小变化
        async handleSizeChange(size) {
            await this.$store.commit('followup/SET_PAGINATION', { size, current: 1 });
            await this.fetchPlanList();
        }
    },

    async mounted() {
        // 初始化加载数据
        await this.fetchPlanList();
    }
};
```

### 10.6 错误处理和日志记录

#### 前端错误处理

**1. API请求错误处理**
```javascript
// api/followupPlan.js 中的错误处理
api.interceptors.response.use(
    response => {
        // 成功响应处理
        if (response.data.code !== 200) {
            // 业务错误处理
            Message.error(response.data.message || '操作失败');
            return Promise.reject(new Error(response.data.message));
        }
        return response;
    },
    error => {
        // 网络错误处理
        let message = '网络错误';
        if (error.response) {
            switch (error.response.status) {
                case 400:
                    message = '请求参数错误';
                    break;
                case 401:
                    message = '未授权访问';
                    break;
                case 403:
                    message = '禁止访问';
                    break;
                case 404:
                    message = '资源不存在';
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                default:
                    message = `错误代码: ${error.response.status}`;
            }
        } else if (error.request) {
            message = '网络连接失败';
        }

        Message.error(message);
        console.error('API请求错误:', error);
        return Promise.reject(error);
    }
);
```

**2. 组件级错误处理**
```javascript
// 组件中的错误处理
export default {
    methods: {
        async handleCardOperation(command, plan) {
            try {
                this.loading = true;

                switch (command) {
                    case 'edit':
                        await this.editPlan(plan);
                        break;
                    case 'pause':
                        await this.pausePlan(plan);
                        break;
                    case 'activate':
                        await this.activatePlan(plan);
                        break;
                    case 'delete':
                        await this.deletePlan(plan);
                        break;
                }

                this.$message.success('操作成功');
                await this.fetchPlanList();

            } catch (error) {
                this.$message.error(error.message || '操作失败');
                console.error('操作错误:', error);
            } finally {
                this.loading = false;
            }
        }
    },

    // 全局错误处理
    errorCaptured(err, instance, info) {
        console.error('组件错误:', err, info);
        this.$message.error('页面出现错误，请刷新重试');
        return false;
    }
};
```

#### 后端错误处理和日志

**1. 全局异常处理器**
```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<Object> handleBusinessException(BusinessException e) {
        logger.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getMessage());
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Object> handleValidationException(MethodArgumentNotValidException e) {
        StringBuilder message = new StringBuilder();
        e.getBindingResult().getFieldErrors().forEach(error -> {
            message.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ");
        });

        logger.warn("参数验证失败: {}", message.toString());
        return Result.error("参数验证失败: " + message.toString());
    }

    /**
     * 处理数据库异常
     */
    @ExceptionHandler(DataAccessException.class)
    public Result<Object> handleDataAccessException(DataAccessException e) {
        logger.error("数据库操作异常", e);
        return Result.error("数据操作失败，请稍后重试");
    }

    /**
     * 处理未知异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Object> handleException(Exception e) {
        logger.error("系统异常", e);
        return Result.error("系统异常，请联系管理员");
    }
}
```

**2. 业务日志记录**
```java
@Service
public class FollowupPlanServiceImpl implements FollowupPlanService {

    private static final Logger logger = LoggerFactory.getLogger(FollowupPlanServiceImpl.class);

    @Override
    public FollowupPlan createPlan(FollowupPlanDTO planDTO) {
        logger.info("开始创建随访计划: {}", planDTO.getName());

        try {
            // 业务逻辑处理
            FollowupPlan plan = new FollowupPlan();
            BeanUtils.copyProperties(planDTO, plan);

            // 保存计划
            followupPlanMapper.insert(plan);
            logger.info("随访计划创建成功, ID: {}, 名称: {}", plan.getId(), plan.getName());

            // 创建子计划
            if (planDTO.getSubPlans() != null) {
                for (FollowupSubplanDTO subplanDTO : planDTO.getSubPlans()) {
                    createSubplan(plan.getId(), subplanDTO);
                }
                logger.info("子计划创建完成, 计划ID: {}, 子计划数量: {}", plan.getId(), planDTO.getSubPlans().size());
            }

            return plan;

        } catch (Exception e) {
            logger.error("创建随访计划失败: {}", planDTO.getName(), e);
            throw new BusinessException("创建随访计划失败: " + e.getMessage());
        }
    }

    @Override
    public void updatePlanStatus(Long id, String status) {
        logger.info("更新计划状态: ID={}, 新状态={}", id, status);

        try {
            FollowupPlan plan = followupPlanMapper.selectById(id);
            if (plan == null) {
                throw new BusinessException("随访计划不存在");
            }

            String oldStatus = plan.getStatus();
            plan.setStatus(status);
            plan.setUpdateTime(new Date());

            followupPlanMapper.updateById(plan);
            logger.info("计划状态更新成功: ID={}, 状态: {} -> {}", id, oldStatus, status);

            // 如果启用计划，生成任务
            if ("active".equals(status)) {
                generateTasksForPlan(id);
                logger.info("计划启用，任务生成完成: ID={}", id);
            }

        } catch (Exception e) {
            logger.error("更新计划状态失败: ID={}, 状态={}", id, status, e);
            throw new BusinessException("更新计划状态失败: " + e.getMessage());
        }
    }
}
```

### 10.7 性能优化实现

#### 前端性能优化

**1. 组件懒加载**
```javascript
// router/nav.js 中的懒加载配置
const menuItems = [
    {
        path: '/followUp/plan',
        name: 'followUpPlan',
        meta: { title: '随访计划' },
        component: () => import(/* webpackChunkName: "followup" */ '@/components/followUp/followUpPlan.vue')
    },
    {
        path: '/followUp/smart-plan',
        name: 'smartFollowUpPlan',
        meta: { title: '智能随访计划' },
        component: () => import(/* webpackChunkName: "followup" */ '@/components/followUp/children/SmartFollowUp.vue')
    }
];
```

**2. 数据缓存策略**
```javascript
// utils/cache.js
class CacheManager {
    constructor() {
        this.cache = new Map();
        this.expireTime = 5 * 60 * 1000; // 5分钟过期
    }

    set(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;

        // 检查是否过期
        if (Date.now() - item.timestamp > this.expireTime) {
            this.cache.delete(key);
            return null;
        }

        return item.data;
    }

    clear() {
        this.cache.clear();
    }
}

export const cacheManager = new CacheManager();

// 在API调用中使用缓存
export async function getFollowupPlanPage(params) {
    const cacheKey = `plan_page_${JSON.stringify(params)}`;
    const cachedData = cacheManager.get(cacheKey);

    if (cachedData) {
        return { data: cachedData };
    }

    const response = await api.get('/emr/followup/plan/page', { params });
    cacheManager.set(cacheKey, response.data);

    return response;
}
```

**3. 虚拟滚动优化**
```javascript
// components/VirtualList.vue - 大数据量列表优化
<template>
    <div class="virtual-list" @scroll="handleScroll" ref="container">
        <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
        <div class="virtual-list-content" :style="{ transform: `translateY(${startOffset}px)` }">
            <div
                v-for="item in visibleData"
                :key="item.id"
                class="virtual-list-item"
                :style="{ height: itemHeight + 'px' }"
            >
                <slot :item="item"></slot>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        listData: Array,
        itemHeight: { type: Number, default: 60 },
        containerHeight: { type: Number, default: 400 }
    },

    data() {
        return {
            scrollTop: 0,
            visibleCount: Math.ceil(this.containerHeight / this.itemHeight)
        };
    },

    computed: {
        totalHeight() {
            return this.listData.length * this.itemHeight;
        },

        startIndex() {
            return Math.floor(this.scrollTop / this.itemHeight);
        },

        endIndex() {
            return Math.min(this.startIndex + this.visibleCount, this.listData.length - 1);
        },

        visibleData() {
            return this.listData.slice(this.startIndex, this.endIndex + 1);
        },

        startOffset() {
            return this.startIndex * this.itemHeight;
        }
    },

    methods: {
        handleScroll() {
            this.scrollTop = this.$refs.container.scrollTop;
        }
    }
};
</script>
```

#### 后端性能优化

**1. 数据库查询优化**
```java
@Service
public class FollowupPlanServiceImpl implements FollowupPlanService {

    /**
     * 优化的分页查询 - 使用索引和避免N+1问题
     */
    @Override
    public IPage<FollowupPlanDTO> getFollowupPlanPage(Integer current, Integer size,
                                                      String category, String status, String name) {
        Page<FollowupPlan> page = new Page<>(current, size);

        // 构建查询条件，利用索引
        LambdaQueryWrapper<FollowupPlan> wrapper = new LambdaQueryWrapper<FollowupPlan>()
            .eq(StringUtils.isNotBlank(category), FollowupPlan::getCategory, category)
            .eq(StringUtils.isNotBlank(status), FollowupPlan::getStatus, status)
            .like(StringUtils.isNotBlank(name), FollowupPlan::getName, name)
            .orderByDesc(FollowupPlan::getCreateTime);

        // 执行分页查询
        IPage<FollowupPlan> planPage = followupPlanMapper.selectPage(page, wrapper);

        // 批量查询关联数据，避免N+1问题
        List<Long> planIds = planPage.getRecords().stream()
            .map(FollowupPlan::getId)
            .collect(Collectors.toList());

        if (!planIds.isEmpty()) {
            // 批量查询统计信息
            Map<Long, PlanStatistics> statisticsMap = batchGetPlanStatistics(planIds);

            // 批量查询分组信息
            Map<Long, PatientGroup> groupMap = batchGetPatientGroups(
                planPage.getRecords().stream()
                    .map(FollowupPlan::getGroupId)
                    .collect(Collectors.toList())
            );

            // 转换为DTO
            List<FollowupPlanDTO> dtoList = planPage.getRecords().stream()
                .map(plan -> convertToDTO(plan, statisticsMap.get(plan.getId()),
                                        groupMap.get(plan.getGroupId())))
                .collect(Collectors.toList());

            // 构建返回结果
            Page<FollowupPlanDTO> resultPage = new Page<>(current, size, planPage.getTotal());
            resultPage.setRecords(dtoList);
            return resultPage;
        }

        return new Page<>(current, size, 0);
    }

    /**
     * 批量获取计划统计信息
     */
    private Map<Long, PlanStatistics> batchGetPlanStatistics(List<Long> planIds) {
        // 使用一条SQL查询所有计划的统计信息
        String sql = """
            SELECT
                fp.id as plan_id,
                COUNT(DISTINCT pgr.patient_id) as patient_count,
                COUNT(DISTINCT ft.id) as task_count,
                COUNT(DISTINCT CASE WHEN ft.status = 'completed' THEN ft.id END) as completed_count,
                COUNT(DISTINCT fs.id) as subplan_count
            FROM followup_plan fp
            LEFT JOIN patient_group_relation pgr ON fp.group_id = pgr.group_id AND pgr.status = '1'
            LEFT JOIN followup_subplan fs ON fp.id = fs.plan_id
            LEFT JOIN followup_task ft ON fs.id = ft.subplan_id
            WHERE fp.id IN (%s)
            GROUP BY fp.id
            """.formatted(planIds.stream().map(String::valueOf).collect(Collectors.joining(",")));

        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);

        return results.stream().collect(Collectors.toMap(
            row -> ((Number) row.get("plan_id")).longValue(),
            row -> new PlanStatistics(
                ((Number) row.get("patient_count")).intValue(),
                ((Number) row.get("task_count")).intValue(),
                ((Number) row.get("completed_count")).intValue(),
                ((Number) row.get("subplan_count")).intValue()
            )
        ));
    }
}
```

**2. Redis缓存实现**
```java
@Service
public class FollowupCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String PLAN_CACHE_PREFIX = "followup:plan:";
    private static final String USER_CACHE_PREFIX = "followup:user:";
    private static final int CACHE_EXPIRE_HOURS = 2;

    /**
     * 缓存随访计划
     */
    public void cachePlan(FollowupPlanDTO plan) {
        String key = PLAN_CACHE_PREFIX + plan.getId();
        redisTemplate.opsForValue().set(key, plan, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
    }

    /**
     * 获取缓存的随访计划
     */
    public FollowupPlanDTO getCachedPlan(Long planId) {
        String key = PLAN_CACHE_PREFIX + planId;
        return (FollowupPlanDTO) redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除计划缓存
     */
    public void evictPlanCache(Long planId) {
        String key = PLAN_CACHE_PREFIX + planId;
        redisTemplate.delete(key);
    }

    /**
     * 缓存用户随访计划列表
     */
    public void cacheUserPlans(String phone, List<Map<String, Object>> plans) {
        String key = USER_CACHE_PREFIX + phone + ":plans";
        redisTemplate.opsForValue().set(key, plans, 30, TimeUnit.MINUTES);
    }

    /**
     * 获取用户缓存的随访计划列表
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getCachedUserPlans(String phone) {
        String key = USER_CACHE_PREFIX + phone + ":plans";
        return (List<Map<String, Object>>) redisTemplate.opsForValue().get(key);
    }
}
```

### 10.8 安全性实现

#### 数据验证和防护

**1. 输入验证**
```java
// DTO中的验证注解
public class FollowupPlanDTO {

    @NotBlank(message = "计划名称不能为空")
    @Length(max = 100, message = "计划名称长度不能超过100字符")
    private String name;

    @Length(max = 500, message = "计划描述长度不能超过500字符")
    private String description;

    @NotBlank(message = "计划分类不能为空")
    @Pattern(regexp = "^(postoperative|chronic_disease|health_assessment|rehabilitation)$",
             message = "无效的计划分类")
    private String category;

    @NotBlank(message = "计划状态不能为空")
    @Pattern(regexp = "^(draft|active|paused|completed)$", message = "无效的计划状态")
    private String status;

    @NotNull(message = "分组ID不能为空")
    @Min(value = 1, message = "分组ID必须大于0")
    private Long groupId;

    @Valid
    private List<FollowupSubplanDTO> subPlans;
}

// 控制器中的验证
@PostMapping
public Result<Object> createPlan(@Valid @RequestBody FollowupPlanDTO planDTO,
                                BindingResult bindingResult) {
    if (bindingResult.hasErrors()) {
        StringBuilder errors = new StringBuilder();
        bindingResult.getFieldErrors().forEach(error ->
            errors.append(error.getField()).append(": ").append(error.getDefaultMessage()).append("; ")
        );
        return Result.error("参数验证失败: " + errors.toString());
    }

    // 业务逻辑处理
    return followupPlanService.createPlan(planDTO);
}
```

**2. SQL注入防护**
```java
// 使用MyBatis Plus的安全查询
@Override
public List<FollowupPlan> searchPlans(String keyword) {
    // 安全的模糊查询，自动处理SQL注入
    LambdaQueryWrapper<FollowupPlan> wrapper = new LambdaQueryWrapper<FollowupPlan>()
        .like(StringUtils.isNotBlank(keyword), FollowupPlan::getName, keyword)
        .or()
        .like(StringUtils.isNotBlank(keyword), FollowupPlan::getDescription, keyword);

    return followupPlanMapper.selectList(wrapper);
}

// 对于复杂查询，使用参数化查询
@Select("SELECT * FROM followup_plan WHERE name LIKE CONCAT('%', #{keyword}, '%') " +
        "AND status = #{status} ORDER BY create_time DESC")
List<FollowupPlan> searchPlansByKeywordAndStatus(@Param("keyword") String keyword,
                                                 @Param("status") String status);
```

**3. 权限控制**
```java
@RestController
@RequestMapping("/emr/followup/plan")
public class FollowupPlanController {

    @Autowired
    private PermissionService permissionService;

    /**
     * 创建随访计划 - 需要管理员权限
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasAuthority('FOLLOWUP_PLAN_CREATE')")
    public Result<Object> createPlan(@RequestBody FollowupPlanDTO planDTO,
                                    HttpServletRequest request) {
        // 获取当前用户信息
        Long userId = getCurrentUserId(request);
        String userName = getCurrentUserName(request);

        // 设置创建者信息
        planDTO.setCreatorId(userId);
        planDTO.setCreatorName(userName);

        return followupPlanService.createPlan(planDTO);
    }

    /**
     * 删除随访计划 - 只能删除自己创建的计划
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Object> deletePlan(@PathVariable Long id, HttpServletRequest request) {
        Long userId = getCurrentUserId(request);

        // 检查权限
        FollowupPlan plan = followupPlanService.getById(id);
        if (plan == null) {
            return Result.error("随访计划不存在");
        }

        if (!plan.getCreatorId().equals(userId) && !isAdmin(request)) {
            return Result.error("无权限删除此计划");
        }

        return followupPlanService.deletePlan(id);
    }

    private Long getCurrentUserId(HttpServletRequest request) {
        String userId = request.getHeader("X-User-Id");
        return userId != null ? Long.parseLong(userId) : null;
    }

    private String getCurrentUserName(HttpServletRequest request) {
        String userName = request.getHeader("X-User-Name");
        try {
            return userName != null ? URLDecoder.decode(userName, "UTF-8") : null;
        } catch (UnsupportedEncodingException e) {
            return userName;
        }
    }

    private boolean isAdmin(HttpServletRequest request) {
        String roles = request.getHeader("X-User-Roles");
        return roles != null && roles.contains("ADMIN");
    }
}
```

### 10.9 监控和运维

#### 系统监控指标

**1. 业务监控**
```java
@Component
public class FollowupMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter planCreateCounter;
    private final Counter taskExecuteCounter;
    private final Timer planQueryTimer;

    public FollowupMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.planCreateCounter = Counter.builder("followup.plan.created")
            .description("随访计划创建数量")
            .register(meterRegistry);
        this.taskExecuteCounter = Counter.builder("followup.task.executed")
            .description("随访任务执行数量")
            .register(meterRegistry);
        this.planQueryTimer = Timer.builder("followup.plan.query")
            .description("随访计划查询耗时")
            .register(meterRegistry);
    }

    public void incrementPlanCreated() {
        planCreateCounter.increment();
    }

    public void incrementTaskExecuted() {
        taskExecuteCounter.increment();
    }

    public Timer.Sample startPlanQueryTimer() {
        return Timer.start(meterRegistry);
    }
}

// 在服务中使用监控
@Service
public class FollowupPlanServiceImpl implements FollowupPlanService {

    @Autowired
    private FollowupMetrics followupMetrics;

    @Override
    public FollowupPlan createPlan(FollowupPlanDTO planDTO) {
        try {
            FollowupPlan plan = doCreatePlan(planDTO);
            followupMetrics.incrementPlanCreated();
            return plan;
        } catch (Exception e) {
            // 记录错误指标
            meterRegistry.counter("followup.plan.create.error").increment();
            throw e;
        }
    }

    @Override
    public IPage<FollowupPlanDTO> getFollowupPlanPage(Integer current, Integer size,
                                                      String category, String status, String name) {
        Timer.Sample sample = followupMetrics.startPlanQueryTimer();
        try {
            return doGetFollowupPlanPage(current, size, category, status, name);
        } finally {
            sample.stop(followupMetrics.planQueryTimer);
        }
    }
}
```

**2. 健康检查**
```java
@Component
public class FollowupHealthIndicator implements HealthIndicator {

    @Autowired
    private FollowupPlanMapper followupPlanMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Health health() {
        try {
            // 检查数据库连接
            int planCount = followupPlanMapper.selectCount(null);

            // 检查Redis连接
            redisTemplate.opsForValue().get("health_check");

            return Health.up()
                .withDetail("database", "UP")
                .withDetail("redis", "UP")
                .withDetail("planCount", planCount)
                .build();

        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

## 十一、总结

本文档详细分析了随访系统的完整技术实现，包括：

1. **前端实现**: Vue.js组件、状态管理、API调用、错误处理
2. **后端实现**: Spring Boot控制器、服务层、数据访问层、任务调度
3. **数据库设计**: 表结构关系、索引优化、查询优化
4. **性能优化**: 缓存策略、批量查询、虚拟滚动
5. **安全防护**: 输入验证、权限控制、SQL注入防护
6. **监控运维**: 业务指标、健康检查、日志记录

该随访系统具有完整的功能闭环，从管理端的计划创建到C端的任务执行，涵盖了医疗随访的全流程管理需求。系统架构清晰，代码规范，具有良好的可维护性和扩展性。

## 十二、子计划问卷关联核心业务逻辑总结

### 12.1 表结构关系修正说明

**正确的关系结构**：
```
随访计划 (followup_plan) 1:1 患者分组 (patient_group)
    ↓ 1:N
随访子计划 (followup_subplan)
    ↓ N:N (通过followup_subplan_questionnaire表)
问卷模板 (questionnaire)
```

**关键理解**：
- 一个随访计划包含多个子计划
- 一个子计划可以关联多个问卷
- 一个问卷可以被多个子计划使用
- 通过`followup_subplan_questionnaire`表建立N:N关系

### 12.2 业务价值和设计合理性

**1. 分阶段管理**
- 术后第1周：疼痛评估问卷 + 基础康复指导
- 术后第2周：康复进度问卷 + 运动能力评估
- 术后第4周：综合评估问卷 + 生活质量调查

**2. 时间节点差异化**
- 每个子计划有独立的执行时间（execution_time）
- 每个子计划有独立的提醒时间（reminder_time）
- 系统根据子计划时间自动推送相应问卷

**3. 内容针对性**
- 不同康复阶段需要不同的评估工具
- 医生可根据患者情况灵活配置问卷组合
- 支持必填和选填问卷的区分

**4. 数据追踪完整性**
- 问卷答案关联到具体的子计划（subplan_id）
- 可以追踪患者在不同阶段的状态变化
- 支持按子计划维度进行数据分析

### 12.3 技术实现要点

**1. 前端实现**
- 子计划编辑时支持多选问卷
- 问卷按子计划分组显示
- 支持拖拽排序和必填设置

**2. 后端实现**
- 创建子计划时批量插入问卷关联
- 查询时按子计划分组返回问卷列表
- 提交答案时验证问卷归属关系

**3. 数据库设计**
- `followup_subplan_questionnaire`表包含排序和必填字段
- 建立适当的索引优化查询性能
- 外键约束保证数据一致性

### 12.4 扩展性考虑

**1. 支持条件触发**
- 可根据前一个问卷的答案决定是否触发后续问卷
- 支持动态问卷流程控制

**2. 支持问卷版本管理**
- 问卷模板更新时不影响进行中的随访计划
- 支持问卷的版本追踪和回滚

**3. 支持个性化配置**
- 可根据患者特征动态调整问卷内容
- 支持AI推荐最适合的问卷组合

这种设计完全符合医疗随访的实际业务需求，既保证了随访计划的整体性，又提供了足够的灵活性来满足不同阶段、不同需求的表单收集要求。

## 十三、完整业务流程示例

### 13.1 术后随访计划示例

**场景**: 膝关节置换术后随访计划

**计划配置**:
- 计划名称: "膝关节置换术后随访"
- 计划分类: "术后随访"
- 结束时间: 90天

**子计划配置**:

**子计划1: 术后早期监护（周期循环）**
- 子计划名称: "术后早期监护"
- 计划类型: "cycle"（周期循环）
- 周期天数: 3天
- 执行时间: "allTime"（全程执行）
- 提醒时间: 09:00
- 内容模板: "术后注意事项提醒"
- 关联问卷:
  - "疼痛评估量表"（必填）
  - "伤口愈合评估"（必填）

**执行效果**: 患者入组后第0天、第3天、第6天、第9天...每3天收到提醒和问卷

**子计划2: 康复节点评估（自定义）**
- 子计划名称: "康复节点评估"
- 计划类型: "custom"（自定义）
- 执行时间: "custom"（自定义时间）
- 提醒时间: 14:00
- 内容模板: "康复指导内容"
- 关联问卷:
  - "膝关节功能评估"（必填）
  - "生活质量评估"（选填）

**自定义执行节点**:
- 第7天: 术后一周评估
- 第14天: 术后两周评估
- 第30天: 术后一月评估
- 第60天: 术后两月评估
- 第90天: 术后三月评估

### 13.2 数据库记录示例

**followup_plan 表**:
```sql
INSERT INTO followup_plan VALUES (
    1, '膝关节置换术后随访', 'postoperative', 'active',
    'days', 90, NULL, 1, 'qr_code_url',
    '2025-07-25 10:00:00', '2025-07-25 10:00:00'
);
```

**followup_subplan 表**:
```sql
-- 子计划1: 周期循环
INSERT INTO followup_subplan VALUES (
    1, 1, '术后早期监护', 'cycle', 'allTime', NULL, NULL,
    '09:00:00', '2025-07-25 10:00:00', '2025-07-25 10:00:00'
);

-- 子计划2: 自定义
INSERT INTO followup_subplan VALUES (
    2, 1, '康复节点评估', 'custom', 'custom', NULL, NULL,
    '14:00:00', '2025-07-25 10:00:00', '2025-07-25 10:00:00'
);
```

**followup_subplan_item 表**:
```sql
-- 子计划1的周期项目（只有一条）
INSERT INTO followup_subplan_item VALUES (
    1, 1, '术后早期监护提醒', 3, NULL, 'instant_message', 1,
    1, '2025-07-25 10:00:00', '2025-07-25 10:00:00'
);

-- 子计划2的自定义项目（多条）
INSERT INTO followup_subplan_item VALUES
(2, 2, '术后一周评估', NULL, 7, 'instant_message', 2, 1, '2025-07-25 10:00:00', '2025-07-25 10:00:00'),
(3, 2, '术后两周评估', NULL, 14, 'instant_message', 2, 2, '2025-07-25 10:00:00', '2025-07-25 10:00:00'),
(4, 2, '术后一月评估', NULL, 30, 'instant_message', 2, 3, '2025-07-25 10:00:00', '2025-07-25 10:00:00'),
(5, 2, '术后两月评估', NULL, 60, 'instant_message', 2, 4, '2025-07-25 10:00:00', '2025-07-25 10:00:00'),
(6, 2, '术后三月评估', NULL, 90, 'instant_message', 2, 5, '2025-07-25 10:00:00', '2025-07-25 10:00:00');
```

**followup_subplan_questionnaire 表**:
```sql
-- 子计划1关联问卷
INSERT INTO followup_subplan_questionnaire VALUES
(1, 1, 1, 1, 1, '2025-07-25 10:00:00'),  -- 疼痛评估量表
(2, 1, 2, 2, 1, '2025-07-25 10:00:00');  -- 伤口愈合评估

-- 子计划2关联问卷
INSERT INTO followup_subplan_questionnaire VALUES
(3, 2, 3, 1, 1, '2025-07-25 10:00:00'),  -- 膝关节功能评估
(4, 2, 4, 2, 0, '2025-07-25 10:00:00');  -- 生活质量评估（选填）
```

### 13.3 任务生成结果示例

**患者张三于2025-07-25入组，系统自动生成的任务**:

**周期循环任务（子计划1）**:
```
2025-07-25 09:00 - 术后早期监护提醒 + 疼痛评估量表 + 伤口愈合评估
2025-07-28 09:00 - 术后早期监护提醒 + 疼痛评估量表 + 伤口愈合评估
2025-07-31 09:00 - 术后早期监护提醒 + 疼痛评估量表 + 伤口愈合评估
...（每3天一次，直到90天结束）
```

**自定义任务（子计划2）**:
```
2025-08-01 14:00 - 术后一周评估 + 膝关节功能评估 + 生活质量评估
2025-08-08 14:00 - 术后两周评估 + 膝关节功能评估 + 生活质量评估
2025-08-24 14:00 - 术后一月评估 + 膝关节功能评估 + 生活质量评估
2025-09-23 14:00 - 术后两月评估 + 膝关节功能评估 + 生活质量评估
2025-10-23 14:00 - 术后三月评估 + 膝关节功能评估 + 生活质量评估
```

### 13.4 患者端体验流程

**1. 入组阶段**
- 扫描医生提供的二维码
- 填写基本信息申请入组
- 等待医生审核通过

**2. 日常随访**
- 每3天上午9点收到术后监护提醒消息
- 消息包含护理指导内容和问卷填写链接
- 填写疼痛评估和伤口愈合情况

**3. 节点评估**
- 在特定时间点（第7、14、30、60、90天）收到评估提醒
- 下午2点收到康复指导内容和功能评估问卷
- 完成膝关节功能评估（必填）和生活质量评估（选填）

**4. 数据追踪**
- 所有问卷答案都关联到具体的子计划和执行时间
- 医生可以查看患者在不同阶段的康复进展
- 系统自动生成康复趋势报告

这个完整的示例展示了随访系统如何通过子计划的周期循环和自定义模式，结合内容模板和问卷收集，实现精准化、个性化的医疗随访管理。

## 十四、C端接口完整列表

### 14.1 用户管理接口

#### 14.1.1 用户注册接口 ✅
- **接口地址**: `POST /emr/followup/cuser/register`
- **功能**: C端用户注册，创建用户账号
- **状态**: 已实现，无需修改

#### 14.1.2 确认绑定接口 ✅
- **接口地址**: `POST /emr/followup/cuser/bind`
- **功能**: 绑定患者信息，关联医院患者档案
- **状态**: 已实现，无需修改

#### 14.1.3 确认授权接口 ✅
- **接口地址**: `POST /emr/followup/cuser/auth`
- **功能**: 授权访问随访计划和相关数据
- **状态**: 已实现，无需修改

#### 14.1.4 患者确认入组接口 ✅
- **接口地址**: `POST /emr/followup/cuser/confirm-group`
- **功能**: 患者确认加入随访分组
- **状态**: 已实现，无需修改

### 14.2 随访管理接口

#### 14.2.1 随访计划列表接口 ✅
- **接口地址**: `POST /emr/followup/customer/plan-list`
- **功能**: 获取用户参与的随访计划列表
- **状态**: 已修改，新增subplanId字段
- **修改内容**: 为每条计划记录添加对应的子计划ID

#### 14.2.2 随访计划详情接口 ✅
- **接口地址**: `POST /emr/followup/customer/plan-detail`
- **功能**: 获取随访计划的详细信息
- **状态**: 已修改，支持特定/汇总模式
- **修改内容**:
  - 支持number参数查询特定子计划
  - 不传number参数时返回所有子计划汇总
  - 添加subplanId字段

#### 14.2.3 获取用户信息接口 ✅
- **接口地址**: `POST /emr/followup/customer/user-info`
- **功能**: 获取C端用户的基本信息
- **状态**: 已实现，无需修改

### 14.3 问卷管理接口

#### 14.3.1 问卷列表获取接口 ✅
- **接口地址**: `POST /emr/followup/customer/questionnaire-list`
- **功能**: 获取患者可填写的问卷列表
- **状态**: 已修改，统一数据结构
- **修改内容**:
  - 支持subplanId参数
  - 返回标准问卷结构（不含result字段）
  - 增加用户权限验证

#### 14.3.2 问卷填报接口 ✅
- **接口地址**: `POST /emr/followup/customer/questionnaire-submit`
- **功能**: 提交问卷答案
- **状态**: 已修改，统一数据结构
- **修改内容**:
  - 入参基于问卷列表接口出参 + result字段
  - 支持多问卷同时提交
  - 自动计算得分和统计信息
  - 支持单选题、多选题、文本题的不同处理

#### 14.3.3 问卷答案查看接口 ✅
- **接口地址**: `POST /emr/followup/customer/questionnaire-answers`
- **功能**: 查看已提交的问卷答案
- **状态**: 已修改，统一数据结构
- **修改内容**:
  - 出参与填报接口入参结构一致
  - 支持subplanId和questionnaireId参数
  - 返回包含result字段的完整结构

### 14.4 接口设计特点

#### 14.4.1 统一数据结构
- **问卷列表接口**: 提供基础问卷结构
- **问卷填报接口**: 基础结构 + result字段
- **问卷答案接口**: 与填报接口结构一致

#### 14.4.2 subplanId支持
- **plan-list**: 返回每条记录的subplanId
- **plan-detail**: 包含当前查看计划的subplanId
- **问卷接口**: 支持subplanId参数，确保数据关联正确

#### 14.4.3 前端友好设计
- 数据结构高度一致，便于组件复用
- 通过result字段有无判断显示/编辑模式
- 完整的错误处理和参数验证

### 14.5 接口调用流程

```
用户注册 → 确认绑定 → 确认授权 → 确认入组
    ↓
获取计划列表 → 查看计划详情 → 获取问卷列表
    ↓
填写问卷 → 提交答案 → 查看答案
```

### 14.6 数据一致性保证

- 所有接口间的subplanId数据保持一致
- 问卷数据结构在不同接口间统一
- 完整的权限验证和数据关联检查

该随访系统的C端接口设计完整、规范，具有良好的数据一致性和用户体验，能够满足医疗随访的全流程需求。
```

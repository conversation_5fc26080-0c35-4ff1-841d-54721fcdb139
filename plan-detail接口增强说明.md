# plan-detail接口增强说明

**修复时间**: 2025-07-25  
**问题**: plan-detail接口缺少第2次计划记录
**解决方案**: 增强接口支持多种查询模式

## 问题分析

### 数据库数据确认
根据数据库数据：
```sql
-- 子计划表
INSERT INTO `followup_subplan` VALUES (44, 34, '第一阶段', 'cycle', 'allTime', NULL, NULL, '09:00:00', 1, ...);
INSERT INTO `followup_subplan` VALUES (45, 34, '第二阶段', 'custom', 'custom', NULL, NULL, '14:00:00', 2, ...);

-- 子计划问卷关联表
INSERT INTO `followup_subplan_questionnaire` VALUES (1, 44, 1, 1, 1, ...); -- 第一阶段关联问卷1
INSERT INTO `followup_subplan_questionnaire` VALUES (2, 45, 2, 1, 1, ...); -- 第二阶段关联问卷2
```

### plan-list接口逻辑
`plan-list`接口为每个子计划生成一条记录：
- 第1次计划（07月24日）- 对应子计划44（疼痛相关不适评估）
- 第2次计划（07月27日）- 对应子计划45（NRS动态疼痛评估）

## 解决方案

### 方案1: 支持number参数查询特定子计划
当传入`number`参数时，返回对应子计划的详情。

### 方案2: 不传number参数时返回所有子计划汇总
当不传`number`参数时，返回包含所有子计划信息的汇总详情。

## 修复内容

### 1. 增强参数处理
```java
Object numberObj = request.get("number"); // 新增：第几次计划
int planNumber = 1; // 默认第1次计划

// 如果没有指定number参数，返回所有子计划的汇总信息
if (numberObj == null) {
    return buildAllSubplansDetail(plan, subplans, cUser);
}
```

### 2. 新增汇总详情方法
```java
private ResponseEntity<Map<String, Object>> buildAllSubplansDetail(FollowupPlan plan, List<FollowupSubplan> subplans, FollowupCUser cUser) {
    // 汇总所有子计划的信息
    List<Map<String, Object>> allProjectItems = new ArrayList<>();
    
    for (int i = 0; i < subplans.size(); i++) {
        FollowupSubplan subplan = subplans.get(i);
        
        // 查询每个子计划关联的问卷
        // 构建项目信息
        Map<String, Object> projectItem = new HashMap<>();
        projectItem.put("title", "问卷调查 - 第" + (i + 1) + "次计划");
        projectItem.put("subQuestions", [...]);
        
        allProjectItems.add(projectItem);
    }
    
    planDetail.put("projectItems", allProjectItems);
    planDetail.put("number", "全部计划（共" + subplans.size() + "次）");
}
```

## 测试用例

### 测试用例1: 不传number参数（汇总模式）
**请求**:
```json
{
    "planId": "34",
    "phone": "13800138000"
}
```

**预期响应**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "name": "高血压随访计划",
        "number": "全部计划（共2次）",
        "date": "07月24日",
        "projectItems": [
            {
                "title": "问卷调查 - 第1次计划",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            },
            {
                "title": "问卷调查 - 第2次计划", 
                "subQuestions": ["NRS动态疼痛评估（共1题）"]
            }
        ]
    }
}
```

### 测试用例2: 传入number=1（特定子计划）
**请求**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 1
}
```

**预期响应**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "name": "高血压随访计划",
        "number": "第1次计划",
        "date": "07月24日",
        "projectItems": [
            {
                "title": "问卷调查",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            }
        ]
    }
}
```

### 测试用例3: 传入number=2（特定子计划）
**请求**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 2
}
```

**预期响应**:
```json
{
    "code": 200,
    "data": {
        "planId": 34,
        "name": "高血压随访计划",
        "number": "第2次计划",
        "date": "07月27日",
        "projectItems": [
            {
                "title": "问卷调查",
                "subQuestions": ["NRS动态疼痛评估（共1题）"]
            }
        ]
    }
}
```

## 接口行为对比

### 修复前
- 只能查看第1次计划详情
- 缺少第2次计划信息
- 无法获取完整的计划概览

### 修复后
- **默认模式**（不传number）: 返回所有子计划的汇总信息
- **特定模式**（传入number）: 返回指定子计划的详情
- **完整覆盖**: 可以查看所有子计划的信息

## 前端适配建议

### 1. 概览页面
使用默认模式获取完整的计划概览：
```javascript
const planDetail = await getPlanDetail({
    planId: "34",
    phone: "13800138000"
    // 不传number参数，获取汇总信息
});
```

### 2. 详情页面
根据用户选择查看特定子计划：
```javascript
// 用户点击"第2次计划"时
const planDetail = await getPlanDetail({
    planId: "34",
    phone: "13800138000",
    number: 2
});
```

### 3. 动态展示
可以根据返回的`number`字段判断显示模式：
```javascript
if (planDetail.number.includes("全部计划")) {
    // 显示汇总模式UI
    showSummaryView(planDetail);
} else {
    // 显示单个计划详情UI
    showDetailView(planDetail);
}
```

## 兼容性说明

### 向后兼容
- 原有不传参数的调用方式仍然有效
- 返回数据结构保持一致
- 只是增加了更多信息

### 增强功能
- 支持查看所有子计划信息
- 支持查看特定子计划详情
- 提供更完整的计划概览

## 数据流程图

```
用户请求 plan-detail
    ↓
是否传入number参数？
    ↓               ↓
   是              否
    ↓               ↓
返回特定子计划    返回所有子计划汇总
    ↓               ↓
第X次计划详情    全部计划（共X次）
```

这次增强解决了原来只能看到第1次计划的问题，现在默认情况下可以看到所有子计划的信息，同时也支持查看特定子计划的详情。

# C端接口设计文档

**文档版本**: v2.0.0
**更新时间**: 2025-07-25
**接口范围**: C端用户管理、随访管理、问卷管理完整接口

## 接口设计原则

**核心设计思路**:
1. **统一数据结构**: 问卷相关接口的数据结构高度一致
2. **subplanId支持**: 为相关接口添加子计划ID字段，确保数据关联正确
3. **前端友好**: 便于前端组件复用和数据传递
4. **完整业务流程**: 从用户注册到问卷填报的完整链路

## 接口分类

### 用户管理接口
- 用户注册、绑定、授权、入组相关接口

### 随访管理接口
- 随访计划列表、详情、用户信息相关接口

### 问卷管理接口
- 问卷列表、填报、答案查看相关接口

## 一、用户管理接口

### 1.1 用户注册接口 ✅

**接口地址**: `POST /emr/followup/cuser/register`
**功能**: C端用户注册，创建用户账号
**状态**: 已实现，无需修改

**请求参数**:
```json
{
    "phone": "13800138000",
    "name": "张三",
    "gender": "男",
    "age": 45,
    "idType": "身份证",
    "idCard": "110101199001011234",
    "guardianName": "李四",
    "guardianPhone": "13900139000"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "userId": 1,
        "phone": "13800138000",
        "name": "张三",
        "status": "registered"
    }
}
```

### 1.2 确认绑定接口 ✅

**接口地址**: `POST /emr/followup/cuser/bind`
**功能**: 绑定患者信息，关联医院患者档案
**状态**: 已实现，无需修改

**请求参数**:
```json
{
    "userId": 1,
    "patientId": 12345,
    "hospitalId": 1,
    "confirmCode": "ABC123"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "绑定成功",
    "data": {
        "bindId": 1,
        "status": "bound",
        "bindTime": "2025-07-25 10:00:00"
    }
}
```

### 1.3 确认授权接口 ✅

**接口地址**: `POST /emr/followup/cuser/auth`
**功能**: 授权访问随访计划和相关数据
**状态**: 已实现，无需修改

**请求参数**:
```json
{
    "userId": 1,
    "authScope": ["followup", "questionnaire"],
    "authConfirm": true
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "授权成功",
    "data": {
        "authId": 1,
        "authScope": ["followup", "questionnaire"],
        "authTime": "2025-07-25 10:00:00"
    }
}
```

### 1.4 患者确认入组接口 ✅

**接口地址**: `POST /emr/followup/cuser/confirm-group`
**功能**: 患者确认加入随访分组
**状态**: 已实现，无需修改

**请求参数**:
```json
{
    "phone": "13800138000",
    "groupId": 1,
    "qrCode": "GROUP_QR_CODE_123",
    "patientInfo": {
        "name": "张三",
        "age": 45,
        "gender": "男"
    }
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "入组申请提交成功",
    "data": {
        "applicationId": 1,
        "status": "pending",
        "groupName": "高血压随访分组",
        "submitTime": "2025-07-25 10:00:00"
    }
}
```

## 二、随访管理接口

### 2.1 随访计划列表接口 ✅

**接口地址**: `POST /emr/followup/customer/plan-list`
**功能**: 获取用户参与的随访计划列表
**状态**: 已修改，新增subplanId字段

**请求参数**:
```json
{
    "phone": "13800138000",
    "state": "0",
    "pageNo": 1,
    "pageSize": 10
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "planId": 34,
            "subplanId": 44,
            "name": "高血压随访计划",
            "number": "第1次计划",
            "date": "07月24日",
            "desc": "",
            "status": "0"
        },
        {
            "planId": 34,
            "subplanId": 45,
            "name": "高血压随访计划",
            "number": "第2次计划",
            "date": "07月27日",
            "desc": "",
            "status": "0"
        }
    ]
}
```

### 2.2 随访计划详情接口 ✅

**接口地址**: `POST /emr/followup/customer/plan-detail`
**功能**: 获取随访计划的详细信息
**状态**: 已修改，支持特定/汇总模式

**请求参数**:
```json
{
    "planId": "34",
    "phone": "13800138000",
    "number": 1
}
```

**响应数据（特定子计划模式）**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "subplanId": 44,
        "name": "高血压随访计划",
        "number": "第1次计划",
        "date": "07月24日",
        "personName": "患者",
        "desc": "",
        "projectItems": [
            {
                "title": "问卷调查",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            }
        ]
    }
}
```

**响应数据（汇总模式，不传number参数）**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "subplanIds": [44, 45],
        "name": "高血压随访计划",
        "number": "全部计划（共2次）",
        "date": "07月24日",
        "personName": "患者",
        "desc": "",
        "projectItems": [
            {
                "title": "问卷调查 - 第1次计划",
                "subQuestions": ["疼痛相关不适评估（共1题）"]
            },
            {
                "title": "问卷调查 - 第2次计划",
                "subQuestions": ["NRS动态疼痛评估（共1题）"]
            }
        ]
    }
}
```

### 2.3 获取用户信息接口 ✅

**接口地址**: `POST /emr/followup/customer/user-info`
**功能**: 获取C端用户的基本信息
**状态**: 已实现，无需修改

**请求参数**:
```json
{
    "phone": "13800138000"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "userId": 1,
        "phone": "13800138000",
        "name": "张三",
        "gender": "男",
        "age": 45,
        "idCard": "110101199001011234",
        "status": "active",
        "registerTime": "2025-07-25 10:00:00"
    }
}
```

## 三、问卷管理接口

### 3.1 问卷列表获取接口 ✅

**接口地址**: `POST /emr/followup/customer/questionnaire-list`
**功能**: 获取患者可填写的问卷列表（基础结构，不含答案）
**状态**: 已修改，统一数据结构

**请求参数**:
```json
{
    "phone": "13800138000",
    "planId": "34",
    "subplanId": "44"
}
```

**响应数据**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "planName": "高血压随访计划",
        "subplanId": 44,
        "subplanName": "第一阶段评估",
        "questionnaires": [
            {
                "id": 1,
                "name": "疼痛相关不适评估",
                "description": "评估疼痛相关症状",
                "sortOrder": 1,
                "isRequired": true,
                "questions": [
                    {
                        "id": 1,
                        "questionText": "请您选择您最近1周内是否出现以下不适症状",
                        "questionType": "checkbox",
                        "isRequired": true,
                        "sortOrder": 1,
                        "options": [
                            {
                                "id": 1,
                                "optionText": "天气下雨或阴天",
                                "optionValue": "1",
                                "score": 5,
                                "sortOrder": 1
                            },
                            {
                                "id": 2,
                                "optionText": "疼痛",
                                "optionValue": "2",
                                "score": 2,
                                "sortOrder": 2
                            },
                            {
                                "id": 3,
                                "optionText": "麻木",
                                "optionValue": "3",
                                "score": 2,
                                "sortOrder": 3
                            },
                            {
                                "id": 5,
                                "optionText": "咳嗽",
                                "optionValue": "5",
                                "score": 2,
                                "sortOrder": 5
                            }
                        ]
                    },
                    {
                        "id": 11,
                        "questionText": "您目前有哪些症状？（可多选）",
                        "questionType": "checkbox",
                        "isRequired": false,
                        "sortOrder": 2,
                        "options": [
                            {
                                "id": 34,
                                "optionText": "肿胀",
                                "optionValue": "swelling",
                                "score": 1,
                                "sortOrder": 1
                            },
                            {
                                "id": 35,
                                "optionText": "僵硬",
                                "optionValue": "stiffness",
                                "score": 1,
                                "sortOrder": 2
                            },
                            {
                                "id": 36,
                                "optionText": "活动受限",
                                "optionValue": "limited_mobility",
                                "score": 2,
                                "sortOrder": 3
                            }
                        ]
                    },
                    {
                        "id": 12,
                        "questionText": "请描述您的康复感受",
                        "questionType": "input",
                        "isRequired": false,
                        "sortOrder": 3,
                        "options": []
                    }
                ]
            }
        ]
    }
}
```

## 二、问卷填报接口

### 接口信息
- **接口地址**: `POST /emr/followup/customer/questionnaire-submit`
- **功能**: 提交问卷答案
- **入参设计**: 基于问卷列表接口的出参，在每道题目上添加`result`字段

### 请求参数
```json
{
    "phone": "13800138000",
    "planId": 1,
    "planName": "膝关节置换术后随访",
    "subplanId": 2,
    "subplanName": "康复节点评估",
    "questionnaires": [
        {
            "id": 3,
            "name": "膝关节功能评估",
            "description": "评估膝关节功能恢复情况",
            "sortOrder": 1,
            "isRequired": true,
            "questions": [
                {
                    "id": 10,
                    "questionText": "您目前的疼痛程度如何？",
                    "questionType": "radio",
                    "isRequired": true,
                    "sortOrder": 1,
                    "result": "1",  // 单选题：选中选项的optionValue
                    "options": [
                        {
                            "id": 30,
                            "optionText": "无疼痛",
                            "optionValue": "0",
                            "score": 0,
                            "sortOrder": 1
                        },
                        {
                            "id": 31,
                            "optionText": "轻微疼痛",
                            "optionValue": "1",
                            "score": 1,
                            "sortOrder": 2
                        },
                        {
                            "id": 32,
                            "optionText": "中度疼痛",
                            "optionValue": "2",
                            "score": 2,
                            "sortOrder": 3
                        },
                        {
                            "id": 33,
                            "optionText": "重度疼痛",
                            "optionValue": "3",
                            "score": 3,
                            "sortOrder": 4
                        }
                    ]
                },
                {
                    "id": 11,
                    "questionText": "您目前有哪些症状？（可多选）",
                    "questionType": "checkbox",
                    "isRequired": false,
                    "sortOrder": 2,
                    "result": ["swelling", "stiffness"],  // 多选题：选中选项optionValue的数组
                    "options": [
                        {
                            "id": 34,
                            "optionText": "肿胀",
                            "optionValue": "swelling",
                            "score": 1,
                            "sortOrder": 1
                        },
                        {
                            "id": 35,
                            "optionText": "僵硬",
                            "optionValue": "stiffness",
                            "score": 1,
                            "sortOrder": 2
                        },
                        {
                            "id": 36,
                            "optionText": "活动受限",
                            "optionValue": "limited_mobility",
                            "score": 2,
                            "sortOrder": 3
                        }
                    ]
                },
                {
                    "id": 12,
                    "questionText": "请描述您的康复感受",
                    "questionType": "input",
                    "isRequired": false,
                    "sortOrder": 3,
                    "result": "恢复情况良好，疼痛明显减轻",  // 文本题：用户输入的文本
                    "options": []
                }
            ]
        }
    ]
}
```

### result字段规则
- **单选题(radio)**: `result`为字符串，值为选中选项的`optionValue`
- **多选题(checkbox)**: `result`为字符串数组，值为选中选项的`optionValue`数组
- **文本题(input)**: `result`为字符串，值为用户输入的文本内容
- **数字题(number)**: `result`为字符串，值为用户输入的数字
- **日期题(date)**: `result`为字符串，值为用户选择的日期（格式：YYYY-MM-DD）

### 响应数据
```json
{
    "code": 200,
    "message": "提交成功",
    "data": {
        "planId": 1,
        "subplanId": 2,
        "totalScore": 4,
        "submitTime": "2025-07-25 15:30:00",
        "submittedQuestionnaires": [
            {
                "questionnaireId": 3,
                "questionnaireName": "膝关节功能评估",
                "totalScore": 4,
                "maxScore": 10,
                "submittedQuestions": 3,
                "totalQuestions": 3
            }
        ]
    }
}
```

## 三、问卷答案查看接口

### 接口信息
- **接口地址**: `POST /emr/followup/customer/questionnaire-answers`
- **功能**: 查看已提交的问卷答案
- **出参设计**: 与问卷填报接口的入参结构完全一致（包含`result`字段）

### 请求参数
```json
{
    "phone": "13800138000",
    "planId": "1",
    "subplanId": "2",  // 可选
    "questionnaireId": "3"  // 可选
}
```

### 响应数据结构
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 1,
        "planName": "膝关节置换术后随访",
        "subplanId": 2,
        "subplanName": "康复节点评估",
        "submitTime": "2025-07-25 15:30:00",
        "totalScore": 4,
        "questionnaires": [
            {
                "id": 3,
                "name": "膝关节功能评估",
                "description": "评估膝关节功能恢复情况",
                "sortOrder": 1,
                "isRequired": true,
                "questions": [
                    {
                        "id": 10,
                        "questionText": "您目前的疼痛程度如何？",
                        "questionType": "radio",
                        "isRequired": true,
                        "sortOrder": 1,
                        "result": "1",  // 已填写的答案
                        "options": [
                            {
                                "id": 30,
                                "optionText": "无疼痛",
                                "optionValue": "0",
                                "score": 0,
                                "sortOrder": 1
                            },
                            {
                                "id": 31,
                                "optionText": "轻微疼痛",
                                "optionValue": "1",
                                "score": 1,
                                "sortOrder": 2
                            }
                            // ... 其他选项
                        ]
                    },
                    {
                        "id": 11,
                        "questionText": "您目前有哪些症状？（可多选）",
                        "questionType": "checkbox",
                        "isRequired": false,
                        "sortOrder": 2,
                        "result": ["swelling", "stiffness"],  // 已填写的答案
                        "options": [
                            {
                                "id": 34,
                                "optionText": "肿胀",
                                "optionValue": "swelling",
                                "score": 1,
                                "sortOrder": 1
                            },
                            {
                                "id": 35,
                                "optionText": "僵硬",
                                "optionValue": "stiffness",
                                "score": 1,
                                "sortOrder": 2
                            }
                            // ... 其他选项
                        ]
                    },
                    {
                        "id": 12,
                        "questionText": "请描述您的康复感受",
                        "questionType": "input",
                        "isRequired": false,
                        "sortOrder": 3,
                        "result": "恢复情况良好，疼痛明显减轻",  // 已填写的答案
                        "options": []
                    }
                ]
            }
        ]
    }
}
```

## 四、接口使用流程

### 4.1 C端开发流程
1. **获取问卷**: 调用问卷列表接口获取基础结构
2. **用户填写**: 在前端页面让用户填写，将答案填入`result`字段
3. **提交答案**: 将完整结构（含`result`字段）提交到填报接口
4. **查看答案**: 调用答案查看接口获取已填写的完整结构

### 4.2 数据结构一致性
- **基础结构**: 问卷列表接口提供标准的问卷结构
- **填写结构**: 在基础结构上添加`result`字段
- **查看结构**: 与填写结构完全一致，便于前端复用组件

### 4.3 前端组件复用
由于三个接口的数据结构高度一致，前端可以：
- 使用同一个问卷渲染组件
- 通过`result`字段的有无判断是显示模式还是编辑模式
- 实现问卷的查看、编辑、提交功能复用

## 五、技术实现要点

### 5.1 后端处理逻辑
- **问卷列表**: 查询数据库构建基础结构，不包含`result`字段
- **问卷提交**: 解析`result`字段，保存到`questionnaire_answer`表
- **答案查看**: 查询答案数据，填充到基础结构的`result`字段中

### 5.2 数据验证
- 验证`result`字段的数据类型和格式
- 单选题验证`optionValue`是否存在
- 多选题验证所有`optionValue`是否都存在
- 必填题验证`result`字段不能为空

### 5.3 评分计算
- 根据选中选项的`score`字段自动计算得分
- 单选题取选中选项的分数
- 多选题累加所有选中选项的分数
- 文本题不参与评分计算

这种设计确保了三个接口的数据结构高度一致，便于前端开发和维护，同时保持了良好的扩展性。

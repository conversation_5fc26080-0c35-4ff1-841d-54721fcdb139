2025-07-25 20:17:37.148 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:17:37.160 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 12120 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:17:37.160 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:17:37.160 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. <PERSON> already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.661 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:17:37.662 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:17:37.803 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:17:38.006 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:17:38.011 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:17:38.012 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:17:38.012 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:17:38.093 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:17:38.093 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 905 ms
2025-07-25 20:17:38.264 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@660f0c'
2025-07-25 20:17:38.343 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:17:38.364 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:17:38.381 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:17:38.395 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:17:38.411 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:17:38.424 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:17:38.438 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:17:38.453 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:17:38.465 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:17:38.485 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:17:38.494 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:17:38.506 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:17:38.521 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:17:38.525 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:17:38.526 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:17:38.532 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:17:38.545 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:17:38.561 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:17:38.572 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:17:38.582 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:17:38.592 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:17:38.601 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:17:38.610 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:17:38.618 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:17:38.625 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:17:38.632 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:17:38.641 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:17:38.652 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:17:39.490 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:17:39.533 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
2025-07-25 20:17:39.537 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
2025-07-25 20:17:39.538 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
2025-07-25 20:17:39.540 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
2025-07-25 20:17:39.540 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:17:39.541 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
2025-07-25 20:17:39.543 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
2025-07-25 20:17:39.544 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
2025-07-25 20:17:39.545 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:17:39.546 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{GET [/emr/image-text/{id}]}: getRecord(Long)
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
2025-07-25 20:17:39.546 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:17:39.547 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
2025-07-25 20:17:39.547 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
	{ [/emr/nhpatient/list]}: getPatientList(Map)
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
2025-07-25 20:17:39.548 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
2025-07-25 20:17:39.548 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
2025-07-25 20:17:39.549 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{GET [/emr/patient/{id}]}: getPatient(Long)
	{POST [/emr/patient]}: createPatient(PatientDTO)
2025-07-25 20:17:39.550 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
2025-07-25 20:17:39.552 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
2025-07-25 20:17:39.553 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
2025-07-25 20:17:39.554 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
	{GET [/emr/questionnaire-options]}: getAllOptions()
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
2025-07-25 20:17:39.554 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
2025-07-25 20:17:39.555 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{GET [/emr/questionnaire-rules]}: getAllRules()
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
2025-07-25 20:17:39.555 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
2025-07-25 20:17:39.556 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
2025-07-25 20:17:39.557 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{POST [/scheduled/{name}/run]}: runScheduled(String)
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
	{GET [/scheduled/log/files]}: logFiles()
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
2025-07-25 20:17:39.558 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
2025-07-25 20:17:39.558 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
2025-07-25 20:17:39.558 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
2025-07-25 20:17:39.558 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/run]}: getRunScheduledName()
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
2025-07-25 20:17:39.559 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-25 20:17:39.560 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:17:39.576 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:17:39.583 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:17:39.666 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:17:39.676 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:17:39.683 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 2.804 seconds (JVM running for 3.366)
2025-07-25 20:17:39.684 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:17:44.961 [http-nio-0.0.0.0-18923-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 20:17:44.961 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 20:17:44.961 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-25 20:17:44.961 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-25 20:17:44.961 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-25 20:17:44.962 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7a6ea47d
2025-07-25 20:17:44.962 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@6dd36ab6
2025-07-25 20:17:44.962 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-25 20:17:44.962 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-25 20:17:44.966 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-answers", parameters={}
2025-07-25 20:17:44.969 [http-nio-0.0.0.0-18923-exec-1] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getQuestionnaireAnswers(Map)
2025-07-25 20:17:44.996 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34, examId=1}]
2025-07-25 20:17:45.065 [http-nio-0.0.0.0-18923-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 20:17:45.607 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:17:45.619 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:17:45.658 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:17:45.663 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:17:45.663 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:17:45.687 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:17:45.689 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:17:45.689 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:17:45.714 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - <==      Total: 1
2025-07-25 20:17:45.714 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:17:45.714 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:17:45.734 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:17:45.739 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:17:45.739 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:17:45.760 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:17:45.763 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:17:45.764 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:17:45.788 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:17:45.790 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:17:45.790 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:17:45.814 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:17:45.815 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:17:45.816 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:17:45.834 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:17:45.835 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:17:45.835 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:17:45.853 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:17:45.856 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ? AND subplan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:17:45.856 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 1(Long), 1(Long), 44(Long)
2025-07-25 20:17:45.878 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:17:45.879 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:17:45.879 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:17:45.898 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:17:45.898 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:17:45.898 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:17:45.924 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:17:45.924 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:17:45.924 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:17:45.948 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:17:45.948 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:17:45.948 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:17:45.969 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:17:45.970 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ? AND subplan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:17:45.970 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 2(Long), 2(Long), 1(Long), 45(Long)
2025-07-25 20:17:45.988 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:17:45.990 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:17:45.991 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:17:46.014 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 1
2025-07-25 20:17:46.016 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?)
2025-07-25 20:17:46.016 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:17:46.038 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 1
2025-07-25 20:17:46.050 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:17:46.051 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={questionnaires=[{name=疼痛相关不适评估, questions=[{result=, isRequired=true, sortOrder=1,  (truncated)...]
2025-07-25 20:17:46.084 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:25:32.553 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:25:32.553 [http-nio-0.0.0.0-18923-exec-5] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:25:32.555 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:25:32.579 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:25:32.580 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:25:32.600 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.602 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:25:32.602 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:25:32.622 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.628 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:25:32.628 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:25:32.648 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.651 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:25:32.651 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:25:32.671 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:25:32.673 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:25:32.674 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:25:32.717 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.719 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:25:32.720 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:25:32.738 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.743 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:25:32.743 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:25:32.760 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.762 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:25:32.763 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:25:32.779 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:25:32.782 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:25:32.782 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:25:32.801 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 1
2025-07-25 20:25:32.802 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:25:32.802 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:25:32.821 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.822 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:25:32.823 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:25:32.846 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.847 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:25:32.847 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:25:32.867 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:25:32.869 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:25:32.869 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:25:32.888 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:25:32.888 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:25:32.889 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:25:32.912 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:25:32.913 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:25:32.913 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, desc=, status=0}, {date=07月27日 (truncated)...]
2025-07-25 20:25:32.914 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:26:22.355 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:26:22.355 [http-nio-0.0.0.0-18923-exec-2] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:26:22.356 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:26:22.356 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:26:22.357 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:26:22.377 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:26:22.379 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:26:22.379 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:26:22.399 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:26:22.401 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:26:22.401 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:26:22.420 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:26:22.421 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:26:22.422 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:26:22.440 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:26:22.441 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:26:22.441 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:26:22.464 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:26:22.465 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:26:22.465 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:26:22.491 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:26:22.492 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:26:22.492 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:26:22.513 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:26:22.514 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:26:22.514 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:26:22.534 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:26:22.535 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:26:22.535 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={date=07月24日, personName=患者, number=第1次计划, name=高血压随访计划, planId=34, projectItems=[{s (truncated)...]
2025-07-25 20:26:22.535 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:28:34.512 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:28:34.531 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 23520 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:28:34.532 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:28:34.532 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.130 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:28:35.131 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:28:35.302 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:28:35.510 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:28:35.516 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:28:35.516 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:28:35.516 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:28:35.606 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:28:35.606 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1033 ms
2025-07-25 20:28:35.790 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@660f0c'
2025-07-25 20:28:35.867 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:28:35.887 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:28:35.900 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:28:35.912 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:28:35.925 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:28:35.935 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:28:35.946 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:28:35.957 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:28:35.969 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:28:35.984 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:28:35.996 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:28:36.016 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:28:36.030 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:28:36.034 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:28:36.034 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:28:36.041 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:28:36.053 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:28:36.068 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:28:36.079 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:28:36.090 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:28:36.101 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:28:36.110 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:28:36.120 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:28:36.128 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:28:36.135 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:28:36.143 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:28:36.152 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:28:36.162 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:28:37.144 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:28:37.186 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
2025-07-25 20:28:37.190 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
2025-07-25 20:28:37.191 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
2025-07-25 20:28:37.193 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
2025-07-25 20:28:37.194 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:28:37.195 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
2025-07-25 20:28:37.196 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
2025-07-25 20:28:37.197 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
2025-07-25 20:28:37.198 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:28:37.199 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
	{GET [/emr/image-text/{id}]}: getRecord(Long)
2025-07-25 20:28:37.199 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:28:37.200 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
2025-07-25 20:28:37.200 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{ [/emr/nhpatient/list]}: getPatientList(Map)
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
2025-07-25 20:28:37.201 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
2025-07-25 20:28:37.202 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
2025-07-25 20:28:37.203 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{POST [/emr/patient]}: createPatient(PatientDTO)
	{GET [/emr/patient/{id}]}: getPatient(Long)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
2025-07-25 20:28:37.204 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
2025-07-25 20:28:37.205 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
2025-07-25 20:28:37.206 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
2025-07-25 20:28:37.207 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
	{GET [/emr/questionnaire-options]}: getAllOptions()
2025-07-25 20:28:37.207 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
2025-07-25 20:28:37.208 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{GET [/emr/questionnaire-rules]}: getAllRules()
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
2025-07-25 20:28:37.209 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
2025-07-25 20:28:37.210 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
2025-07-25 20:28:37.211 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{POST [/scheduled/{name}/run]}: runScheduled(String)
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
	{GET [/scheduled/log/files]}: logFiles()
2025-07-25 20:28:37.211 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
2025-07-25 20:28:37.211 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
2025-07-25 20:28:37.212 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
2025-07-25 20:28:37.212 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/run]}: getRunScheduledName()
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
2025-07-25 20:28:37.213 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-25 20:28:37.214 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:28:37.242 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:28:37.249 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:28:37.354 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:28:37.371 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:28:37.383 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 3.271 seconds (JVM running for 4.019)
2025-07-25 20:28:37.386 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:28:46.751 [http-nio-0.0.0.0-18923-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 20:28:46.751 [http-nio-0.0.0.0-18923-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 20:28:46.751 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-25 20:28:46.751 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-25 20:28:46.751 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-25 20:28:46.752 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@63f9b562
2025-07-25 20:28:46.752 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@6dd36ab6
2025-07-25 20:28:46.752 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-25 20:28:46.752 [http-nio-0.0.0.0-18923-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-25 20:28:46.757 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:28:46.759 [http-nio-0.0.0.0-18923-exec-2] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:28:46.788 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:28:46.860 [http-nio-0.0.0.0-18923-exec-2] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 20:28:47.091 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:28:47.103 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:28:47.138 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.140 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:28:47.140 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:28:47.172 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.178 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:28:47.178 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:28:47.205 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.208 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:28:47.208 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:28:47.227 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:28:47.228 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:28:47.228 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:28:47.254 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.255 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:28:47.256 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:28:47.279 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.282 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:28:47.282 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:28:47.302 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.305 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:28:47.306 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:28:47.332 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:28:47.334 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:28:47.334 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:28:47.368 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 1
2025-07-25 20:28:47.369 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:28:47.370 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:28:47.391 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.392 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:28:47.393 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:28:47.414 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.415 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:28:47.416 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:28:47.434 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:28:47.435 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:28:47.435 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:28:47.455 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:28:47.456 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:28:47.456 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:28:47.480 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:28:47.488 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:28:47.488 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, desc=, status=0}, {date=07月27日 (truncated)...]
2025-07-25 20:28:47.499 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:28:52.887 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-answers", parameters={}
2025-07-25 20:28:52.887 [http-nio-0.0.0.0-18923-exec-1] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getQuestionnaireAnswers(Map)
2025-07-25 20:28:52.888 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34, examId=1}]
2025-07-25 20:28:52.889 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:28:52.890 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:28:52.913 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:28:52.915 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:28:52.915 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:28:52.938 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:28:52.941 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:28:52.941 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:28:52.960 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - <==      Total: 1
2025-07-25 20:28:52.960 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:28:52.960 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:28:52.978 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:28:52.980 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:28:52.981 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:28:53.001 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:28:53.003 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:28:53.003 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:28:53.019 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:28:53.021 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:28:53.021 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:28:53.042 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:28:53.043 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:28:53.043 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:28:53.060 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:28:53.061 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:28:53.061 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:28:53.086 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:28:53.087 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:28:53.088 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 1(Long), 1(Long)
2025-07-25 20:28:53.410 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 1
2025-07-25 20:28:53.411 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:28:53.411 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:28:53.426 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:28:53.426 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:28:53.426 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:28:53.445 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:28:53.445 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:28:53.445 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:28:53.466 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:28:53.466 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:28:53.467 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:28:53.490 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:28:53.491 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:28:53.491 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 2(Long), 2(Long), 1(Long)
2025-07-25 20:28:53.511 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:28:53.512 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:28:53.513 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:28:53.535 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 1
2025-07-25 20:28:53.537 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?)
2025-07-25 20:28:53.537 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:28:53.558 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 1
2025-07-25 20:28:53.558 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:28:53.559 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={questionnaires=[{name=疼痛相关不适评估, questions=[{result=[2, 3, 5], isRequired=true, sort (truncated)...]
2025-07-25 20:28:53.571 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:29:07.210 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:29:07.211 [http-nio-0.0.0.0-18923-exec-4] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:29:07.211 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:29:07.212 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:29:07.212 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:29:07.233 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:29:07.234 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:29:07.235 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:29:07.254 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:29:07.256 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:29:07.256 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:29:07.275 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:29:07.275 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:29:07.276 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:29:07.296 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:29:07.297 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:29:07.297 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:29:07.323 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:29:07.324 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:29:07.324 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:29:07.348 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:29:07.349 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:29:07.349 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:29:07.373 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:29:07.374 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:29:07.374 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:29:07.395 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:29:07.395 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:29:07.395 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={date=07月24日, personName=患者, number=第1次计划, name=高血压随访计划, planId=34, projectItems=[{s (truncated)...]
2025-07-25 20:29:07.396 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:30:58.681 [http-nio-0.0.0.0-18923-exec-7] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-answers", parameters={}
2025-07-25 20:30:58.682 [http-nio-0.0.0.0-18923-exec-7] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getQuestionnaireAnswers(Map)
2025-07-25 20:30:58.683 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34, examId=1}]
2025-07-25 20:30:58.709 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:30:58.710 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:30:58.735 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:30:58.737 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:30:58.737 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:30:58.759 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:30:58.760 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:30:58.761 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:30:58.778 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - <==      Total: 1
2025-07-25 20:30:58.779 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:30:58.780 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:30:58.801 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:30:58.802 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:30:58.803 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:30:58.825 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:30:58.826 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:30:58.826 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:30:58.844 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:30:58.844 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:30:58.844 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:30:58.862 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:30:58.862 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:30:58.863 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:30:58.880 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:30:58.880 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:30:58.880 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:30:58.903 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:30:58.904 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:30:58.905 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 1(Long), 1(Long)
2025-07-25 20:30:58.927 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:30:58.928 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:30:58.929 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:30:59.208 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:30:59.209 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:30:59.209 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:30:59.231 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:30:59.231 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:30:59.231 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:30:59.248 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:30:59.248 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:30:59.248 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:30:59.264 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:30:59.266 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:30:59.266 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 2(Long), 2(Long), 1(Long)
2025-07-25 20:30:59.284 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:30:59.285 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:30:59.286 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:30:59.305 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:30:59.306 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?)
2025-07-25 20:30:59.307 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:30:59.329 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:30:59.330 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:30:59.330 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={questionnaires=[{name=疼痛相关不适评估, questions=[{result=[], isRequired=true, sortOrder=1 (truncated)...]
2025-07-25 20:30:59.331 [http-nio-0.0.0.0-18923-exec-7] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:32:16.409 [http-nio-0.0.0.0-18923-exec-10] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-submit", parameters={}
2025-07-25 20:32:16.410 [http-nio-0.0.0.0-18923-exec-10] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#submitQuestionnaire(Map)
2025-07-25 20:32:16.410 [http-nio-0.0.0.0-18923-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34, examType=questionForm, answers=[{qId=1, type=checkbox, result=2,3,5,  (truncated)...]
2025-07-25 20:32:16.410 [http-nio-0.0.0.0-18923-exec-10] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:32:16.411 [http-nio-0.0.0.0-18923-exec-10] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=400, message=问卷数据不能为空}]
2025-07-25 20:32:16.411 [http-nio-0.0.0.0-18923-exec-10] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 400 BAD_REQUEST
2025-07-25 20:32:40.416 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:32:40.430 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 11084 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:32:40.430 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:32:40.431 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.912 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:40.913 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:32:41.062 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:32:41.246 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:32:41.251 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:32:41.252 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:32:41.252 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:32:41.333 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:32:41.333 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 874 ms
2025-07-25 20:32:41.527 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@5c77ba8f'
2025-07-25 20:32:41.609 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:32:41.631 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:32:41.647 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:32:41.662 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:32:41.677 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:32:41.690 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:32:41.704 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:32:41.718 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:32:41.729 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:32:41.746 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:32:41.755 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:32:41.765 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:32:41.776 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:32:41.779 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:32:41.780 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:32:41.786 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:32:41.797 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:32:41.810 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:32:41.819 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:32:41.828 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:32:41.838 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:32:41.846 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:32:41.854 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:32:41.863 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:32:41.873 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:32:41.883 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:32:41.891 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:32:41.904 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:32:42.709 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:32:42.746 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
2025-07-25 20:32:42.749 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
2025-07-25 20:32:42.750 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
2025-07-25 20:32:42.751 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
2025-07-25 20:32:42.751 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:32:42.753 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
2025-07-25 20:32:42.754 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
2025-07-25 20:32:42.755 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
2025-07-25 20:32:42.756 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:32:42.756 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
	{GET [/emr/image-text/{id}]}: getRecord(Long)
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
2025-07-25 20:32:42.757 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:32:42.757 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
2025-07-25 20:32:42.758 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
	{ [/emr/nhpatient/list]}: getPatientList(Map)
2025-07-25 20:32:42.758 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
2025-07-25 20:32:42.759 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
2025-07-25 20:32:42.759 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{POST [/emr/patient]}: createPatient(PatientDTO)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{GET [/emr/patient/{id}]}: getPatient(Long)
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
2025-07-25 20:32:42.760 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
2025-07-25 20:32:42.761 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
2025-07-25 20:32:42.762 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
2025-07-25 20:32:42.763 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{GET [/emr/questionnaire-options]}: getAllOptions()
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
2025-07-25 20:32:42.763 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
2025-07-25 20:32:42.763 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{GET [/emr/questionnaire-rules]}: getAllRules()
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
2025-07-25 20:32:42.764 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
2025-07-25 20:32:42.764 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
2025-07-25 20:32:42.765 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{POST [/scheduled/{name}/run]}: runScheduled(String)
	{GET [/scheduled/log/files]}: logFiles()
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
2025-07-25 20:32:42.766 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
2025-07-25 20:32:42.766 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
2025-07-25 20:32:42.766 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
2025-07-25 20:32:42.766 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/run]}: getRunScheduledName()
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
2025-07-25 20:32:42.767 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-25 20:32:42.769 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:32:42.782 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:32:42.788 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:32:42.868 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:32:42.879 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:32:42.886 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 2.742 seconds (JVM running for 3.288)
2025-07-25 20:32:42.887 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:32:49.501 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:32:49.517 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 22736 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:32:49.517 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:32:49.517 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.988 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.989 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:32:49.989 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:32:50.145 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:32:50.344 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:32:50.350 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:32:50.350 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:32:50.350 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:32:50.429 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:32:50.429 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 884 ms
2025-07-25 20:32:50.608 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@660f0c'
2025-07-25 20:32:50.685 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:32:50.705 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:32:50.721 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:32:50.735 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:32:50.750 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:32:50.763 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:32:50.776 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:32:50.790 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:32:50.802 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:32:50.822 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:32:50.833 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:32:50.846 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:32:50.863 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:32:50.869 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:32:50.869 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:32:50.880 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:32:50.893 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:32:50.910 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:32:50.920 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:32:50.931 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:32:50.946 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:32:50.959 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:32:50.969 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:32:50.977 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:32:50.984 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:32:50.992 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:32:51.001 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:32:51.011 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:32:51.834 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:32:51.873 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
2025-07-25 20:32:51.876 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
2025-07-25 20:32:51.878 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
2025-07-25 20:32:51.879 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
2025-07-25 20:32:51.880 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:32:51.881 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
2025-07-25 20:32:51.882 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
2025-07-25 20:32:51.883 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
2025-07-25 20:32:51.884 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:32:51.885 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{GET [/emr/image-text/{id}]}: getRecord(Long)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
2025-07-25 20:32:51.885 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:32:51.886 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
2025-07-25 20:32:51.886 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{ [/emr/nhpatient/list]}: getPatientList(Map)
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
2025-07-25 20:32:51.886 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
2025-07-25 20:32:51.888 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
2025-07-25 20:32:51.888 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{GET [/emr/patient/{id}]}: getPatient(Long)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{POST [/emr/patient]}: createPatient(PatientDTO)
2025-07-25 20:32:51.889 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
2025-07-25 20:32:51.890 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
2025-07-25 20:32:51.891 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
2025-07-25 20:32:51.892 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
	{GET [/emr/questionnaire-options]}: getAllOptions()
2025-07-25 20:32:51.893 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
2025-07-25 20:32:51.893 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{GET [/emr/questionnaire-rules]}: getAllRules()
2025-07-25 20:32:51.893 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
2025-07-25 20:32:51.894 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
2025-07-25 20:32:51.895 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{GET [/scheduled/log/files]}: logFiles()
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
	{POST [/scheduled/{name}/run]}: runScheduled(String)
2025-07-25 20:32:51.895 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
2025-07-25 20:32:51.895 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
2025-07-25 20:32:51.896 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
2025-07-25 20:32:51.896 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/run]}: getRunScheduledName()
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
2025-07-25 20:32:51.896 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-25 20:32:51.898 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:32:51.912 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:32:51.918 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:32:52.004 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:32:52.014 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:32:52.020 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 2.829 seconds (JVM running for 3.657)
2025-07-25 20:32:52.022 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:33:26.286 [http-nio-0.0.0.0-18923-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 20:33:26.286 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 20:33:26.286 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-25 20:33:26.286 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-25 20:33:26.286 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-25 20:33:26.287 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@5426cb36
2025-07-25 20:33:26.287 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@74606204
2025-07-25 20:33:26.287 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-25 20:33:26.287 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-25 20:33:26.293 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-answers", parameters={}
2025-07-25 20:33:26.296 [http-nio-0.0.0.0-18923-exec-1] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getQuestionnaireAnswers(Map)
2025-07-25 20:33:26.324 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34}]
2025-07-25 20:33:26.399 [http-nio-0.0.0.0-18923-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 20:33:26.636 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:33:26.648 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:33:26.688 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:33:26.693 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:33:26.694 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:33:26.714 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:33:26.715 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:33:26.716 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:33:26.738 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - <==      Total: 1
2025-07-25 20:33:26.738 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:33:26.738 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:33:26.763 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:33:26.767 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:33:26.768 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:33:26.800 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:33:26.803 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:33:26.803 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:33:26.824 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:33:26.826 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:33:26.826 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:33:26.843 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:33:26.844 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:33:26.845 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:33:26.870 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:33:26.870 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:33:26.871 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:33:26.900 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:33:26.902 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:33:26.903 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 1(Long), 1(Long)
2025-07-25 20:33:26.929 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:33:26.930 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:33:26.931 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:33:26.950 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:33:26.951 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:33:26.951 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:33:26.976 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:33:26.976 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:33:26.976 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:33:26.995 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:33:26.996 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:33:26.996 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:33:27.015 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:33:27.016 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:33:27.016 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 2(Long), 2(Long), 1(Long)
2025-07-25 20:33:27.038 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:33:27.040 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:33:27.040 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:33:27.062 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:33:27.064 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?)
2025-07-25 20:33:27.064 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:33:27.088 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:33:27.097 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:33:27.098 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={questionnaires=[{name=疼痛相关不适评估, questions=[{result=[], isRequired=true, sortOrder=1 (truncated)...]
2025-07-25 20:33:27.124 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:33:42.482 [http-nio-0.0.0.0-18923-exec-3] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:33:42.482 [http-nio-0.0.0.0-18923-exec-3] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:33:42.484 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:33:42.485 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:33:42.485 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:33:42.512 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.513 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:33:42.513 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:33:42.543 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.548 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:33:42.548 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:33:42.573 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.575 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:33:42.575 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:33:42.598 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:33:42.600 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:33:42.601 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:33:42.619 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.621 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:33:42.622 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:33:42.641 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.645 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:33:42.646 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:33:42.672 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.673 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:33:42.673 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:33:42.693 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:33:42.697 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:33:42.697 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:33:42.720 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:33:42.723 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:33:42.723 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:33:42.743 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.744 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:33:42.744 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:33:42.766 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.769 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:33:42.769 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:33:42.788 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:33:42.789 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:33:42.789 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:33:42.805 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:33:42.805 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:33:42.805 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:33:42.822 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:33:42.823 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:33:42.823 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, desc=, status=0}, {date=07月27日 (truncated)...]
2025-07-25 20:33:42.824 [http-nio-0.0.0.0-18923-exec-3] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:33:53.019 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:33:53.019 [http-nio-0.0.0.0-18923-exec-4] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:33:53.019 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:33:53.020 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:33:53.020 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:33:53.042 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:33:53.044 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:33:53.044 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:33:53.070 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:33:53.071 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:33:53.071 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:33:53.097 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:33:53.097 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:33:53.098 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:33:53.117 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:33:53.118 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:33:53.118 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:33:53.143 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:33:53.144 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:33:53.145 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:33:53.169 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:33:53.170 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:33:53.171 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:33:53.196 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:33:53.197 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:33:53.197 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:33:53.219 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:33:53.220 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:33:53.220 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={date=07月24日, personName=患者, number=第1次计划, name=高血压随访计划, planId=34, projectItems=[{s (truncated)...]
2025-07-25 20:33:53.221 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:34:00.329 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:34:00.329 [http-nio-0.0.0.0-18923-exec-5] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:34:00.330 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:34:00.330 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:34:00.330 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:34:00.350 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:34:00.351 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:34:00.351 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:34:00.376 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:34:00.377 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:34:00.377 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:34:00.401 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:34:00.401 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:34:00.402 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:34:00.417 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:34:00.417 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:34:00.418 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:34:00.438 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:34:00.439 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:34:00.439 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:34:00.462 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:34:00.463 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:34:00.463 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:34:00.485 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:34:00.486 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:34:00.486 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:34:00.511 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:34:00.512 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:34:00.512 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={date=07月24日, personName=患者, number=第1次计划, name=高血压随访计划, planId=34, projectItems=[{s (truncated)...]
2025-07-25 20:34:00.513 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:36:39.691 [http-nio-0.0.0.0-18923-exec-8] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:36:39.691 [http-nio-0.0.0.0-18923-exec-8] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:36:39.692 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:36:39.713 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:36:39.714 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:36:39.739 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.740 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:36:39.740 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:36:39.768 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.769 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:36:39.770 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:36:39.793 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.794 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:36:39.794 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:36:39.822 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:36:39.823 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:36:39.823 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:36:39.842 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.844 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:36:39.844 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:36:39.863 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.864 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:36:39.864 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:36:39.886 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.887 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:36:39.887 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:36:39.905 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:36:39.905 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:36:39.905 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:36:39.925 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:36:39.926 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:36:39.926 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:36:39.946 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.947 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:36:39.947 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:36:39.964 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.965 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:36:39.965 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:36:39.984 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:36:39.985 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:36:39.985 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:36:40.003 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:36:40.004 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:36:40.004 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:36:40.024 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:36:40.024 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:36:40.024 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, desc=, status=0}, {date=07月27日 (truncated)...]
2025-07-25 20:36:40.025 [http-nio-0.0.0.0-18923-exec-8] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:43:24.662 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:43:24.675 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 24904 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:43:24.676 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:43:24.676 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.162 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.163 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:43:25.164 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:43:25.305 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:43:25.506 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:43:25.511 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:43:25.512 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:43:25.512 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:43:25.591 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:43:25.591 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 887 ms
2025-07-25 20:43:25.763 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@660f0c'
2025-07-25 20:43:25.835 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:43:25.859 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:43:25.874 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:43:25.889 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:43:25.904 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:43:25.918 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:43:25.931 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:43:25.947 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:43:25.959 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:43:25.977 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:43:25.987 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:43:26.000 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:43:26.013 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:43:26.017 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:43:26.018 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:43:26.024 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:43:26.036 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:43:26.051 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:43:26.060 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:43:26.070 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:43:26.080 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:43:26.088 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:43:26.097 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:43:26.104 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:43:26.111 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:43:26.119 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:43:26.129 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:43:26.138 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:43:26.957 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:43:26.996 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
2025-07-25 20:43:26.999 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
2025-07-25 20:43:27.001 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
2025-07-25 20:43:27.002 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
2025-07-25 20:43:27.002 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:43:27.003 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
2025-07-25 20:43:27.004 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
2025-07-25 20:43:27.005 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
2025-07-25 20:43:27.006 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:43:27.007 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
	{GET [/emr/image-text/{id}]}: getRecord(Long)
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
2025-07-25 20:43:27.007 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:43:27.008 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
2025-07-25 20:43:27.008 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
	{ [/emr/nhpatient/list]}: getPatientList(Map)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
2025-07-25 20:43:27.009 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
2025-07-25 20:43:27.010 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
2025-07-25 20:43:27.011 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{GET [/emr/patient/{id}]}: getPatient(Long)
	{POST [/emr/patient]}: createPatient(PatientDTO)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
2025-07-25 20:43:27.012 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
2025-07-25 20:43:27.013 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
2025-07-25 20:43:27.014 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
2025-07-25 20:43:27.014 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
	{GET [/emr/questionnaire-options]}: getAllOptions()
2025-07-25 20:43:27.015 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
2025-07-25 20:43:27.015 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{GET [/emr/questionnaire-rules]}: getAllRules()
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
2025-07-25 20:43:27.015 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
2025-07-25 20:43:27.016 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
2025-07-25 20:43:27.017 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
	{POST [/scheduled/{name}/run]}: runScheduled(String)
	{GET [/scheduled/log/files]}: logFiles()
2025-07-25 20:43:27.017 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
2025-07-25 20:43:27.017 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
2025-07-25 20:43:27.017 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
2025-07-25 20:43:27.018 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
	{GET [/scheduled/name/run]}: getRunScheduledName()
2025-07-25 20:43:27.018 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-25 20:43:27.019 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:43:27.033 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:43:27.038 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:43:27.123 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:43:27.133 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:43:27.141 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 2.747 seconds (JVM running for 3.307)
2025-07-25 20:43:27.142 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:46:48.511 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:46:48.526 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 16612 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:46:48.526 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:46:48.526 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:46:49.008 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.009 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.010 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.010 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:46:49.010 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:46:49.150 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:46:49.334 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:46:49.340 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:46:49.340 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:46:49.340 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:46:49.415 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:46:49.415 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 863 ms
2025-07-25 20:46:49.584 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@7a0ef219'
2025-07-25 20:46:49.663 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:46:49.684 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:46:49.700 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:46:49.716 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:46:49.732 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:46:49.745 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:46:49.759 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:46:49.774 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:46:49.786 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:46:49.806 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:46:49.816 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:46:49.828 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:46:49.844 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:46:49.848 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:46:49.848 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:46:49.855 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:46:49.867 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:46:49.883 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:46:49.893 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:46:49.902 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:46:49.913 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:46:49.922 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:46:49.932 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:46:49.941 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:46:49.949 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:46:49.957 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:46:49.966 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:46:49.977 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:46:50.768 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:46:50.803 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
2025-07-25 20:46:50.806 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
2025-07-25 20:46:50.808 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
2025-07-25 20:46:50.809 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
2025-07-25 20:46:50.809 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:46:50.810 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
2025-07-25 20:46:50.811 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
2025-07-25 20:46:50.812 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
2025-07-25 20:46:50.813 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:46:50.814 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{GET [/emr/image-text/{id}]}: getRecord(Long)
2025-07-25 20:46:50.814 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:46:50.815 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
2025-07-25 20:46:50.815 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{ [/emr/nhpatient/list]}: getPatientList(Map)
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
2025-07-25 20:46:50.815 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
2025-07-25 20:46:50.816 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
2025-07-25 20:46:50.817 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
	{GET [/emr/patient/{id}]}: getPatient(Long)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{POST [/emr/patient]}: createPatient(PatientDTO)
2025-07-25 20:46:50.817 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
2025-07-25 20:46:50.818 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
2025-07-25 20:46:50.819 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
2025-07-25 20:46:50.820 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
	{GET [/emr/questionnaire-options]}: getAllOptions()
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
2025-07-25 20:46:50.820 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
2025-07-25 20:46:50.821 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{GET [/emr/questionnaire-rules]}: getAllRules()
2025-07-25 20:46:50.821 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
2025-07-25 20:46:50.822 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
2025-07-25 20:46:50.823 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
	{POST [/scheduled/{name}/run]}: runScheduled(String)
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{GET [/scheduled/log/files]}: logFiles()
2025-07-25 20:46:50.823 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
2025-07-25 20:46:50.823 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
2025-07-25 20:46:50.823 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
2025-07-25 20:46:50.824 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/run]}: getRunScheduledName()
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
2025-07-25 20:46:50.824 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-07-25 20:46:50.825 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:46:50.839 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:46:50.844 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:46:50.926 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:46:50.935 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:46:50.942 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 2.7 seconds (JVM running for 3.25)
2025-07-25 20:46:50.943 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:46:55.779 [http-nio-0.0.0.0-18923-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 20:46:55.779 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 20:46:55.779 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-25 20:46:55.779 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-25 20:46:55.779 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-25 20:46:55.780 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@65e22def
2025-07-25 20:46:55.780 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@6b6eae52
2025-07-25 20:46:55.780 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-25 20:46:55.780 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-25 20:46:55.785 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:46:55.787 [http-nio-0.0.0.0-18923-exec-1] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:46:55.818 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:46:55.889 [http-nio-0.0.0.0-18923-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 20:46:56.129 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:46:56.141 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:46:56.185 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.187 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:46:56.187 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:46:56.214 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.221 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:46:56.222 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:46:56.252 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.256 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:46:56.256 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:46:56.284 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:46:56.285 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:46:56.285 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:46:56.305 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.306 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:46:56.307 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:46:56.326 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.329 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:46:56.329 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:46:56.357 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.361 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:46:56.362 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:46:56.395 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:46:56.398 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:46:56.398 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:46:56.439 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:46:56.440 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:46:56.440 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:46:56.459 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.461 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:46:56.461 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:46:56.481 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.483 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:46:56.483 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:46:56.503 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:46:56.504 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:46:56.504 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:46:56.526 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:46:56.527 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:46:56.527 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:46:56.551 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:46:56.559 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:46:56.560 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, subplanId=44, desc=, status=0} (truncated)...]
2025-07-25 20:46:56.572 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:47:09.067 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-answers", parameters={}
2025-07-25 20:47:09.067 [http-nio-0.0.0.0-18923-exec-4] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getQuestionnaireAnswers(Map)
2025-07-25 20:47:09.067 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34}]
2025-07-25 20:47:09.069 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:47:09.069 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:47:09.096 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:47:09.097 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:47:09.098 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:47:09.122 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:47:09.125 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:47:09.125 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:47:09.149 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - <==      Total: 1
2025-07-25 20:47:09.149 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:47:09.149 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:47:09.171 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:47:09.173 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:47:09.174 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:47:09.197 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:47:09.199 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:47:09.199 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:47:09.220 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:47:09.223 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:47:09.223 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:47:09.242 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:47:09.243 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:47:09.244 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:47:09.267 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:47:09.267 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:47:09.268 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:47:09.291 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:47:09.293 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:47:09.293 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 1(Long), 1(Long)
2025-07-25 20:47:09.322 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:47:09.323 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:47:09.324 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:47:09.348 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:47:09.348 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:47:09.348 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:47:09.373 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:47:09.373 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:47:09.373 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:47:09.394 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:47:09.394 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:47:09.394 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:47:09.415 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:47:09.416 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:47:09.416 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 2(Long), 2(Long), 1(Long)
2025-07-25 20:47:09.431 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:47:09.433 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:47:09.433 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:47:09.448 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:47:09.450 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?)
2025-07-25 20:47:09.450 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:47:09.466 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:47:09.466 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:47:09.466 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={questionnaires=[{name=疼痛相关不适评估, questions=[{result=[], isRequired=true, sortOrder=1 (truncated)...]
2025-07-25 20:47:09.480 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:47:14.651 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:47:14.651 [http-nio-0.0.0.0-18923-exec-5] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:47:14.652 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:47:14.653 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:47:14.653 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:47:14.678 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:47:14.679 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:47:14.679 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:47:14.700 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:47:14.702 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:47:14.702 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:47:14.724 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:47:14.726 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:47:14.726 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:47:14.756 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:47:14.757 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:47:14.757 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:47:14.779 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:47:14.780 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:47:14.780 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:47:14.806 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:47:14.808 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:47:14.808 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:47:14.831 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:47:14.832 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:47:14.832 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:47:14.852 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:47:14.852 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:47:14.852 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:47:14.871 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:47:14.873 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:47:14.873 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:47:14.891 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:47:14.893 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:47:14.893 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:47:14.917 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:47:14.918 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:47:14.918 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:47:14.935 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:47:14.935 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:47:14.936 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={personName=患者, date=07月24日, number=全部计划（共2次）, name=高血压随访计划, subplanIds=[44, 45], pl (truncated)...]
2025-07-25 20:47:14.936 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:48:39.875 [http-nio-0.0.0.0-18923-exec-7] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:48:39.875 [http-nio-0.0.0.0-18923-exec-7] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:48:39.876 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:48:39.899 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:48:39.900 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:48:39.923 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:48:39.924 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:48:39.924 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:48:39.945 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:48:39.946 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:48:39.946 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:48:39.972 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:48:39.973 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:48:39.973 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:48:40.001 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:48:40.001 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:48:40.001 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:48:40.028 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:48:40.028 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:48:40.029 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:48:40.052 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:48:40.053 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:48:40.054 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:48:40.080 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:48:40.081 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:48:40.081 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:48:40.103 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:48:40.104 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:48:40.104 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:48:40.128 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:48:40.130 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:48:40.130 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:48:40.152 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:48:40.153 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:48:40.153 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:48:40.180 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:48:40.181 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:48:40.181 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:48:40.206 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:48:40.207 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:48:40.207 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={personName=患者, date=07月24日, number=全部计划（共2次）, name=高血压随访计划, subplanIds=[44, 45], pl (truncated)...]
2025-07-25 20:48:40.207 [http-nio-0.0.0.0-18923-exec-7] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:49:15.458 [http-nio-0.0.0.0-18923-exec-8] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:49:15.459 [http-nio-0.0.0.0-18923-exec-8] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:49:15.459 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:49:15.460 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:49:15.461 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:49:15.487 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.488 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:49:15.488 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:49:15.517 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.519 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:49:15.519 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:49:15.545 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.546 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:49:15.547 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:49:15.566 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:49:15.567 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:49:15.567 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:49:15.590 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.591 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:49:15.591 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:49:15.612 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.614 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:49:15.614 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:49:15.638 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.639 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:49:15.640 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:49:15.668 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:49:15.669 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:49:15.670 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:49:15.693 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:49:15.695 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:49:15.695 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:49:15.719 [http-nio-0.0.0.0-18923-exec-8] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.720 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:49:15.721 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:49:15.743 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.745 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:49:15.745 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:49:15.762 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:49:15.764 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:49:15.764 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:49:15.784 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:49:15.784 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:49:15.785 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:49:15.805 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:49:15.806 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:49:15.806 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, subplanId=44, desc=, status=0} (truncated)...]
2025-07-25 20:49:15.807 [http-nio-0.0.0.0-18923-exec-8] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:50:35.267 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:50:35.267 [http-nio-0.0.0.0-18923-exec-2] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:50:35.268 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:50:35.292 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:50:35.292 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:50:35.315 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:50:35.316 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:50:35.316 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:50:35.343 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:50:35.344 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:50:35.344 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:50:35.372 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:50:35.374 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:50:35.374 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:50:35.402 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:50:35.403 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:50:35.403 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:50:35.424 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:50:35.425 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:50:35.426 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:50:35.452 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:50:35.454 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:50:35.454 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:50:35.474 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:50:35.475 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:50:35.475 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:50:35.495 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:50:35.496 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:50:35.497 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:50:35.515 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:50:35.517 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:50:35.517 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:50:35.536 [http-nio-0.0.0.0-18923-exec-2] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:50:35.538 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:50:35.538 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:50:35.554 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:50:35.555 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:50:35.555 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:50:35.579 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:50:35.581 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:50:35.581 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={personName=患者, date=07月24日, number=全部计划（共2次）, name=高血压随访计划, subplanIds=[44, 45], pl (truncated)...]
2025-07-25 20:50:35.582 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:52:07.492 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 20:52:07.507 [main] INFO  com.nx.inf.MainApplication - Starting MainApplication using Java 1.8.0_281 on DESKTOP-CAIV21I with PID 26728 (C:\work\4th\svn\hlyyyintface\target\classes started by 机械师L15C in C:\work\4th\svn)
2025-07-25 20:52:07.508 [main] DEBUG com.nx.inf.MainApplication - Running with Spring Boot v2.7.2, Spring v5.3.22
2025-07-25 20:52:07.508 [main] INFO  com.nx.inf.MainApplication - The following 1 profile is active: "prod"
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateCategoryMapper' and 'com.nx.inf.mapper.ContentTemplateCategoryMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'contentTemplateMapper' and 'com.nx.inf.mapper.ContentTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpExecutionMapper' and 'com.nx.inf.mapper.FollowUpExecutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpPlanDetailMapper' and 'com.nx.inf.mapper.FollowUpPlanDetailMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followUpTemplateMapper' and 'com.nx.inf.mapper.FollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupCUserMapper' and 'com.nx.inf.mapper.FollowupCUserMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupPlanMapper' and 'com.nx.inf.mapper.FollowupPlanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanItemMapper' and 'com.nx.inf.mapper.FollowupSubplanItemMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanMapper' and 'com.nx.inf.mapper.FollowupSubplanMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupSubplanQuestionnaireMapper' and 'com.nx.inf.mapper.FollowupSubplanQuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'followupTaskMapper' and 'com.nx.inf.mapper.FollowupTaskMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'formTemplateMapper' and 'com.nx.inf.mapper.FormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'imageTextRecordMapper' and 'com.nx.inf.mapper.ImageTextRecordMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'internetPatientMapper' and 'com.nx.inf.mapper.InternetPatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.043 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhHospitalizationInfoMapper' and 'com.nx.inf.mapper.NhHospitalizationInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhPatientInfoMapper' and 'com.nx.inf.mapper.NhPatientInfoMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nhReferralAuditMapper' and 'com.nx.inf.mapper.NhReferralAuditMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'nursingHomeInstitutionMapper' and 'com.nx.inf.mapper.NursingHomeInstitutionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupMapper' and 'com.nx.inf.mapper.PatientGroupMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientGroupRelationMapper' and 'com.nx.inf.mapper.PatientGroupRelationMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'patientMapper' and 'com.nx.inf.mapper.PatientMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireAnswerMapper' and 'com.nx.inf.mapper.QuestionnaireAnswerMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireMapper' and 'com.nx.inf.mapper.QuestionnaireMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireOptionMapper' and 'com.nx.inf.mapper.QuestionnaireOptionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireQuestionMapper' and 'com.nx.inf.mapper.QuestionnaireQuestionMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'questionnaireRuleMapper' and 'com.nx.inf.mapper.QuestionnaireRuleMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFollowUpTemplateMapper' and 'com.nx.inf.mapper.SmartFollowUpTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormOtherDataMapper' and 'com.nx.inf.mapper.SmartFormOtherDataMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormQuestionnaireRelMapper' and 'com.nx.inf.mapper.SmartFormQuestionnaireRelMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.044 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'smartFormTemplateMapper' and 'com.nx.inf.mapper.SmartFormTemplateMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 20:52:08.045 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.nx.inf.mapper]' package. Please check your configuration.
2025-07-25 20:52:08.206 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'superScheduledConfig' of type [com.gyx.superscheduled.core.SuperScheduledConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 20:52:08.407 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 18923 (http)
2025-07-25 20:52:08.413 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:52:08.413 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 20:52:08.413 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.65]
2025-07-25 20:52:08.502 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 20:52:08.502 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 962 ms
2025-07-25 20:52:08.696 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@660f0c'
2025-07-25 20:52:08.776 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateCategoryMapper.xml]'
2025-07-25 20:52:08.799 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ContentTemplateMapper.xml]'
2025-07-25 20:52:08.815 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpExecutionMapper.xml]'
2025-07-25 20:52:08.832 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanDetailMapper.xml]'
2025-07-25 20:52:08.849 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpPlanMapper.xml]'
2025-07-25 20:52:08.865 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowUpTemplateMapper.xml]'
2025-07-25 20:52:08.880 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanItemMapper.xml]'
2025-07-25 20:52:08.897 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanMapper.xml]'
2025-07-25 20:52:08.911 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupSubplanQuestionnaireMapper.xml]'
2025-07-25 20:52:08.938 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FollowupTaskMapper.xml]'
2025-07-25 20:52:08.950 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\FormTemplateMapper.xml]'
2025-07-25 20:52:08.964 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\ImageTextRecordMapper.xml]'
2025-07-25 20:52:08.980 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhHospitalizationInfoMapper.xml]'
2025-07-25 20:52:08.986 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.nx.inf.model.entity.NhPatientInfo".
2025-07-25 20:52:08.986 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.nx.inf.model.entity.NhPatientInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-25 20:52:08.993 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhPatientInfoMapper.xml]'
2025-07-25 20:52:09.005 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NhReferralAuditMapper.xml]'
2025-07-25 20:52:09.019 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\NursingHomeInstitutionMapper.xml]'
2025-07-25 20:52:09.030 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientGroupMapper.xml]'
2025-07-25 20:52:09.040 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\PatientMapper.xml]'
2025-07-25 20:52:09.052 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireAnswerMapper.xml]'
2025-07-25 20:52:09.062 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireMapper.xml]'
2025-07-25 20:52:09.071 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireOptionMapper.xml]'
2025-07-25 20:52:09.079 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireQuestionMapper.xml]'
2025-07-25 20:52:09.087 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\QuestionnaireRuleMapper.xml]'
2025-07-25 20:52:09.096 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormOtherDataMapper.xml]'
2025-07-25 20:52:09.105 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormQuestionnaireRelMapper.xml]'
2025-07-25 20:52:09.116 [main] DEBUG c.b.m.e.spring.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [C:\work\4th\svn\hlyyyintface\target\classes\mapper\SmartFormTemplateMapper.xml]'
2025-07-25 20:52:10.001 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-07-25 20:52:10.040 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ContentTemplateController:
	{POST [/emr/content-template/getTemplateById]}: getTemplateById(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplateList]}: getTemplateList(ContentTemplateRequestVO)
	{POST [/emr/content-template/batchDeleteTemplates]}: batchDeleteTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplateStatus]}: updateTemplateStatus(ContentTemplateRequestVO)
	{POST [/emr/content-template/getTemplatesByCategory]}: getTemplatesByCategory(ContentTemplateRequestVO)
	{POST [/emr/content-template/createTemplate]}: createTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/deleteTemplate]}: deleteTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/getCategoryList]}: getCategoryList(Map)
	{POST [/emr/content-template/copyTemplate]}: copyTemplate(ContentTemplateRequestVO)
	{POST [/emr/content-template/searchTemplates]}: searchTemplates(ContentTemplateRequestVO)
	{POST [/emr/content-template/updateTemplate]}: updateTemplate(ContentTemplateRequestVO)
2025-07-25 20:52:10.044 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.CustomerFollowupController:
	{POST [/emr/followup/customer/plan-list]}: getFollowupPlanList(Map)
	{POST [/emr/followup/customer/plan-detail]}: getFollowupPlanDetail(Map)
	{POST [/emr/followup/customer/questionnaire-list]}: getQuestionnaireList(Map)
	{POST [/emr/followup/customer/questionnaire-submit]}: submitQuestionnaire(Map)
	{POST [/emr/followup/customer/questionnaire-answers]}: getQuestionnaireAnswers(Map)
	{POST [/emr/followup/customer/user-info]}: getUserInfo(Map)
2025-07-25 20:52:10.045 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.EmrController:
	{POST [/emr/get-imaging-url]}: getImagingSystemUrl(MedicalImagingRequestVO)
	{POST [/emr/patient-card]}: getPatientCardByIdCard(PatientCardRequestVO)
	{POST [/emr/inpatient-records]}: getInpatientRecords(MedicalRequestVO)
	{POST [/emr/examination-reports]}: getExaminationReports(MedicalRequestVO)
	{POST [/emr/outpatient-records]}: getOutpatientRecords(MedicalRequestVO)
	{POST [/emr/delete-health-record]}: deleteHealthRecord(HealthDeleteRequestVO)
	{POST [/emr/redirect-imaging]}: redirectToImagingSystem(MedicalImagingRequestVO)
	{POST [/emr/health-measurement]}: addHealthMeasurement(HealthMeasurementVO)
	{POST [/emr/phone-by-idcard]}: getPhoneByIdCard(IdCardRequestVO)
	{POST [/emr/test-reports]}: getTestReports(MedicalRequestVO)
	{POST [/emr/health-records]}: getHealthRecords(HealthQueryRequestVO)
2025-07-25 20:52:10.046 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpExecutionController:
	{GET [/emr/followup/execution/patient/{patientId}]}: getExecutionsByPatientId(Long)
	{PUT [/emr/followup/execution/{id}/status]}: updateExecutionStatus(Long,String)
	{GET [/emr/followup/execution/plan/{planId}]}: getExecutionsByPlanId(Long)
	{PUT [/emr/followup/execution/{id}]}: updateFollowUpExecution(Long,FollowUpExecutionDTO)
	{POST [/emr/followup/execution]}: createFollowUpExecution(FollowUpExecutionDTO)
	{GET [/emr/followup/execution/plan/{planId}/patients]}: getPatientsByPlanId(Long)
	{GET [/emr/followup/execution/{id}]}: getFollowUpExecution(Long)
	{GET [/emr/followup/execution/page]}: getExecutionPage(int,int,Long,String,Boolean)
2025-07-25 20:52:10.046 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowUpTemplateController:
	{GET [/emr/followup/template/list]}: getTemplateList(String,String)
2025-07-25 20:52:10.047 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupCUserController:
	{POST [/emr/followup/cuser/auth]}: auth(Map)
	{GET [/emr/followup/cuser/getByPhone]}: getUserByPhone(String)
	{PUT [/emr/followup/cuser/updateLoginInfo]}: updateLoginInfo(Map)
	{GET [/emr/followup/cuser/page]}: getUserPage(int,int,String,String,String,String)
	{PUT [/emr/followup/cuser/updateUserInfo]}: updateUserInfo(FollowupCUser)
	{POST [/emr/followup/cuser/confirm-group]}: confirmGroup(Map)
	{POST [/emr/followup/cuser/register]}: register(FollowupCUser)
	{GET [/emr/followup/cuser/list]}: list(int,int,String,String)
	{POST [/emr/followup/cuser/bind]}: bind(Map)
2025-07-25 20:52:10.048 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanCController:
	{GET [/emr/followup/plan/c/patient-plans]}: getPatientPlans(Long,String,Integer,Integer)
	{POST [/emr/followup/plan/c/join-group]}: joinGroup(Map)
	{GET [/emr/followup/plan/c/group-status/{patientId}]}: getGroupStatus(Long)
	{GET [/emr/followup/plan/c/plan-detail/{planId}]}: getPlanDetail(Long)
	{GET [/emr/followup/plan/c/patient-info/{patientId}]}: getPatientInfo(Long)
	{GET [/emr/followup/plan/c/patient-tasks/{patientId}]}: getPatientTasks(Long,String,Integer,Integer)
	{GET [/emr/followup/plan/c/validate-qr]}: validateQRCode(String,Long,String)
	{GET [/emr/followup/plan/c/patient-stats/{patientId}]}: getPatientStats(Long)
2025-07-25 20:52:10.050 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FollowupPlanController:
	{POST [/emr/followup/plan/update-all-statistics]}: updateAllPlanStatistics()
	{GET [/emr/followup/plan/{id}/statistics]}: getPlanStatistics(Long)
	{POST [/emr/followup/plan/{id}/update-statistics]}: updatePlanStatistics(Long)
	{POST [/emr/followup/plan]}: createPlan(FollowupPlanDTO)
	{GET [/emr/followup/plan/page]}: getPlanPage(Integer,Integer,String,String,String)
	{POST [/emr/followup/plan/{id}/qrcode]}: generateQRCode(Long)
	{GET [/emr/followup/plan/{id}]}: getPlanById(Long)
	{PUT [/emr/followup/plan/{id}/status]}: updatePlanStatus(Long,Map)
	{PUT [/emr/followup/plan/{id}]}: updatePlan(Long,FollowupPlanDTO)
	{DELETE [/emr/followup/plan/{id}]}: deletePlan(Long)
2025-07-25 20:52:10.051 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.FormTemplateController:
	{GET [/emr/form/template/list]}: getTemplateList(String,String)
2025-07-25 20:52:10.051 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.ImageTextRecordController:
	{GET [/emr/image-text/creator/{creatorId}]}: getRecordsByCreatorId(Long)
	{GET [/emr/image-text/medical-ocr-status]}: checkMedicalOCRStatus()
	{POST [/emr/image-text/upload]}: uploadAndRecognize(MultipartFile,Long,String)
	{GET [/emr/image-text/status/{status}]}: getRecordsByStatus(String)
	{POST [/emr/image-text/medical-report-test]}: testMedicalReport(MultipartFile)
	{GET [/emr/image-text/ocr-status]}: checkOCRStatus()
	{GET [/emr/image-text/page]}: getRecordPage(int,int,String,Long,String)
	{GET [/emr/image-text/{id}]}: getRecord(Long)
	{POST [/emr/image-text/{id}/re-recognize]}: reRecognize(Long)
	{POST [/emr/image-text/ocr-test]}: testBaiduOCR(MultipartFile)
	{DELETE [/emr/image-text/{id}]}: deleteRecord(Long)
2025-07-25 20:52:10.052 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.InternetPatientController:
	{POST [/emr/patient/list]}: getPatientList(PatientListRequestVO)
2025-07-25 20:52:10.052 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhHospitalizationInfoController:
	{GET [/emr/hospitalization/statistics/department]}: getDepartmentStatistics()
	{GET [/emr/hospitalization/admission-records]}: getAdmissionRecords(LocalDateTime,LocalDateTime)
	{GET [/emr/hospitalization/page]}: page(long,long)
	{GET [/emr/hospitalization/{id}]}: getById(Long)
	{GET [/emr/hospitalization/patient/{patientId}]}: getByPatientId(String)
	{DELETE [/emr/hospitalization/{id}]}: remove(Long)
	{POST [/emr/hospitalization/update]}: update(NhHospitalizationInfo)
	{POST [/emr/hospitalization/save]}: save(NhHospitalizationInfo)
2025-07-25 20:52:10.053 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhPatientInfoController:
	{DELETE [/emr/nhpatient/{patientId}]}: deletePatient(String)
	{ [/emr/nhpatient/list]}: getPatientList(Map)
	{GET [/emr/nhpatient/{patientId}]}: getPatientById(String)
	{POST [/emr/nhpatient]}: addPatient(NhPatientInfo)
	{PUT [/emr/nhpatient]}: updatePatient(NhPatientInfo)
2025-07-25 20:52:10.053 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NhReferralAuditController:
	{POST [/emr/referral-audit]}: createReferralAudit(NhReferralAudit)
	{GET [/emr/referral-audit/{id}]}: getReferralAuditDetail(Long)
	{POST [/emr/referral-audit/list]}: getReferralAuditList(Map)
	{PUT [/emr/referral-audit/{id}/audit]}: updateAuditStatus(String,Map)
2025-07-25 20:52:10.054 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.NursingHomeInstitutionController:
	{POST [/emr/nursing-home/createInstitution]}: createInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchDeleteInstitutions]}: batchDeleteInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitutionStatus]}: updateInstitutionStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getInstitutionById]}: getInstitutionById(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getAllActiveInstitutions]}: getAllActiveInstitutions(Map)
	{POST [/emr/nursing-home/getInstitutionList]}: getInstitutionList(NursingHomeRequestVO)
	{POST [/emr/nursing-home/deleteInstitution]}: deleteInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/batchUpdateStatus]}: batchUpdateStatus(NursingHomeRequestVO)
	{POST [/emr/nursing-home/countInstitutions]}: countInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateCurrentPatients]}: updateCurrentPatients(NursingHomeRequestVO)
	{POST [/emr/nursing-home/getNearbyInstitutions]}: getNearbyInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/exportInstitutions]}: exportInstitutions(NursingHomeRequestVO)
	{POST [/emr/nursing-home/updateInstitution]}: updateInstitution(NursingHomeRequestVO)
	{POST [/emr/nursing-home/checkCodeExists]}: checkCodeExists(NursingHomeRequestVO)
	{POST [/emr/nursing-home/copyInstitution]}: copyInstitution(NursingHomeRequestVO)
2025-07-25 20:52:10.055 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientController:
	{PUT [/emr/patient/{id}/status]}: updatePatientStatus(Long,String,Long,String,Map)
	{PUT [/emr/patient/status/batch]}: batchUpdatePatientStatus(String,Long,String,List)
	{DELETE [/emr/patient/{id}]}: deletePatient(Long)
	{PUT [/emr/patient/{id}]}: updatePatient(Long,PatientDTO)
	{GET [/emr/patient/page]}: getPatientPage(int,int,String,String,String,String,String)
	{POST [/emr/patient]}: createPatient(PatientDTO)
	{GET [/emr/patient/{id}]}: getPatient(Long)
2025-07-25 20:52:10.055 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.PatientGroupController:
	{POST [/emr/patient/group/audit]}: auditPatientJoinRequest(Long,String,Long,String,String)
	{GET [/emr/patient/group/{groupId}/patients]}: getPatientsByGroupId(Long,int,int,String,String,String,String,String)
	{POST [/emr/patient/group/{groupId}/patients]}: addPatientsToGroup(Long,String,List)
	{POST [/emr/patient/group]}: createPatientGroup(PatientGroupDTO)
	{POST [/emr/patient/group/audit/batch]}: batchAuditPatientJoinRequest(List,String,Long,String,String)
	{DELETE [/emr/patient/group/{groupId}/patients]}: removePatientsFromGroup(Long,List)
	{PUT [/emr/patient/group/{id}]}: updatePatientGroup(Long,PatientGroupDTO)
	{DELETE [/emr/patient/group/{id}]}: deletePatientGroup(Long)
	{GET [/emr/patient/group/all-with-status]}: getAllGroupsWithJoinStatus(Long)
	{GET [/emr/patient/group/page]}: getPatientGroupPage(int,int,String)
	{GET [/emr/patient/group/patients]}: getAllPatients(int,int,String,String,String,String,String,Long)
	{GET [/emr/patient/group/{id}]}: getPatientGroup(Long)
2025-07-25 20:52:10.057 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireAnswerController:
	{POST [/emr/questionnaire-answers/save]}: saveAnswer(QuestionnaireAnswer)
	{GET [/emr/questionnaire-answers/score]}: calculateQuestionnaireScore(Long,Long)
	{GET [/emr/questionnaire-answers/completed]}: isQuestionnaireCompleted(Long,Long,Long)
	{GET [/emr/questionnaire-answers/stats/{patientId}]}: getPatientQuestionnaireStats(Long)
	{GET [/emr/questionnaire-answers/by-task/{taskId}]}: getAnswersByTaskId(Long)
	{GET [/emr/questionnaire-answers/by-patient/{patientId}]}: getAnswersByPatientId(Long)
	{GET [/emr/questionnaire-answers/completion-rate/{patientId}]}: getPatientCompletionRate(Long)
	{GET [/emr/questionnaire-answers/by-patient-questionnaire]}: getAnswersByPatientAndQuestionnaire(Long,Long)
	{POST [/emr/questionnaire-answers/batch-save]}: batchSaveAnswers(List)
	{GET [/emr/questionnaire-answers/page]}: getAnswerPage(Integer,Integer,Long,Long,Long,String,String)
	{DELETE [/emr/questionnaire-answers/{id}]}: deleteAnswer(Long)
	{GET [/emr/questionnaire-answers/{id}]}: getAnswerById(Long)
2025-07-25 20:52:10.057 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireController:
	{GET [/emr/questionnaires/{id}]}: getQuestionnaireById(Long)
	{POST [/emr/questionnaires]}: createQuestionnaire(Questionnaire)
	{DELETE [/emr/questionnaires/{id}]}: deleteQuestionnaire(Long)
	{GET [/emr/questionnaires/with-details/{id}]}: getQuestionnaireWithDetails(Long)
	{GET [/emr/questionnaires/with-details]}: getAllQuestionnairesWithDetails()
	{PUT [/emr/questionnaires/with-details/{id}]}: updateQuestionnaireWithDetails(Long,QuestionnaireDTO)
	{POST [/emr/questionnaires/with-details]}: createQuestionnaireWithDetails(QuestionnaireDTO)
	{PUT [/emr/questionnaires/{id}]}: updateQuestionnaire(Long,Questionnaire)
	{GET [/emr/questionnaires]}: getAllQuestionnaires()
2025-07-25 20:52:10.058 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireOptionController:
	{GET [/emr/questionnaire-options/by-question/{questionId}]}: getOptionsByQuestionId(Long)
	{GET [/emr/questionnaire-options/{id}]}: getOptionById(Long)
	{POST [/emr/questionnaire-options]}: createOption(QuestionnaireOption)
	{DELETE [/emr/questionnaire-options/{id}]}: deleteOption(Long)
	{PUT [/emr/questionnaire-options/{id}]}: updateOption(Long,QuestionnaireOption)
	{GET [/emr/questionnaire-options]}: getAllOptions()
2025-07-25 20:52:10.058 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireQuestionController:
	{GET [/emr/questionnaire-questions/by-questionnaire/{questionnaireId}]}: getQuestionsByQuestionnaireId(Long)
	{DELETE [/emr/questionnaire-questions/{id}]}: deleteQuestion(Long)
	{GET [/emr/questionnaire-questions/{id}]}: getQuestionById(Long)
	{POST [/emr/questionnaire-questions]}: createQuestion(QuestionnaireQuestion)
	{GET [/emr/questionnaire-questions]}: getAllQuestions()
	{PUT [/emr/questionnaire-questions/{id}]}: updateQuestion(Long,QuestionnaireQuestion)
2025-07-25 20:52:10.059 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.QuestionnaireRuleController:
	{GET [/emr/questionnaire-rules/by-question/{questionId}]}: getRulesByQuestionId(Long)
	{PUT [/emr/questionnaire-rules/{id}]}: updateRule(Long,QuestionnaireRule)
	{GET [/emr/questionnaire-rules/{id}]}: getRuleById(Long)
	{DELETE [/emr/questionnaire-rules/{id}]}: deleteRule(Long)
	{POST [/emr/questionnaire-rules]}: createRule(QuestionnaireRule)
	{GET [/emr/questionnaire-rules]}: getAllRules()
2025-07-25 20:52:10.059 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFollowUpTemplateController:
	{PUT [/emr/template/smart-followup/{id}/status]}: updateTemplateStatus(Long,Map)
	{POST [/emr/template/smart-followup]}: createTemplate(SmartFollowUpTemplateDTO)
	{DELETE [/emr/template/smart-followup/{id}]}: deleteTemplate(Long)
	{PUT [/emr/template/smart-followup/{id}]}: updateTemplate(Long,SmartFollowUpTemplateDTO)
	{GET [/emr/template/smart-followup/{id}]}: getTemplate(Long)
	{GET [/emr/template/smart-followup/page]}: getTemplatePage(int,int,String,String,Integer)
2025-07-25 20:52:10.060 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.n.i.c.SmartFormController:
	{POST [/emr/smart-form/updateFormQuestionnaire]}: updateFormQuestionnaire(Map)
	{POST [/emr/smart-form/createFormTemplate]}: createFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/removeFormQuestionnaire]}: removeFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormSubmissionList]}: getFormSubmissionList(Map)
	{POST [/emr/smart-form/getFormSubmissionDetail]}: getFormSubmissionDetail(Map)
	{POST [/emr/smart-form/updateFormTemplateStatus]}: updateFormTemplateStatus(Map)
	{POST [/emr/smart-form/deleteFormTemplate]}: deleteFormTemplate(Map)
	{POST [/emr/smart-form/getAvailableQuestionnaires]}: getAvailableQuestionnaires(Map)
	{POST [/emr/smart-form/getFormStatistics]}: getFormStatistics(Map)
	{POST [/emr/smart-form/getFormTemplateById]}: getFormTemplateById(Map)
	{POST [/emr/smart-form/getFormDataAnalysis]}: getFormDataAnalysis(Map)
	{POST [/emr/smart-form/getFormTemplateList]}: getFormTemplateList(SmartFormQueryDTO)
	{POST [/emr/smart-form/batchDeleteFormTemplates]}: batchDeleteFormTemplates(Map)
	{POST [/emr/smart-form/addFormQuestionnaire]}: addFormQuestionnaire(Map)
	{POST [/emr/smart-form/getFormForFilling]}: getFormForFilling(Map)
	{POST [/emr/smart-form/generateQRCode]}: generateFormQRCode(Map)
	{POST [/emr/smart-form/updateFormTemplate]}: updateFormTemplate(SmartFormTemplateDTO)
	{POST [/emr/smart-form/copyFormTemplate]}: copyFormTemplate(Map)
	{POST [/emr/smart-form/submitFormData]}: submitFormData(Map)
	{POST [/emr/smart-form/getFormDraft]}: getFormDraft(Map)
	{POST [/emr/smart-form/getQRCode]}: getFormQRCode(Map)
	{POST [/emr/smart-form/saveFormDraft]}: saveFormDraft(Map)
	{POST [/emr/smart-form/updateQRCode]}: updateFormQRCode(Map)
2025-07-25 20:52:10.061 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledBaseApi:
	{GET [/scheduled/log/{fileName}]}: getLogs(String)
	{POST [/scheduled/{name}/callOff]}: callOffScheduled(String)
	{DELETE [/scheduled/{name}]}: cancelScheduled(String)
	{POST [/scheduled/{name}/run]}: runScheduled(String)
	{GET [/scheduled/log/files]}: logFiles()
2025-07-25 20:52:10.061 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledCronApi:
	{POST [/scheduled/cron/{name}/add]}: addCronScheduled(String,String)
	{POST [/scheduled/cron/{name}/set]}: setScheduledCron(String,String)
2025-07-25 20:52:10.062 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedDelayApi:
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedDelay/{name}/set/{fixedDelay}]}: setScheduledFixedRate(String,Long)
	{POST [/scheduled/fixedDelay/{name}/add/{fixedDelay}]}: addFixedDelayScheduled(String,Long)
2025-07-25 20:52:10.062 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledFixedRateApi:
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}]}: addFixedRateScheduled(String,Long)
	{POST [/scheduled/fixedRate/{name}/add/{fixedRate}/{initialDelay}]}: addFixedRateScheduled(String,Long,Long)
	{POST [/scheduled/fixedRate/{name}/set/{fixedRate}]}: setScheduledFixedRate(String,Long)
2025-07-25 20:52:10.062 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	c.g.s.a.m.ScheduledNameApi:
	{GET [/scheduled/name/run]}: getRunScheduledName()
	{GET [/scheduled/name/all]}: getAllSuperScheduledName()
2025-07-25 20:52:10.063 [main] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
	{ [/error]}: error(HttpServletRequest)
2025-07-25 20:52:10.064 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 213 mappings in 'requestMappingHandlerMapping'
2025-07-25 20:52:10.077 [main] DEBUG o.s.web.servlet.handler.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-07-25 20:52:10.085 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-07-25 20:52:10.173 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-0.0.0.0-18923"]
2025-07-25 20:52:10.183 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 18923 (http) with context path ''
2025-07-25 20:52:10.191 [main] INFO  com.nx.inf.MainApplication - Started MainApplication in 3.016 seconds (JVM running for 3.809)
2025-07-25 20:52:10.192 [main] INFO  com.nx.inf.MainApplication - 
	----------------------------------------------------------
	Application Serve is running! Access URLs:
	Local: 		http://localhost:18923//
	External: 	http://*******:18923//
	----------------------------------------------------------
2025-07-25 20:53:30.794 [http-nio-0.0.0.0-18923-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 20:53:30.794 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-25 20:53:30.794 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-07-25 20:53:30.794 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-07-25 20:53:30.794 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-07-25 20:53:30.795 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@5426cb36
2025-07-25 20:53:30.796 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@74606204
2025-07-25 20:53:30.796 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-07-25 20:53:30.796 [http-nio-0.0.0.0-18923-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-25 20:53:30.807 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/followup/cuser/list?page=0&size=10&name=&phone=", parameters={masked}
2025-07-25 20:53:30.812 [http-nio-0.0.0.0-18923-exec-1] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.FollowupCUserController#list(int, int, String, String)
2025-07-25 20:53:30.978 [http-nio-0.0.0.0-18923-exec-1] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-25 20:53:31.281 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.FollowupCUserMapper.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM followup_c_user
2025-07-25 20:53:31.298 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.FollowupCUserMapper.selectPage_mpCount - ==> Parameters: 
2025-07-25 20:53:31.333 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.FollowupCUserMapper.selectPage_mpCount - <==      Total: 1
2025-07-25 20:53:31.336 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectPage - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user LIMIT ?
2025-07-25 20:53:31.337 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectPage - ==> Parameters: 10(Long)
2025-07-25 20:53:31.366 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectPage - <==      Total: 2
2025-07-25 20:53:31.377 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id IN (?,?))
2025-07-25 20:53:31.377 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:53:31.401 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 2
2025-07-25 20:53:31.408 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectBatchIds - ==>  Preparing: SELECT id,name,description,category,patient_count,creator_id,creator_name,create_time,update_time FROM patient_group WHERE id IN ( ? )
2025-07-25 20:53:31.408 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectBatchIds - ==> Parameters: 2(Long)
2025-07-25 20:53:31.433 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectBatchIds - <==      Total: 1
2025-07-25 20:53:31.453 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:31.453 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{code=200, data={total=2, current=1, size=10, records=[{gender=1, phone=13800138000, idCard=11010119 (truncated)...]
2025-07-25 20:53:31.478 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:31.489 [http-nio-0.0.0.0-18923-exec-10] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/patient/group/patients?page=0&size=10", parameters={masked}
2025-07-25 20:53:31.490 [http-nio-0.0.0.0-18923-exec-10] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.PatientGroupController#getAllPatients(int, int, String, String, String, String, String, Long)
2025-07-25 20:53:31.499 [http-nio-0.0.0.0-18923-exec-10] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation
2025-07-25 20:53:31.500 [http-nio-0.0.0.0-18923-exec-10] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 
2025-07-25 20:53:31.528 [http-nio-0.0.0.0-18923-exec-10] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 2
2025-07-25 20:53:31.542 [http-nio-0.0.0.0-18923-exec-10] DEBUG c.n.i.m.FollowupCUserMapper.selectPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM followup_c_user WHERE (id IN (?, ?))
2025-07-25 20:53:31.543 [http-nio-0.0.0.0-18923-exec-10] DEBUG c.n.i.m.FollowupCUserMapper.selectPage_mpCount - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:53:31.562 [http-nio-0.0.0.0-18923-exec-10] DEBUG c.n.i.m.FollowupCUserMapper.selectPage_mpCount - <==      Total: 1
2025-07-25 20:53:31.563 [http-nio-0.0.0.0-18923-exec-10] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectPage - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (id IN (?,?)) LIMIT ?
2025-07-25 20:53:31.563 [http-nio-0.0.0.0-18923-exec-10] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectPage - ==> Parameters: 1(Long), 2(Long), 10(Long)
2025-07-25 20:53:31.586 [http-nio-0.0.0.0-18923-exec-10] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectPage - <==      Total: 2
2025-07-25 20:53:31.590 [http-nio-0.0.0.0-18923-exec-10] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:31.591 [http-nio-0.0.0.0-18923-exec-10] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@3e582513, message=获取所有已入组 (truncated)...]
2025-07-25 20:53:31.631 [http-nio-0.0.0.0-18923-exec-10] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:32.632 [http-nio-0.0.0.0-18923-exec-3] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/patient/group/page?page=0&size=10", parameters={masked}
2025-07-25 20:53:32.632 [http-nio-0.0.0.0-18923-exec-3] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.PatientGroupController#getPatientGroupPage(int, int, String)
2025-07-25 20:53:32.638 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.PatientGroupMapper.selectGroupPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM patient_group
2025-07-25 20:53:32.638 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.PatientGroupMapper.selectGroupPage_mpCount - ==> Parameters: 
2025-07-25 20:53:32.658 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.PatientGroupMapper.selectGroupPage_mpCount - <==      Total: 1
2025-07-25 20:53:32.659 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectGroupPage - ==>  Preparing: SELECT id, name, description, patient_count, creator_id, creator_name, create_time, update_time FROM patient_group ORDER BY create_time DESC LIMIT ?
2025-07-25 20:53:32.660 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectGroupPage - ==> Parameters: 10(Long)
2025-07-25 20:53:32.677 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectGroupPage - <==      Total: 2
2025-07-25 20:53:32.684 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:32.684 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@4c29448e, message=获取患者分组列 (truncated)...]
2025-07-25 20:53:32.688 [http-nio-0.0.0.0-18923-exec-3] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:33.327 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/followup/plan/page?current=1&size=15&category=&status=&name=", parameters={masked}
2025-07-25 20:53:33.328 [http-nio-0.0.0.0-18923-exec-4] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.FollowupPlanController#getPlanPage(Integer, Integer, String, String, String)
2025-07-25 20:53:33.347 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.FollowupPlanMapper.selectPlanPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM followup_plan fp
2025-07-25 20:53:33.348 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.FollowupPlanMapper.selectPlanPage_mpCount - ==> Parameters: 
2025-07-25 20:53:33.371 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.FollowupPlanMapper.selectPlanPage_mpCount - <==      Total: 1
2025-07-25 20:53:33.372 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.nx.inf.mapper.FollowupPlanMapper.selectPlanPage - ==>  Preparing: SELECT fp.id, fp.name, fp.description, fp.category, fp.status, fp.end_time_type, fp.end_time_value, fp.group_id, fp.qr_code_url, fp.creator_id, fp.creator_name, fp.create_time, fp.update_time, COALESCE(fp.patient_count, 0) as patient_count, COALESCE(fp.task_count, 0) as task_count, COALESCE(fp.completed_task_count, 0) as completed_task_count FROM followup_plan fp ORDER BY fp.create_time DESC LIMIT ?
2025-07-25 20:53:33.373 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.nx.inf.mapper.FollowupPlanMapper.selectPlanPage - ==> Parameters: 15(Long)
2025-07-25 20:53:33.629 [http-nio-0.0.0.0-18923-exec-6] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/questionnaires/with-details", parameters={}
2025-07-25 20:53:33.629 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/patient/group/page?page=0&size=1000", parameters={masked}
2025-07-25 20:53:33.630 [http-nio-0.0.0.0-18923-exec-6] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.QuestionnaireController#getAllQuestionnairesWithDetails()
2025-07-25 20:53:33.630 [http-nio-0.0.0.0-18923-exec-5] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.PatientGroupController#getPatientGroupPage(int, int, String)
2025-07-25 20:53:33.680 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.nx.inf.mapper.FollowupPlanMapper.selectPlanPage - <==      Total: 1
2025-07-25 20:53:33.681 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.PatientGroupMapper.selectGroupPage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM patient_group
2025-07-25 20:53:33.682 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.PatientGroupMapper.selectGroupPage_mpCount - ==> Parameters: 
2025-07-25 20:53:33.703 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.PatientGroupMapper.selectGroupPage_mpCount - <==      Total: 1
2025-07-25 20:53:33.703 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectGroupPage - ==>  Preparing: SELECT id, name, description, patient_count, creator_id, creator_name, create_time, update_time FROM patient_group ORDER BY create_time DESC LIMIT ?
2025-07-25 20:53:33.704 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectGroupPage - ==> Parameters: 1000(Long)
2025-07-25 20:53:33.723 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.nx.inf.mapper.PatientGroupMapper.selectGroupPage - <==      Total: 2
2025-07-25 20:53:33.723 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectAll - ==>  Preparing: SELECT * FROM questionnaire ORDER BY create_time DESC
2025-07-25 20:53:33.723 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectAll - ==> Parameters: 
2025-07-25 20:53:33.725 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:33.725 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@39ab7627, message=获取患者分组列 (truncated)...]
2025-07-25 20:53:33.726 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:33.746 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectAll - <==      Total: 3
2025-07-25 20:53:33.746 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.PatientGroupMapper.selectById - ==>  Preparing: SELECT id,name,description,category,patient_count,creator_id,creator_name,create_time,update_time FROM patient_group WHERE id=?
2025-07-25 20:53:33.746 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.PatientGroupMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:53:33.767 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.PatientGroupMapper.selectById - <==      Total: 1
2025-07-25 20:53:33.768 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:33.770 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 4(Long)
2025-07-25 20:53:33.792 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:33.793 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.nx.inf.mapper.FollowupSubplanMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:53:33.794 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.nx.inf.mapper.FollowupSubplanMapper.selectCount - ==> Parameters: 34(Long)
2025-07-25 20:53:34.072 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.nx.inf.mapper.FollowupSubplanMapper.selectCount - <==      Total: 1
2025-07-25 20:53:34.072 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:53:34.073 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 4(Long)
2025-07-25 20:53:34.076 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:34.077 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(success=true, message=操作成功!, code=00, result=com.baomidou.mybatisplus.extension.plugins.pagin (truncated)...]
2025-07-25 20:53:34.079 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:34.094 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:53:34.101 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:53:34.101 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 4(Long)
2025-07-25 20:53:34.125 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 3
2025-07-25 20:53:34.131 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:34.132 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:53:34.148 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:34.151 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:53:34.151 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:53:34.170 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:53:34.173 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:53:34.173 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:53:34.197 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:53:34.199 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:34.199 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:53:34.222 [http-nio-0.0.0.0-18923-exec-6] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:34.222 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:53:34.223 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:53:34.245 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:53:34.246 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:53:34.247 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:53:34.272 [http-nio-0.0.0.0-18923-exec-6] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:53:34.277 [http-nio-0.0.0.0-18923-exec-6] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:34.277 [http-nio-0.0.0.0-18923-exec-6] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [[QuestionnaireDTO(id=4, name=测试, type=custom, status=active, isScoring=false, isGuidance=true, isTri (truncated)...]
2025-07-25 20:53:34.285 [http-nio-0.0.0.0-18923-exec-6] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:35.188 [http-nio-0.0.0.0-18923-exec-7] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/followup/plan/34", parameters={}
2025-07-25 20:53:35.190 [http-nio-0.0.0.0-18923-exec-7] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.FollowupPlanController#getPlanById(Long)
2025-07-25 20:53:35.193 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.nx.inf.mapper.FollowupPlanMapper.selectPlanById - ==>  Preparing: SELECT fp.id, fp.name, fp.description, fp.category, fp.status, fp.end_time_type, fp.end_time_value, fp.group_id, fp.qr_code_url, fp.creator_id, fp.creator_name, fp.create_time, fp.update_time, COALESCE(fp.patient_count, 0) as patient_count, COALESCE(fp.task_count, 0) as task_count, COALESCE(fp.completed_task_count, 0) as completed_task_count FROM followup_plan fp WHERE fp.id = ?
2025-07-25 20:53:35.193 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.nx.inf.mapper.FollowupPlanMapper.selectPlanById - ==> Parameters: 34(Long)
2025-07-25 20:53:35.215 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.nx.inf.mapper.FollowupPlanMapper.selectPlanById - <==      Total: 1
2025-07-25 20:53:35.216 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.PatientGroupMapper.selectById - ==>  Preparing: SELECT id,name,description,category,patient_count,creator_id,creator_name,create_time,update_time FROM patient_group WHERE id=?
2025-07-25 20:53:35.216 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.PatientGroupMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:53:35.234 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.PatientGroupMapper.selectById - <==      Total: 1
2025-07-25 20:53:35.235 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.FollowupSubplanMapper.selectByPlanId - ==>  Preparing: SELECT id, plan_id, name, description, type, execution_time_type, custom_start_day, custom_end_day, reminder_time, plan_type, sort_order, create_time, update_time FROM followup_subplan WHERE plan_id = ? ORDER BY sort_order ASC, create_time ASC
2025-07-25 20:53:35.236 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.FollowupSubplanMapper.selectByPlanId - ==> Parameters: 34(Long)
2025-07-25 20:53:35.260 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.mapper.FollowupSubplanMapper.selectByPlanId - <==      Total: 2
2025-07-25 20:53:35.266 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==>  Preparing: SELECT id, subplan_id, item_name, cycle_days, join_day, event_type, content_template_id, sort_order, create_time, update_time FROM followup_subplan_item WHERE subplan_id = ? ORDER BY sort_order ASC, create_time ASC
2025-07-25 20:53:35.267 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==> Parameters: 44(Long)
2025-07-25 20:53:35.292 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - <==      Total: 1
2025-07-25 20:53:35.298 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==>  Preparing: SELECT id, subplan_id, questionnaire_id, sort_order, is_required, create_time FROM followup_subplan_questionnaire WHERE subplan_id = ? ORDER BY sort_order ASC, create_time ASC
2025-07-25 20:53:35.298 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==> Parameters: 44(Long)
2025-07-25 20:53:35.327 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - <==      Total: 1
2025-07-25 20:53:35.333 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:35.333 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:53:35.351 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:35.352 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==>  Preparing: SELECT id, subplan_id, item_name, cycle_days, join_day, event_type, content_template_id, sort_order, create_time, update_time FROM followup_subplan_item WHERE subplan_id = ? ORDER BY sort_order ASC, create_time ASC
2025-07-25 20:53:35.352 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==> Parameters: 45(Long)
2025-07-25 20:53:35.373 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - <==      Total: 1
2025-07-25 20:53:35.373 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==>  Preparing: SELECT id, subplan_id, questionnaire_id, sort_order, is_required, create_time FROM followup_subplan_questionnaire WHERE subplan_id = ? ORDER BY sort_order ASC, create_time ASC
2025-07-25 20:53:35.374 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - ==> Parameters: 45(Long)
2025-07-25 20:53:35.392 [http-nio-0.0.0.0-18923-exec-7] DEBUG c.n.i.m.F.selectBySubplanId - <==      Total: 1
2025-07-25 20:53:35.393 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:35.393 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:53:35.417 [http-nio-0.0.0.0-18923-exec-7] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:35.417 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:35.418 [http-nio-0.0.0.0-18923-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [Result(success=true, message=操作成功!, code=00, result=FollowupPlanDTO(id=34, name=高血压随访计划, description (truncated)...]
2025-07-25 20:53:35.424 [http-nio-0.0.0.0-18923-exec-7] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:37.398 [http-nio-0.0.0.0-18923-exec-8] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/smart-form/getFormTemplateList", parameters={}
2025-07-25 20:53:37.398 [http-nio-0.0.0.0-18923-exec-8] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.SmartFormController#getFormTemplateList(SmartFormQueryDTO)
2025-07-25 20:53:37.436 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [SmartFormQueryDTO(pageNum=1, pageSize=10, name=null, formType=null, category=null, status=null, crea (truncated)...]
2025-07-25 20:53:37.456 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.S.selectFormTemplatePage_mpCount - ==>  Preparing: SELECT COUNT(*) FROM (SELECT sft.id, sft.name, sft.description, sft.form_type, sft.category, sft.status, sft.permission, sft.config, sft.repeat_config, sft.remark_text, sft.consent_config, sft.creator_name, sft.creator_dept_name, sft.submission_count, COUNT(DISTINCT sfqr.questionnaire_id) AS questionnaire_count, CASE WHEN sft.last_submitted_time IS NULL THEN NULL ELSE DATE_FORMAT(sft.last_submitted_time, '%Y-%m-%d %H:%i:%s') END AS last_submitted_time, sft.create_time, sft.update_time FROM smart_form_template sft LEFT JOIN smart_form_questionnaire_rel sfqr ON sft.id = sfqr.form_template_id AND sfqr.is_deleted = 0 WHERE sft.is_deleted = 0 GROUP BY sft.id ORDER BY sft.create_time DESC) TOTAL
2025-07-25 20:53:37.456 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.S.selectFormTemplatePage_mpCount - ==> Parameters: 
2025-07-25 20:53:37.485 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.S.selectFormTemplatePage_mpCount - <==      Total: 1
2025-07-25 20:53:37.486 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.S.selectFormTemplatePage - ==>  Preparing: SELECT sft.id, sft.name, sft.description, sft.form_type, sft.category, sft.status, sft.permission, sft.config, sft.repeat_config, sft.remark_text, sft.consent_config, sft.creator_name, sft.creator_dept_name, sft.submission_count, COUNT(DISTINCT sfqr.questionnaire_id) AS questionnaire_count, CASE WHEN sft.last_submitted_time IS NULL THEN NULL ELSE DATE_FORMAT(sft.last_submitted_time, '%Y-%m-%d %H:%i:%s') END AS last_submitted_time, sft.create_time, sft.update_time FROM smart_form_template sft LEFT JOIN smart_form_questionnaire_rel sfqr ON sft.id = sfqr.form_template_id AND sfqr.is_deleted = 0 WHERE sft.is_deleted = 0 GROUP BY sft.id ORDER BY sft.create_time DESC LIMIT ?
2025-07-25 20:53:37.486 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.S.selectFormTemplatePage - ==> Parameters: 10(Long)
2025-07-25 20:53:37.504 [http-nio-0.0.0.0-18923-exec-8] DEBUG c.n.i.m.S.selectFormTemplatePage - <==      Total: 2
2025-07-25 20:53:37.504 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:37.505 [http-nio-0.0.0.0-18923-exec-8] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{data=com.baomidou.mybatisplus.extension.plugins.pagination.Page@68a0e7ec, success=true, message=获取成 (truncated)...]
2025-07-25 20:53:37.507 [http-nio-0.0.0.0-18923-exec-8] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:37.703 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - GET "/emr/questionnaires/with-details", parameters={}
2025-07-25 20:53:37.703 [http-nio-0.0.0.0-18923-exec-9] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/content-template/getCategoryList", parameters={}
2025-07-25 20:53:37.703 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/content-template/getTemplateList", parameters={}
2025-07-25 20:53:37.704 [http-nio-0.0.0.0-18923-exec-1] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.QuestionnaireController#getAllQuestionnairesWithDetails()
2025-07-25 20:53:37.704 [http-nio-0.0.0.0-18923-exec-2] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.ContentTemplateController#getTemplateList(ContentTemplateRequestVO)
2025-07-25 20:53:37.704 [http-nio-0.0.0.0-18923-exec-9] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.ContentTemplateController#getCategoryList(Map)
2025-07-25 20:53:37.705 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectAll - ==>  Preparing: SELECT * FROM questionnaire ORDER BY create_time DESC
2025-07-25 20:53:37.705 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectAll - ==> Parameters: 
2025-07-25 20:53:37.711 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [ContentTemplateRequestVO(id=null, ids=null, page=1, pageSize=10, category=null, keyword=null, permis (truncated)...]
2025-07-25 20:53:37.719 [http-nio-0.0.0.0-18923-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{}]
2025-07-25 20:53:37.720 [http-nio-0.0.0.0-18923-exec-9] DEBUG c.n.i.m.C.selectActiveCategoriesOrderBySort - ==>  Preparing: SELECT id, code, name, description, sort_order, status, create_time, update_time, is_deleted FROM content_template_category WHERE status = 'active' AND is_deleted = 0 ORDER BY sort_order ASC, create_time ASC
2025-07-25 20:53:37.720 [http-nio-0.0.0.0-18923-exec-9] DEBUG c.n.i.m.C.selectActiveCategoriesOrderBySort - ==> Parameters: 
2025-07-25 20:53:37.725 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectAll - <==      Total: 3
2025-07-25 20:53:37.725 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:37.725 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 4(Long)
2025-07-25 20:53:37.746 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:37.746 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.C.selectTemplatePage_mpCount - ==>  Preparing: SELECT COUNT(*) AS total FROM content_template WHERE is_deleted = 0 AND (permission = 'all' OR (permission = 'department' AND creator_dept_id = ?) OR (permission = 'self' AND creator_id = ?))
2025-07-25 20:53:37.747 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.C.selectTemplatePage_mpCount - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:53:37.748 [http-nio-0.0.0.0-18923-exec-9] DEBUG c.n.i.m.C.selectActiveCategoriesOrderBySort - <==      Total: 6
2025-07-25 20:53:37.748 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:53:37.749 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 4(Long)
2025-07-25 20:53:37.753 [http-nio-0.0.0.0-18923-exec-9] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:37.754 [http-nio-0.0.0.0-18923-exec-9] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{data=[ContentTemplateCategoryDTO(code=health_education, name=健康教育, description=健康知识教育相关模板, sortOrde (truncated)...]
2025-07-25 20:53:37.755 [http-nio-0.0.0.0-18923-exec-9] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:37.768 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:53:37.770 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:53:37.770 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 4(Long)
2025-07-25 20:53:37.792 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 3
2025-07-25 20:53:37.793 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:37.793 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:53:37.816 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:37.818 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:53:37.818 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:53:37.839 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:53:37.840 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:53:37.841 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:53:37.860 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:53:37.861 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:53:37.861 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:53:37.882 [http-nio-0.0.0.0-18923-exec-1] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:53:37.883 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:53:37.883 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:53:37.890 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.C.selectTemplatePage_mpCount - <==      Total: 1
2025-07-25 20:53:37.891 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.ContentTemplateMapper.selectTemplatePage - ==>  Preparing: SELECT id, name, description, category, message_type, permission, title, content, content_text, status, creator_id, creator_name, creator_dept_id, creator_dept_name, use_count, CASE WHEN last_used_time IS NULL THEN NULL ELSE last_used_time END as last_used_time, create_time, update_time, is_deleted FROM content_template WHERE is_deleted = 0 AND (permission = 'all' OR (permission = 'department' AND creator_dept_id = ?) OR (permission = 'self' AND creator_id = ?)) ORDER BY create_time DESC LIMIT ?
2025-07-25 20:53:37.891 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.ContentTemplateMapper.selectTemplatePage - ==> Parameters: 1(Long), 1(Long), 10(Long)
2025-07-25 20:53:37.903 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:53:37.904 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:53:37.904 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:53:37.917 [http-nio-0.0.0.0-18923-exec-2] DEBUG c.n.i.m.ContentTemplateMapper.selectTemplatePage - <==      Total: 2
2025-07-25 20:53:37.926 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:37.926 [http-nio-0.0.0.0-18923-exec-2] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{data={total=2, list=[ContentTemplateDTO(id=3, name=CS, description=, category=diet_guide, messageTy (truncated)...]
2025-07-25 20:53:37.928 [http-nio-0.0.0.0-18923-exec-2] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:53:37.927 [http-nio-0.0.0.0-18923-exec-1] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:53:37.931 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:53:37.931 [http-nio-0.0.0.0-18923-exec-1] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [[QuestionnaireDTO(id=4, name=测试, type=custom, status=active, isScoring=false, isGuidance=true, isTri (truncated)...]
2025-07-25 20:53:37.932 [http-nio-0.0.0.0-18923-exec-1] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:54:02.635 [http-nio-0.0.0.0-18923-exec-3] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/questionnaire-answers", parameters={}
2025-07-25 20:54:02.635 [http-nio-0.0.0.0-18923-exec-3] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getQuestionnaireAnswers(Map)
2025-07-25 20:54:02.636 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, planId=34}]
2025-07-25 20:54:02.640 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:54:02.640 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:54:02.669 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:54:02.669 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:54:02.669 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:54:02.705 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:54:02.707 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==>  Preparing: SELECT COUNT( * ) AS total FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:54:02.707 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:54:02.739 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.PatientGroupRelationMapper.selectCount - <==      Total: 1
2025-07-25 20:54:02.740 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:54:02.740 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:54:02.766 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:54:02.773 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:54:02.773 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:54:02.803 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:54:02.808 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:54:02.808 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:54:02.840 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:54:02.840 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:54:02.840 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:54:02.868 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:54:02.868 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:54:02.868 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:54:02.898 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:54:02.899 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:54:02.899 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 1(Long)
2025-07-25 20:54:02.928 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 11
2025-07-25 20:54:02.931 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:54:02.932 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 1(Long), 1(Long)
2025-07-25 20:54:02.972 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:54:02.973 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:54:02.973 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:54:02.989 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:54:02.989 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:54:02.990 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:54:03.007 [http-nio-0.0.0.0-18923-exec-3] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:54:03.007 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:54:03.007 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:54:03.028 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:54:03.028 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionId - ==>  Preparing: SELECT * FROM questionnaire_option WHERE question_id = ? ORDER BY sort ASC
2025-07-25 20:54:03.028 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionId - ==> Parameters: 2(Long)
2025-07-25 20:54:03.045 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.m.Q.selectByQuestionId - <==      Total: 5
2025-07-25 20:54:03.047 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (questionnaire_id = ? AND question_id = ? AND patient_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:54:03.048 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 2(Long), 2(Long), 1(Long)
2025-07-25 20:54:03.068 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:54:03.071 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?) ORDER BY answer_time DESC LIMIT 1
2025-07-25 20:54:03.071 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:54:03.092 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:54:03.094 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==>  Preparing: SELECT id,questionnaire_id,question_id,patient_id,patient_name,answer_type,answer_text,answer_value,answer_score,answer_time,time_spent,task_id,subplan_id,plan_id,answer_source,create_time,update_time FROM questionnaire_answer WHERE (patient_id = ? AND plan_id = ?)
2025-07-25 20:54:03.094 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - ==> Parameters: 1(Long), 34(Long)
2025-07-25 20:54:03.120 [http-nio-0.0.0.0-18923-exec-3] DEBUG c.n.i.mapper.QuestionnaireAnswerMapper.selectList - <==      Total: 0
2025-07-25 20:54:03.120 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:54:03.120 [http-nio-0.0.0.0-18923-exec-3] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={questionnaires=[{name=疼痛相关不适评估, questions=[{result=[], isRequired=true, sortOrder=1 (truncated)...]
2025-07-25 20:54:03.124 [http-nio-0.0.0.0-18923-exec-3] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:54:14.022 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-detail", parameters={}
2025-07-25 20:54:14.023 [http-nio-0.0.0.0-18923-exec-5] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanDetail(Map)
2025-07-25 20:54:14.023 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{planId=34, phone=13800138000}]
2025-07-25 20:54:14.024 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:54:14.025 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:54:14.042 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:54:14.042 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE id=?
2025-07-25 20:54:14.042 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - ==> Parameters: 34(Long)
2025-07-25 20:54:14.064 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectById - <==      Total: 1
2025-07-25 20:54:14.066 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:54:14.066 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:54:14.085 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:54:14.086 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:54:14.086 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:54:14.108 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:54:14.108 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:54:14.108 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 1(Long)
2025-07-25 20:54:14.134 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:54:14.134 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:54:14.135 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 1(Long)
2025-07-25 20:54:14.163 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:54:14.165 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?) ORDER BY sort_order ASC
2025-07-25 20:54:14.165 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:54:14.188 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:54:14.188 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==>  Preparing: SELECT id,name,type,status,is_scoring,is_guidance,is_trigger,create_time,update_time FROM questionnaire WHERE id=?
2025-07-25 20:54:14.188 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - ==> Parameters: 2(Long)
2025-07-25 20:54:14.207 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.QuestionnaireMapper.selectById - <==      Total: 1
2025-07-25 20:54:14.208 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==>  Preparing: SELECT * FROM questionnaire_question WHERE questionnaire_id = ? ORDER BY sort ASC
2025-07-25 20:54:14.208 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - ==> Parameters: 2(Long)
2025-07-25 20:54:14.227 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.m.Q.selectByQuestionnaireId - <==      Total: 1
2025-07-25 20:54:14.228 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:54:14.228 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:54:14.250 [http-nio-0.0.0.0-18923-exec-5] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:54:14.251 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:54:14.252 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:54:14.280 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:54:14.284 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:54:14.285 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:54:14.309 [http-nio-0.0.0.0-18923-exec-5] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:54:14.310 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:54:14.310 [http-nio-0.0.0.0-18923-exec-5] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data={personName=患者, date=07月24日, number=全部计划（共2次）, name=高血压随访计划, subplanIds=[44, 45], pl (truncated)...]
2025-07-25 20:54:14.311 [http-nio-0.0.0.0-18923-exec-5] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:54:23.822 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - POST "/emr/followup/customer/plan-list", parameters={}
2025-07-25 20:54:23.823 [http-nio-0.0.0.0-18923-exec-4] TRACE o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.nx.inf.controller.CustomerFollowupController#getFollowupPlanList(Map)
2025-07-25 20:54:23.823 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{phone=13800138000, state=0, pageNo=1, pageSize=10}]
2025-07-25 20:54:23.824 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:54:23.825 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:54:23.843 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:54:23.845 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ?)
2025-07-25 20:54:23.845 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long)
2025-07-25 20:54:23.873 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.017 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==>  Preparing: SELECT id,name,description,category,status,end_time_type,end_time_value,group_id,qr_code_url,creator_id,creator_name,create_time,update_time,patient_count,task_count,completed_task_count FROM followup_plan WHERE (group_id IN (?) AND status = ?)
2025-07-25 20:54:24.018 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - ==> Parameters: 2(Long), active(String)
2025-07-25 20:54:24.038 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupPlanMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.038 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==>  Preparing: SELECT id,plan_id,name,description,type,execution_time_type,custom_start_day,custom_end_day,reminder_time,plan_type,sort_order,create_time,update_time FROM followup_subplan WHERE (plan_id = ?)
2025-07-25 20:54:24.038 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - ==> Parameters: 34(Long)
2025-07-25 20:54:24.061 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupSubplanMapper.selectList - <==      Total: 2
2025-07-25 20:54:24.062 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:54:24.062 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:54:24.083 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.084 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:54:24.084 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:54:24.102 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.103 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:54:24.104 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 44(Long)
2025-07-25 20:54:24.128 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.129 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:54:24.129 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==> Parameters: 44(Long)
2025-07-25 20:54:24.155 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:54:24.157 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:54:24.157 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 1(Long)
2025-07-25 20:54:24.184 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:54:24.185 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==>  Preparing: SELECT id,phone,name,gender,age,birth_date,id_card,address,emergency_contact,emergency_phone,bind_status,auth_status,last_login_time,last_login_ip,id_type,relationship,id_number,jhr_id_type,jhr_id_number,jhr_name,jhr_phone,is_bind,is_auth,create_time,update_time FROM followup_c_user WHERE (phone = ?)
2025-07-25 20:54:24.185 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - ==> Parameters: 13800138000(String)
2025-07-25 20:54:24.210 [http-nio-0.0.0.0-18923-exec-4] DEBUG com.nx.inf.mapper.FollowupCUserMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.210 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==>  Preparing: SELECT id,patient_id,group_id,patient_name,patient_phone,join_type,user_agree,doctor_agree,join_time,agree_time,audit_time,auditor_id,auditor_name,audit_remark,status,join_method,creator_id,creator_name,create_time,update_time FROM patient_group_relation WHERE (patient_id = ? AND group_id = ? AND status = ?)
2025-07-25 20:54:24.211 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - ==> Parameters: 1(Long), 2(Long), 1(String)
2025-07-25 20:54:24.229 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.PatientGroupRelationMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.230 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==>  Preparing: SELECT id,subplan_id,item_name,cycle_days,join_day,event_type,content_template_id,sort_order,create_time,update_time FROM followup_subplan_item WHERE (subplan_id = ?)
2025-07-25 20:54:24.231 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - ==> Parameters: 45(Long)
2025-07-25 20:54:24.252 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.mapper.FollowupSubplanItemMapper.selectList - <==      Total: 1
2025-07-25 20:54:24.253 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==>  Preparing: SELECT id,subplan_id,questionnaire_id,sort_order,is_required,create_time FROM followup_subplan_questionnaire WHERE (subplan_id = ?)
2025-07-25 20:54:24.253 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - ==> Parameters: 45(Long)
2025-07-25 20:54:24.271 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.F.selectList - <==      Total: 1
2025-07-25 20:54:24.271 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==>  Preparing: SELECT qa.*, q.name as questionnaire_name, qq.title as question_title, qq.type as question_type, qq.required as question_required FROM questionnaire_answer qa LEFT JOIN questionnaire q ON qa.questionnaire_id = q.id LEFT JOIN questionnaire_question qq ON qa.question_id = qq.id WHERE qa.patient_id = ? AND qa.questionnaire_id = ? ORDER BY qa.answer_time DESC
2025-07-25 20:54:24.271 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - ==> Parameters: 1(Long), 2(Long)
2025-07-25 20:54:24.290 [http-nio-0.0.0.0-18923-exec-4] DEBUG c.n.i.m.Q.selectAnswersByPatientAndQuestionnaire - <==      Total: 0
2025-07-25 20:54:24.291 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-07-25 20:54:24.291 [http-nio-0.0.0.0-18923-exec-4] DEBUG o.s.w.s.m.m.annotation.HttpEntityMethodProcessor - Writing [{code=200, data=[{date=07月24日, number=第1次计划, name=高血压随访计划, planId=34, subplanId=44, desc=, status=0} (truncated)...]
2025-07-25 20:54:24.292 [http-nio-0.0.0.0-18923-exec-4] DEBUG org.springframework.web.servlet.DispatcherServlet - Completed 200 OK
2025-07-25 20:58:22.188 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed

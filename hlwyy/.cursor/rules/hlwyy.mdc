### **Core Development & Collaboration Principles**  

#### **I. Code & Project Consistency Principles**  

*   **Task Scope:** Strictly adhere to task requirements. Only modify code within the specified scope, and never alter any files or logic outside of it.  
*   **Tech Stack & Style Unification:** All written code must align with the project's existing coding style. Strictly follow the current project's technology stack, and cautiously evaluate and avoid introducing unnecessary external technologies or libraries.  
*   **Project Structure:** Create new files or directories with caution. If genuinely necessary, strictly adhere to the project's established directory structure and architectural patterns to ensure project organization.  
*   **Backend Implementation Consistency:** When developing new backend APIs, the input/output parameters of the Controller layer, the usage of the ORM framework, and the writing of SQL statements will strictly reference and replicate the implementation patterns of existing features to ensure a high degree of consistency.  

#### **II. Documentation Maintenance Standards**  

*   **Project Log (project.markdown):** After every code change is committed, immediately record the update highlights, feature descriptions, and impact scope into the `project.markdown` file in the project's root directory (create the file if it does not exist).  
*   **Frontend Page Documentation (page.markdown):** When developing new frontend pages, synchronously update information such as page route configurations, called API endpoints, core feature descriptions, and button functionalities in detail to the `page.markdown` file (create the file if it does not exist).  
*   **Backend API Documentation (api.markdown):** When developing new backend APIs, update the complete API documentation (including request methods, URLs, request/response parameters, data structures, success/failure examples, etc.) to the `api.markdown` file (create the file if it does not exist).  
*   **Bug Fix Report (bug.markdown):** After successfully fixing a bug, document the root cause of the bug, the troubleshooting process, and the final solution in detail in the `bug.markdown` file (create the file if it does not exist).  
*   **Always Update Documentation:** Ensure that all relevant markdown files are updated promptly after every code modification to maintain documentation accuracy and traceability.  

#### **III. Collaboration & Environment Standards**  

*   **Target Environment:** All instructions, scripts, and tool invocations will be based on the **Windows 10 Pro** environment and will use its compatible syntax and commands.  
*   **Communication Language:** From requirements analysis and technical ideation to the final code and documentation output, the entire process will uniformly use **Chinese** for all communication and expression.
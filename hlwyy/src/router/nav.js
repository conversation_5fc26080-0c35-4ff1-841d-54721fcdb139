import Home from '@/components/special/Home';
import filter from 'lodash/filter';
import each from 'lodash/each';
import WarningKnowledge from '@/components/warning/warningKnowledge'
import PersonalWarning from '@/components/warning/personalWarning'

const menuItems = [
    //工作台
    {
        path: '/workbench',
        name: 'workbench',
        meta: {
            title: '工作台',
        },
        component: () => import('components/works/workbench')
    },

    // 远程医疗
    {
        path: '/internet-clinic',
        name: 'internetClinic',
        meta: {
            title: '远程医疗'
        },
        component: {
            render(c) { return c('router-view') }
        },
        children: [
            // 会诊相关路由
            {
                path: 'consultation/apply',
                name: 'consultationApply',
                meta: {
                    title: '会诊申请',
                    parent: 'internetClinic'
                },
                component: () => import('@/components/internetClinic/consultation/ConsultationApply.vue')
            },
            {
                path: 'consultation/list',
                name: 'myConsultationList',
                meta: {
                    title: '我的会诊',
                    parent: 'internetClinic'
                },
                component: () => import('@/components/internetClinic/consultation/MyConsultationList.vue')
            },
            {
                path: 'consultation/team',
                name: 'consultationTeam',
                meta: {
                    title: '会诊团队',
                    parent: 'internetClinic'
                },
                component: () => import('@/components/internetClinic/consultation/TeamManagement.vue')
            },
            {
                path: 'consultation/detail',
                name: 'consultationDetail',
                meta: {
                    title: '会诊详情',
                    parent: 'internetClinic'
                },
                component: () => import('@/components/internetClinic/consultation/ConsultationDetail.vue')
            },
            {
                path: 'consultation/edit',
                name: 'consultationEdit',
                meta: {
                    title: '修改会诊',
                    parent: 'internetClinic'
                },
                component: () => import('@/components/internetClinic/consultation/ConsultationApply.vue')
            },
            {
                path: 'consultation/meeting',
                name: 'consultationMeeting',
                meta: {
                    title: '会诊会议',
                    parent: 'internetClinic'
                },
                component: () => import('@/components/internetClinic/consultation/ConsultationMeeting.vue') // 这个组件需要另外创建
            }
        ]
    },

    //监护人认证
    {
        path: '/guardianAuth',
        name: 'guardianAuth',
        meta: {
            title: '监护人认证'
        },
        component: () => import('components/guardianAuth/guardianAuthIndex')
    },

    //监护人审核
    {
        path: '/guardianReview',
        name: 'guardianReview',
        meta: {
            title: '监护人审核'
        },
        component: () => import('components/guardianAuth/guardianAuthList')
    },

    //远程门诊
    {
        path: '/videoConsultation',
        name: 'videoConsultation',
        meta: {
            title: '视频咨询'
        },
        component:  () => import('components/visitDispensing/children/videoConsultation'),
    },
    {
        path: '/imgTextConsultation',
        name: 'imgTextConsultation',
        meta: {
            title: '图文咨询'
        },
        component:  () => import('components/visitDispensing/children/imgTextConsultation'),
    },

    //复诊配药
    {
        path: '/visitDispensing',
        name: 'visitDispensing',
        meta: {
            title: '复诊配药'
        },
        component:  () => import('components/visitDispensing/children/visitDispensingIndex'),
    },

    //检查预约申请
    {
        path: '/examAppointment',
        name: 'examAppointment',
        meta: {
            title: '检查预约申请'
        },
        component: () => import('components/examAppointment/examAppointmentIndex.vue'),
    },

    //养老机构端管理平台
    {
        path: '/nursingHomePortal',
        name: 'nursingHomePortal',
        meta: {
            title: '养老机构端'
        },
        component: () => import('components/nursingHomePortal/nursingHomePortal'),
        children: [
            {
                path: 'manage',
                name: 'nursingHomeManage',
                meta: {
                    title: '机构管理',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomeManage/index.vue'),
            },
            {
                path: '',
                name: 'nursingHomeIndex',
                meta: {
                    title: '机构首页',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/nursingHomeIndex'),
            },
            {
                path: 'patientList',
                name: 'nursingHomePatientList',
                meta: {
                    title: '患者信息列表',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/patientList'),
            },
            {
                path: 'patientReferral',
                name: 'nursingHomePatientReferral',
                meta: {
                    title: '患者转介信',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/patientReferral'),
            },
            {
                path: 'deviceData',
                name: 'nursingHomeDeviceData',
                meta: {
                    title: '设备数据采集',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/deviceDataCollection'),
            },
            {
                path: 'patientDetail',
                name: 'nursingHomePatientDetail',
                meta: {
                    title: '患者详情',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/patientManage/children/patientDetail'),
            },
            {
                path: 'patientAdd',
                name: 'nursingHomePatientAdd',
                meta: {
                    title: '新增患者',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/patientAdd'),
            },
            {
                path: 'patientEdit',
                name: 'nursingHomePatientEdit',
                meta: {
                    title: '编辑患者',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/patientEdit'),
            },
            {
                path: 'referralDetail',
                name: 'nursingHomeReferralDetail',
                meta: {
                    title: '转介信详情',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/referralDetail'),
            },
            {
                path: 'deviceDataDetail',
                name: 'nursingHomeDeviceDataDetail',
                meta: {
                    title: '设备数据详情',
                    parent: 'nursingHomePortal'
                },
                component: () => import('components/nursingHomePortal/children/deviceDataDetail'),
            }
        ]
    },

    //患者360展示
    {
        path: '/patient360',
        name: 'patient360',
        meta: {
            title: '患者管理'
        },
        component: () => import('components/patient360/index.vue'),
    },

    //患者360展示Demo（用于第三方集成）
    {
        path: '/patient360-demo',
        name: 'patient360-demo',
        meta: {
            title: '患者360集成示例'
        },
        component: () => import('components/patient360/demo-page.vue'),
    },

    //护士站病人列表
    {
        path: '/nursePatientList',
        name: 'nursePatientList',
        meta: {
            title: '护士站病人列表'
        },
        component: () => import('@/components/internetWard/PatientList.vue'),
    },

    //医生站患者列表
    {
        path: '/doctorPatientList',
        name: 'doctorPatientList',
        meta: {
            title: '医生站患者列表'
        },
        component: () => import('@/components/internetWard/DoctorStationPatientList.vue'),
    },

    //医生站医嘱处理
    {
        path: '/order',
        name: 'Order',
        meta: {
            title: '医嘱处理'
        },
        component: () => import('@/components/internetWard/Order.vue'),
    },

    //医生站检验处理
    {
        path: '/labtest',
        name: 'LabTest',
        meta: {
            title: '检验处理'
        },
        component: () => import('@/components/internetWard/LabTest.vue'),
    },

    //医生站检查申请
    {
        path: '/examine',
        name: 'Examine',
        meta: {
            title: '检查申请'
        },
        component: () => import('@/components/internetWard/Examine.vue'),
    },

    //医生查房
    {
        path: '/doctorRounds',
        name: 'DoctorRounds',
        meta: {
            title: '医生查房'
        },
        component: () => import('@/components/internetWard/DoctorRounds.vue'),
    },

    //病历书写
    {
        path: '/medicalRecord',
        name: 'MedicalRecord',
        meta: {
            title: '病历书写'
        },
        component: () => import('@/components/internetWard/MedicalRecord.vue'),
    },

    //护士交接班管理
    {
        path: '/nurse-shift',
        name: 'NurseShift',
        meta: {
            title: '护士交班',
            parent: 'internetWard'
        },
        component: () => import('@/components/internetWard/NurseShift.vue'),
    },
    {
        path: '/nurse-succession',
        name: 'NurseSuccession',
        meta: {
            title: '护士接班',
            parent: 'internetWard'
        },
        component: () => import('@/components/internetWard/NurseSuccession.vue'),
    },

    //订单管理
    {
        path: '/orderManage',
        meta: {
            title: '订单管理',
        },
        component: () => import('components/orderManage/orderManage'),
        children:[
            {
                path: '',
                name: 'orderManageIndex',
                meta: {
                    title: '订单管理',
                },
                component:  () => import('components/orderManage/children/orderManageIndex'),
            },
            {
                path: 'orderDetail',
                name: 'orderDetail',
                meta: {
                    title: '咨询记录',
                },
                component:  () => import('components/orderManage/children/orderDetail'),
            },
        ],
    },

    //患者管理
    {
        path: '/patientManage',
        meta: {
            title: '患者管理',
        },
        component: () => import('components/patientManage/patientManage'),
        children: [
            {
                path: '',
                name: 'patientManage',
                meta: {
                    title: '患者管理',
                    parent: 'patientManage'
                },
                component: () => import('components/patientManage/children/patientManageIndex'),
            },
            {
                path: 'patientDetail',
                name: 'patientDetail',
                meta: {
                    title: '患者详情',
                    parent: 'patientManage'
                },
                component: () => import('components/patientManage/children/patientDetail'),
            },
            {
                path: 'followUpRecords',
                name: 'followUpRecords',
                meta: {
                    title: '慢病随访记录',
                },
                component: () => import('components/patientManage/children/followUpRecords'),
            },
            {
                path: 'followUpQuestionnaire',
                name: 'followUpQuestionnaire',
                meta: {
                    title: '随访问卷',
                    parent: 'patientManage'
                },
                component: () => import('components/patientManage/children/followUpQuestionnaire'),
            },
            {
                path: 'healthGuidanceRecords',
                name: 'healthGuidanceRecords',
                meta: {
                    title: '健康指导记录',
                    parent: 'patientManage'
                },
                component: () => import('components/patientManage/children/healthGuidanceRecords'),
            },
            {
                path: 'patientGroups',
                name: 'patientGroups',
                meta: {
                    title: '患者分组',
                    parent: 'patientManage'
                },
                component: () => import('components/patientManage/children/patientGroups'),
            }
        ]
    },

    //随访系统
    {
        path: '/followUp',
        name: 'followUpSystem',
        meta: {
            title: '随访系统'
        },
        component: {
            render(c) { return c('router-view') }
        },
        children: [
            {
                path: 'cUserList',
                name: 'followupUserList',
                meta: {
                    title: '随访用户列表',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/cUserList')
            },
            {
                path: 'plan',
                name: 'followUpPlan',
                meta: {
                    title: '随访计划',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/followUpPlan')
            },
            {
                path: 'smart-plan',
                name: 'smartFollowUpPlan',
                meta: {
                    title: '智能随访计划',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/children/SmartFollowUp')
            },
            {
                path: 'smart-edit',
                name: 'smartFollowUpEdit',
                meta: {
                    title: '智能随访编辑',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/children/SmartFollowUpEdit')
            },
            {
                path: 'wearableData',
                name: 'wearableData',
                meta: {
                    title: '可穿戴设备数据',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/wearableData')
            },
            {
                path: 'patientGroup',
                name: 'patientGroup',
                meta: {
                    title: '患者入组管理',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/patientGroup')
            },
            {
                path: 'groupList',
                name: 'groupList',
                meta: {
                    title: '分组列表',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/groupList')
            },
            {
                path: 'templateManage',
                name: 'templateManage',
                meta: {
                    title: '模板管理',
                    parent: 'followUpSystem'
                },
                component: () => import('components/followUp/templateManage')
            }
        ]
    },

    //模板管理
    {
        path: '/caseHistoryTemplate',
        name: 'caseHistoryTemplate',
        meta: {
            title: '病历模板'
        },
        component:  () => import('components/templateManage/children/caseHistoryTemplate'),
    },
    {
        path: '/prescriptionTemplate',
        name: 'prescriptionTemplate',
        meta: {
            title: '处方模板'
        },
        component:  () => import('components/templateManage/children/prescriptionTemplate'),
    },

    //账号设置
    {
        path: '/personInfo',
        name: 'personInfo',
        meta: {
            title: '个人资料',
            parent: 'accountSetting'
        },
        component: () => import('components/accountSetting/personInfo'),
    },
    {
        path: '/accountInfo',
        name: 'accountInfo',
        meta: {
            title: '账号信息',
            parent: 'accountSetting'
        },
        component: () => import('components/accountSetting/accountInfo'),
    },
    {
        path: '/pwdManage',
        name: 'pwdManage',
        meta: {
            title: '密码修改',
            parent: 'accountSetting'
        },
        component: () => import('components/accountSetting/pwdManage'),
    },

    //出院延续性服务
    {
        path: '/dischargeService',
        name: 'dischargeService',
        meta: {
            title: '出院延续性服务'
        },
        component: {
            render(c) { return c('router-view') }
        },
        children: [
            {
                path: 'referralAudit',
                name: 'referralAudit',
                meta: {
                    title: '转介信审核',
                    parent: 'dischargeService'
                },
                component: () => import('@/components/dischargeService/referralAudit.vue')
            }
        ]
    },

    //警示知识库建立
    {
        path: '/warningKnowledge',
        name: 'warningKnowledge',
        component: WarningKnowledge,
        meta: {
            title: '警示知识库建立'
        }
    },

    //个人专项警示
    {
        path: '/personalWarning',
        name: 'personalWarning',
        component: PersonalWarning,
        meta: {
            title: '个人专项警示'
        }
    },

    {
        path: '/outpatientSetDetail', //门诊设置详情页
        name: 'outpatientSetDetail',
        component: () =>
            import('components/outpatientManage/outpatientDetail.vue')
    }, {
        path: '/outpatientSet', //门诊设置
        name: 'outpatientSet',
        component: () =>
            import('components/outpatientManage/outpatientSet.vue')
    }, {
        path: '/serviceManagement', //服务管理
        name: 'serviceManagement',
        component: () =>
            import('components/outpatientManage/serviceManagement.vue')
    }, {
        path: '/schedulingPreview', //排班一览
        name: 'schedulingPreview',
        component: () =>
            import('components/outpatientManage/schedulingPreview.vue')
    }, {
        path: '/doctorScheduling', //排班设置
        name: 'doctorScheduling',
        component: () =>
            import('components/outpatientManage/doctorScheduling.vue')
    }, {
        path: '/outpatientNotice', //门诊问诊
        name: 'outpatientNotice',
        component: () =>
            import('components/outpatientManage/outpatientNotice.vue')
    // }, {
    //     path: '/videoOutpatient', //视频问诊
    //     name: 'videoOutpatient',
    //     component: () =>
    //         import('components/workforceManage/videoOutpatient.vue')
    // }, {
    //     path: '/workforceRule', //排班规则
    //     name: 'workforceRule',
    //     component: () =>
    //         import('components/workforceManage/workforceRule.vue')
    // }, {
    //     path: '/addWorkforce', //新增视频问诊排班
    //     name: 'addWorkforce',
    //     component: () =>
    //         import('components/workforceManage/addWorkforce.vue')
    },
];

const nav = [
    {
        path: '/',
        name: 'Redirect',
        redirect: '/login',
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('components/special/login')
    },
    {
        path: '/misspwd',
        name: 'misspwd',
        component: () => import('components/special/misspwd')
    },
    {
        path: '/home',
        component: Home,
        name: 'home',
        children: menuItems,
        redirect: '/workbench'
    },
    
    // 独立的Patient360页面（不需要登录）
    {
        path: '/patient360-embed',
        name: 'patient360-embed',
        component: () => import('components/patient360/index.vue'),
        meta: {
            requiresAuth: true,
            title: 'Patient360外部集成'
        }
    },
    // 独立的Patient360演示页面（不需要登录）
    {
        path: '/patient360-demo-embed',
        name: 'patient360-demo-embed',
        component: () => import('components/patient360/demo-page.vue'),
        meta: {
            requiresAuth: true,
            title: 'Patient360演示'
        }
    }
];

export default nav;

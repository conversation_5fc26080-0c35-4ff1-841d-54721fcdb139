import request from '@/utils/request'

// 保存问卷答案（C端患者填写问卷）
export function saveQuestionnaireAnswer(data) {
    return request({
        url: '/emr/questionnaire-answers/save',
        method: 'post',
        data
    })
}

// 批量保存问卷答案
export function batchSaveQuestionnaireAnswers(data) {
    return request({
        url: '/emr/questionnaire-answers/batch-save',
        method: 'post',
        data
    })
}

// 根据ID获取答案
export function getQuestionnaireAnswerById(id) {
    return request({
        url: `/emr/questionnaire-answers/${id}`,
        method: 'get'
    })
}

// 分页查询问卷答案
export function getQuestionnaireAnswerPage(params) {
    return request({
        url: '/emr/questionnaire-answers/page',
        method: 'get',
        params
    })
}

// 根据任务ID查询问卷答案
export function getQuestionnaireAnswersByTaskId(taskId) {
    return request({
        url: `/emr/questionnaire-answers/by-task/${taskId}`,
        method: 'get'
    })
}

// 根据患者ID和问卷ID查询答案
export function getQuestionnaireAnswersByPatientAndQuestionnaire(params) {
    return request({
        url: '/emr/questionnaire-answers/by-patient-questionnaire',
        method: 'get',
        params
    })
}

// 根据患者ID查询所有答案
export function getQuestionnaireAnswersByPatientId(patientId) {
    return request({
        url: `/emr/questionnaire-answers/by-patient/${patientId}`,
        method: 'get'
    })
}

// 统计患者问卷完成情况
export function getPatientQuestionnaireStats(patientId) {
    return request({
        url: `/emr/questionnaire-answers/stats/${patientId}`,
        method: 'get'
    })
}

// 计算问卷得分
export function calculateQuestionnaireScore(params) {
    return request({
        url: '/emr/questionnaire-answers/score',
        method: 'get',
        params
    })
}

// 检查问卷是否已完成
export function isQuestionnaireCompleted(params) {
    return request({
        url: '/emr/questionnaire-answers/completed',
        method: 'get',
        params
    })
}

// 获取患者问卷完成率
export function getPatientCompletionRate(patientId) {
    return request({
        url: `/emr/questionnaire-answers/completion-rate/${patientId}`,
        method: 'get'
    })
}

// 删除答案
export function deleteQuestionnaireAnswer(id) {
    return request({
        url: `/emr/questionnaire-answers/${id}`,
        method: 'delete'
    })
}

// 默认导出所有API方法
export default {
    saveQuestionnaireAnswer,
    batchSaveQuestionnaireAnswers,
    getQuestionnaireAnswerById,
    getQuestionnaireAnswerPage,
    getQuestionnaireAnswersByTaskId,
    getQuestionnaireAnswersByPatientAndQuestionnaire,
    getQuestionnaireAnswersByPatientId,
    getPatientQuestionnaireStats,
    calculateQuestionnaireScore,
    isQuestionnaireCompleted,
    getPatientCompletionRate,
    deleteQuestionnaireAnswer
} 
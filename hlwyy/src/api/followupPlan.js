import axios from 'axios';

// 配置基础URL - 使用代理路径
const baseURL = '/api';

const api = axios.create({
    baseURL: baseURL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        // 可以在这里添加token等认证信息
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        console.error('API请求错误:', error);
        return Promise.reject(error);
    }
);

// 获取随访计划分页列表
export function getFollowupPlanPage(params) {
    return api.get('/emr/followup/plan/page', { params });
}

// 根据ID获取随访计划详情
export function getFollowupPlanById(id) {
    return api.get(`/emr/followup/plan/${id}`);
}

// 创建随访计划
export function saveFollowupPlan(data) {
    return api.post('/emr/followup/plan', data);
}

// 更新随访计划
export function updateFollowupPlan(data) {
    return api.put(`/emr/followup/plan/${data.id}`, data);
}

// 删除随访计划
export function deleteFollowupPlan(id) {
    return api.delete(`/emr/followup/plan/${id}`);
}

// 更新计划状态
export function updatePlanStatus(id, status) {
    return api.put(`/emr/followup/plan/${id}/status`, { status });
}

// 生成二维码
export function generateQRCode(id) {
    return api.post(`/emr/followup/plan/${id}/qrcode`);
}

// 获取计划统计信息
export function getPlanStatistics(id) {
    return api.get(`/emr/followup/plan/${id}/statistics`);
}

// 获取子计划问卷关联信息
export function getSubplanQuestionnaires(subplanId) {
    return api.get(`/emr/followup/subplan/${subplanId}/questionnaires`);
}

// 默认导出所有API方法
export default {
    getFollowupPlanPage,
    getFollowupPlanById,
    saveFollowupPlan,
    updateFollowupPlan,
    deleteFollowupPlan,
    updatePlanStatus,
    generateQRCode,
    getPlanStatistics,
    getSubplanQuestionnaires
} 
import {
    Loading,
    Message
} from 'element-ui';
import axios from 'axios';

import { encryptSm4 } from 'js/yydjs'
import md5 from 'md5';



let base = '/cas-admin';
// let ser = process.env.NODE_ENV;
// if(ser == "development") {
//     base = '/cas-admin';
// } else if(ser == "production") {
//     base = ''
// }
var locationUrl = location.hostname == 'localhost' ? proxyUrl : location.origin;

var commonurl, imguploadurl, imgview, filedowned, qrcodeurl, verImgUrl, profile, socketUrl;
verImgUrl = locationUrl;
const Version = '*******';
//上海大区交接测试
// base = "";
commonurl = base + '/*.jsonRequest';
imguploadurl = '/cas-admin/file/upload';
imgview = 'http://file-prm.sh4th.com/upload/image/';
filedowned = 'http://file-prm.sh4th.com/download/';
qrcodeurl = 'https://app-prm.sh4th.com/app/down.html?data=';
socketUrl = locationUrl.replace('https', 'ws').replace('http', 'ws');
profile = 'prm';


export {
    commonurl,
    imguploadurl,
    imgview,
    filedowned,
    qrcodeurl,
    verImgUrl,
    socketUrl,
    profile,
};

// 登录提交获取角色列表
export const requestrolelist = params => {
    let loadingInstance1 = Loading.service({
        fullscreen: false,
        target: "content"
    });
    var instance = axios.create({
        headers: {
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
        }
    });
    return instance.post(`${base}/logon/myRoles`, params).then(res => {
        loadingInstance1.close();
        if (res.data) {
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        loadingInstance1.close();
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            Message({
                type: "error",
                showClose: true,
                message: "服务器内部错误"
            });
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;
    });
};
// 登录提交保存sessionStorage的accessToken
export const requestLoginon = params => {
    var instance = axios.create({
        headers: {
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
        }
    })
    return instance.post(`${base}/logon/login`, params).then(res => {
        if (res.data) {
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            Message({
                type: "error",
                showClose: true,
                message: "服务器内部错误"
            });
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;
    });
}

const getSignature = (data) => {
    let accessToken = sessionStorage.getItem("accessToken");

    if (accessToken.length >= 8) {
        let salt = accessToken.substr(4, 4)
        let body = [salt, JSON.stringify(data)].sort()
        let signature = md5(body[0] + body[1])

        return signature;
    }

    return '';
}

// 通用ajax请求方法
export const commonAjax = (ServiceId, ServiceMethod, params, noMask) => {
    let loadingInstance1 = null;

    if (!noMask) {
        loadingInstance1 = Loading.service({
            target: "#app",
            background: 'transparent',
        });
    }
    params = {
        data: encryptSm4(params)
    }
    var signature = getSignature(params);
    var headSign = {};
    if (signature) {
        headSign['X-Signature'] = signature;
    }
    let instance = axios.create({
        headers: {
            "X-Access-Token": sessionStorage.getItem("accessToken"),
            "X-Service-Id": ServiceId,
            "X-Service-Method": ServiceMethod,
            "Content-Type": "application/json",
            "roleIds": sessionStorage.getItem("roleIds"),
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
            ...headSign,
        }
    })
    return instance.post(`${base}/*.jsonRequest`, params).then(res => {
        setTimeout(() => {
            if (!noMask) loadingInstance1.close();
        }, 300);
        if (res.data) {
            // if (res.data.code && res.data.code != 200) {
            //     var err = new Error();
            //     err.msg = res.data.msg ? res.data.msg : "服务器内部错误";
            //     return Promise.reject(err);
            // }
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        setTimeout(() => {
            if (!noMask) loadingInstance1.close();
        }, 300);
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            if (err.response.status == 403) {
                if (window.nim) {
                    window.nim.destroy({
                        done: function (err) {
                            window.nim = null;
                            console.log('nim已销毁');
                        }
                    });
                }
                
                // 创建或更新本地存储中的错误计数器
                let errorCount = localStorage.getItem('api_error_count') || 0;
                errorCount = parseInt(errorCount) + 1;
                localStorage.setItem('api_error_count', errorCount);
                
                // 只在第一次出现错误时显示提示，避免大量提示
                if (errorCount <= 3) {
                    Message({
                        type: "warning",
                        showClose: true,
                        message: "检测到登录状态异常，如遇功能问题请刷新页面"
                    });
                }
                
                // 尝试从localStorage获取备份的token (如果有)
                const backupToken = localStorage.getItem('backup_token');
                if (backupToken) {
                    sessionStorage.setItem("accessToken", backupToken);
                    console.log('已恢复会话token');
                }
            } else {
                Message({
                    type: "error",
                    showClose: true,
                    message: "服务器内部错误"
                });
            }
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;
    });
}

// 通用ajax请求方法,不加密
export const commonAjaxNoEncrypt = (ServiceId, ServiceMethod, params, noMask) => {
    let loadingInstance1 = null;

    if (!noMask) {
        loadingInstance1 = Loading.service({
            target: "#app",
            background: 'transparent',
        });
    }
    // params = {
    //     data: encryptSm4(params)
    // }
    // var signature = getSignature(params);
    var headSign = {};
    // if (signature) {
    //     headSign['X-Signature'] = signature;
    // }
    let instance = axios.create({
        headers: {
            "X-Access-Token": sessionStorage.getItem("accessToken"),
            "X-Service-Id": ServiceId,
            "X-Service-Method": ServiceMethod,
            "Content-Type": "application/json",
            "roleIds": sessionStorage.getItem("roleIds"),
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
            ...headSign,
        }
    })
    return instance.post(`${base}/*.jsonRequest`, params).then(res => {
        setTimeout(() => {
            if (!noMask) loadingInstance1.close();
        }, 300);
        if (res.data) {
            // if (res.data.code && res.data.code != 200) {
            //     var err = new Error();
            //     err.msg = res.data.msg ? res.data.msg : "服务器内部错误";
            //     return Promise.reject(err);
            // }
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        setTimeout(() => {
            if (!noMask) loadingInstance1.close();
        }, 300);
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            if (err.response.status == 403) {
                if (window.nim) {
                    window.nim.destroy({
                        done: function (err) {
                            window.nim = null;
                            console.log('nim已销毁');
                        }
                    });
                }
                
                // 创建或更新本地存储中的错误计数器
                let errorCount = localStorage.getItem('api_error_count') || 0;
                errorCount = parseInt(errorCount) + 1;
                localStorage.setItem('api_error_count', errorCount);
                
                // 只在第一次出现错误时显示提示，避免大量提示
                if (errorCount <= 3) {
                    Message({
                        type: "warning",
                        showClose: true,
                        message: "检测到登录状态异常，如遇功能问题请刷新页面"
                    });
                }
                
                // 尝试从localStorage获取备份的token (如果有)
                const backupToken = localStorage.getItem('backup_token');
                if (backupToken) {
                    sessionStorage.setItem("accessToken", backupToken);
                    console.log('已恢复会话token');
                }
            } else {
                Message({
                    type: "error",
                    showClose: true,
                    message: "服务器内部错误"
                });
            }
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;
    });
}

// 用于编辑器的图片上传
export const imguploadAjax = params => {
    let loadingInstance1 = Loading.service({
        fullscreen: false,
        target: "content"
    });
    var instance = axios.create({
        headers: {
            "X-Access-Token": sessionStorage.getItem("accessToken"),
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
        }
    });
    return instance.post(`${base}/file/upload`, params).then(res => {
        loadingInstance1.close();
        if (res.data) {
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        loadingInstance1.close();
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            Message({
                type: "error",
                showClose: true,
                message: "服务器内部错误"
            });
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;
    });
};

export const signDistrictReportURL = `${base}/down/districtReport`;
export const signOrgReportURL = `${base}/down/orgReport`;

// 地区的请求
export const areaAjax = (params) => {
    let loadingInstance1 = Loading.service({
        fullscreen: false,
        target: "content"
    });
    var instance = axios.create({
        headers: {
            "X-Access-Token": sessionStorage.getItem("accessToken"),
            "Content-Type": "application/json",
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
        },
        // method: 'get',
        params: params,
    });
    return instance.get(`${base}/cfs.dic.base_region.dic/`).then(res => {
        loadingInstance1.close();
        if (res.data) {
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            Message({
                type: "error",
                showClose: true,
                message: "服务器内部错误"
            });
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;
    });
};


//
export const groupKindAjax = (params) => {
    let loadingInstance1 = Loading.service({
        fullscreen: false,
        target: "content"
    });
    var instance = axios.create({
        headers: {
            "X-Access-Token": sessionStorage.getItem("accessToken"),
            "Content-Type": "application/json",
            "B-Product-Code": sessionStorage.getItem("peoCode"),
            "B-Version": Version,
        },
        // method: 'get',
        params: params,
    });
    return instance.get(`${base}/cfs.dic.base_group.dic/`).then(res => {
        loadingInstance1.close();
        if (res.data) {
            return res.data;
        } else {
            var err = new Error();
            err.msg = "服务器内部错误";
            return Promise.reject(err);
        }
    }).catch(function (err) {
        loadingInstance1.close();
        if (err.msg) {
            Message({
                type: "error",
                showClose: true,
                message: err.msg
            });
        } else if (err.response) {
            Message({
                type: "error",
                showClose: true,
                message: "服务器内部错误"
            });
        } else {
            Message({
                type: "error",
                showClose: true,
                message: "网络错误!请检查网络!"
            });
        }
        throw err;

    });
};

// export const requestLogin = params => {
//  return axios.post(`${base}/login`, params).then(res => res.data);
// };

// export const getUserList = params => {
//  return axios.get(`${base}/user/list`, {
//      params: params
//  });
// };

// export const getUserListPage = params => {
//  return axios.get(`${base}/user/listpage`, {
//      params: params
//  });
// };

// export const removeUser = params => {
//  return axios.get(`${base}/user/remove`, {
//      params: params
//  });
// };

// export const batchRemoveUser = params => {
//  return axios.get(`${base}/user/batchremove`, {
//      params: params
//  });
// };

// export const editUser = params => {
//  return axios.get(`${base}/user/edit`, {
//      params: params
//  });
// };

// export const addUser = params => {
//  return axios.get(`${base}/user/add`, {
//      params: params
//  });
// };

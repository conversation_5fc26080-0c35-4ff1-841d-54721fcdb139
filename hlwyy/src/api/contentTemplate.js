import axios from 'axios';

// 配置基础URL - 使用代理路径
const baseURL = '/api';

const api = axios.create({
    baseURL: baseURL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        // 可以在这里添加token等认证信息
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    response => {
        return response.data;
    },
    error => {
        console.error('API请求错误:', error);
        // 处理错误响应
        if (error.response && error.response.data) {
            const errorData = error.response.data;
            if (errorData.error && errorData.message) {
                // 抛出带有错误信息的异常
                const errorMessage = errorData.message;
                return Promise.reject(new Error(errorMessage));
            }
        }
        return Promise.reject(error);
    }
);

// 内容模板管理接口
export const contentTemplateApi = {
    // 获取模板列表
    async getTemplateList(params) {
        try {
            const response = await api.post('/emr/content-template/getTemplateList', params);
            return response;
        } catch (error) {
            console.error('获取模板列表失败:', error);
            throw error;
        }
    },

    // 根据ID获取模板详情
    async getTemplateById(id) {
        try {
            const response = await api.post('/emr/content-template/getTemplateById', { id });
            return response;
        } catch (error) {
            console.error('获取模板详情失败:', error);
            throw error;
        }
    },

    // 创建内容模板
    async createTemplate(params) {
        try {
            const response = await api.post('/emr/content-template/createTemplate', params);
            return response;
        } catch (error) {
            console.error('创建模板失败:', error);
            throw error;
        }
    },

    // 更新内容模板
    async updateTemplate(params) {
        try {
            const response = await api.post('/emr/content-template/updateTemplate', params);
            return response;
        } catch (error) {
            console.error('更新模板失败:', error);
            throw error;
        }
    },

    // 删除内容模板
    async deleteTemplate(id) {
        try {
            const response = await api.post('/emr/content-template/deleteTemplate', { id });
            return response;
        } catch (error) {
            console.error('删除模板失败:', error);
            throw error;
        }
    },

    // 复制内容模板
    async copyTemplate(id) {
        try {
            const response = await api.post('/emr/content-template/copyTemplate', { id });
            return response;
        } catch (error) {
            console.error('复制模板失败:', error);
            throw error;
        }
    },

    // 批量删除模板
    async batchDeleteTemplates(ids) {
        try {
            const response = await api.post('/emr/content-template/batchDeleteTemplates', { ids });
            return response;
        } catch (error) {
            console.error('批量删除模板失败:', error);
            throw error;
        }
    },

    // 获取模板分类列表
    async getCategoryList() {
        try {
            const response = await api.post('/emr/content-template/getCategoryList', {});
            return response;
        } catch (error) {
            console.error('获取分类列表失败:', error);
            throw error;
        }
    },

    // 根据分类获取模板列表
    async getTemplatesByCategory(params) {
        try {
            const response = await api.post('/emr/content-template/getTemplatesByCategory', params);
            return response;
        } catch (error) {
            console.error('根据分类获取模板列表失败:', error);
            throw error;
        }
    },

    // 搜索模板
    async searchTemplates(params) {
        try {
            const response = await api.post('/emr/content-template/searchTemplates', params);
            return response;
        } catch (error) {
            console.error('搜索模板失败:', error);
            throw error;
        }
    },

    // 更新模板状态
    async updateTemplateStatus(id, status) {
        try {
            const response = await api.post('/emr/content-template/updateTemplateStatus', { id, status });
            return response;
        } catch (error) {
            console.error('更新模板状态失败:', error);
            throw error;
        }
    }
};

export default contentTemplateApi; 
import request from '@/utils/request'

/**
 * 任务调度API
 */

// 手动触发任务生成
export function generateTasks(planId) {
  return request({
    url: '/api/emr/followup/scheduler/generate-tasks',
    method: 'post',
    params: { planId }
  })
}

// 发送任务提醒消息
export function sendReminders(taskIds) {
  return request({
    url: '/api/emr/followup/scheduler/send-reminders',
    method: 'post',
    data: taskIds
  })
}

// 更新任务状态
export function updateTaskStatus(taskId, status) {
  return request({
    url: '/api/emr/followup/scheduler/update-task-status',
    method: 'put',
    params: { taskId, status }
  })
}

// 批量更新任务状态
export function batchUpdateTaskStatus(taskIds, status) {
  return request({
    url: '/api/emr/followup/scheduler/batch-update-status',
    method: 'put',
    params: { status },
    data: taskIds
  })
}

// 获取调度统计信息
export function getSchedulerStats() {
  return request({
    url: '/api/emr/followup/scheduler/stats',
    method: 'get'
  })
}

// 获取计划调度统计
export function getPlanSchedulerStats(planId) {
  return request({
    url: `/api/emr/followup/scheduler/plan-stats/${planId}`,
    method: 'get'
  })
}

// 启动任务调度器
export function startScheduler() {
  return request({
    url: '/api/emr/followup/scheduler/start',
    method: 'post'
  })
}

// 停止任务调度器
export function stopScheduler() {
  return request({
    url: '/api/emr/followup/scheduler/stop',
    method: 'post'
  })
}

// 获取调度器状态
export function getSchedulerStatus() {
  return request({
    url: '/api/emr/followup/scheduler/status',
    method: 'get'
  })
}

// 重新调度任务
export function rescheduleTask(taskId) {
  return request({
    url: `/api/emr/followup/scheduler/reschedule/${taskId}`,
    method: 'post'
  })
}

// 清理过期任务
export function cleanupExpiredTasks() {
  return request({
    url: '/api/emr/followup/scheduler/cleanup-expired',
    method: 'post'
  })
}

// 获取任务执行日志
export function getTaskExecutionLogs(params) {
  return request({
    url: '/api/emr/followup/scheduler/logs',
    method: 'get',
    params
  })
} 
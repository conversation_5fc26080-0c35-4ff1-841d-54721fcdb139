import axios from 'axios';

// 配置基础URL - 使用代理路径
const baseURL = '/api/emr';

const api = axios.create({
    baseURL: baseURL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        // 可以在这里添加token等认证信息
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    response => {
        console.log('API响应:', response.config.url, response.data);
        return response.data;
    },
    error => {
        console.error('API请求错误:', error);
        // 处理错误响应
        if (error.response && error.response.data) {
            const errorData = error.response.data;
            if (errorData.error && errorData.message) {
                // 抛出带有错误信息的异常
                const errorMessage = errorData.message;
                return Promise.reject(new Error(errorMessage));
            }
        }
        return Promise.reject(error);
    }
);

// 问卷相关API
export const questionnaireApi = {
    // 获取所有问卷列表
    async getAllQuestionnaires() {
        try {
            const response = await api.get('/questionnaires/with-details');
            return response;
        } catch (error) {
            console.error('获取问卷列表失败:', error);
            throw error;
        }
    },
    
    // 根据ID获取问卷详情
    async getQuestionnaireById(id) {
        try {
            const response = await api.get(`/questionnaires/with-details/${id}`);
            return response;
        } catch (error) {
            console.error('获取问卷详情失败:', error);
            throw error;
        }
    },
    
    // 创建问卷
    async createQuestionnaire(data) {
        try {
            const response = await api.post('/questionnaires/with-details', data);
            return response;
        } catch (error) {
            console.error('创建问卷失败:', error);
            throw error;
        }
    },
    
    // 更新问卷
    async updateQuestionnaire(id, data) {
        try {
            const response = await api.put(`/questionnaires/with-details/${id}`, data);
            return response;
        } catch (error) {
            console.error('更新问卷失败:', error);
            throw error;
        }
    },
    
    // 删除问卷
    async deleteQuestionnaire(id) {
        try {
            const response = await api.delete(`/questionnaires/${id}`);
            return response;
        } catch (error) {
            console.error('删除问卷失败:', error);
            throw error;
        }
    }
};

export default questionnaireApi; 
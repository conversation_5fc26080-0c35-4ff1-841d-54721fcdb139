import axios from 'axios';

// 配置基础URL - 使用代理路径
const baseURL = '/api';

const api = axios.create({
    baseURL: baseURL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        // 可以在这里添加token等认证信息
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        console.error('API请求错误:', error);
        return Promise.reject(error);
    }
);

// 获取患者分组列表
export function getPatientGroupList(params) {
    return api.get('/emr/patient/group/page', { params });
}

// 根据ID获取患者分组详情
export function getPatientGroupById(id) {
    return api.get(`/emr/patient/group/${id}`);
}

// 创建患者分组
export function savePatientGroup(data) {
    return api.post('/emr/patient/group', data);
}

// 更新患者分组
export function updatePatientGroup(data) {
    return api.put(`/emr/patient/group/${data.id}`, data);
}

// 删除患者分组
export function deletePatientGroup(id) {
    return api.delete(`/emr/patient/group/${id}`);
}

// 获取分组内的患者列表
export function getGroupPatients(groupId, params) {
    return api.get(`/emr/patient/group/${groupId}/patients`, { params });
}

// 审核患者入组申请
export function auditPatientJoinRequest(data) {
    return api.post('/emr/patient/group/audit', data);
}

// 批量审核患者入组申请
export function batchAuditPatientJoinRequest(data) {
    return api.post('/emr/patient/group/audit/batch', data);
}

// 默认导出所有API方法
export default {
    getPatientGroupList,
    getPatientGroupById,
    savePatientGroup,
    updatePatientGroup,
    deletePatientGroup,
    getGroupPatients,
    auditPatientJoinRequest,
    batchAuditPatientJoinRequest
} 
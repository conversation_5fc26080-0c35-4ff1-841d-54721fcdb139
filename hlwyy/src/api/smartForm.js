import axios from 'axios';

// 配置基础URL - 使用代理路径
const baseURL = '/api/emr';

const api = axios.create({
    baseURL: baseURL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});

// 请求拦截器
api.interceptors.request.use(
    config => {
        // 可以在这里添加token等认证信息
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
api.interceptors.response.use(
    response => {
        return response.data;
    },
    error => {
        console.error('API请求错误:', error);
        // 处理错误响应
        if (error.response && error.response.data) {
            const errorData = error.response.data;
            if (errorData.error && errorData.message) {
                // 抛出带有错误信息的异常
                const errorMessage = errorData.message;
                return Promise.reject(new Error(errorMessage));
            }
        }
        return Promise.reject(error);
    }
);

// 智能表单相关API
export const smartFormApi = {
    // ========================
    // 表单模板管理
    // ========================
    
    // 获取表单模板列表
    async getFormTemplateList(params) {
        try {
            const response = await api.post('/smart-form/getFormTemplateList', params);
            return response;
        } catch (error) {
            console.error('获取表单模板列表失败:', error);
            throw error;
        }
    },
    
    // 根据ID获取表单模板详情
    async getFormTemplateById(id) {
        try {
            const response = await api.post('/smart-form/getFormTemplateById', { id });
            return response;
        } catch (error) {
            console.error('获取表单模板详情失败:', error);
            throw error;
        }
    },
    
    // 创建表单模板
    async createFormTemplate(data) {
        try {
            const response = await api.post('/smart-form/createFormTemplate', data);
            return response;
        } catch (error) {
            console.error('创建表单模板失败:', error);
            throw error;
        }
    },
    
    // 更新表单模板
    async updateFormTemplate(data) {
        try {
            const response = await api.post('/smart-form/updateFormTemplate', data);
            return response;
        } catch (error) {
            console.error('更新表单模板失败:', error);
            throw error;
        }
    },
    
    // 删除表单模板
    async deleteFormTemplate(id) {
        try {
            const response = await api.post('/smart-form/deleteFormTemplate', { id });
            return response;
        } catch (error) {
            console.error('删除表单模板失败:', error);
            throw error;
        }
    },
    
    // 复制表单模板
    async copyFormTemplate(sourceId, newName) {
        try {
            const response = await api.post('/smart-form/copyFormTemplate', { sourceId, newName });
            return response;
        } catch (error) {
            console.error('复制表单模板失败:', error);
            throw error;
        }
    },
    
    // 批量删除表单模板
    async batchDeleteFormTemplates(ids) {
        try {
            const response = await api.post('/smart-form/batchDeleteFormTemplates', { ids });
            return response;
        } catch (error) {
            console.error('批量删除表单模板失败:', error);
            throw error;
        }
    },
    
    // 更新表单模板状态
    async updateFormTemplateStatus(id, status) {
        try {
            const response = await api.post('/smart-form/updateFormTemplateStatus', { id, status });
            return response;
        } catch (error) {
            console.error('更新表单模板状态失败:', error);
            throw error;
        }
    },

    // ========================
    // 表单问卷关联管理
    // ========================
    
    // 获取可用问卷列表
    async getAvailableQuestionnaires(params) {
        try {
            const response = await api.post('/smart-form/getAvailableQuestionnaires', params);
            return response;
        } catch (error) {
            console.error('获取可用问卷列表失败:', error);
            throw error;
        }
    },
    
    // 添加表单问卷关联
    async addFormQuestionnaire(data) {
        try {
            const response = await api.post('/smart-form/addFormQuestionnaire', data);
            return response;
        } catch (error) {
            console.error('添加表单问卷关联失败:', error);
            throw error;
        }
    },
    
    // 更新表单问卷关联
    async updateFormQuestionnaire(data) {
        try {
            const response = await api.post('/smart-form/updateFormQuestionnaire', data);
            return response;
        } catch (error) {
            console.error('更新表单问卷关联失败:', error);
            throw error;
        }
    },
    
    // 删除表单问卷关联
    async removeFormQuestionnaire(id) {
        try {
            const response = await api.post('/smart-form/removeFormQuestionnaire', { id });
            return response;
        } catch (error) {
            console.error('删除表单问卷关联失败:', error);
            throw error;
        }
    },

    // ========================
    // 表单数据收集
    // ========================
    
    // 获取表单填写页面数据
    async getFormForFilling(params) {
        try {
            const response = await api.post('/smart-form/getFormForFilling', params);
            return response;
        } catch (error) {
            console.error('获取表单填写页面数据失败:', error);
            throw error;
        }
    },
    
    // 提交表单数据
    async submitFormData(data) {
        try {
            const response = await api.post('/smart-form/submitFormData', data);
            return response;
        } catch (error) {
            console.error('提交表单数据失败:', error);
            throw error;
        }
    },
    
    // 保存草稿
    async saveFormDraft(data) {
        try {
            const response = await api.post('/smart-form/saveFormDraft', data);
            return response;
        } catch (error) {
            console.error('保存草稿失败:', error);
            throw error;
        }
    },
    
    // 获取草稿数据
    async getFormDraft(params) {
        try {
            const response = await api.post('/smart-form/getFormDraft', params);
            return response;
        } catch (error) {
            console.error('获取草稿数据失败:', error);
            throw error;
        }
    },

    // ========================
    // 数据查看与分析
    // ========================
    
    // 获取表单提交数据列表
    async getFormSubmissionList(params) {
        try {
            const response = await api.post('/smart-form/getFormSubmissionList', params);
            return response;
        } catch (error) {
            console.error('获取表单提交数据列表失败:', error);
            throw error;
        }
    },
    
    // 获取表单提交详情
    async getFormSubmissionDetail(id) {
        try {
            const response = await api.post('/smart-form/getFormSubmissionDetail', { id });
            return response;
        } catch (error) {
            console.error('获取表单提交详情失败:', error);
            throw error;
        }
    },
    
    // 获取表单数据分析
    async getFormDataAnalysis(params) {
        try {
            const response = await api.post('/smart-form/getFormDataAnalysis', params);
            return response;
        } catch (error) {
            console.error('获取表单数据分析失败:', error);
            throw error;
        }
    },
    
    // 获取表单统计分析
    async getFormStatistics(params) {
        try {
            const response = await api.post('/smart-form/getFormStatistics', params);
            return response;
        } catch (error) {
            console.error('获取表单统计分析失败:', error);
            throw error;
        }
    },

    // ========================
    // 表单分享配置
    // ========================
    
    // 创建表单分享
    async createFormShare(data) {
        try {
            const response = await api.post('/smart-form/createFormShare', data);
            return response;
        } catch (error) {
            console.error('创建表单分享失败:', error);
            throw error;
        }
    },
    
    // 获取表单分享信息
    async getFormShareInfo(formTemplateId) {
        try {
            const response = await api.post('/smart-form/getFormShareInfo', { formTemplateId });
            return response;
        } catch (error) {
            console.error('获取表单分享信息失败:', error);
            throw error;
        }
    },
    
    // 更新表单分享
    async updateFormShare(data) {
        try {
            const response = await api.post('/smart-form/updateFormShare', data);
            return response;
        } catch (error) {
            console.error('更新表单分享失败:', error);
            throw error;
        }
    },
    
    // 删除表单分享
    async deleteFormShare(id) {
        try {
            const response = await api.post('/smart-form/deleteFormShare', { id });
            return response;
        } catch (error) {
            console.error('删除表单分享失败:', error);
            throw error;
        }
    },

    // ========================
    // 其他功能
    // ========================
    
    // 获取表单分类列表
    async getFormCategories() {
        try {
            const response = await api.post('/smart-form/getFormCategories', {});
            return response;
        } catch (error) {
            console.error('获取表单分类列表失败:', error);
            throw error;
        }
    },
    
    // 导出表单数据
    async exportFormData(params) {
        try {
            const response = await api.post('/smart-form/exportFormData', params, {
                responseType: 'blob'
            });
            return response;
        } catch (error) {
            console.error('导出表单数据失败:', error);
            throw error;
        }
    },
    
    // 导入表单模板
    async importFormTemplate(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await api.post('/smart-form/importFormTemplate', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response;
        } catch (error) {
            console.error('导入表单模板失败:', error);
            throw error;
        }
    },
    
    // 验证表单配置
    async validateFormConfig(data) {
        try {
            const response = await api.post('/smart-form/validateFormConfig', data);
            return response;
        } catch (error) {
            console.error('验证表单配置失败:', error);
            throw error;
        }
    }
};

export default smartFormApi; 
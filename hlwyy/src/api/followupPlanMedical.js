import request from '@/utils/request'

/**
 * 随访计划医护端API
 */

// 分页查询随访计划列表
export function getFollowupPlanPage(params) {
  return request({
    url: '/emr/followup/plan/page',
    method: 'get',
    params
  })
}

// 获取随访计划详情
export function getFollowupPlanById(id) {
  return request({
    url: `/emr/followup/plan/${id}`,
    method: 'get'
  })
}

// 创建随访计划
export function createFollowupPlan(data) {
  return request({
    url: '/emr/followup/plan',
    method: 'post',
    data
  })
}

// 更新随访计划
export function updateFollowupPlan(id, data) {
  return request({
    url: `/emr/followup/plan/${id}`,
    method: 'put',
    data
  })
}

// 删除随访计划
export function deleteFollowupPlan(id) {
  return request({
    url: `/emr/followup/plan/${id}`,
    method: 'delete'
  })
}

// 批量删除随访计划
export function batchDeleteFollowupPlans(ids) {
  return request({
    url: '/emr/followup/plan/batch',
    method: 'delete',
    data: ids
  })
}

// 更新计划状态
export function updatePlanStatus(id, status) {
  return request({
    url: `/emr/followup/plan/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 获取计划统计信息
export function getPlanStats(id) {
  return request({
    url: `/emr/followup/plan/${id}/stats`,
    method: 'get'
  })
}

// 获取子计划列表
export function getSubplansByPlanId(planId) {
  return request({
    url: `/emr/followup/plan/${planId}/subplans`,
    method: 'get'
  })
}

// 创建子计划
export function createSubplan(planId, data) {
  return request({
    url: `/emr/followup/plan/${planId}/subplan`,
    method: 'post',
    data
  })
}

// 更新子计划
export function updateSubplan(id, data) {
  return request({
    url: `/emr/followup/plan/subplan/${id}`,
    method: 'put',
    data
  })
}

// 删除子计划
export function deleteSubplan(id) {
  return request({
    url: `/emr/followup/plan/subplan/${id}`,
    method: 'delete'
  })
}

// 获取任务列表
export function getTasksByPlanId(planId, params) {
  return request({
    url: `/emr/followup/plan/${planId}/tasks`,
    method: 'get',
    params
  })
}

// 手动生成任务
export function generateTasks(planId) {
  return request({
    url: `/emr/followup/plan/${planId}/generate-tasks`,
    method: 'post'
  })
}

// 更新任务状态
export function updateTaskStatus(id, status) {
  return request({
    url: `/emr/followup/plan/task/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 获取计划执行统计
export function getPlanExecutionStats(planId) {
  return request({
    url: `/emr/followup/plan/${planId}/execution-stats`,
    method: 'get'
  })
}

// 复制随访计划
export function copyPlan(id) {
  return request({
    url: `/emr/followup/plan/${id}/copy`,
    method: 'post'
  })
}

// 导出计划数据
export function exportPlanData(id) {
  return request({
    url: `/emr/followup/plan/${id}/export`,
    method: 'get'
  })
}

// 默认导出所有API方法
export default {
  getFollowupPlanPage,
  getFollowupPlanById,
  createFollowupPlan,
  updateFollowupPlan,
  deleteFollowupPlan,
  batchDeleteFollowupPlans,
  updatePlanStatus,
  getPlanStats,
  getSubplansByPlanId,
  createSubplan,
  updateSubplan,
  deleteSubplan,
  getTasksByPlanId,
  generateTasks,
  updateTaskStatus,
  getPlanExecutionStats,
  copyPlan,
  exportPlanData
} 
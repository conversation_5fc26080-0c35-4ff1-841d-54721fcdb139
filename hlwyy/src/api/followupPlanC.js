import request from '@/utils/request'

// 获取患者参与的随访计划列表
export function getPatientPlans(params) {
    return request({
        url: '/emr/followup/plan/c/patient-plans',
        method: 'get',
        params
    })
}

// 获取随访计划详情
export function getPlanDetail(planId) {
    return request({
        url: `/emr/followup/plan/c/plan-detail/${planId}`,
        method: 'get'
    })
}

// 患者确认入组申请
export function joinGroup(data) {
    return request({
        url: '/emr/followup/plan/c/join-group',
        method: 'post',
        data
    })
}

// 获取患者分组状态
export function getGroupStatus(patientId) {
    return request({
        url: `/emr/followup/plan/c/group-status/${patientId}`,
        method: 'get'
    })
}

// 获取患者基本信息
export function getPatientInfo(patientId) {
    return request({
        url: `/emr/followup/plan/c/patient-info/${patientId}`,
        method: 'get'
    })
}

// 获取患者任务列表
export function getPatientTasks(params) {
    return request({
        url: `/emr/followup/plan/c/patient-tasks/${params.patientId}`,
        method: 'get',
        params: {
            status: params.status,
            current: params.current,
            size: params.size
        }
    })
}

// 获取患者统计信息
export function getPatientStats(patientId) {
    return request({
        url: `/emr/followup/plan/c/patient-stats/${patientId}`,
        method: 'get'
    })
}

// 验证二维码参数
export function validateQRCode(params) {
    return request({
        url: '/emr/followup/plan/c/validate-qr',
        method: 'get',
        params
    })
}

// 默认导出所有API方法
export default {
    getPatientPlans,
    getPlanDetail,
    joinGroup,
    getGroupStatus,
    getPatientInfo,
    getPatientTasks,
    getPatientStats,
    validateQRCode
} 
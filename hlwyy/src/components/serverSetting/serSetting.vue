<template>
    <div class="inq-list">
        <div class="list-main-head">
            <div  class="fieldName" >
                <label class="shopName" style="color:#409eff">图文咨询 >></label>
            </div>
            <div  class="fieldName" >
                <el-button type="text" class="shopName" size="small" @click="goIquRecord(patDetail,'01')">更新</el-button>
            </div>
            <div  class="fieldName baseWidth" >
                <label class="shopName" >是否开通图文咨询服务:</label>
                <el-select size="small" clearable class="shopValue"  placeholder="启用情况">
                    <el-option label="全部状态" value="-1"></el-option>
                </el-select>
            </div>

            <div  class="fieldName baseWidth" >
                <label class="shopName" >咨询费用:</label>
                <el-input size="small"  clearable  class="shopValue" placeholder="咨询费用"></el-input>
            </div>

            <div  class="fieldName baseWidth" >
                <label class="shopName" >每日允许咨询人数:</label>
                <el-input size="small"  clearable  class="shopValue" placeholder="每日允许咨询人数"></el-input>
            </div>
        </div>
        <div class="line"></div>
        <div class="list-main-head">
            <div  class="fieldName" >
                <label class="shopName" style="color:#409eff">视频咨询 >></label>
            </div>
            <div  class="fieldName" >
                <el-button type="text" class="shopName" size="small" @click="goIquRecord(patDetail,'01')">更新</el-button>
            </div>

            <div  class="fieldName baseWidth" >
                <label class="shopName" >是否开通视频咨询服务:</label>
                <el-select size="small" clearable class="shopValue"  placeholder="启用情况">
                    <el-option label="全部状态" value="-1"></el-option>
                </el-select>
            </div>

            <div  class="fieldName baseWidth" >
                <label class="shopName" >咨询费用:</label>
                <el-input size="small"  clearable  class="shopValue" placeholder="咨询费用"></el-input>
            </div>


        </div>
        <div class="line"></div>
        <div class="list-main-head">
            <div  class="fieldName" >
                <label class="shopName" style="color:#409eff">基层会诊 >></label>
            </div>
            <div  class="fieldName" >
                <el-button type="text" class="shopName" size="small" @click="goIquRecord(patDetail,'01')">更新</el-button>
            </div>

            <div  class="fieldName baseWidth" >
                <label class="shopName" >是否开通基层会诊服务:</label>
                <el-select size="small" clearable class="shopValue"  placeholder="启用情况">
                    <el-option label="全部状态" value="-1"></el-option>
                </el-select>
            </div>
        </div>
    </div>
</template>


<script>
import { commonAjax } from '../../api/api';
    export default {
        data() {
            return {

            }
        },
        methods:{

        },
        mounted() {

        }
    }
</script>



<style lang="scss" scoped>
.inq-list{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;
    .line {
            border-bottom: 1px solid #ddd;
        }
    .list-main-head{
        width: 100%;
        background: white;
        height: 100%;
        // text-align: center;
        margin-bottom: 10px;
        overflow: hidden;

        .fieldName{
            margin-bottom: 20px;
            line-height: 50px;
            min-width: 200px;
            width: 45%;
            height: 50px;
            overflow: hidden;
            float: left;
            .shopName{
                float: left;
                height:50px;
                width:150px;
                display: block;
                line-height: 50px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue{
                width: 500px;
                min-width: 300px;
                height: auto;
                float: left;
                display: block;
                padding-left:10px;
            }
            .conTag{
                word-wrap: break-word;
                word-break: normal;
            }
        }
        .baseWidth{
            width: 100%;
        }

    }
}
</style>
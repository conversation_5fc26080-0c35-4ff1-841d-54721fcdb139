<template>
    <div class="inq-list">
        <div style="width:100%;margin-bottom:10px;">
            <el-button type="text" class="inq-btn" size="small" @click="goList">查看全部视频咨询 >></el-button>
        </div>
        <div class="inq-card" v-for="item in iquList" :key="item.id">
            <div class="inq-title">
                <img :src="item.avatar?item.avatar:patientAvatar" alt="">
                <span class="pat-name">{{item.patientName}}</span>
                <el-button type="primary" class="inq-btn" @click="showChat(item)" size="small">开始咨询</el-button>
            </div>
            <p class="inq-con">{{item.body}}</p>
            <p class="inq-time">预约时间：{{item.startTime}} - {{item.endTime}}</p>
        </div>
        <div style="width:100%;height:300px;background:white;" v-if="iquList.length == 0">
            暂无消息
        </div>
    </div>
</template>
<script>
import { commonAjax,imgview } from '@/api/api';
import patientAvatar from '@/assets/img/pat_avatar.png';
import _ from 'lodash';
export default {
    data() {
        return {
            doctorId:sessionStorage.getItem("docId"),
            orgId:sessionStorage.getItem("orgId"),
            iquList:[],
            patientAvatar:patientAvatar,
            imgview:imgview,
        }
    },
    methods:{
        //点击全部咨询
        goList() {
            this.$router.push("videoIquList");
        },


        //打开聊天框
        showChat(item) {

            sessionStorage.setItem("accId",item.patientAccId)
            sessionStorage.setItem("buzType","40");
            sessionStorage.setItem("buzId",item.orderDetailId);
            sessionStorage.setItem("patientName",item.patientName);
            let avatarUrl = item.avatar?item.avatar:patientAvatar; //本地处理用户头像
            sessionStorage.setItem("avatarUrl",avatarUrl);

            window.yunXin.openChatBox(item.patientAccId,"p2p",'02');


        },
        getVideoTextMsg() {
            let params = {
                itemCode:"02",   //会诊类型(01图文，02视频，03咨询),
                doctorId:this.doctorId,
                orgId:this.orgId
            };

            commonAjax("cas.foshan_doctorConsultService","getConsultList",[params]).then(res => {
                if(res.code == 200) {
                    this.iquList = res.body;
                    _.each(this.iquList,function(item) {
                        if(item.body && item.type) {
                            if(item.type == 'TEXT') {
                                item.body = item.body;
                            } else if(item.type == 'NETCALL_VEDIO'){
                                item.body = '视频通话已结束';
                            }else{
                                item.body = JSON.parse(item.body);
                                if(item.type == 'CUSTOM') {
                                    item.body = item.body.data.value;
                                } else if(item.type == 'PICTURE'){
                                    item.body = '[图片]';
                                } else if(item.type == 'AUDIO') {
                                    item.body = '[语音消息]';
                                } else if(item.type == 'VIDEO' || item.type == 'FILE') {
                                    item.body = '[文件]';
                                }
                            }
                        }
                    })
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    })
                }
            })
        }

    },
    mounted() {
        this.getVideoTextMsg();
    }
}
</script>
<style lang="scss" scoped>
.inq-list{
    width: 100%;

    padding: 20px;
    background: #fff;
    display: flex;
    flex-wrap:wrap;
    .inq-card{
        padding: 20px;
        width: 350px;
        height: 200px;
        border: 1px solid #ccc;
        border-radius: 5px;
        overflow: hidden;
        margin-bottom: 10px;
        margin-right: 10px;
        .inq-title{
            position: relative;
            height: 50px;
            border-bottom: 1px solid #ccc;
            margin-bottom: 10px;

            img{
                width:45px;
                height: 45px;
                border: 1px solid #ccc;
                border-radius: 100px;
                display: inline-block;
            }
            .pat-name{
                position: absolute;
                top: 20%;
                margin-left: 10px;
            }
            .inq-btn{
                position: absolute;
                top: 20%;
                right: 10px;
            }
        }
        .inq-con{
            width:100%;
            height: 80px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}
</style>

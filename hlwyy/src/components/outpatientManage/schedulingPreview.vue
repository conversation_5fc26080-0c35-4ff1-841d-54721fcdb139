<template>
    <div class="table">
        <div class="crumbs">
            <el-row class="navbreadcrumb cbafter">
                <el-col :span="12" class="zuhu">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item>服务管理</el-breadcrumb-item>
                        <el-breadcrumb-item>排班一览</el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
            <el-row class="search_con" :gutter="24" style="margin-bottom:10px;display: flex;align-items: center;">
                <el-col :span="3" style="padding-right: 0px;">
                    <label style="color:#333;font-size: 14px;">科室名称:</label>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="deptId" placeholder="请选择" @change="getTableData" filterable>
                        <el-option v-for="item in deptArr" :label="item.deptName" :value="item.deptId" :key="item.deptId"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="3">
                    <el-button type="primary" @click="addTemporaryScheduling">新增临时排班</el-button>
                </el-col>
                <el-col :span="3">
                    <el-button type="primary" @click="stopServices">批量停诊</el-button>
                </el-col>
                <el-col>
                    <div style="float: right;padding-right: 50px;">
                        <el-button type="primary" :disabled="flag == '0'" @click="getSchedulingByFlag('0')">上周</el-button>
                        <el-button type="primary" :disabled="flag == '1'" @click="getSchedulingByFlag('1')">本周</el-button>
                        <el-button type="primary" :disabled="flag == '2'" @click="getSchedulingByFlag('2')">下周</el-button>
                    </div>
                </el-col>
            </el-row>
        </div>
        <div class="doctorScheduling">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column v-for="(item, index) in defineTable" :key="index" :label="item.label" :prop="item.prop" :width="item.width?item.width:''">
                    <template slot-scope="scope">
                        <template v-if="index==0">
                            {{scope.row[item.prop]}}
                        </template>
                        <template v-else>
                            <div class="doctorSchedulingPreviewBox" v-for="(doctorItem, doctorIndex) in (scope.row[item.prop])" :key="doctorIndex">
                                <div class="doctorSchedulingPreview">
                                    <div @click="doctorSchedulingEdit(doctorItem)" v-if="doctorItem.doctorName" class="doctorName">{{doctorItem.doctorName}}</div>
                                    <ul v-if="doctorItem.list.length">
                                        <li v-for="(listItem, listIndex) in doctorItem.list" :key="listIndex" class="list">
                                            <div class="doctorDate">{{listItem.startTime | substrTime}} - {{listItem.endTime | substrTime}}</div>
                                            <div @click="bookingSituation(listItem)" class="doctorBookingSit">预约情况：{{listItem.planNum}}/{{listItem.allNum}}</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog width="80%" :title="temporarySchedulingTitle" :visible.sync="showTemporaryScheduling" @close="closeTemporary">
            <div style="padding: 10px 20px;">
                <el-row class="search_con" :gutter="24" style="margin-bottom:10px;display: flex;align-items: center;">
                    <el-col :span="3" style="padding-right: 0px;width:100px;">
                        <label style="color:#333;font-size: 14px;">医生名称:</label>
                    </el-col>
                    <el-col :span="8">
                        <search-select
                            @emitSelectItem="setelectItem"
                            :top="40"
                            :left="0"
                            :params="searchSelectParams"
                            :api="api"
                        ></search-select>
                    </el-col>
                </el-row>
                <el-table :data="tableDataTemporary" border style="width: 100%">
                    <el-table-column label="日期">
                    <template slot-scope="scope">
                        <el-date-picker :picker-options="pickerOptions" type="date" placeholder="选择日期" v-model="scope.row.workDate" style="width: 100%;" format="yyyy-MM-dd" :clearable="false" :editable="false" @change="formatDate">
                        </el-date-picker>
                    </template>
                    </el-table-column>
                    <el-table-column label="时间段" width="620">
                    <template slot-scope="scope">
                        <el-time-select placeholder="起始时间" v-model="scope.row.startTime" :picker-options="{
                            start: '00:00',
                            step: '00:30',
                            end: '23:30',
                            maxTime:scope.row.endTime
                        }">
                        </el-time-select>
                        <label>-</label>
                        <el-time-select placeholder="结束时间" v-model="scope.row.endTime" :picker-options="{
                            start: '00:00',
                            step: '00:30',
                            end: '23:30',
                            minTime: scope.row.startTime
                        }">
                        </el-time-select>
                    </template>
                    </el-table-column>
                    <el-table-column label="分时段类型" width="230">
                    <template slot-scope="scope">
                        <el-select placeholder="请选择分时段" v-model="scope.row.minutePeriod">
                                <el-option
                                    v-for="item in minutePeriodList"
                                    :key="item.key"
                                    :label="item.text"
                                    :value="item.key"
                                >
                                </el-option>
                        </el-select>
                    </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="dialog-footer center-foot">
                <el-button @click="closeTemporary">取 消</el-button>
                <el-button type="primary" @click="submitFormTemporary">确 定</el-button>
            </div>
        </el-dialog>
        <el-dialog
            title="批量停诊"
            :visible.sync="showStopServices"
            @close="showStopServices = false">
            <el-row class="search_con" :gutter="24" style="margin-bottom:10px;display: flex;align-items: center;">
                <el-col :span="3" style="padding-right: 0px;width:100px;">
                    <label style="color:#333;font-size: 14px;">科室名称:</label>
                </el-col>
                <el-col :span="4">
                    <el-select v-model="deptId" placeholder="请选择" @change="getTableData" filterable>
                        <el-option v-for="item in deptArr" :label="item.deptName" :value="item.deptId" :key="item.deptId"></el-option>
                    </el-select>
                </el-col>
                <el-col :span="3" style="padding-right: 0px;width:100px;">
                    <label style="color:#333;font-size: 14px;">选择时间:</label>
                </el-col>
                <el-col :span="3">
                    <el-date-picker v-model.lazy="stopDateRange" type="daterange" format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :editable="false">
                    </el-date-picker>
                </el-col>
            </el-row>
            <span slot="footer">
                <el-button @click="showStopServices = false">取消</el-button>
                <el-button type="primary" @click="stopServiceAction">确认</el-button>
            </span>
        </el-dialog>

        <!--  -->
        <booking-situation :currentInfo="current" v-if="showBookingSituation" :show="showBookingSituation" @closeBookingSituation="closeBookingSituation"></booking-situation>
    </div>
</template>
<script>
import { commonAjax } from '../../api/api'
import searchSelect from 'components/common/searchSelectByApi'
import bookingSituation from './bookingSituation'
import { dateFormat0 } from 'js/yydjs'
export default {
    data () {
        return {
            api: {
                service: 'cas.foshan_orderManageService',
                method: 'findOpenDoctors',
                filterDepts: ['呼吸科养老院互联网门诊', '神经内科养老院互联网门诊'] // 添加允许的科室列表
            },
            minutePeriodList: [
                {key: 1,text: '每1分钟'},
                {key: 5,text: '每5分钟'},
                {key: 6,text: '每6分钟'},
                {key: 10,text: '每10分钟'},
                {key: 15,text: '每15分钟'},
                {key: 30,text: '每半小时'},
                {key: 60,text: '每一小时'},
            ],
            doctorOnlineExtraId: '',
            current: {},
            flag: '1',
            deptId: '',
            showBookingSituation: false,
            params: {
                deptId: '',
                doctorName: ''
            },
            tableData: [],
            deptArr: [],
            deptId: '',
            showTemporaryScheduling: false,
            tableDataTemporary: [],
            tableDataTemporaryParams: [],
            temporarySchedulingTitle: '新增临时排班',
            pickerOptions: {
                disabledDate(time) {
                    return  ((Date.now() - 3600 * 1000 * 24) > time.getTime()) || (time.getTime() > (Date.now() + 3600 * 1000 * 24 * 6))
                }
            },
            defineTable: [{
                label: '科室',
                prop: 'deptName'
            }, {
                label: '周一',
                prop: 'doctorArrOne',
            }, {
                label: '周二',
                prop: 'doctorArrTwo',
            }, {
                label: '周三',
                prop: 'doctorArrThree',
            }, {
                label: '周四',
                prop: 'doctorArrFour',
            }, {
                label: '周五',
                prop: 'doctorArrFive',
            }, {
                label: '周六',
                prop: 'doctorArrSix',
            }, {
                label: '周日',
                prop: 'doctorArrSeven',
            }],
            showStopServices: false, // 批量停诊模态框
            stopDateRange: ''
        }
    },
    computed: {
        searchSelectParams () {
            return {
                orgId: '', // 就诊医院
                doctorName: this.params.doctorName || '', // 就诊医生
                deptId: this.params.deptId || '', // 就诊科室
            }
        }
    },
    filters: {
        substrTime(value) {
            return value.substring(0,5)
        }
    },
    components: {
        searchSelect,
        bookingSituation
    },
    methods: {
        stopServices(){
            this.showStopServices = true;
        },
        stopServiceAction() {
            console.log(this.stopDateRange);
            if(!this.stopDateRange[0] && !this.stopDateRange[0]) {
                this.$message({type: 'info', message: '请选择停诊时段'});
                return;
            }
            let startDate = dateFormat0(this.stopDateRange[0],'yyyy-MM-dd');
            let endDate = dateFormat0(this.stopDateRange[1],'yyyy-MM-dd');
            let params = {
                tenantId: sessionStorage.getItem('tenantId'),
                deptId: this.deptId || '',
                startDate: startDate,
                endDate: endDate
            }
            console.log(params)
            commonAjax('cas.foshan_DocScheduleHyRecordAdminService','stopSchduleDayByDate',[params]).then(res => {
                if (res && res.code == 200) {
                    this.$message({type: 'success', message: res.msg || '停诊成功。'})
                    this.showStopServices = false;
                    this.getTableData()
                } else {
                    this.$message({type: 'error', message: res.msg || '请求错误。'})
                    this.showStopServices = false;
                    this.getTableData()
                }
            })
        },
        queryOpenDoctor () {
            commonAjax(this.api.service, this.api.method, ['', this.params.deptId, this.params.doctorName]).then(res => {
                if (res && res.code == 200) {
                    const allowedDepts = ['呼吸科养老院互联网门诊', '神经内科养老院互联网门诊'];
                    this.dataList = res.body.filter(doctor => allowedDepts.includes(doctor.deptName));
                    let ret = false
                    for (let i = 0; i < this.dataList.length; i++) {
                        if (this.dataList[i].id == this.doctorOnlineExtraId) {
                            ret = true
                            break
                        }
                    }
                    if (ret) {
                        this.addDocScheduleDay()
                    } else {
                        this.$message({type: 'error', message: '医生暂不支持排班'})
                    }
                } else {
                    this.$message({type: 'error', message: res.msg})
                }
            })
        },
        getDeptArr () {
            let allArr = [{
                deptName: '全部',
                deptId: ''
            }]
            commonAjax('cas.departmentServiceAdmin', 'findDeptsByTenantId', [sessionStorage.getItem('tenantId')]).then(res => {
                if (res && res.code == 200) {
                    // Filter to only show specific departments
                    const allowedDepts = ['呼吸科养老院互联网门诊', '神经内科养老院互联网门诊'];
                    const filteredDepts = res.body.filter(dept => allowedDepts.includes(dept.deptName));
                    this.deptArr = filteredDepts.length ? allArr.concat(filteredDepts) : allArr;
                } else {
                    this.$message({type: 'error', message: res.msg})
                }
            })
        },
        copyObj (obj) {
            return JSON.parse(JSON.stringify(obj))
        },
        changeToTemporaryParams () {
            this.tableDataTemporaryParams = this.copyObj(this.tableDataTemporary)
            this.tableDataTemporaryParams = this.tableDataTemporaryParams.map((v) => {
                v.startTime = v.startTime + ':00'
                v.endTime = v.endTime + ':00'
                return v
            })
            this.tableDataTemporaryParams[0].doctorOnlineExtraId = this.doctorOnlineExtraId
        },
        //点击视频排班设置按钮
        doctorSchedulingEdit(doctorItem) {
            this.$router.push({name:"doctorScheduling", query:{doctorOnlineExtraId: doctorItem.doctorOnlineExtraId, doctorName: encodeURIComponent(doctorItem.doctorName)}})
        },
        closeBookingSituation () {
            this.current = {}
            this.showBookingSituation = false
            this.getTableData()
        },
        bookingSituation (listItem) {
            console.log(listItem, 'listItem')
            listItem.workDate = listItem.workDate.substring(0,10)
            this.current = listItem
            this.showBookingSituation = true
        },
        setelectItem (val) {
            console.log(val, 'vasssl')
            this.params.doctorName = val.doctorName
            this.doctorOnlineExtraId = val.id
        },
        formatDate (val) {
            this.tableDataTemporary[0].workDate = val
        },
        addDocScheduleDay () {
            this.changeToTemporaryParams()
            commonAjax('cas.foshan_DoctorScheduleAdminService', 'addDocScheduleDay', this.tableDataTemporaryParams).then(res => {
                if (res && res.code == 200) {
                    this.$message({ type: 'success', message: '新增临时排班成功!'})
                    this.closeTemporary()
                    this.getTableData()
                } else {
                    this.$message({type: 'error', message: res.msg})
                }
            })
        },
        //保存按钮提交数据
        submitFormTemporary() {
            let [obj] = this.tableDataTemporary
            let {workDate, minutePeriod='', startTime='', endTime=''} = obj
            console.log(obj, 'obj', this.doctorOnlineExtraId)
            if (!this.doctorOnlineExtraId) {
                return this.$message({type: 'error', message: '请选择医生'})
            }
            if (!workDate) {
                return this.$message({type: 'error', message: '请选择日期'})
            }
            if (!startTime) {
                return this.$message({type: 'error', message: '请选择时间段起始时间'})
            }
            if (!endTime) {
                return this.$message({type: 'error', message: '请选择时间段结束时间'})
            }
            if (!minutePeriod) {
                return this.$message({type: 'error', message: '请选择分时段类型'})
            }
            let a = +startTime.split(':')[1]
            let b = +endTime.split(':')[1]
            let c = Math.abs(b - a)
            console.log(c, minutePeriod)
            // if ((minutePeriod == '30' && c != 30) || (minutePeriod == '60' && c != 0)) {
            if (minutePeriod == '60' && c != 0) {
                return this.$message({type: 'error', message: '分时段类型设置有误'})
            } else {
                this.$confirm('确定执行此操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true
                }).then(() => {
                    this.queryOpenDoctor()
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消'})
                })
            }
        },
        closeTemporary() {
            this.tableDataTemporary = [{
                type: 1,
                minutePeriod: '',
                startTime: '',
                endTime: '',
                workDate: ''
            }]
            this.doctorOnlineExtraId = ''
            this.showTemporaryScheduling = false
            this.params.doctorName = ''
        },
        addTemporaryScheduling () {
            this.tableDataTemporary = [{
                type: 1,
                minutePeriod: '',
                startTime: '',
                endTime: '',
                workDate: ''
            }]
            this.doctorOnlineExtraId = ''
            this.params.doctorName = ''
            this.showTemporaryScheduling = true
        },
        getSchedulingByFlag (flag) {
            this.flag = flag
            this.getTableData()
        },
        getTableData () {
            let params = [{
                tenantId: sessionStorage.getItem('tenantId'),
                flag: this.flag,
                deptId: this.deptId
            }]
            commonAjax('cas.foshan_DoctorScheduleAdminService', 'findDoctorScheduleList', params).then(res => {
                if (res && res.code == 200) {
                    let timeBeans = []
                    let ret = res.body
                    // Filter departments
                    const allowedDepts = ['呼吸科养老院互联网门诊', '神经内科养老院互联网门诊'];
                    ret = ret.filter(item => allowedDepts.includes(item.deptName));
                    
                    timeBeans = (ret && ret.length && ret[0].timeBeans) || []
                    if (ret && ret.length && ret[0].map) {
                        this.tableData = ret.map((v) => {
                            let obj = {
                                deptName: v.deptName,
                                deptId: v.deptId
                            }
                            let map = v.map
                            if (map) {
                                obj.doctorArrOne = map[1] || []
                                obj.doctorArrTwo = map[2] || []
                                obj.doctorArrThree = map[3] || []
                                obj.doctorArrFour = map[4] || []
                                obj.doctorArrFive = map[5] || []
                                obj.doctorArrSix = map[6] || []
                                obj.doctorArrSeven = map[7] || []
                            }
                            return obj
                        })
                    } else {
                        this.tableData = []
                    }
                    this.defineTable = this.defineTable.map((v, i) => {
                        if (i > 0) {
                            v.label = `${timeBeans[i-1].weekDay}(${timeBeans[i-1].dayTime})`
                        } else {
                            v.label = v.label
                        }
                        return v
                    })
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                }
            })
            console.log(this.tableData, 'ssss')
        },
    },
    mounted () {
        this.getDeptArr()
        this.getTableData()
    }
}
</script>
<style lang="less">
.doctorScheduling {
    .el-table .cell {
        padding: 0px;
    }
    .el-table td {
        padding: 0px;
    }
    .doctorSchedulingPreviewBox {
        border-bottom: 1px solid rgb(223, 236, 235);
        .doctorSchedulingPreview {
            text-align: center;
            padding: 5px 5px 8px 5px;
            .list {
                padding: 4px 0px;
                .doctorBookingSit {
                    cursor: pointer;
                    color: rgb(64, 158, 255);
                }
            }
            .doctorName {
                cursor: pointer;
                color: rgb(64, 158, 255);
            }
        }
    }
    .doctorSchedulingPreviewBox:last-child {
        border-bottom: none;
    }
}
</style>

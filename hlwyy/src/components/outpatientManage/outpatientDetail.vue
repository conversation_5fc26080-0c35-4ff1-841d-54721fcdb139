<template>
  <div class="table">
    <div class="crumbs">
      <el-row class="navbreadcrumb cbafter">
        <el-col :span="12" class="zuhu">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>服务管理</el-breadcrumb-item>
            <el-breadcrumb-item>服务设置</el-breadcrumb-item>
          </el-breadcrumb>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button type="primary" @click="goBack">返回</el-button>
        </el-col>
      </el-row>
      <div>
        <el-form :inline="true" :model="doctorInfo" class="demo-form-inline" style="display: flex;
    justify-content: space-around;">
          <el-form-item label="姓名">
            <el-input v-model="doctorInfo.doctorName" disabled></el-input>
          </el-form-item>
          <el-form-item label="职称">
            <el-input v-model="doctorInfo.doctorLevelText" disabled></el-input>
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="doctorInfo.phoneNo" disabled></el-input>
          </el-form-item>
        </el-form>
        <el-table border :data="doctorInfo.items" style="width: 100%" :default-sort="{prop: 'id', order: 'itemCode'}">
          <!-- 01图文问诊，02视频问诊，03基层会诊-->
          <el-table-column label="问诊项目">
            <template slot-scope="scope">
              <span v-if="scope.row.itemCode=='01'">图文问诊</span>
              <span v-if="scope.row.itemCode=='02'">视频问诊</span>
              <span v-if="scope.row.itemCode=='03'">基层会诊</span>
              <span v-if="scope.row.itemCode=='04'">复诊配方</span>
            </template>
          </el-table-column>
          <el-table-column label="服务配置">
            <template slot-scope="scope">
              <span v-if="scope.row.itemCode=='01'">{{scope.row.price}}元/次，每日允许{{scope.row.planNum}}人问诊</span>
              <!-- <span v-if="scope.row.itemCode=='02'&&scope.row.price!=0">{{scope.row.price}}元/次</span> -->
              <span v-if="scope.row.itemCode=='02'">{{scope.row.price}}元/次</span>
              <span v-if="scope.row.itemCode=='04'">{{scope.row.price}}元/次</span>
            </template>
          </el-table-column>
          <el-table-column label="是否开通">
            <template slot-scope="scope">
              <span v-if="scope.row.enabled=='1'">是</span>
              <span v-if="scope.row.enabled=='0'">否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button size="small" @click="closeStatus(scope.row)" v-if="scope.row.enabled=='1'">关闭
              </el-button>
              <el-button size="small" @click="openDialog(scope.row)" v-if="scope.row.enabled=='0'">开通
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!--开通弹框-->
    <div class="openDialog">
      <el-dialog :title="itemTitle" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="费用:">
            <el-input v-model="form.price" auto-complete="off" placeholder="请输入1-10000之间的数字" type="number"></el-input>
            <span>元/次</span>
          </el-form-item>
          <el-form-item label="每日允许:" v-if="form.itemCode=='01'">
            <el-input v-model="form.planNum" auto-complete="off" placeholder="请输入1-100之间的数字" type="number"></el-input>
            <span>人问诊</span>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveButton">确认开通</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { commonAjax } from '../../api/api'

export default {
  data() {
    return {
      dialogFormVisible: false,
      form: {
        itemCode: '',
        price: 0,
        planNum: 0,
        enabled: 0
      },
      doctorInfo: {},
      itemTitle: ''
    }
  },
  mounted() {
    this.getDoctorInfo()
  },
  methods: {
    //获取医生信息
    getDoctorInfo() {
      let id = parseInt(sessionStorage.getItem('id'))
      commonAjax('cas.doctorOnlineAdminService', 'detailDoctorItem', [id]).then(
        res => {
          if (res && res.code == 200) {
            this.doctorInfo = res.body
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            })
          }
        }
      )
    },
    //关闭状态
    closeStatus(row) {
      let text = ''
      if (row.itemCode == '01') {
        text = '图文问诊'
      } else if (row.itemCode == '02') {
        text = '视频问诊'
      } else if (row.itemCode == '03') {
        text = '基层会诊'
      } else {
        text = '复诊配方'
      }
      this.$confirm(`是否确认关闭${text}服务?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      })
        .then(() => {
          //更新问诊设置
          let params = [
            {
              tenantId: this.doctorInfo.tenantId,
              doctorOnlineExtraId: this.doctorInfo.id,
              itemCode: row.itemCode,
              price: row.price,
              planNum: row.planNum,
              enabled: 0
            }
          ]
          commonAjax(
            'cas.doctorOnlineAdminService',
            'updateDoctorItem',
            params
          ).then(res => {
            if (res && res.code == 200) {
              this.getDoctorInfo()
            } else {
              this.$message({
                type: 'error',
                message: res.msg
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    //开通状态
    openDialog(row) {
      this.form.itemCode = row.itemCode
      if (row.itemCode == '01' || row.itemCode == '02' || row.itemCode == '04') {
        this.dialogFormVisible = true
        this.form.price = row.price
        this.form.planNum = row.planNum
        if (row.itemCode == '01') {
          this.itemTitle = '图文问诊服务设置'
        } else if (row.itemCode == '02') {
          this.itemTitle = '视频问诊服务设置'
        } else {
            this.itemTitle = '复诊配方服务设置'
        }
      } else {
        let text = ''
        // if (row.itemCode == '03') {
          text = '基层会诊'
        // } else {
        //   text = '复诊配方'
        // }
        this.$confirm(`是否确认开通${text}服务？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.saveButton()
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      }
    },
    //确定开通
    saveButton() {
      let reg = /^[0-9]+\d*$/
      if (reg.test(this.form.planNum) && this.form.planNum <= 100) {
        let params = [
          {
            tenantId: this.doctorInfo.tenantId,
            doctorOnlineExtraId: this.doctorInfo.id,
            itemCode: this.form.itemCode,
            price: this.form.price,
            planNum: this.form.planNum,
            enabled: 1
          }
        ]
        commonAjax(
          'cas.doctorOnlineAdminService',
          'updateDoctorItem',
          params
        ).then(res => {
          if (res && res.code == 200) {
            this.dialogFormVisible = false
            this.getDoctorInfo()
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            })
          }
        })
      } else {
        this.$message({
          type: 'error',
          message: '每日允许人数为小于100的正整数'
        })
      }
      return
    },
    //返回按钮
    goBack() {
      this.$router.push('outpatientSet')
    }
  }
}
</script>
<style lang="less" scoped>
.labelCon {
  width: 30%;
  float: left;
  margin-bottom: 20px;

  .labelName {
    width: 100px;
    display: inline-block;
    height: 50px;
    line-height: 50px;

    .labelSpan {
      color: red;
    }
  }
  .labelValue {
    width: 70%;
  }
}

.openDialog .el-input {
  width: 27%;
}
.openDialog .dialog-footer {
  text-align: center;
}
</style>


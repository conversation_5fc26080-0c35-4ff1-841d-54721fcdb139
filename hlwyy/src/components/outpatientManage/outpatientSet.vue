<template>
  <div class="table">
    <div class="crumbs">
      <el-row class="navbreadcrumb cbafter">
        <el-col :span="12" class="zuhu">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>服务管理</el-breadcrumb-item>
            <el-breadcrumb-item>服务设置</el-breadcrumb-item>
          </el-breadcrumb>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <el-button type="primary" icon="plus" @click="openDialog">添加</el-button>
        </el-col>
      </el-row>
      <el-row class="search_con" :gutter="24">
        <el-col :span="4">
          <el-input placeholder="请输入医生" v-model="params.doctorName">
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入联系电话" v-model="params.phoneNo">
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select placeholder="选择医院" clearable v-model="params.orgId">
            <el-option label="全部医院" value=""></el-option>
            <el-option v-for="item in hospitalList" :label="item.orgFullName" :value="item.orgId" :key="item.orgId"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="params.textStatus" placeholder="选择图文问诊" clearable>
            <el-option label="选择图文问诊" value=""></el-option>
            <el-option label="已开通" value=1></el-option>
            <el-option label="未开通" value=0></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="params.videoStatus" placeholder="选择视频问诊" clearable>
            <el-option label="选择视频问诊" value=""></el-option>
            <el-option label="已开通" value=1></el-option>
            <el-option label="未开通" value=0></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select v-model="params.consultationStatus" placeholder="选择基层会诊" clearable>
            <el-option label="选择基层会诊" value=""></el-option>
            <el-option label="已开通" value=1></el-option>
            <el-option label="未开通" value=0></el-option>
          </el-select>
        </el-col>
        <el-button type="primary" icon="search" @click="searchClick">搜索</el-button>
        <el-button type="warning" icon="delete" @click="deleteClick">清除</el-button>
      </el-row>
    </div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="doctorName" label="医生" width="80">
      </el-table-column>
      <el-table-column prop="phoneNo" label="联系电话">
      </el-table-column>
      <el-table-column prop="doctorLevelText" label="职称">
      </el-table-column>
      <el-table-column prop="orgName" label="医院">
      </el-table-column>
      <el-table-column prop="deptName" label="科室">
      </el-table-column>
      <el-table-column label="图文问诊">
        <template slot-scope="scope">
          <div v-for="item in scope.row.items">
            <div v-if="item.itemCode=='01'">
              <!--1开通 0关闭-->
              <span v-if="item.enabled==1">已开通，每日{{item.planNum}}人问诊</span>
              <span v-if="item.enabled==0">未开通</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="videoState" label="视频问诊">
        <template slot-scope="scope">
          <div v-for="item in scope.row.items">
            <div v-if="item.itemCode=='02'">
              <!--1开通 0关闭-->
              <span v-if="item.enabled=='1'">已开通</span>
              <span v-if="item.enabled=='0'">未开通</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="consultationState" label="基层会诊">
        <template slot-scope="scope">
          <div v-for="item in scope.row.items">
            <div v-if="item.itemCode=='03'">
              <!--1开通 0关闭-->
              <span v-if="item.enabled=='1'">已开通</span>
              <span v-if="item.enabled=='0'">未开通</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="consultationState" label="复诊配方">
        <template slot-scope="scope">
          <div v-for="item in scope.row.items">
            <div v-if="item.itemCode=='04'">
              <!--1开通 0关闭-->
              <span v-if="item.enabled=='1'">已开通</span>
              <span v-if="item.enabled=='0'">未开通</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button size="small" @click="handleEdit(scope.$index, scope.row)">设置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :page-sizes="[10,20,50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="params.pageSize">
      </el-pagination>
    </div>
    <!--门诊设置弹框-->
    <el-dialog title="添加问诊医生" :visible.sync="centerDialogVisible" center>
      <div class="crumbs">
        <el-row class="search_con" :gutter="20">
          <el-col :span="4">
            <el-input placeholder="请输入医生姓名" v-model="addParams.doctorName">
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-input placeholder="请输入联系电话" v-model="addParams.phoneNo">
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select placeholder="选择医院" clearable v-model="addParams.orgId">
              <el-option label="选择医院" value=""></el-option>
              <el-option v-for="item in hospitalList" :label="item.orgFullName" :value="item.orgId" :key="item.orgId"></el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-button type="primary" icon="search" @click="addSearchClick">搜索</el-button>
            <el-button type="warning" icon="delete" @click="addDeleteClick">清除</el-button>
          </el-col>
          <el-col :span="3" class="addorg">
            <el-button type="primary" icon="plus" @click="confirmDdd">确认添加</el-button>
          </el-col>
        </el-row>
      </div>
      <el-table :data="addTableData" border style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column prop="doctorName" label="医生">
        </el-table-column>
        <el-table-column prop="phoneNo" label="联系电话">
        </el-table-column>
        <el-table-column prop="doctorLevelText" label="职称">
        </el-table-column>
        <el-table-column prop="orgName" label="所在医院">
        </el-table-column>
        <el-table-column prop="deptName" label="所在科室">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="addTotal" :page-sizes="[10,20,50]" @size-change="handleSizeChange1" @current-change="handleCurrentChange1" :page-size="addParams.pageSize">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { commonAjax } from '../../api/api'
export default {
  data() {
    return {
      tableData: [], //获取医生列表信息
      params: {
        doctorName: '',
        phoneNo: '',
        textStatus: '', //图文问诊状态(0:关闭, 1:开通)
        videoStatus: '', //视频问诊状态(0:关闭, 1:开通)
        consultationStatus: '', //基层问诊状态(0:关闭, 1:开通)
        orgId: '',
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      centerDialogVisible: false, //弹框
      /*=======================新增医生列表相关信息=======================*/
      addTableData: [], //获取新增医生列表信息
      addParams: {
        doctorName: '',
        phoneNo: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      },
      addTotal: 0,
      multipleSelection: [], //选中的医生
      hospitalList: [] //医院列表
    }
  },
  methods: {
    //获取当前租户下的医院
    getHospitalData() {
      let params = [
        {
          pageNo: '1',
          pageSize: '100',
          tenantId: sessionStorage.getItem('tenantId')
        }
      ]
      commonAjax('cas.tenantManageService', 'searchTenantOrg', params).then(
        res => {
          if (res.code == 200) {
            this.hospitalList = res.body.data
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            })
          }
        }
      )
    },
    //获取医生列表数据
    getTableData() {
      commonAjax('cas.doctorOnlineAdminService', 'pageDoctorOnline', [
        this.params
      ]).then(res => {
        if (res && res.code == 200) {
          this.tableData = res.body.data
          this.total = res.body.total
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      })
    },
    //点击设置按钮
    handleEdit(index, row) {
      this.$router.push('outpatientSetDetail')
      sessionStorage.setItem('id', row.id)
    },
    //改变分页显示的数量
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getTableData()
    },
    //跳转到分页输入框中页码的页面
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getTableData()
    },
    //点击搜索按钮
    searchClick() {
      this.getTableData()
    },
    //点击清除按钮
    deleteClick() {
      this.params = {
        doctorName: '',
        phoneNo: '',
        textStatus: '', //图文问诊状态(0:关闭, 1:开通)
        videoStatus: '', //视频问诊状态(0:关闭, 1:开通)
        consultationStatus: '', //基层问诊状态(0:关闭, 1:开通)
        orgId: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getTableData()
    },
    //获取添加弹出框中医生列表数据
    getAddTableData() {
      commonAjax('cas.doctorOnlineAdminService', 'pageOrgDoctors', [
        this.addParams
      ]).then(res => {
        if (res && res.code == 200) {
          this.addTableData = res.body.data
          this.addTotal = res.body.total
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      })
    },
    //点击添加
    openDialog() {
      this.centerDialogVisible = true
      this.getAddTableData()
    },
    //搜索按钮点击
    addSearchClick() {
      this.getAddTableData()
    },
    //清除按钮按钮点击
    addDeleteClick() {
      this.addParams = {
        doctorName: '',
        phoneNo: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getAddTableData()
    },
    //改变分页显示的数量
    handleSizeChange1(val) {
      this.addParams.pageSize = val
      this.getAddTableData()
    },
    //跳转到分页输入框中页码的页面
    handleCurrentChange1(val) {
      this.addParams.pageNo = val
      this.getAddTableData()
    },
    //获取选中的医生
    handleSelectionChange(val) {
      let newDoctorIdArr = val.map(v => v.doctorId)
      let newOrgIdArr = val.map(v => v.orgId)
      let newDeptIdArr = val.map(v => v.deptId)
      this.multipleSelection = [newDoctorIdArr,newDeptIdArr,newOrgIdArr]
    },
    //点击确认添加按钮
    confirmDdd() {
      commonAjax(
        'cas.doctorOnlineAdminService',
        'addDoctorOnline',
        this.multipleSelection
      ).then(res => {
        if (res && res.code == 200) {
          this.centerDialogVisible = false
          this.$message({
            message: res.body
          })
          this.getTableData()
        } else {

        }
      })
    }
  },
  mounted() {
    this.getTableData()
    this.getHospitalData()
  }
}
</script>
<style type="text/css" scoped>
</style>

<template>
    <div class="table">
        <div class="crumbs">
            <el-row class="navbreadcrumb cbafter">
                <el-col :span="18" class="zuhu">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item>服务管理</el-breadcrumb-item>
                        <el-breadcrumb-item>服务须知</el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
        </div>
        <el-row class="search_con" :gutter="20" style="margin-left: 60%">
            <el-col :span="3" class="addorg">
                <el-button type="primary" icon="plus" @click="isEditClick">编辑</el-button>
            </el-col>
        </el-row>
        <el-form :model="noticeData" auto-complete="off" @submit.native.prevent>
            <el-form-item label="问诊须知:" label-width="143px" >
                <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8}"
                          v-model="noticeData.noticeText" placeholder="500字以内" :maxlength=500 style="width: 50%;" :disabled="isEdit"></el-input>
            </el-form-item>
        </el-form>
        <!--弹框-->
        <el-dialog title="编辑问诊须知" :visible.sync="dialogVisible">
            <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8}"
                      v-model="editText" placeholder="500字以内" :maxlength=500 style="width: 80%;"></el-input>
            <div slot="footer" class="dialog-footer" style="text-align: center">
            <el-button @click="cancelClick">取 消</el-button>
            <el-button type="primary" @click="saveClick">确 定</el-button>
           </div>
        </el-dialog>
    </div>
</template>
<script>
    import {commonAjax}from '../../api/api';

    export default {
        data() {
            return {
                dialogVisible:false,
                isEdit:true,
                noticeData:{
                   noticeText:''
                },
               editText:''
            }
        },
        methods: {
            isEditClick(){
                this.dialogVisible = true;
                this.editText =  this.noticeData.noticeText
            },
            getNotice(){
                commonAjax("cas.doctorOnlineAdminService", "getInquiryNote", []).then(res => {
                    if (res && res.code == 200) {
                        this.noticeData.noticeText = res.body;
                        console.log(this.noticeData.noticeText)
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.msg
                        });
                    }
                });
            },
            cancelClick(){
                this.editText='';
                this.dialogVisible = false;
            },
            saveClick(){
                commonAjax("cas.doctorOnlineAdminService", "updateInquiryNote", [this.editText]).then(res => {
                    if (res && res.code == 200) {
                        this.$message({
                            message: '保存成功',
                            type: 'success'
                        });
                        this.dialogVisible = false;
                        this.getNotice()
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.msg
                        });
                    }
                });
            },

        },
        mounted() {
           this.getNotice()
        },

    }
</script>
<style type="text/css" scoped>

</style>

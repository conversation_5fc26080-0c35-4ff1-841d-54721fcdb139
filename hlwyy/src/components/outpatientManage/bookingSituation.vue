<template>
    <div class="table">
        <el-dialog :visible.sync="showDialog" @close="closeBookingSituation">
            <div class="crumbs">
                <el-row class="search_con" :gutter="24" style="margin-bottom:20px;display: flex;align-items: center;">
                    <el-col :span="3" style="padding-right: 0px;width:81px;">
                        <label style="color:#333;font-size: 14px;">排班日期:</label>
                    </el-col>
                    <el-col :span="7">
                        <el-select v-model="selectScheduleDayId" filterable placeholder="请选择" @change="getDocRegInfoByWorkDate">
                            <el-option
                                v-for="item in alreadyRegDate"
                                :key="item.id"
                                :label="item.label"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3" style="padding-right: 0px;width:81px;" v-if="!beforeNow">
                        <el-button type="primary" @click.stop="stopDiagnosis">停诊</el-button>
                    </el-col>
                </el-row>
            </div>
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column label="排班">
                    <template slot-scope="scope">
                        {{`${scope.row.startTime} - ${scope.row.endTime}`}}
                    </template>
                </el-table-column>
                <el-table-column prop="personName" label="患者姓名">
                </el-table-column>
                <el-table-column prop="itemCodeText" label="订单类型">
                </el-table-column>
                <el-table-column prop="userPhone" label="联系电话" width="110">
                </el-table-column>
                <el-table-column label="订单状态">
                     <template slot-scope="scope">
                        <span>{{statusMap[scope.row.status]}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="130" align="center">
                    <template slot-scope="scope">
                        <div v-if="scope.row.status!='11'">
                            <el-button size="small" v-if="scope.row.showRefund&&scope.row.price>0&&scope.row.refundStatus != '46'" @click="refund(scope.$index, scope.row)">退款</el-button>
                            <el-button size="small" v-if="!scope.row.showRefund" @click="handleEdit(scope.$index, scope.row)">退号</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog :title="dialogFormTitle" :visible.sync="dialogFormVisible" @close="resetForm('adinfoForm')" size="tiny">
            <el-form center :model="formdata" :rules="formrules" ref="adinfoForm" auto-complete="off" id="adinfoForm">
                <el-form-item label="退款金额:" :label-width="formLabelWidth" prop="price">
                    <el-input :disabled="+payAmount==0" style="width: 50%;" v-model="formdata.price" placeholder="请输入退款金额，退款金额不得大于实付金额"></el-input>
                </el-form-item>
                <el-form-item v-if="showReturnNo">
                    <el-checkbox v-model="formdata.returnflag" style="padding-left:50px;">号源退回到号池</el-checkbox>
                </el-form-item>
            </el-form>
            <div class="dialog-footer center-foot">
                <el-button @click="closemodal('adinfoForm')">取 消</el-button>
                <el-button type="primary" @click="submitForm('adinfoForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { commonAjax } from '../../api/api'
import { stringify } from 'querystring';
export default {
    data() {
        let that = this
        let moneyReg = /^(([1-9]\d*)|(0))(\.\d{1,2})?$/
        let checkApplyAmount = (rule, value, callback) => {
        if (value === '') {
            callback(new Error('请输入退款金额'));
        } else if (+this.payAmount == 0) {
            callback();
        } else if (!(moneyReg.test(value) && value != 0)) {
            callback(new Error('退款金额格式不正确'));
        }  else if (value > this.payAmount) {
            callback(new Error('退款金额不得大于实付金额'));
        } else {
            callback();
        }
        };
        return {
            beforeNow: false,
            formLabelWidth: '120px',
            dialogFormVisible: false, //模态框显示隐藏用
            showReturnNo: false,
            formdata: { //表单绑定数据用
                id: "",
                price: "", //退款金额
            },
            formrules: { //表单验证规则
            price: [{
                validator: checkApplyAmount,
                trigger: 'blur'
            }]
            },
            tableData: [],
            selectScheduleDayId: '',
            alreadyRegDate: [],
            statusMap: {
                '11': '待支付',
                '01': '待接诊',
                '02': '进行中',
                '05': '已完成',
                '50': '待接诊',
                '51': '未开始',
                '52': '进行中',
                '53': '已完成',
            },
            payAmount: 0,
            dialogFormTitle: '退款',
            alreadyRegDateMap: {},
        }
    },
    computed: {
        showDialog: {
            get () {
                return this.show
            },
            set (val) {
                !val && this.$emit('closeBookingSituation', val)
            }
        }
    },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        doctorOnlineExtraId: {
            type: Number,
            default: -1,
        },
        doctorName: {
            type: String,
            default: ''
        },
        currentInfo: {
            type: Object,
            default: {}
        },
        scheduleDayId: {
            type: Number,
            default: -1,
        },
    },
    methods: {
        returnScheduleHy (obj) {
            commonAjax('cas.foshan_DocScheduleHyRecordAdminService', 'returnScheduleHy', [obj.id, +obj.returnflag, obj.price]).then(res => {
                if (res && res.code == 200) {
                    this.$message({ type: 'success', message: '退号成功!'})
                    this.dialogFormVisible = false
                    this.payAmount = 0
                    this.formdata = {}
                    this.showReturnNo = false
                    this.getDocRegInfoByWorkDate()
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                }
            })
        },
        refundFee (obj) {
            commonAjax('cas.foshan_DocScheduleHyRecordAdminService', 'refundFee', [obj.id, +obj.price]).then(res => {
                if (res && res.code == 200) {
                    this.$message({ type: 'success', message: '退款成功!'})
                    this.dialogFormVisible = false
                    this.payAmount = 0
                    this.formdata = {}
                    this.getDocRegInfoByWorkDate()
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                }
            })
        },
        getDocRegInfoByWorkDate () {
            this.beforeNow = this.alreadyRegDateMap[this.selectScheduleDayId].beforeNow
            let params = [{
                doctorOnlineExtraId: this.currentInfo.doctorOnlineExtraId,
                scheduleDayId: this.selectScheduleDayId
            }]
            commonAjax('cas.foshan_DocScheduleHyRecordAdminService', 'findDocRegInfoByWorkDate', params).then(res => {
                if (res && res.code == 200) {
                    this.tableData = res.body || []
                    this.tableData = this.tableData.map((v) => {
                        v.startTime = v.startTime.substring(0,5)
                        v.endTime = v.endTime.substring(0,5)
                        v.showRefund = ['02', '05', '52', '53'].indexOf(v.status) > -1
                        return v
                    })
                } else {
                    this.$message({
                    type: 'error',
                    message: res.msg
                    })
                }
            })
        },
        getDate () {
            commonAjax('cas.foshan_DoctorScheduleAdminService', 'findDocScheduleDays', [this.currentInfo.doctorOnlineExtraId]).then(res => {
                if (res && res.code == 200) {
                    let ret = res.body || []
                    this.selectScheduleDayId = this.currentInfo.id
                    this.alreadyRegDate = ret.map((item) => {
                        item.workDate = item.workDate.substring(0,10)
                        item.startTime = item.startTime.substring(0,5)
                        item.endTime = item.endTime.substring(0,5)
                        item.label = `${item.workDate} ${item.startTime}-${item.endTime}`
                        this.alreadyRegDateMap[item.id] = item
                        return item
                    })
                    console.log(this.alreadyRegDateMap, 'map')
                    console.log(this.currentInfo, 'info')
                    this.beforeNow = this.alreadyRegDateMap[this.selectScheduleDayId].beforeNow
                    this.getDocRegInfoByWorkDate()
                    
                } else {
                    this.$message({
                    type: 'error',
                    message: res.msg
                    })
                }
            })
        },
        stopDiagnosis () {
            if (!this.selectScheduleDayId) {
                return this.$message({ type: 'error', message: '请选择排班日期'})
            }
            let params = [{
                doctorOnlineExtraId: this.currentInfo.doctorOnlineExtraId,
                scheduleDayId: this.selectScheduleDayId
            }]
            let str = `确定将${this.currentInfo.doctorName}的${this.alreadyRegDateMap[this.selectScheduleDayId].label}的排班做停诊操作?`
            this.$confirm(str, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                center: true
            }).then(() => {
                commonAjax('cas.foshan_DocScheduleHyRecordAdminService', 'stopSchduleDay', params).then(res => {
                    if (res && res.code == 200) {
                        this.$message({ type: 'success', message: '停诊成功!'})
                        this.$emit('closeBookingSituation', false)
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.msg
                        })
                    }
                })
            }).catch(() => {
                this.$message({ type: 'info', message: '已取消停诊'})
            })
        },
        closeBookingSituation () {
            this.$emit('closeBookingSituation', false)
        },
        refund (index, row) {
            this.dialogFormTitle = '退款'
            this.dialogFormVisible = true;
            this.payAmount = row.price
            this.formdata = {
                id: row.id,
                price: row.price, //退款金额
            }
        },
        //详情页
        handleEdit(index, row) {
            this.dialogFormTitle = '退号'
            this.dialogFormVisible = true;
            this.showReturnNo = true
            this.payAmount = row.price
            this.formdata = {
                id: row.id,
                price: row.price, //退款金额
                returnflag: true
            }
        },
        //保存按钮提交数据
        submitForm(formName) {
            let params = JSON.parse(JSON.stringify(this.formdata))
            params.price = +params.price
            console.log(params, 'ssss')
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$confirm('确定执行此操作?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                        center: true
                    }).then(() => {
                        if(this.showReturnNo) {
                            this.returnScheduleHy(params)
                        } else {
                            this.refundFee(params)
                        }
                    }).catch(() => {
                        this.$message({ type: 'info', message: '已取消退款'})
                    })
                } else {
                    return false;
                }
            });
        },
        //点击取消的时候关闭
        closemodal(formName) {
            this.$refs[formName].resetFields()
            this.dialogFormVisible = false
        },
        resetForm(formName) {
            this.$refs[formName].resetFields()
        },
        checkPropHas (arr = [], prop, propValue = '') {
            let ret = false
            arr.map((v) => {
                if (v[prop] == propValue) {
                ret = true
                }
            })
            return ret
        }
    },
    mounted () {
        this.getDate()
    }
}
</script>
<style lang="less" scoped>
.el-date-editor.el-input {
  width: 100%;
}
</style>

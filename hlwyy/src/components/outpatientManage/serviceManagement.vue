<template>
    <div class="table">
        <div class="crumbs">
            <el-row class="navbreadcrumb cbafter">
                <el-col :span="12" class="zuhu">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item>服务管理</el-breadcrumb-item>
                        <el-breadcrumb-item>服务管理</el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
                <el-col :span="12" style="text-align: right">
                    <el-button type="primary" icon="plus" @click="openDialog">添加</el-button>
                </el-col>
            </el-row>
            <el-row class="search_con" :gutter="24" style="margin-bottom:20px;display: flex;align-items: center;">
                <el-col :span="3" style="padding-right: 0px;width:81px;">
                    <label style="color:#333;font-size: 14px;">图文咨询:</label>
                </el-col>
                <el-col :span="3">
                    <el-select v-model="params.textStatus" placeholder="请选择" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已开通" value=1></el-option>
                        <el-option label="未开通" value=0></el-option>
                    </el-select>
                </el-col>
                <el-col :span="3" style="padding-right: 0px;width:81px;">
                    <label style="color:#333;font-size: 14px;">视频咨询:</label>
                </el-col>
                <el-col :span="3">
                    <el-select v-model="params.videoStatus" placeholder="请选择" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已开通" value=1></el-option>
                        <el-option label="未开通" value=0></el-option>
                    </el-select>
                </el-col>
                <el-col :span="3" style="padding-right: 0px;width:81px;">
                    <label style="color:#333;font-size: 14px;">复诊配药:</label>
                </el-col>
                <el-col :span="3">
                    <el-select v-model="params.compoundStatus" placeholder="请选择" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已开通" value=1></el-option>
                        <el-option label="未开通" value=0></el-option>
                    </el-select>
                </el-col>
            </el-row>
            <el-row class="search_con" :gutter="24" style="margin-bottom:20px;display: flex;align-items: center;">
                <el-col :span="3" style="padding-right: 0px;width:81px;">
                    <label style="color:#333;font-size: 14px;">医院名称:</label>
                </el-col>
                <el-col :span="3">
                    <el-select v-model="params.orgId" filterable placeholder="请输入">
                        <el-option v-for="item in orgIdArr" :key="item.orgId" :label="item.orgFullName" :value="item.orgId">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="3" style="padding-right: 0px;width:81px;">
                    <label style="color:#333;font-size: 14px;">医生名称:</label>
                </el-col>
                <el-col :span="3">
                    <search-select @emitSelectItem="setelectItem" :top="40" :left="0" :params="searchSelectParams" :api="api"></search-select>
                </el-col>
                <el-col :span="6">
                    <el-button type="primary" icon="search" @click="searchClick">搜索</el-button>
                    <el-button type="warning" icon="delete" @click="deleteClick">清除</el-button>
                </el-col>
            </el-row>
        </div>
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="id" label="医生编号" width="80">
            </el-table-column>
            <el-table-column prop="doctorName" label="医生名称" width="80">
            </el-table-column>
            <el-table-column prop="deptName" label="科室名称">
            </el-table-column>
            <el-table-column prop="orgName" label="医院名称">
            </el-table-column>
            <el-table-column label="图文咨询">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.items" :key="index">
                        <div v-if="item.itemCode=='01'">
                            <!--1开通 0关闭-->
                            <div v-if="item.enabled==1">
                                <div>
                                    已开通
                                </div>
                                <div>{{item.price}}元/次<span v-if="!scope.row.planFlag&&item.planNum">,每天{{item.planNum}}人</span></div>
                            </div>
                            <span v-if="item.enabled==0">未开通</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="videoState" label="视频咨询">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.items" :key="index">
                        <div v-if="item.itemCode=='02'">
                            <!--1开通 0关闭-->
                            <div v-if="item.enabled==1">
                                <div>
                                    已开通
                                </div>
                                <div>{{item.price}}元/次<span v-if="!scope.row.planFlag&&item.planNum">,每天{{item.planNum}}人</span></div>
                            </div>
                            <span v-if="item.enabled==0">未开通</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="consultationState" label="复诊配药">
                <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.items" :key="index">
                        <div v-if="item.itemCode=='04'">
                            <!--1开通 0关闭-->
                            <div v-if="item.enabled==1">
                                <div>
                                    已开通
                                </div>
                                <div>{{item.price}}元/次<span v-if="!scope.row.planFlag&&item.planNum">,每天{{item.planNum}}人</span></div>
                            </div>
                            <span v-if="item.enabled==0">未开通</span>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="240">
                <template slot-scope="scope">
                    <el-button size="small" @click="handleEdit(scope.$index, scope.row)">服务设置</el-button>
                    <el-button size="small" v-if="scope.row.planFlag" @click="doctorSchedulingEdit(scope.$index, scope.row)">排班设置</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :page-sizes="[10,20,50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="params.pageSize">
            </el-pagination>
        </div>
        <el-dialog :title="serviceSettingsTitle" :visible.sync="showServiceSettings" @close="closeServiceSettings">
            <div style="padding: 10px 20px;">
                <el-table :data="serviceSetTableData" border style="width: 100%">
                    <el-table-column label="服务名称">
                        <template slot-scope="scope">
                            <div>{{serviceNameMap[scope.row.itemCode]}}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="服务设置">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.enabled">
                                <el-option label="已开通" value=1></el-option>
                                <el-option label="未开通" value=0></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="服务价格">
                        <template slot-scope="scope">
                            <el-input :disabled="scope.row.enabled == '0'" placeholder="请输入服务价格" v-model="scope.row.price"></el-input>
                            <div class="tip" v-if="scope.row.showPriceTip">服务价格：两位小数，0-99999.99</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="服务人数" v-if="showPlanNum">
                        <template slot-scope="scope">
                            <el-input :disabled="scope.row.enabled == '0' || scope.row.isRequirePlan==1" placeholder="请输入服务人数" v-model="scope.row.planNum"></el-input>
                            <div class="tip" v-if="scope.row.showPlanNumTip">服务人数: 整数，1-999</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="dialog-footer center-foot">
                <el-button @click="closeServiceSettings">取 消</el-button>
                <el-button type="primary" @click="submitServiceSettings">确 定</el-button>
            </div>
        </el-dialog>
        <!--门诊设置弹框-->
        <el-dialog title="添加问诊医生" :visible.sync="centerDialogVisible" center>
            <div class="crumbs">
                <el-row class="search_con" :gutter="20">
                    <el-col :span="4">
                        <el-input placeholder="请输入医生姓名" v-model="addParams.doctorName">
                        </el-input>
                    </el-col>
                    <el-col :span="4">
                        <el-input placeholder="请输入联系电话" v-model="addParams.phoneNo">
                        </el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-select placeholder="选择医院" v-model="addParams.orgId" filterable>
                            <el-option v-for="item in hospitalList" :key="item.orgId" :label="item.orgFullName" :value="item.orgId">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="5">
                        <el-button type="primary" icon="search" @click="addSearchClick">搜索</el-button>
                        <el-button type="warning" icon="delete" @click="addDeleteClick">清除</el-button>
                    </el-col>
                    <el-col :span="3" class="addorg">
                        <el-button type="primary" icon="plus" @click="confirmDdd">确认添加</el-button>
                    </el-col>
                </el-row>
            </div>
            <el-table :data="addTableData" border style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column prop="doctorName" label="医生">
                </el-table-column>
                <el-table-column prop="phoneNo" label="联系电话">
                </el-table-column>
                <el-table-column prop="doctorLevelText" label="职称">
                </el-table-column>
                <el-table-column prop="orgName" label="所在医院">
                </el-table-column>
                <el-table-column prop="deptName" label="所在科室">
                </el-table-column>
            </el-table>
            <div class="pagination">
                <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="addTotal" :page-sizes="[10,20,50]" @size-change="handleSizeChange1" @current-change="handleCurrentChange1" :page-size="addParams.pageSize">
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>
<script>
    import { commonAjax } from '../../api/api'
import searchSelect from 'components/common/searchSelectByApi'
export default {
    data() {
        return {
            api: {
                service: 'cas.foshan_doctorOnlineAdminService',
                method: 'queryIhDoctorList'
            },
            orgIdArr: [],
            doctorNameArr: [],
            tableData: [], //获取医生列表信息
            allFilteredData: [], //缓存所有筛选后的数据
            params: {
                orgId: '',
                doctorName: '',
                phoneNo: '',
                textStatus: '', //图文问诊状态(0:关闭, 1:开通)
                videoStatus: '', //视频问诊状态(0:关闭, 1:开通)
                compoundStatus: '', //基层问诊状态(0:关闭, 1:开通)
                pageNo: 1,
                pageSize: 10
            },
            total: 0,
            serviceSettingsTitle: '服务设置',
            serviceSetTableData: [],
            showServiceSettings: false,
            serviceNameMap: {
                '01': '图文咨询',
                '02': '视频咨询',
                '04': '复诊配药'
            },
            showPlanNum: false,
            centerDialogVisible: false, //弹框
            /*=======================新增医生列表相关信息=======================*/
            addTableData: [], //获取新增医生列表信息
            addParams: {
                doctorName: '',
                phoneNo: '',
                orgId: '',
                pageNo: 1,
                pageSize: 10
            },
            addTotal: 0,
            multipleSelection: [], //选中的医生
            hospitalList: [] //医院列表
        }
    },
    computed: {
        searchSelectParams () {
            return {
                orgId: this.params.orgId || '', // 就诊医院
                doctorName: this.params.doctorName || '', // 就诊医生
                deptId: '', // 就诊科室
            }
        }
    },
    methods: {
        copyObj (obj) {
            return JSON.parse(JSON.stringify(obj))
        },
        checkServiceSettings () {
            let moneyReg = /^(([1-9]\d{0,4})|(0))(\.\d{1,2})?$/
            let intReg = /^[1-9]\d{0,2}?$/
            let checkArr = []
            this.serviceSetTableData = this.serviceSetTableData.map((v) => {
                if (v.enabled == '1') {
                    v.showPriceTip = !moneyReg.test(v.price)
                    v.showPlanNumTip = v.isRequirePlan == 0 ? !intReg.test(v.planNum) : false
                    checkArr.push(v.showPriceTip)
                    checkArr.push(v.showPlanNumTip)
                } else {
                    v.showPriceTip = false
                    v.showPlanNumTip = false
                }
                return v
            })
            return checkArr.includes(true)
        },
        //保存按钮提交数据
        submitServiceSettings () {
            let submitArr = []
            let ret = this.checkServiceSettings()
            if (!ret) {
                this.$confirm('确定执行此操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true
                }).then(() => {
                    submitArr = this.copyObj(this.serviceSetTableData)
                    submitArr = submitArr.map((v) => {
                        delete v.showPriceTip
                        delete v.showPlanNumTip
                        return v
                    })
                    console.log(submitArr, 'submitArr')
                    commonAjax('cas.foshan_doctorOnlineAdminService', 'updateDoctorItems', [submitArr]).then(res => {
                        if (res && res.code == 200) {
                            this.closeServiceSettings()
                            this.$message({ type: 'success', message: '保存成功！'})
                            this.getTableData()
                        } else {
                            this.$message({type: 'error', message: res.msg})
                        }
                    })
                }).catch(() => {
                    this.$message({ type: 'info', message: '已取消'})
                })

            }
        },
        closeServiceSettings () {
            this.serviceSetTableData = []
            this.showServiceSettings = false
        },
        checkPropHas (arr = [], prop, propValue = '') {
            let ret = false
            arr.map((v) => {
                if (v[prop] == propValue) {
                    ret = true
                }
            })
            return ret
        },
        getOrgIdArr () {
            let allArr = [{
                orgFullName: '全部',
                orgId: ''
            }]
            commonAjax('cas.foshan_orderManageService', 'queryOpenOrg', ['']).then(res => {
                if (res && res.code == 200) {
                    this.orgIdArr = res.body.length ? allArr.concat(res.body) : allArr
                } else {
                    this.$message({type: 'error', message: res.msg})
                }
            })
        },
        setelectItem (val) {
            this.params.doctorName = val.doctorName
        },
        //获取医生列表数据
        async getTableData() {
            try {
                // 先获取第一页数据以获取总记录数
                const firstPageResponse = await commonAjax('cas.foshan_doctorOnlineAdminService', 'pageDoctorOnline', [{
                    ...this.params,
                    pageNo: 1,
                    pageSize: 100 // 增加单页大小以减少请求次数
                }]);
                
                if (firstPageResponse && firstPageResponse.code == 200) {
                    let allData = [...firstPageResponse.body.data];
                    const totalRecords = firstPageResponse.body.total;
                    const pageSize = 100;
                    const totalPages = Math.ceil(totalRecords / pageSize);
                    
                    // 如果有多页，继续获取剩余页面的数据
                    if (totalPages > 1) {
                        const requests = [];
                        for (let i = 2; i <= totalPages; i++) {
                            requests.push(
                                commonAjax('cas.foshan_doctorOnlineAdminService', 'pageDoctorOnline', [{
                                    ...this.params,
                                    pageNo: i,
                                    pageSize: pageSize
                                }])
                            );
                        }
                        
                        const responses = await Promise.all(requests);
                        responses.forEach(res => {
                            if (res && res.code == 200) {
                                allData = allData.concat(res.body.data);
                            }
                        });
                    }
                    
                    // 前端筛选，只显示科室名称包含"养老院互联网门诊"的医生
                    this.allFilteredData = allData.filter(item => {
                        return item.deptName && item.deptName.includes('养老院互联网门诊')
                    });
                    
                    // 根据当前页面参数进行分页显示
                    this.updateTableDataByPage();
                    this.total = this.allFilteredData.length;
                    
                } else {
                    this.$message({
                        type: 'error',
                        message: firstPageResponse.msg
                    })
                }
            } catch (error) {
                console.error('获取医生数据失败:', error);
                this.$message({
                    type: 'error',
                    message: '获取医生数据失败'
                })
            }
        },
        //根据当前页面参数更新表格数据
        updateTableDataByPage() {
            const startIndex = (this.params.pageNo - 1) * this.params.pageSize;
            const endIndex = startIndex + this.params.pageSize;
            this.tableData = this.allFilteredData.slice(startIndex, endIndex);
        },
        //点击服务设置按钮
        handleEdit(index, row) {
            let arr = this.copyObj(row.items)
            this.showPlanNum = false
            let hasPlanArr = []
            arr.map((v) => {
                if (v.itemCode != '03') {
                    v.doctorOnlineExtraId = row.id
                    v.showPriceTip = false
                    v.showPlanNumTip = false
                    hasPlanArr.push(v.isRequirePlan)
                    this.serviceSetTableData.push(v)
                }
            })
            this.showPlanNum = hasPlanArr.indexOf(0) > -1 ? true : false
            this.showServiceSettings = true
        },
        //点击视频排班设置按钮
        doctorSchedulingEdit(index, row) {
            this.$router.push({name:"doctorScheduling", query:{doctorOnlineExtraId: +row.id, doctorName: encodeURIComponent(row.doctorName)}})
        },
        //改变分页显示的数量
        handleSizeChange(val) {
            this.params.pageSize = val
            this.params.pageNo = 1 // 重置到第一页
            this.updateTableDataByPage()
        },
        //跳转到分页输入框中页码的页面
        handleCurrentChange(val) {
            this.params.pageNo = val
            this.updateTableDataByPage()
        },
        //点击搜索按钮
        searchClick() {
            this.getTableData()
        },
        //点击清除按钮
        deleteClick() {
            this.params = {
                orgId: '',
                doctorName: '',
                phoneNo: '',
                textStatus: '', //图文问诊状态(0:关闭, 1:开通)
                videoStatus: '', //视频问诊状态(0:关闭, 1:开通)
                compoundStatus: '', //基层问诊状态(0:关闭, 1:开通)
                pageNo: 1,
                pageSize: 10
            }
            this.getTableData()
        },
        //点击添加
        openDialog() {
            this.centerDialogVisible = true
            this.getAddTableData()
        },
        //搜索按钮点击
        addSearchClick() {
            this.getAddTableData()
        },
        //清除按钮按钮点击
        addDeleteClick() {
            this.addParams = {
                doctorName: '',
                phoneNo: '',
                orgId: '',
                pageNo: 1,
                pageSize: 10
            }
            this.getAddTableData()
        },
        //改变分页显示的数量
        handleSizeChange1(val) {
            this.addParams.pageSize = val
            this.getAddTableData()
        },
        //跳转到分页输入框中页码的页面
        handleCurrentChange1(val) {
            this.addParams.pageNo = val
            this.getAddTableData()
        },
        //获取选中的医生
        handleSelectionChange(val) {
            let newDoctorIdArr = val.map(v => v.doctorId)
            let newOrgIdArr = val.map(v => v.orgId)
            let newDeptIdArr = val.map(v => v.deptId)
            this.multipleSelection = [newDoctorIdArr,newDeptIdArr,newOrgIdArr]
        },
        //点击确认添加按钮
        confirmDdd() {
            if (this.multipleSelection.length == 0) {
                return this.$message({ type: 'error', message: '添加医生列表不能为空'})
            }
            commonAjax(
                'cas.foshan_doctorOnlineAdminService',
                'addDoctorOnline',
                this.multipleSelection
            ).then(res => {
                if (res && res.code == 200) {
                    this.centerDialogVisible = false
                    this.$message({
                        message: res.body
                    })
                    this.getTableData()
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                }
            })
        },
         //获取添加弹出框中医生列表数据
        getAddTableData() {
            commonAjax('cas.foshan_doctorOnlineAdminService', 'pageOrgDoctors', [
                this.addParams
            ]).then(res => {
                if (res && res.code == 200) {
                    this.addTableData = res.body.data
                    this.addTotal = res.body.total
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                }
            })
        },
        //获取当前租户下的医院
        getHospitalData() {
            let params = [{
                pageNo: '1',
                pageSize: '100',
                tenantId: sessionStorage.getItem('tenantId')
            }]
            let allArr = [{
                orgFullName: '选择医院',
                orgId: ''
            }]
            commonAjax('cas.tenantManageService', 'searchTenantOrg', params).then(
                res => {
                    if (res.code == 200) {
                        this.hospitalList = res.body.data.length ? allArr.concat(res.body.data) : allArr
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.msg
                        })
                    }
                }
            )
        },
    },
    components: {
        searchSelect
    },
    mounted() {
        this.getOrgIdArr()
        this.getHospitalData()
        this.getTableData()
    }
}
</script>
<style type="text/css" scoped>
.tip {
    color: red;
}

</style>

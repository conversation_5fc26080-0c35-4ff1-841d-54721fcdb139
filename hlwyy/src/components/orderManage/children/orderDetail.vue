<template>
    <div class="orderDetail">
        <el-table :data="iquRecordList" border style="width: 100%">
            <el-table-column label="发言人" prop="fromUserName" width="150">
            </el-table-column>
            <el-table-column label="发言时间" prop="msgTime">
            </el-table-column>
            <el-table-column label="发言内容">
                <template slot-scope="scope">
                    <span v-if="scope.row.type == 'TEXT' || scope.row.type == 'CUSTOM' || scope.row.type == 'NETCALL_AUDIO' || scope.row.type == 'NETCALL_VEDIO'">{{scope.row.body}}</span>
                    <span v-if="scope.row.type == 'PICTURE'"><img :src="scope.row.body" style="width:100px;height:80px;"/></span>
                    <span v-if="scope.row.type == 'AUDIO' || scope.row.type == 'FILE' || scope.row.type == 'VIDEO'"><a :href="scope.row.body">点击下载</a></span>
                </template>
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                :current-page="params.pageNo"
                :page-size="params.pageSize"
                :page-sizes="[10, 20, 50]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>
<script>
import {commonAjax,imgview,filedowned} from 'api/api.js';
import _ from 'lodash';
import {doctorOrderImHistory} from 'services';

export default {
    data() {
        let {buzId,buzType}=this.$router.currentRoute.query;

        return {
            iquRecordList:[],
            params:[{
                buzId,
                buzType,
                pageSize:10,
                pageNo:1,
            }],
            total:0,
            callJson:{
                SUCCESS:'',
            },
            imgview:imgview,
        }
    },
    methods:{
        //获取记录列表
        getRecordList() {
            doctorOrderImHistory(this.params,(res)=>{
                if(res.body){
                    this.iquRecordList = res.body.data;
                    this.total = res.body.total;
                    this.handleConten(this.iquRecordList);
                }
            });
        },
         formatSeconds(value) {
            var secondTime = parseInt(value);// 秒
            var minuteTime = 0;// 分
            var hourTime = 0;// 小时
            if(secondTime > 60) {//如果秒数大于60，将秒数转换成整数
                //获取分钟，除以60取整数，得到整数分钟
                minuteTime = parseInt(secondTime / 60);
                //获取秒数，秒数取佘，得到整数秒数
                secondTime = parseInt(secondTime % 60);
                //如果分钟大于60，将分钟转换成小时
                if(minuteTime > 60) {
                    //获取小时，获取分钟除以60，得到整数小时
                    hourTime = parseInt(minuteTime / 60);
                    //获取小时后取佘的分，获取分钟除以60取佘的分
                    minuteTime = parseInt(minuteTime % 60);
                }
            }
            var result = "" + parseInt(secondTime) + "秒";
            if(minuteTime > 0) {
                result = "" + parseInt(minuteTime) + "分" + result;
            }
            if(hourTime > 0) {
                result = "" + parseInt(hourTime) + "小时" + result;
            }
            return result;
        },

        //处理发言内容
        handleConten(arr) {
            let that = this;
            _.each(arr,function(item) {
                if(item.type == 'TEXT') {
                    item.body = item.body;
                } else if(item.type == 'NETCALL_AUDIO' || item.type == 'NETCALL_VEDIO'){
                    let typeText=item.type == 'NETCALL_AUDIO'?'音频':'视频';

                    if(item.body == 'SUCCESS') {
                        item.body = `${typeText}时间:${that.formatSeconds(item.duration)}`;
                    } else if(item.body == 'SINGLE_PARTICIPATE'){
                        item.body = `${typeText}未接通`;
                    }
                }else{
                    item.body = JSON.parse(item.body);
                    if(item.type == 'CUSTOM') {
                        item.body = item.body.data.value;
                    } else if(item.type == 'PICTURE'){
                        item.body = item.body.url;
                    } else if(item.type == 'AUDIO' || item.type == 'FILE' || item.type == 'VIDEO') {
                        item.body = item.body.url;
                    }
                }

            })
        },
        handleSizeChange(val) {
            this.params[0].pageSize = val;
            this.getRecordList();
        },
        handleCurrentChange(val) {
            this.params[0].pageNo = val;
            this.getRecordList();
        },
    },
    mounted() {
        this.getRecordList();
    }
}
</script>
<style lang="scss" scoped>
.orderDetail{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;
    .pagination {
        margin-top: 20px;
    }
}
</style>

<template>
    <div class="inq-list">
         <div class="list-main-head">
            <div  class="fieldName" >
                <el-input size="small" v-model="patientName" clearable  class="shopValue" placeholder="患者姓名"></el-input>
            </div>
            <div  class="fieldName" >
                <el-date-picker
                    v-model="startTime"
                    type="date"
                    size="small"
                    value-format="yyyy-MM-dd"
                    class="shopValue"
                    placeholder="开始时间"
                    :picker-options="pickerOptions">
                </el-date-picker>
                <span class="line">—</span>
                <el-date-picker
                    v-model="endTime"
                    type="date"
                    size="small"
                    value-format="yyyy-MM-dd"
                    class="shopValue"
                    placeholder="结束时间"
                    :picker-options="pickerOptions">
                </el-date-picker>
            </div>
            <div  class="fieldName" >
                <el-select
                    size="small"
                    clearable
                    class="shopValue"
                    v-model="itemCode"
                    placeholder="订单类型"
                    @change="itemCodeChange"
                >
                    <el-option label="订单类型" :value="''"></el-option>
                    <el-option
                        v-for="item in orderTypeList"
                        :label="item.name"
                        :value="item.key"
                        :key="item.key"
                    />
                </el-select>
            </div>
            <div  class="fieldName" >
                <el-select
                    size="small"
                    clearable
                    class="shopValue"
                    v-model="status"
                    placeholder="启用情况"
                    @change="statusChange"
                >
                    <el-option label="全部状态" :value="''"></el-option>
                    <el-option
                        v-for="item in statusList"
                        :label="item.name"
                        :value="item.key"
                        :key="item.key"
                    />
                </el-select>
            </div>

            <div class="fieldName handleWrap">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
            </div>
        </div>
        <el-table
            :data="orderList"
        >
            <el-table-column label="订单号" prop="orderNo" width="150">
            </el-table-column>
            <el-table-column label="患者" prop="patientName" width="150">
            </el-table-column>
            <el-table-column label="性别" prop="sexText"></el-table-column>
            <el-table-column label="年龄" prop="age"></el-table-column>
            <el-table-column label="订单类型" prop="itemCodeText"></el-table-column>
            <el-table-column label="订单时间" prop="createDt"></el-table-column>
            <el-table-column label="状态" prop="orderStatusText"></el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="toDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--分页-->
        <div class="pagination">
            <el-pagination
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                :current-page="params.pageNo"
                :page-size="params.pageSize"
                :page-sizes="[10, 20, 50]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>
<script>
import {commonAjax,imgview} from 'api/api.js';
import patientAvatar from '@/assets/img/pat_avatar.png';
import _ from 'lodash';
import {sStore} from 'js/yydjs';
import {doctorOrderList} from 'services';

export default {
    data() {
        return {
            timeOut:new Date().getTime(),
            orderList:[],
            total:0,
            orderTypeList:[
                {
                    key:'01',
                    name:'图文',
                },
                {
                    key:'02',
                    name:'视频',
                },
                {
                    key:'revisit',
                    name:'复诊配药',
                },
            ],
            statusList:[
                // {
                //     key:'1',
                //     name:'待支付',
                // },
                {
                    key:'2',
                    name:'已取消',
                },
                {
                    key:'3',
                    name:'待接诊',
                },
                {
                    key:'4',
                    name:'待开始',
                },
                {
                    key:'5',
                    name:'进行中',
                },
                {
                    key:'6',
                    name:'已完成',
                },
            ],
            patientName:sStore.get('patName')||'',// 患者姓名关键字
            itemCode:'',// 类型 01：图文，02:视频 revisit：复诊配药
            status:'',// 状态：1待支付 2已取消 3待接诊 4待开始 5进行中 6已完成
            startTime:'',
            endTime:'',
            params:[{
                orgId:sStore.get('orgId'),
                doctorId:sStore.get('docId'),
                patientName:sStore.get('patName')||'',// 患者姓名关键字
                itemCode:'',// 类型 01：图文，02:视频 revisit：复诊配药
                status:'',// 状态：1待支付 2已取消 3待接诊 4待开始 5进行中 6已完成
                startTime:'',
                endTime:'',
                pageNo:1,
                pageSize:10
            }],
        }
    },
    computed:{
        pickerOptions(){
            return {
                disabledDate(time) {
                    let oDate=new Date();

                    return +time>+oDate;
                },
            };
        },
    },
    methods:{
        //获取全部咨询列表
        getOrderList(){
            let {patientName,itemCode,status,startTime,endTime}=this;

            this.params[0].patientName=patientName;
            this.params[0].itemCode=itemCode;
            this.params[0].status=status;
            this.params[0].startTime=startTime;
            this.params[0].endTime=endTime;

            doctorOrderList(this.params,(res)=>{
                if(res.body){
                    let {data,total}=res.body;
                    // 筛选出2025年1月1日及之后的订单
                    let filteredData = data.filter(item => {
                        return item.createDt >= "2025-01-01 00:00:00";
                    });
                    this.orderList = filteredData;
                    this.total = filteredData.length;
                }
            });
        },
        itemCodeChange(value){
            this.params[0].itemCode=value;
            this.params[0].pageNo=1;
            this.params[0].pageSize=10;
            this.getOrderList();
        },
        statusChange(value){
            this.params[0].status=value;
            this.params[0].pageNo=1;
            this.params[0].pageSize=10;
            this.getOrderList();
        },
        handleSizeChange(val) {
            this.params[0].pageSize = val;
            this.getOrderList();
        },
        handleCurrentChange(val) {
            this.params[0].pageNo = val;
            this.getOrderList();
        },
        searchList() {
            this.getOrderList();
        },
        clearParams() {
            this.patientName='';
            this.itemCode='';
            this.status='';
            this.startTime='';
            this.endTime='';
            this.params[0].pageNo=1;
            this.params[0].pageSize=10;
            this.getOrderList();
        },
        //跳转咨询记录详情
        toDetail(row) {
            let {orderDetailId:buzId,itemCode}=row;
            let buzTypeJson={
                '01':30,
                '02':40,
                'revisit':70,
            };
            let buzType=buzTypeJson[itemCode];

            this.$router.push({
                path:'/orderManage/orderDetail',
                query:{
                    buzId,
                    buzType,
                },
            });
        }
    },
    beforeRouteLeave (to, from, next) {
        sessionStorage.removeItem("patName");
        next();
    },
    mounted() {
        this.getOrderList();
    }
}
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .inq-list{
        @include titleAndTable;
    }
</style>


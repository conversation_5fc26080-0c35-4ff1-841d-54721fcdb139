<template>
    <div class="patientManageIndex">
        <section class="content">
            <div class="title">
                <ol>
                    <li>
                        <span>患者姓名：</span>
                        <div class="inputWrap">
                            <el-input v-model="params.personName" type="text" placeholder="请输入" clearable />
                        </div>
                    </li>
                    <li>
                        <span>患者性别：</span>
                        <div class="selectWrap">
                            <el-select v-model="params.sex" placeholder="全部" clearable filterable>
                                <el-option v-for="item in [{label:'男',value:'1'},{label:'女',value:'2'}]" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </li>
                    <li>
                        <span>慢病标签：</span>
                        <div class="selectWrap">
                            <el-select v-model="params.tagCode" placeholder="全部" clearable filterable>
                                <el-option v-for="item in tagList" :key="item.key" :label="item.text" :value="item.key">
                                </el-option>
                            </el-select>
                        </div>
                    </li>
                </ol>
                <a class="search" @click="search">搜索</a>
            </div>
            <div class="main">
                <table>
                    <thead>
                        <tr>
                            <th v-for="item in ['患者姓名','性别','年龄','慢病标签','最近预约','操作']">
                                <div class="item">
                                    {{item}}
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item,index) in dataList">
                            <td>
                                <div class="item">
                                    {{item.personName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.sex=='1'?'男':'女'}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.age}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{getTags(item)}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.updateTime|date('yyyy.MM.dd hh:mm')}}
                                </div>
                            </td>
                            <td>
                                <div class="item handle blue">
                                    <a @click="toPatientDetail(item)">患者详情</a>
<!--                                    <a @click="getMDTUrlForWebFn">申请会诊</a>-->
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="end">
                <el-pagination layout="total,sizes,prev,pager,next,jumper" :total="total" :current-page="params.pageNo" :page-size="params.pageSize" :page-sizes="[10,20,50]" @size-change="sizeChange" @current-change="currentChange" />
            </div>
        </section>
    </div>
</template>
<script>
import { sStore } from 'js/yydjs';
import { findDic, queryPatientList, getMDTUrlForWeb } from 'services';

export default {
    data() {
        return {
            tagList: [],
            dataList: [],
            params: {
                personName: '', // 患者姓名
                sex: '', // 性别
                tagCode: '', // 标签code（包含）
                excTagCode: '', // 标签code（除外）
                pageNo: 1,
                pageSize: 10
            },
            total: 0,
        }
    },
    mounted() {
        this.findDicFn();
        this.getDataList();
    },
    methods: {
        sizeChange(value) {
            this.params.pageSize = value;
            this.getDataList();
        },
        currentChange(value) {
            this.params.pageNo = value;
            this.getDataList();
        },
        search() {
            this.params.pageNo = 1;
            this.params.pageSize = 10;
            this.getDataList();
        },
        getTags(item) {
            let { tags = [] } = item;
            let result = tags.map((item) => item.tagName).join('、')

            return result
            // return result.length > 20 ? result.substring(0, 20) + '...' : result;
        },
        findDicFn() {
            findDic(['cfs.dic.base_disease'], (res) => {
                if (res.body) {
                    let [item = {}] = res.body
                    let { items = [] } = item;

                    this.tagList = items;
                }
            });
        },
        getDataList() {
            queryPatientList([this.params], (res) => {
                console.log(res)
                let { data = [], total = 0 } = res.body || {};

                this.dataList = data;
                this.total = total;
            });
        },
        toPatientDetail(item) {
            let { mpiId } = item;

            this.$router.push({
                path: '/patientManage/patientDetail',
                query: {
                    mpiId,
                },
            });
        },
        getMDTUrlForWebFn() {
            let phoneNo = sStore.get('phoneNo');
            let doctorName = sStore.get('doctorName');

            getMDTUrlForWeb([phoneNo, doctorName], (res) => {
                if (res.body) {
                    window.open(res.body);
                }
            });
        },
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.patientManageIndex {
    .content {
        padding: 20px;
        .title {
            @include conditionSearch;
        }
        .main {
            table {
                @include table1;
            }
        }
        .end {
            display: flex;
            justify-content: flex-end;
            padding: 20px 0;
            /deep/ .el-input__inner {
                height: 28px !important;
                line-height: 28px !important;
            }
        }
    }
}

</style>

<template>
    <div class="inq-list">
         <div class="list-main-head">
            <div  class="fieldName" >
                <el-input size="small" v-model="params.name" clearable  class="shopValue" placeholder="患者姓名"></el-input>
            </div>
            <div  class="fieldName" >
                <el-input size="small" v-model="params.phone" clearable  class="shopValue" placeholder="患者手机号"></el-input>
            </div>
            <div class="fieldName handleWrap">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
            </div>

        </div>
        <el-table :data="patList">
            <el-table-column label="患者" prop="name" >
            </el-table-column>
            <el-table-column label="性别" prop="sex">
                <template slot-scope="scope">
                    <span v-if="scope.row.sex == '1'">男</span>
                    <span v-if="scope.row.sex == '2'">女</span>
                </template>
            </el-table-column>
            <el-table-column label="年龄" prop="age">
            </el-table-column>
            <el-table-column label="手机号" prop="phone">
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="goDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--分页-->
        <div class="pagination">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :current-page="params.pageNo" :page-sizes="[10, 20, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="params.pageSize">
            </el-pagination>
        </div>
    </div>
</template>
<script>
import {commonAjax} from '../../../api/api.js';
export default {
    data() {

        return {
            patList:[],
            params:{
                doctorId:sessionStorage.getItem("docId"),
                name:sessionStorage.getItem("patName")?sessionStorage.getItem("patName"):"",
                phone:"",
                pageNo:1,
                pageSize:10,
            },

            total:0,

        }
    },
    methods:{

        //获取消息列表
        getPatList() {
            commonAjax("cas.foshan_managePatientService","searchAllPatientInfo",[this.params]).then(res => {
                if(res.code == 200) {
                    this.patList = res.body.data;
                    this.total = res.body.total;
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    })
                }
            })
        },

        handleSizeChange(val) {
            this.params.pageSize = val;
            this.getPatList();
        },
        handleCurrentChange(val) {
            this.params.pageNo = val;
            this.getPatList();
        },
        searchList() {
            this.getPatList();
        },
        clearParams() {
            this.params = {
                doctorId:sessionStorage.getItem("docId"),
                name : "",
                phone : "",
                pageNo : 1,
                pageSize : 10,
            }
            this.getPatList();
        },
        goDetail(row) {
            let {id}=row;

            console.log(111,row);
            this.$router.push({
                path:'/patientDetail',
                query:{
                    id,
                },
            });
        }


    },
    beforeRouteLeave (to, from, next) {
        sessionStorage.removeItem("patName");
        next();
    },
    mounted() {
        this.getPatList();
    }
}
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .inq-list{
        @include titleAndTable;
    }
</style>


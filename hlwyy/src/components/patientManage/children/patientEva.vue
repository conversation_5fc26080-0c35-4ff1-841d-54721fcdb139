<template>
    <div class="inq-list">
         <div class="list-main-head">
            <div  class="fieldName" >
                <label class="shopName" >患者姓名:</label>
                <el-input size="small" v-model="patientName" clearable  class="shopValue" placeholder="患者姓名"></el-input>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >类型:</label>
                <el-select size="small" clearable class="shopValue" v-model="itemCode" placeholder="">
                    <el-option label="全部类型" value=""></el-option>
                    <el-option label="图文咨询" value="01"></el-option>
                    <el-option label="视频咨询" value="02"></el-option>
                </el-select>
            </div>

            <div class="fieldName">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
            </div>
        </div>
        <el-table :data="evaList" border style="width: 100%">
            <el-table-column label="患者" prop="patientName" width="150">
            </el-table-column>
            <el-table-column label="咨询类型">
                <template slot-scope="scope">
                    <span v-if="scope.row.itemCode=='01'">图文咨询</span>
                    <span v-if="scope.row.itemCode=='02'">视频咨询</span>
                </template>
            </el-table-column>
            <el-table-column label="咨询时间" prop="consultStartAt">
            </el-table-column>
            <el-table-column label="评价时间" prop="createAt">
            </el-table-column>
            <el-table-column label="操作" >
                <template slot-scope="scope">
                    <el-button size="small" type="text" @click="evaDetail(scope.row)" >详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--分页-->
        <div class="pagination">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :current-page="pageNo" :page-sizes="[10, 20, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="pageSize">
            </el-pagination>
        </div>
    </div>
</template>
<script>
import {commonAjax} from '../../../api/api.js';
export default {
    data() {
        return {
            evaList:[],
            patientName:"",
            itemCode:"",
            total:0,
            pageNo:1,
            pageSize:10,
            doctorId:sessionStorage.getItem("docId"),
            orgId:sessionStorage.getItem("orgId"),
        }
    },
    methods:{
        //获取记录列表
        getEvaList() {
            let params = {
                "doctorId":this.doctorId,    //
                "orgId":this.orgId,
                "patientName":this.patientName,
                "itemCode":this.itemCode,
                "pageNo":this.pageNo,
                "pageSize":this.pageSize,
            }
            commonAjax("cas.foshan_searchPatientCommentService","searchComments",[params]).then(res => {
                if(res.code == 200) {
                    this.evaList = res.body.data;
                    this.total = res.body.total;
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    })
                }
            })
        },

        handleSizeChange(val) {
            this.pageSize = val;
            this.getEvaList();
        },
        handleCurrentChange(val) {
            this.pageNo = val;
            this.getEvaList();
        },
        searchList() {
            this.getEvaList();
        },
        clearParams() {
            this.patientName = "";
            this.itemCode = "";
            this.pageNo = 1;
            this.pageSize = 10;
            this.getEvaList();
        },
        evaDetail(row) {
            sessionStorage.setItem("orderNo",row.orderNo);
            this.$router.push("patientEvaDetail");
        }
    },
    mounted() {
        this.getEvaList();
    }
}
</script>

<style lang="scss" scoped>
.inq-list{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;
    .pagination {
        margin-top: 20px;
    }
    .list-main-head{
        width: 80%;
        // padding: 20px;
        background: white;
        float: left;
        // border-radius: 10px;
        margin-bottom: 10px;
        display: flex;
        flex-wrap: wrap;
        .fieldName{
            margin-bottom: 20px;
            margin-right: 10px;
            line-height: 30px;
            .shopName{
                float: left;
                height:30px;
                // width:100px;
                line-height: 30px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue{
                width:180px;
                height:30px;
                float: left;
                margin-right:10px;
            }
        }
    }
}
</style>


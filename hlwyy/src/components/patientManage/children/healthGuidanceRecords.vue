<template>
    <div class="healthGuidanceRecords">
        <section class="content">
            <div class="title">
                <ol>
                    <li>
                        <span>姓名：</span>
                        <div class="inputWrap">
                            <el-input v-model="params.personName" type="text" placeholder="请输入" clearable />
                        </div>
                    </li>
                    <li>
                        <span>指导类型：</span>
                        <div class="selectWrap">
                            <el-select v-model="params.documentType" placeholder="全部" clearable filterable>
                                <el-option v-for="item in [{label:'膳食指导',value:'06'},{label:'运动指导',value:'07'},{label:'健康宣教',value:'08'}]" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                    </li>
                </ol>
                <a class="search" @click="searchList">搜索</a>
            </div>
            <div class="main">
                <table>
                    <thead>
                        <tr>
                            <th v-for="item in ['姓名','性别','年龄','指导类型','发送日期','状态']">
                                <div class="item">
                                    {{item}}
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item,index) in dataList">
                            <td>
                                <div class="item">
                                    {{item.personName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.sex=='1'?'男':'女'}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.age}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{documentTypeJson[item.documentType]}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.createDt|date('yyyy.MM.dd hh:mm')}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    已发
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="end">
                <el-pagination layout="total,sizes,prev,pager,next,jumper" :total="total" :current-page="params.pageNo" :page-size="params.pageSize" :page-sizes="[10,20,50]" @size-change="sizeChange" @current-change="currentChange" />
            </div>
        </section>
    </div>
</template>
<script>
import { sStore, alerts, copyJson } from 'js/yydjs';
import { queryHealthyInstruction } from 'services';

export default {
    data() {
        return {
            documentTypeJson: {
                '06': '膳食指导',
                '07': '运动指导',
                '08': '健康宣教',
            },
            params: {
                personName: '',
                documentType: '', // 06:膳食指导;07运动指导;08:健康宣教
                mpiId: '',
                pageNo: 1,
                pageSize: 10,
            },
            dataList: [],
            total: 0,
        }
    },
    methods: {
        sizeChange(value) {
            this.params.pageSize = value;
            this.getDataList();
        },
        currentChange(value) {
            this.params.page = value;
            this.getDataList();
        },
        handleSizeChange(val) {
            this.params.pageSize = val;
            this.getDataList();
        },
        handleCurrentChange(val) {
            this.params.pageNo = val;
            this.getDataList();
        },
        searchList() {
            this.getDataList();
        },
        getDataList() {
            queryHealthyInstruction([this.params], (res) => {
                let { data = [], total = 0 } = res.body || {};

                this.dataList = data;
                this.total = total;
            });
        },
    },
    beforeRouteLeave(to, from, next) {
        sessionStorage.removeItem("patName");
        next();
    },
    mounted() {
        this.getDataList();
    }
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.healthGuidanceRecords {
    .content {
        padding: 20px;
        .title {
            @include conditionSearch;
        }
        .main {
            table {
                @include table1;
            }
        }
        .end {
            display: flex;
            justify-content: flex-end;
            padding: 20px 0;
            /deep/ .el-input__inner {
                height: 28px !important;
                line-height: 28px !important;
            }
        }
    }
}

</style>

<template>
    <div class="patientGroups">
        <div class="main">
            <div class="leftTree">
                <el-select
                        v-model="groupType"
                        disabled
                >
                    <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>

                <el-row class="group-list">
                    <el-col v-for="(item,index)  in inlessList" :key="index" :class="{ active:index == number }" @click.native="nodeClickFn(item.tagCode,index)">{{ item.tagName }}</el-col>
                </el-row>
            </div>
            <div class="rightTable">
                <div class="title">
                    <ol>
                        <li>
                            <span>患者姓名：</span>
                            <div :class="{inputWrap:true,disabled:!tagCode}">
                                <el-input v-model="params.personName" type="text" placeholder="请输入" :disabled="!tagCode" clearable />
                            </div>
                            <a class="search" @click="search">搜索</a>
                        </li>
                    </ol>
                    <a class="search" @click="addPatient">添加患者</a>
                </div>
                <div class="main">
                    <el-table :data="tableDataList" height="615" border>
                        <el-table-column v-for="(item,index) in defineTable" :key="index" :prop="item.prop" :label="item.label" :width="item.width">
                            <template slot-scope="scope">
                                <template v-if="index==1">
                                    {{scope.row.sex=='1'?'男':'女'}}
                                </template>
                                <template v-if="index==3">
                                    {{getTags(scope.row)}}
                                </template>
                                <template v-else-if="index==defineTable.length-1">
                                    <el-button type="text" size="small" @click="deleteFn(scope.row)">删除</el-button>
                                </template>
                                <template v-else>
                                    {{scope.row[item.prop]}}
                                </template>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="end">
                    <el-pagination layout="total,sizes,prev,pager,next,jumper" :total="total" :current-page="params.pageNo" :page-size="params.pageSize" :page-sizes="[10,20,50]" @size-change="sizeChange" @current-change="currentChange" />
                </div>
            </div>
        </div>
        <div class="addPatientDialog">
            <el-dialog :close-on-click-modal="false" title="添加患者" :visible.sync="showAddPatientDialog" width="440px" @close="resetForm('params2')">
                <div class="wrap">
                    <div class="main">
                        <el-form :model="params2" ref="params2" :rules="rules" label-width="90px">
                            <el-form-item label="选择患者" prop="mpiIdArr">
                                <el-select v-model="params2.mpiIdArr" placeholder="请选择患者" clearable filterable multiple>
                                    <el-option v-for="item in addPatientlist" :key="item.mpiId" :label="item.personName" :value="item.mpiId">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="end">
                        <button type="button" @click="showAddPatientDialog=false">取消</button>
                        <button type="button" class="blue" @click="save">确认</button>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import vm from 'src/main';
import { sStore, alerts, copyJson } from 'js/yydjs';
import { queryPatientList, deletePatientIllTag, addPatientIllTag, getDiseaseTagInfo } from 'services';

export default {
    data() {
        let validateInt = (rule, value, callback) => {
            if (value) {
                value = +value;
                if (parseInt(value) != value || value < 0 || value > 999) {
                    callback(new Error('请输入0-999的整数'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        let tagCode = '01';

        return {
            showAddPatientDialog: false,
            defineTable: [{
                    prop: 'personName',
                    label: '姓名',
                },
                {
                    label: '性别',
                },
                {
                    prop: 'age',
                    label: '年龄',
                },
                {
                    label: '慢病标签',
                },
                {
                    label: '操作',
                },
            ],
            options: [
            {
                value: '0',
                label: '病种类型'
            }],
            tableDataList: [],
            addPatientlist: [],
            params: {
                tagCode, // 标签code（包含）
                personName: '', // 患者姓名
                sex: '', // 性别
                excTagCode: '', // 标签code（除外）
                pageNo: 1,
                pageSize: 10
            },
            params1: {
                excTagCode: tagCode, // 标签code（除外）
                personName: '', // 患者姓名
                sex: '', // 性别
                tagCode: '', // 标签code（包含）
                pageNo: 1,
                pageSize: 1000
            },
            params2: {
                mpiIdArr: [],
            },
            rules: {
                mpiIdArr: [
                    { required: true, message: '请选择患者', trigger: 'blur' }
                ]
            },
            total: 0,
            nodeList: [],
            orgList: [],
            tagCode,
            groupType: '0', // 左侧分类
            inlessList: [], // 病种列表
            riskList: [], // 病危列表
            number:'0'
        };
    },
    mounted() {
        this.getDataList();
        this.getDiseaseList()
    },
    methods: {
        sizeChange(value) {
            this.params.pageSize = value;
            this.getDataList();
        },
        currentChange(value) {
            this.params.page = value;
            this.getDataList();
        },
        handleSizeChange(val) {
            this.params.pageSize = val;
            this.getDataList();
        },
        handleCurrentChange(val) {
            this.params.pageNo = val;
            this.getDataList();
        },
        search() {
            this.params.pageNo = 1;
            this.params.pageSize = 10;
            this.getDataList();
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        getTags(item) {
            let { tags = [] } = item;
            let result = tags.map((item) => item.tagName).join('、')

            // return result.length > 20 ? result.substring(0, 20) + '...' : result;
            return result
        },
        //患者分组列表
        getDiseaseList() {
            getDiseaseTagInfo([], res => {
                if (res.body) {
                    let data = res.body
                    this.inlessList = data.filter(item => item.tagType == '0')
                }
            })
        },
        getDataList() {
            queryPatientList([this.params], (res) => {
                let { data = [], total = 0 } = res.body || {};
                console.log(data)
                this.tableDataList = data;
                this.total = total;
            });
        },
        loadNodeFn(res) {
            let { level, nodeList } = res;

            if (level == 1) {
                this.nodeList = nodeList;
            }
        },
        nodeClickFn(code, number) {
            if (!code) return;
            this.tagCode = code;
            this.number = number
            this.params.tagCode = code;
            this.params1.excTagCode = code;
            this.params.personName = '';
            this.search();
        },
        deleteFn(row) {
            this.$msgbox({
                title: '',
                message: '是否删除该患者？',
                showCancelButton: true,
                showConfirmButton: true,
                cancelButtonText: '取消',
                confirmButtonText: '确定',
            }).then(() => {
                let { mpiId } = row;

                deletePatientIllTag([
                    [this.tagCode], mpiId
                ], (res) => {
                    alerts('删除成功', 'success');
                    this.getDataList();
                });
            });
        },
        addPatient() {
            if (!this.tagCode) return alerts('未选中分组', 'warning');
            this.showAddPatientDialog = true;
            queryPatientList([this.params1], (res) => {
                if (res.body) {
                    let { data = [] } = res.body;

                    this.addPatientlist = data;
                }
            });
        },
        async save() {
            this.$refs.params2.validate((valid) => {
                if (valid) {
                    let mpiIdArr = copyJson(this.params2.mpiIdArr);
                    this.showAddPatientDialog = false;
                    addPatientIllTag([this.tagCode, mpiIdArr], (res) => {
                        alerts('保存成功', 'success');
                        this.getDataList();
                    });
                }
            });
        },
    }
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.patientGroups {
    padding: 20px;
    background-color: #fff;
    >.main {
        display: flex;
        .leftTree {
            min-width: 320px;
            /*border: 1px solid #EBEEF5;*/
            margin-right: 10px;
            /deep/ .el-tree-node__children {
                .el-tree-node.active {
                    &:first-of-type {
                        background-color: #f5f7fa;
                    }
                }
            }
            .el-select {
                min-width: 100%;
            }
            .group-list{
                border: 1px solid #EBEEF5;
                margin-top: 10px;
                height: 732px;
                padding: 20px;
                    .el-col{
                        font-size: 15px;
                        height:  px;
                        line-height: 25px;
                        cursor: pointer;
                        padding-left: 5px;
                    }
                    .active{
                        background-color: #F5f7fa;
                    }
            }
        }
        .rightTable {
            flex: 1;
            padding: 20px;
            border: 1px solid #EBEEF5;
            .title {
                @include conditionSearch;
                justify-content: space-between;
                .search {
                    margin-left: 20px;
                }
            }
            .main {
                @include table4;
            }
            .end {
                display: flex;
                justify-content: flex-end;
            }
        }
    }
    .switchMask {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 10;
    }
    .defaultTree {
        width: 300px;
        height: 617px;
        border: 1px solid #ddd;
    }
    .addPatientDialog {
        @include elementDialog;
        .wrap {
            .end {
                @include buttonWrap1;
            }
        }
    }
}

</style>

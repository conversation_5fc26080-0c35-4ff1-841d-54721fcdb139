<template>
    <div class="prescriptionRecord">
        <div class="title multiLine-2">
            <span>
                <em>处方类型：</em>
                <b>{{recipeTypeCodeTextJson[detail.recipeTypeCode]}}</b>
            </span>
            <span v-show="detail.recipeTypeCode=='23'">
                <em>药品剂型：</em>
                <b>{{medicineTypeJson[detail.medicineType]}}</b>
                <a @click="prescribing">电子处方</a>
            </span>
        </div>
        <div class="main">
            <div v-show="detail.recipeTypeCode=='21'" class="tableWrap">
                <table class="inputTable">
                    <thead>
                        <tr>
                            <th v-for="(item, index) in ['药品名称','规格','厂家','剂型','用法','剂量（单位）','频次','天数','总量','单位','备注']" :key="index">
                                <div class="item">
                                    <span>{{item}}</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="tbodyMain">
                        <tr v-for="item in detail.drugs">
                            <td>
                                <div class="item">
                                    {{item.drugName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugSpecifications}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>{{item.drugProductionPlaceName}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.pharmaceuticalDosageFormName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugUsePathwaysCodeText}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugUseDose}}{{item.drugUseDoseUnitText}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugUsingRateText}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugUseDays}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugSendNumber}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.drugPackSpecificationUnitText}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.remark}}
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-show="detail.recipeTypeCode=='23'" class="tableWrap">
                <table class="tableTitle check">
                    <tbody class="tbodyTitle">
                        <tr>
                            <td>
                                <div class="item">
                                    <em>剂数：</em>
                                    <span>{{detail.piecesChineseMedicineAgentNumb}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <em>制法：</em>
                                    <span>{{detail.piecesChineseMedicineMethodText}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <em>用量：</em>
                                    <span>{{detail.agentNumbPerDayText}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <em>频次：</em>
                                    <span>{{detail.drugUsingRateText}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <em>用法：</em>
                                    <span>{{detail.drugUsePathwaysCodeText}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <em>服法：</em>
                                    <span>{{detail.drugTakeDemandText}}</span>
                                </div>
                            </td>
                            <td style="width:200px">
                                <div class="item">
                                    <em>备注：</em>
                                    <span>{{detail.chineseHerbalMedicineMethod}} {{remarkExpansion}}</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="tableMain">
                    <div class="leftContent">
                        <table class="inputTable">
                            <thead>
                                <tr>
                                    <th v-for="(item, index) in defineTCMTable" :key="index">
                                        <div class="item">
                                            <span>{{item}}</span>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="tbodyMain">
                                <tr v-for="item in detail.drugs">
                                    <td>
                                        <div class="item">
                                            {{item.drugName}}
                                        </div>
                                    </td>
                                    <td v-if="detail.medicineType!='2'">
                                        <div class="item">
                                            {{item.drugSpecifications}}
                                        </div>
                                    </td>
                                    <!-- <td>
                                            <div class="item">
                                                {{item.drugProductionPlaceName}}
                                            </div>
                                        </td> -->
                                    <td>
                                        <div class="item">
                                            {{item.drugUseDose}}{{item.drugUseDoseUnitText}}
                                        </div>
                                    </td>
                                    <td v-if="detail.medicineType!='2'">
                                        <div class="item">
                                            {{item.remark}}
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="rightContent" v-if="detail.medicineType!='2'">
                        <ul class="expansion">
                            <li :class="{active:detail.medicineDecoctingFlag=='1'}">
                                <a>
                                    <span>代煎</span>
                                </a>
                            </li>
                            <li :class="{active:detail.concentrationInfo=='浓缩100ml'}">
                                <a>
                                    <span>浓缩100ML</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <previewPrescription v-if="showPreviewPrescription" :parent="this" :show="true" :showName="'showPreviewPrescription'" :orderNo="orderNo" :recipeId="recipeId" />
    </div>
</template>
<script>
import previewPrescription from 'components/common/previewPrescription';
import { getTemplateRecipeContent } from 'services';

export default {
    data() {
        return {
            showPreviewPrescription: false,
            recipeTypeCodeTextJson: {
                '21': '西药',
                '22': '中成药',
                '23': '中药',
            },
            medicineTypeJson: {
                '1': '饮片',
                '2': '颗粒',
            },
            tabIndex: 0,
            templateIndex: 0,
            orderNo: -1,
            recipeId: '',
        }
    },

    computed: {
        defineTCMTable() {
            let arr1 = ['药品名称', '规格', '剂量（单位）', '脚注'];
            let arr2 = ['药品名称', '剂量（单位）'];
            let { medicineType } = this.detail;
            let arr = medicineType != '2' ? arr1 : arr2;

            return arr;
        },
        remarkExpansion() {
            let { detail = {} } = this;
            let { templateRecipe = {} } = detail;
            let { medicineDecoctingFlag, concentrationInfo } = templateRecipe;
            let result = [];

            if (medicineDecoctingFlag == '1') {
                result.push('代煎');
            }
            if (concentrationInfo == '浓缩100ml') {
                result.push('浓缩100ML');
            }
            return result.join('，');
        },
    },

    /*
        <prescriptionRecord
            :detail="detail"
        />
    */

    props: {
        detail: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },

    mounted() {

    },

    methods: {
        prescribing() {
            let { orderNo, recipeId } = this.detail;

            this.orderNo = orderNo;
            this.recipeId = recipeId;
            this.showPreviewPrescription = true;
        },
    },

    components: {
        previewPrescription,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.prescriptionRecord {
    padding: 20px 30px;
    .title {
        max-height: 60px;
        line-height: 30px;
        margin-bottom: 20px;
        overflow: hidden;
        span {
            padding-right: 56px;
            &:last-of-type {
                padding-right: 0;
            }
            em {
                color: #666;
            }
            a {
                display: inline-block;
                margin-left: 20px;
                width: 100px;
                height: 26px;
                line-height: 24px;
                text-align: center;
                border: 1px solid $main;
                color: $main;
                border-radius: 15px;
            }
        }
    }
    .main {
        max-height: 340px;
        overflow: hidden;
        overflow-y: auto;
        table {
            @include table2('../../../');
        }
        .tableTitle {
            @include tableTitle;
        }
        .tableMain {
            @include tableMain('../../../');
        }
    }
}

</style>

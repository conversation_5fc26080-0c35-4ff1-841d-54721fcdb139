<template>
    <div class="chronicDisease">
        <div class="labelWrap">
            <div class="title">
                慢病标签
            </div>
            <div class="main">
                <template v-if="currentTagList.length">
                    <span v-for="(item,index) in currentTagList" :key="index">
                        {{item.tagName}}
                        <template v-if="index<currentTagList.length-1">、</template>
                    </span>
                </template>
                <template v-else>暂无</template>
                <a @click="showTagListDialog=true">编辑慢病标签</a>
            </div>
        </div>

        <div class="followUpWrap">
            <div class="title">
                慢病随访
            </div>
            <div class="buttonWrap">
                <a @click="showFollowUpDialog=true">+慢病随访</a>
            </div>
            <div class="main">
                <el-table :data="followUpList" height="240" border>
                    <el-table-column v-for="(item,index) in defineTable1" :key="index" :prop="item.prop" :label="item.label" :width="item.width">
                        <template slot-scope="scope">
                            <template v-if="index==2">
                                {{scope.row.createDt|date('yyyy.MM.dd hh:mm')}}
                            </template>
                            <template v-if="index==3">
                                {{scope.row.endDt|date('yyyy.MM.dd hh:mm')}}
                            </template>
                            <template v-else-if="index==defineTable1.length-1">
                                <el-button type="text" size="small" @click="toFollowUpQuestionnaire(scope.row)">{{scope.row.visitStatus=='01'?'填写问卷':'查看问卷'}}</el-button>
                            </template>
                            <template v-else>
                                {{scope.row[item.prop]}}
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="healthGuidanceWrap">
            <div class="title">
                健康指导
            </div>
            <div class="buttonWrap">
                <a @click="showHealthGuidance=true">+健康指导</a>
            </div>
            <div class="main">
                <el-table :data="healthGuidanceList" height="240" border>
                    <el-table-column v-for="(item,index) in defineTable2" :key="index" :prop="item.prop" :label="item.label" :width="item.width">
                        <template slot-scope="scope">
                            <template v-if="index==0">
                                {{documentTypeJson[scope.row.documentType]}}
                            </template>
                            <template v-else-if="index==1">
                                {{scope.row.createDt|date('yyyy.MM.dd hh:mm')}}
                            </template>
                            <template v-else-if="index==2">
                                已发
                            </template>
                            <template v-else-if="index==defineTable2.length-1">
                                <el-button type="text" size="small" @click="openUrl(scope.row)">查看详情</el-button>
                            </template>
                            <template v-else>
                                {{scope.row[item.prop]}}
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="tagListDialogWrap">
            <el-dialog title="编辑慢病标签" :visible.sync="showTagListDialog">
                <div class="wrap">
                    <div class="main">
                        <span v-for="(item,index) in tagList" :key="index" :class="{active:selectedTagList.includes(item.key)}">
                            {{item.text}}
                            <label :for="createId(index,'illness')" class="labelFor">
                                <input v-model="selectedTagList" :value="item.key" type="checkbox" :id="createId(index,'illness')" />
                            </label>
                        </span>
                    </div>
                    <div class="end">
                        <button type="button" @click="showTagListDialog=false">取消</button>
                        <button type="button" class="blue" @click="updateIllnessTagFn">保存</button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <div class="followUpDialogWrap">
            <el-dialog title="慢病随访" :visible.sync="showFollowUpDialog" @close="resetForm('params1')">
                <div class="wrap">
                    <div class="main">
                        <el-form ref="params1" :model="params1" :rules="rules1" label-width="90px">
                            <el-form-item label="随访疾病" prop="diseaseType">
                                <el-select v-model="params1.diseaseType" placeholder="请选择" clearable filterable>
                                    <el-option v-for="item in visitDiseaseTypeList" :key="item.key" :label="item.text" :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="随访方式" prop="visitType">
                                <el-select v-model="params1.visitType" placeholder="请选择" clearable filterable>
                                    <el-option v-for="item in visitTypeList" :key="item.key" :label="item.text" :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <div class="hint">
                            注：在线问卷点击提交后自动发送对应问卷到患者在线语音由医生主动与患者语音通话并记录问卷
                        </div>
                    </div>
                    <div class="end">
                        <button type="button" @click="showFollowUpDialog=false">取消</button>
                        <button type="button" class="blue" @click="createVisitFn">提交</button>
                    </div>
                </div>
            </el-dialog>
        </div>
        <div class="healthGuidanceWrap">
            <el-dialog title="健康指导" :visible.sync="showHealthGuidance" @close="resetForm('params2')">
                <div class="wrap">
                    <div class="main">
                        <el-form ref="params2" :model="params2" :rules="rules2" label-width="90px">
                            <el-form-item label="指导类型" prop="documentType">
                                <el-select v-model="params2.documentType" placeholder="请选择" clearable filterable @change="findHealthyInstructionDocumentFn">
                                    <el-option v-for="item in [{label:'膳食指导',value:'06'},{label:'运动指导',value:'07'},{label:'健康宣教',value:'08'}]" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="模板名称" prop="instructionId">
                                <el-select v-model="params2.instructionId" placeholder="请选择" clearable filterable :disabled="!params2.documentType" @change="setUrl">
                                    <el-option v-for="item in instructionList" :key="item.id" :label="item.name" :value="item.id">
                                    </el-option>
                                </el-select>
                                <button type="button" class="blue" @click="openUrl()" v-if="params2.instructionId">预览</button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="end">
                        <button type="button" @click="showHealthGuidance=false">取消</button>
                        <button type="button" class="blue" @click="addHealthyInstructionFn">提交</button>
                    </div>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import { alerts, copyJson, sStore } from 'js/yydjs';
import { findDic, findMpiIllnessTagList, updateIllnessTag, visitPageList, createVisit, queryHealthyInstruction, addHealthyInstruction, findHealthyInstructionDocument } from 'services';

export default {
    data() {
        let orgId = sStore.get('orgId');
        let docId = sStore.get('docId');
        let { mpiId } = this.$router.currentRoute.query;

        return {
            query: this.$router.currentRoute.query,
            showTagListDialog: false,
            showrRiskTagListDialog: false,
            showFollowUpDialog: false,
            showHealthGuidance: false,
            documentTypeJson: {
                '06': '膳食指导',
                '07': '运动指导',
                '08': '健康宣教',
            },
            defineTable1: [{
                    prop: 'visitTypeText',
                    label: '随访方式',
                },
                {
                    prop: 'diseaseTypeText',
                    label: '随访类型',
                },
                {
                    label: '随访日期',
                },
                {
                    label: '填表日期',
                },
                {
                    prop: 'visitDoctorName',
                    label: '随访医生',
                },
                {
                    prop: 'visitStatusText',
                    label: '状态',
                },
                {
                    label: '操作',
                },
            ],
            defineTable2: [{
                    label: '指导类型',
                },
                {
                    label: '发送日期',
                },
                {
                    label: '状态',
                },
                {
                    label: '操作',
                },
            ],
            followUpList: [],
            healthGuidanceList: [],
            visitDiseaseTypeList: [],
            visitTypeList: [],
            tagList: [],
            currentTagList: [],
            selectedTagList: [],
            instructionList: [],
            viewUrl: '',
            params1: {
                orgId,
                visitDoctorId: docId,
                mpiId, //居民mpi
                diseaseType: '', //随访慢病类型 1高血压随访 2糖尿病随访
                visitType: '', //随访方式 01在线问卷
                questionnaireId: '', //问卷id 目前只有1高血压 2糖尿病
            },
            params2: {
                mpiId,
                documentType: '',
                instructionId: '', // 文档id
            },
            rules1: {
                diseaseType: [
                    { required: true, message: '请选择随访疾病', trigger: 'blur' },
                ],
                visitType: [
                    { required: true, message: '请选择随访方式', trigger: 'blur' },
                ],
            },
            rules2: {
                documentType: [
                    { required: true, message: '请选择指导类型', trigger: 'blur' },
                ],
                instructionId: [
                    { required: true, message: '请选择模板名称', trigger: 'blur' },
                ],
            },
        }
    },

    mounted() {
        this.findDicFn();
        this.findMpiIllnessTagListFn();
        this.visitPageListFn();
        this.queryHealthyInstructionFn();
    },

    methods: {
        createId(index,type) {
            return `${type}${index}`;
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        toFollowUpQuestionnaire(item) {
            let { mpiId, visitId } = item;

            this.$router.push({
                path: '/patientManage/followUpQuestionnaire',
                query: {
                    mpiId,
                    visitId,
                },
            });
        },
        toHealthEducation(item) {
            this.$router.push({
                path: '/patientManage/healthEducation',
            });
        },
        findDicFn() {
            findDic(['cfs.dic.base_disease', 'cfs.dic.base_visitDiseaseType', 'cfs.dic.base_visitType', 'cfs.dic.base_disease_type'], (res) => {
                if (res.body) {
                    let [item1 = {}, item2 = {}, item3 = {},item4 = {}] = res.body;
                    this.tagList = item1.items || [];
                    this.visitDiseaseTypeList = item2.items || [];
                    this.visitTypeList = item3.items || [];
                }
            });
        },
        findMpiIllnessTagListFn() {
            let { mpiId = '' } = this.query;

            findMpiIllnessTagList([mpiId], (res) => {
                let data = res.body
                if (data) {
                    // 慢病标签
                    this.currentTagList = data.filter( item => item.tagType == "0")
                    this.selectedTagList =this.currentTagList.map((item) => item.tagCode)
                }
            });
        },
        visitPageListFn() {
            let { mpiId } = this.query;

            visitPageList([{ mpiId, pageNo: 1, pageSize: 1000 }], (res) => {
                if (res.body) {
                    let { data = [] } = res.body;

                    this.followUpList = data;
                }
            });
        },
        queryHealthyInstructionFn() {
            let { mpiId } = this.query;

            queryHealthyInstruction([{ mpiId, pageNo: 1, pageSize: 1000 }], (res) => {
                if (res.body) {
                    let { data = [] } = res.body;

                    this.healthGuidanceList = data;
                }
            });
        },
        updateIllnessTagFn() {
            if (this.showTagListDialog == false) return;
            this.showTagListDialog = false;
            let { mpiId } = this.query;

            let selectedTagList = this.selectedTagList.map((item) => {
                let item2 = this.tagList.find((item1) => item == item1.key);
                let { key: tagCode, text: tagName } = item2;

                return {
                    tagCode,
                    tagName,
                    tagType: 0
                };
            });

            selectedTagList.length ? selectedTagList : selectedTagList.push({tagType:0})

            updateIllnessTag([mpiId, selectedTagList], (res) => {
                alerts('保存成功', 'success');
                this.findMpiIllnessTagListFn();
            });
        },
        createVisitFn() {
            this.$refs.params1.validate((valid) => {
                if (valid) {
                    this.params1.questionnaireId = this.params1.diseaseType;
                    let params1 = copyJson(this.params1);

                    this.showFollowUpDialog = false;
                    createVisit([params1], (res) => {
                        alerts('提交成功', 'success');
                        this.visitPageListFn();
                    });
                }
            });
        },
        findHealthyInstructionDocumentFn() {
            this.params2.instructionId = '';
            findHealthyInstructionDocument([this.params2.documentType], (res) => {
                if (res.body) {
                    this.instructionList = res.body;
                }
            });
        },
        addHealthyInstructionFn() {
            this.$refs.params2.validate((valid) => {
                if (valid) {
                    let params2 = copyJson(this.params2);

                    this.showHealthGuidance = false;
                    addHealthyInstruction([params2], (res) => {
                        alerts('提交成功', 'success');
                        this.queryHealthyInstructionFn();
                    });
                }
            });
        },
        setUrl() {
            let item = this.instructionList.find((item) => item.id == this.params2.instructionId);
            let { url } = item;

            this.viewUrl = url;
        },
        openUrl(item = {}) {
            let { instruction = '' } = item;
            let url = instruction || this.viewUrl;

            window.open(url);
        },
    },

    components: {

    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.chronicDisease {
    .title {
        padding: 0 20px;
        line-height: 40px;
        background-color: #f5f5f5;
    }
    .buttonWrap {
        padding: 10px 0;
        a {
            display: inline-block;
            padding: 0 20px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            background-color: $main;
            color: #fff;
            border-radius: 5px;
        }
    }
    .labelWrap {
        .main {
            padding: 20px;
            line-height: 30px;
            a {
                display: inline-block;
                padding: 0 10px;
                margin-left: 10px;
                height: 20px;
                line-height: 20px;
                text-align: center;
                background-color: $main;
                color: #fff;
                border-radius: 15px;
            }
        }
    }
    .followUpWrap, .healthGuidanceWrap {
        margin-bottom: 20px;
        >.main {
            @include table4;
            border: 1px solid #eee;
        }
    }
    .tagListDialogWrap {
        @include elementDialog;
        /deep/ .el-dialog {
            width: 500px;
            .wrap {
                padding: 0 20px !important;
                padding-top: 30px !important;
                .title {}
                .main {
                    padding-top: 10px;
                    overflow: hidden;
                    span {
                        float: left;
                        padding: 0 10px;
                        margin: 0 5px;
                        margin-bottom: 10px;
                        height: 26px;
                        line-height: 26px;
                        text-align: center;
                        background-color: #eee;
                        color: #666;
                        font-size: 12px;
                        border-radius: 13px;
                        cursor: pointer;
                        position: relative;
                        &.active {
                            background-color: $main;
                            color: #fff;
                        }
                    }
                }
            }
            .end {
                @include buttonWrap1;
                justify-content: flex-end !important;
            }
        }
    }
    .followUpDialogWrap, .healthGuidanceWrap {
        @include elementDialog;
        /deep/ .el-dialog {
            width: 450px;
            .wrap {
                padding: 0 20px !important;
                padding-top: 30px !important;
                .title {}
                .main {}
            }
            .end {
                @include buttonWrap1;
            }
        }
        &.followUpDialogWrap {
            .hint {
                padding-left: 20px;
                line-height: 24px;
                color: #ff0000;
                font-size: 12px;
            }
        }
        &.healthGuidanceWrap {
            /deep/ {
                .el-form-item__content {
                    display: flex;
                    .el-select {
                        flex: 1;
                    }
                    button {
                        padding: 0 20px;
                        margin-left: 10px;
                        height: 30px;
                        border: 1px solid #ddd;
                        border-radius: 3px;
                        background-color: $main;
                        color: #fff;
                    }
                }
            }
        }
    }
}

</style>

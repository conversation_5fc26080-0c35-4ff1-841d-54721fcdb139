<template>
    <div class="voiceCall">
        <div :class="{
                    videoWrap:true,
                    active:showNimVideoCall,
                }">
            <div class="wrap">
                <div :class="{
                            containerWrap:true,
                            active:callAccepted,
                        }">
                    <div class="videoCount">
                        {{videoCount|time('h:mm:ss')}}
                    </div>
                    <div class="handleWrap">
                        <a class="microphone">
                            <span v-show="!microphoneValue">＼</span>
                            <div class="sliderWrap">
                                <el-slider v-model="microphoneValue" vertical height="80px" :disabled="!callAccepted" @input="microphoneChange" />
                            </div>
                        </a>
                        <a class="voice">
                            <span v-show="!voiceValue">＼</span>
                            <div class="sliderWrap">
                                <el-slider v-model="voiceValue" vertical height="80px" :disabled="!callAccepted" @input="voiceChange" />
                            </div>
                        </a>
                    </div>
                    <a class="hangup" @click="hangup(false,true)">挂断</a>
                </div>
                <a class="call" @click="getDocDetail">
                    点击发起语音
                </a>
                <div v-show="showNimVideoCall&&!callAccepted" class="maskWrap">
                    <div class="patientInfo">
                        <div class="portrait" :style="{
                                    backgroundImage:patientInfo.avatar?`url(${imgview}${patientInfo.avatar})`:'',
                                }"></div>
                        <div class="rightContent">
                            <h3>
                                <span>{{patientInfo.personName}}</span>
                            </h3>
                            <h4 class="multiLine">
                                {{callStatusJson[callStatus]}}
                            </h4>
                        </div>
                    </div>
                    <div class="hangupWrap">
                        <a @click="hangup(false,true)"></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import vm from 'src/main';
import nimInit, { safeParse } from 'components/NIM/nimInit';
import nimNetcallInit from 'components/NIM/nimNetcallInit';
import { Type, lStore, sStore, dateFormat0, resetFile, copyJson, fileType, normalDate, toTwo, alerts, imgFilesToBase64, isSafari } from 'js/yydjs';
import { imgview, profile, endConsult, finishRevisit, doRecipeSyncPdf, validConsultBeforeEndConsult, validDataBeforeFinishRevisit, isOrderFinish, findCommonExpressionsByTypeAndContent, commonAjax, sendVideoMessage } from 'services';

export default {
    data() {
        let doctorName = sStore.get('doctorName');

        console.log('voiceCall', sStore.get('nimChat'));
        return {
            query: this.$router.currentRoute.query,
            //业务相关
            params: [{
                // orderDetailId:'',
                // itemCode:'',
            }],
            showPreviewPrescription: false,
            previewPrescriptionUrl: '',
            recipeCheckStatus: '',
            consultFinish: false,
            doctorName,
            oldHeight: 0,
            countDownTimer: null,
            countDown: 0,
            //nim聊天
            nimChat: sStore.get('nimChat') || {},
            tabIndex: -1,
            normalDate,
            dateFormat0,
            lStore,
            fileType,
            netcallMsgJson: {
                netcallBill: '通话已结束',
                netcallRejected: '呼叫被拒绝',
                cancelNetcallBeforeAccept: '通话已取消',
            },
            //nim音视频
            netcallType: !isSafari() ? 0 : 1,
            agentDownloadUrl: 'https://yx-web-nosdn.netease.im/package/WebAgent_Setup_V3.0.4.0.exe?download=WebAgent_Setup_V3.0.4.0.exe​',
            showNimVideoCall: false,
            netcallInited: false,
            signalInited: false,
            pushConfig: {
                enable: true,
                needBadge: true,
                needPushNick: true,
                pushContent: '',
                custom: '',
                pushPayload: '',
                sound: '',
                forceKeepCalling: 0,
            },
            sessionConfig: {
                videoQuality: Netcall.CHAT_VIDEO_QUALITY_HIGH,
                videoFrameRate: Netcall.CHAT_VIDEO_FRAME_RATE_15,
                videoBitrate: 0,
                recordVideo: true,
                recordAudio: true,
                recordType: 1,
                highAudio: false,
                bypassRtmp: false,
                rtmpUrl: '',
                rtmpRecord: false,
                splitMode: Netcall.LAYOUT_SPLITLATTICETILE,
            },
            netcallInfo: {
                beCalling: false, //是否被叫中
                type: null, //呼叫类型
                beCalledInfo: null, //被叫信息
                busy: false, //是否正忙
            },
            callTimer: null,
            callDoneTimer: null,
            scrollTimer: null,
            callDone: true,
            calleeOffline: false,
            callAccepted: false,
            sendTimer: null,
            sendDone: false,
            delayCallTimer: null,
            videoCount: 0,
            videoCountTimer: null,
            microphoneValue: 50,
            voiceValue: 50,
            callStatusJson: {
                0: '等待对方接听...',
                1: '对方不在线',
                2: '对方无人接听,请稍候再试',
                3: '对方拒绝了音频通话，请稍候再试',
                4: '对方正忙，请稍后再试',
            },
            callStatus: 0,
            imgview,
        }
    },

    computed: {
        captureVolume() {
            let { microphoneValue } = this;

            return Math.floor(microphoneValue / 100 * 255);
        },
        playVolume() {
            let { voiceValue } = this;

            return Math.floor(voiceValue / 100 * 255);
        },
    },

    /*
        <voiceCall
            :patientInfo="patientInfo"
        />
    */

    props: {
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
    },

    created() {
        vm.$on('componentsUpdate', this.componentsUpdate);
    },

    mounted() {
        //初始化nim
        nimInit();
    },

    beforeDestroy() {
        vm.$off('componentsUpdate', this.componentsUpdate);
        clearTimeout(this.callTimer);
        clearInterval(this.countDownTimer);
        clearInterval(this.videoCountTimer);
        this.hangup();
    },

    methods: {
        getDocDetail() {
            commonAjax("cas.doctorServiceAdmin", "doctorInfo", []).then(res => {
                if (res && res.code == 200) {
                    this.call();
                } else {
                    this.$message({
                        type: "error",
                        message: res.msg,
                    })
                }
            })
        },
        //业务相关
        countDownFn() {
            this.countDown = this.countDown <= 0 ? 0 : this.countDown;
            clearInterval(this.countDownTimer);
            this.countDownTimer = setInterval(() => {
                this.countDown--;
                if (this.countDown <= 0) {
                    this.countDown = 0;
                    clearInterval(this.countDownTimer);
                }
            }, 1000);
        },
        //nim聊天
        componentsUpdate(controlName) {
            setTimeout(() => {
                let { patientInfo = {} } = this;
                let nowTime = new Date();
                let closeTime = normalDate(dateFormat0(nowTime, 'yyyy-MM-dd') + ' 24:00:00');

                this.nimChat = sStore.get('nimChat') || {};
                this.callStatus = 0;
                this.hangup();

                this.countDown = (closeTime - nowTime) / 1000;
                this.countDownFn();
            });
        },
        //nim音视频
        checkPlatform(endFn) {
            const checkBrowser1 = () => {
                //判断是否是Chrome, Edge, IE 11
                if (/Chrome/gi.test(platform.name) || platform.name == 'Microsoft Edge' || (platform.name == 'IE' && platform.version == '11.0')) {
                    endFn && endFn();
                } else {
                    alert('当前浏览器不支持音视频功能，请使用 Chrome、IE 11 或者 Edge 浏览器');
                }
            };
            const checkBrowser2 = () => {
                //判断是否是Chrome, Firefox, Safari
                if (/Chrome/gi.test(platform.name) || /Firefox/gi.test(platform.name) || /Safari/gi.test(platform.name)) {
                    endFn && endFn();
                } else {
                    alert('当前浏览器不支持音视频功能，请使用 Chrome、Firefox 或者 Safari 浏览器');
                }
            };

            if (this.netcallType == 0) {
                //判断是否是win7或win10
                if (~platform.os.family.indexOf("Windows") && (platform.os.version == '7' || platform.os.version == '10')) {
                    checkBrowser1();
                } else {
                    alert('当前系统不支持音视频功能，请使用win7、win10系统');
                }
            } else {
                checkBrowser2();
            }
        },
        controlNimVideoCall(show) {
            this.showNimVideoCall = show;
        },
        hangup(sendCustom, hasConfirm, delayClose) {
            if (!window.netcall) return;
            const hangupFn = () => {
                if (this.callAccepted) sendCustom = false;

                netcall.hangup();
                this.resetWhenHangup(delayClose);
            };

            if (!hasConfirm) {
                hangupFn();
            } else {
                this.$msgbox({
                    title: '',
                    message: '是否确定挂断？',
                    showCancelButton: true,
                    showConfirmButton: true,
                    cancelButtonText: '取消',
                    confirmButtonText: '确定',
                }).then(() => {
                    hangupFn();
                }).catch(() => {

                });
            }
        },
        resetWhenHangup(delayClose) {
            if (!delayClose) {
                this.controlNimVideoCall(false);
            } else {
                setTimeout(() => {
                    this.controlNimVideoCall(false);
                }, 3000);
            }

            //停止设备麦克风
            netcall.stopDevice(Netcall.DEVICE_TYPE_AUDIO_IN);

            //停止播放本地音频
            netcall.stopDevice(Netcall.DEVICE_TYPE_AUDIO_OUT_LOCAL);

            //停止播放对端音频
            netcall.stopDevice(Netcall.DEVICE_TYPE_AUDIO_OUT_CHAT);

            if (this.netcallType == 0) { //PCAgent
                //停止信令通道
                netcall.stopSignal();
            }

            this.netcallInited = false;
            this.signalInited = false;
            this.callDone = true;
            this.calleeOffline = false;
            this.callAccepted = false;

            this.clearVideoCount();
            clearTimeout(this.delayCallTimer);
        },
        netcallInit(endFn) {
            nimNetcallInit(this.netcallType);
            this.netcallInited = true;

            //初始化信令
            this.signalInit(endFn);

            //被叫收到呼叫通知
            this.beCalling();

            //主叫收到被叫响应回调
            this.callResponse();
        },
        signalInit(endFn) {
            //先检查系统和浏览器是否支持pcAgent
            this.checkPlatform(() => {
                //信令通道初始化完毕之后, 开发者可以启用音视频通话相关的 UI, 比如说展示呼叫别人的按钮
                //信令通道初始化失败的时候, 请展示错误并禁用所有音视频通话相关的 UI
                if (this.netcallType == 0) {
                    this.$notify({
                        title: '提示',
                        message: '插件加载中，请耐心等待',
                        duration: 3000
                    });
                }
                netcall.initSignal().then(() => {
                    //当信令通道断开时, 会触发 signalClosed 事件
                    netcall.off('signalClosed');
                    netcall.on('signalClosed', () => {
                        this.signalInited = false;
                        this.hangup();
                    });

                    //初始化过程中会通过 devices 事件回传所有的设备列表
                    netcall.off('devices');
                    netcall.on('devices', (obj) => {
                        //console.log('on devices',obj);
                    });

                    this.signalInited = true;

                    if (Type(endFn) == 'function') {
                        endFn();
                    }
                }).catch((err) => {
                    console.log(err);
                    this.signalInited = false;
                    if (err.code == 'noPC') {
                        this.$confirm('请安装PC Agent，方可使用音视频功能?', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                        }).then(() => {
                            window.open(this.agentDownloadUrl);
                            this.$message({
                                type: 'success',
                                message: '下载完成后，需手动安装插件!'
                            });
                            setTimeout(() => {
                                this.$message({
                                    type: 'success',
                                    message: '插件安装成功后，请刷新页面！'
                                })
                            }, 3000)
                        })
                    }
                });
            });
        },
        beCalling() {
            netcall.off('beCalling');
            netcall.on('beCalling', (obj) => {
                const channelId = obj.channelId;

                //被叫回应主叫自己已经收到了通话请求
                netcall.control({
                    channelId,
                    command: WebRTC.NETCALL_CONTROL_COMMAND_START_NOTIFY_RECEIVED,
                });

                //只有在没有通话并且没有被叫的时候才记录被叫信息, 否则通知对方忙并拒绝通话
                if (!netcall.calling && !this.netcallInfo.beCalling) {
                    this.netcallInfo.type = obj.type;
                    this.netcallInfo.beCalling = true;
                    this.netcallInfo.beCalledInfo = obj;
                    this.controlNimVideoCall(true);
                } else {
                    if (netcall.calling) {
                        this.netcallInfo.busy = netcall.notCurrentChannelId(obj);
                    } else if (beCalling) {
                        this.netcallInfo.busy = this.netcallInfo.beCalledInfo.channelId !== channelId;
                    }

                    if (this.netcallInfo.busy) {
                        netcall.control({
                            channelId,
                            command: WebRTC.NETCALL_CONTROL_COMMAND_BUSY,
                        });

                        //拒绝通话
                        netcall.response({
                            accepted: false,
                            beCalledInfo: obj,
                        });
                    }
                }
            });
        },
        callResponse() {
            //被叫拒绝的通知
            netcall.off('callRejected');
            netcall.on('callRejected', (obj) => {
                this.callStatus = 3;

                //取消呼叫倒计时
                clearTimeout(this.callTimer);

                //挂断
                this.hangup(true, null, true);
            });

            //被叫接受的通知
            netcall.off('callAccepted');
            netcall.on('callAccepted', (obj) => {
                this.callAccepted = true;

                //缓存呼叫类型，后面开启音视频连接需要用到
                this.netcallInfo.type = obj.type;

                //取消呼叫倒计时
                clearTimeout(this.callTimer);

                //可以开启音视频连接操作。。。
                this.videoLink();

                //开始统计时长
                this.videoCountFn();
            });

            //被叫正忙的通知
            netcall.off('control');
            netcall.on('control', (obj) => {
                let { type } = obj;

                if (type == 9) {
                    this.callStatus = 4;

                    //取消呼叫倒计时
                    clearTimeout(this.callTimer);

                    //挂断
                    this.hangup(true, null, true);
                }
            });

            //收到挂断通知
            netcall.off('hangup');
            netcall.on('hangup', (obj) => {
                let { beCalledInfo } = this.netcallInfo;

                // 判断需要挂断的通话是否是当前正在进行中的通话
                if (!beCalledInfo || beCalledInfo.channelId === obj.channelId) {
                    // 清理工作
                    this.resetWhenHangup();
                }
            });

            //其他端已处理的通知
            netcall.on('callerAckSync', function (obj) {
                console.log('其他端已经做了处理', obj);
            });

            //sdk内部消息通知
            netcall.on('error', function (obj) {
                console.log('sdk反馈错误信息', obj);
                if (obj.code) {
                    if (obj.code == 509) {
                        console.warn('你被提出房间了');
                    } else if (obj.code == 500) {
                        console.warn('你的web不支持H264视频编码，无法进行呼叫，请检测浏览器的H264插件是否正常');
                    }
                }
            });
        },
        videoLink() {
            let { scene, to } = this.nimChat;
            const startRemoteLink = () => {
                //播放对方声音
                netcall.startDevice({
                    type: Netcall.DEVICE_TYPE_AUDIO_OUT_CHAT,
                }).catch((err) => {
                    console.log(err);
                });

                //设置本地音量播放大小, 该API可以在通话过程中动态调用调整自己的音量播放大小(即自己听对端的音量)
                netcall.setPlayVolume(this.playVolume);
            };
            const videoLinkFn = () => {
                //开启麦克风
                return netcall.startDevice({
                    type: Netcall.DEVICE_TYPE_AUDIO_IN,
                }).then(() => {
                    //通知对方自己开启了麦克风
                    netcall.control({
                        command: Netcall.NETCALL_CONTROL_COMMAND_NOTIFY_AUDIO_ON,
                    });

                    //设置本地音量采集大小, 该API可以在通话过程中动态调用调整自己的音量采集大小
                    netcall.setCaptureVolume(this.captureVolume);
                }).then(() => {
                    if (this.netcallType == 0) {
                        //开启远程音视频连接
                        startRemoteLink();
                    }
                    this.controlNimVideoCall(true);
                }).catch(function (err) {
                    console.log(err);
                });
            };

            if (this.netcallType == 0) { //开启PCAgent连接
                videoLinkFn();
            } else { //开启WebRTC连接
                //在回调里监听对方加入通话，并显示对方的视频画面
                netcall.on('remoteTrack', function (obj) {
                    console.log('user join', obj)
                    //开启远程音视频连接
                    startRemoteLink();
                });
                netcall.startRtc().then(() => {
                    console.log('webrtc连接成功');
                    return videoLinkFn();
                }).catch(function (err) {
                    console.log('发生错误, 结束会话', err);
                    netcall.leaveChannel();
                });
            }
        },
        getCustom() {
            let { patientInfo = {} } = this;
            let fromUserName = sStore.get('doctorName');
            let { patientName: toUserName, patientAccId: toUser } = patientInfo;
            let result = {
                fromUserName,
                toUserName,
            };

            return JSON.stringify(result);
        },
        sendVideoMessage(channelId = '') {
            return; //下期再说
            channelId = channelId.toString()
            //防止多次发送通知
            if (!this.sendDone) {
                sendVideoMessage([this.buzType, this.buzId, channelId], (res) => {
                    if (res.code == 200) {
                        this.sendDone = true
                        clearTimeout(this.sendTimer)
                        this.sendTimer = setTimeout(() => {
                            this.sendDone = false
                            console.log(this.sendDone, 'setTimeout success')
                        }, 30000)
                        console.log('sendVideoMessage success')
                    }
                })
            }
        },
        call() {
            if (!this.callDone) return alert('请不要频繁发起');
            this.callStatus = 0;
            let { scene, to } = this.nimChat;
            let { pushConfig, sessionConfig, patientInfo = {}, type } = this;
            let { patientName, startTime, endTime } = patientInfo;
            let iDate = +new Date();

            console.log('call', scene, to);
            pushConfig.custom = this.getCustom();
            pushConfig.pushPayload = this.getCustom();
            const netcallCall = () => {
                this.controlNimVideoCall(true);
                netcall.call({
                    type: Netcall.NETCALL_TYPE_AUDIO,
                    account: to,
                    pushConfig,
                    sessionConfig,
                    webrtcEnable: true,
                }).then((obj) => {
                    //成功发起呼叫
                    vm.$emit('nimVideoCallSuccess', obj);
                    console.log('call success', obj);
                }).catch((err) => {
                    console.log(err);
                    //被叫不在线
                    vm.$emit('nimVideoCallFail', err);
                    if (err.event && err.event.code === 11001 || err.event && err.event.event && err.event.event.code === 11001) {
                        console.log('callee offline');
                        this.calleeOffline = true;
                        this.callStatus = 1;
                        clearTimeout(this.delayCallTimer);
                        this.delayCallTimer = setTimeout(() => {
                            netcallCall();
                        }, 3000);
                        // this.hangup(null, null, true);
                        vm.$emit('nimVideoCallcalleeOffline', err);
                    }

                    //防止频繁发起
                    clearTimeout(this.callDoneTimer);
                    this.callDoneTimer = setTimeout(() => {
                        this.callDone = true;
                    }, 3000);
                });

                //设置超时计时器
                if (!this.callTimer) {
                    this.callTimer = setTimeout(() => {
                        if (!netcall.callAccepted) {
                            this.callStatus = 2;
                            this.hangup(null, null, false);
                            this.callTimer = null;
                        }
                    }, 1000 * 30);
                }
            };
            const callFn = () => {
                this.callDone = false;
                this.netcallInit(netcallCall);
            };
            callFn();
        },
        videoCountFn() {
            clearInterval(this.videoCountTimer);
            this.videoCountTimer = setInterval(() => {
                this.videoCount++;
            }, 1000);
        },
        clearVideoCount() {
            clearInterval(this.videoCountTimer);
            this.videoCount = 0;
        },
        microphoneChange(value) {
            if (!window.netcall) return;
            netcall.setCaptureVolume(this.captureVolume);
        },
        voiceChange(value) {
            if (!window.netcall) return;
            netcall.setPlayVolume(this.playVolume);
        },
    },

    components: {

    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.voiceCall {
    max-width: 1200px;
    .videoWrap {
        width: 320px;
        height: 568px;
        background: url('../../../assets/img/bg_fzpy_video.png') no-repeat center center;
        background-size: cover;
        position: relative;
        .wrap {
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            position: absolute;
            .containerWrap {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                bottom: 50px;
                opacity: 0;
                filter: alpha(opacity=0);
                flex: 1;
                padding: 0 10px;
                .hangup {
                    display: block;
                    width: 100%;
                    margin-top: 50px;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                    background-color: $red;
                    color: #fff;
                }
                .videoCount {
                    padding: 41px 0;
                    line-height: 30px;
                    text-align: center;
                    font-size: 16px;
                }
                .handleWrap {
                    display: flex;
                    height: 30px;
                    a {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        flex: 1;
                        background: url('../../../assets/img/handle1.png') no-repeat center center;
                        background-size: 24px;
                        color: #666;
                        position: relative;
                        span {
                            font-size: 24px;
                            color: #666;
                        }
                        .sliderWrap {
                            width: 20px;
                            height: 100px;
                            background-color: rgba(0, 0, 0, .6);
                            border-radius: 15px;
                            position: absolute;
                            top: 0;
                            left: 50%;
                            display: none;
                            transform: translate3d(-50%, -100%, 0);
                            & /deep/ .el-slider {
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                transform: translate3d(-50%, -50%, 0);
                            }
                        }
                        &:hover {
                            .sliderWrap {
                                display: block;
                            }
                        }
                        &:nth-of-type(2) {
                            background-image: url('../../../assets/img/handle2.png');
                            background-size: 20px;
                        }
                    }
                }
                &.active {
                    opacity: 1;
                    filter: alpha(opacity=1);
                }
            }
            .call {
                padding-top: 80px;
                background: url('../../../assets/img/vedioPic.png') no-repeat top center;
                background-size: 90px;
                line-height: 20px;
                text-align: center;
                color: #999;
                position: absolute;
                left: 50%;
                top: 50%;
                z-index: 10;
                transform: translate3d(-50%, -50%, 0);
            }
            .maskWrap {
                width: 100%;
                height: 100%;
                background-color: #000;
                position: absolute;
                left: 0;
                top: 0;
                z-index: 10;
                display: none;
                .patientInfo {
                    @include patientInfo('../../../', 48, 19);
                    flex-direction: column;
                    justify-content: center;
                    width: 200px;
                    padding: 0;
                    padding-top: 50px;
                    margin: 0 auto;
                    overflow: hidden;
                    .portrait {
                        width: 64px;
                        height: 64px;
                    }
                    .rightContent {
                        padding: 0;
                        padding-top: 20px;
                        h3, h4 {
                            text-align: center;
                            color: #fff;
                        }
                        h3 {
                            justify-content: center;
                        }
                    }
                }
                .hangupWrap {
                    padding-top: 200px;
                    text-align: center;
                    a {
                        display: inline-block;
                        width: 50px;
                        height: 50px;
                        background: url('../../../assets/img/icon_refuse.png') no-repeat center center;
                        border-radius: 50%;
                        background-size: cover;
                    }
                }
            }
        }
        &.active {
            .wrap {
                .call {
                    display: none;
                }
                .maskWrap {
                    display: block;
                }
            }
        }
    }
}

</style>

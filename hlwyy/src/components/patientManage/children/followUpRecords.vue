<template>
    <div class="followUpRecords">
        <section class="content">
            <div class="title">
                <ol>
                    <li>
                        <span>患者姓名：</span>
                        <div class="inputWrap">
                            <el-input v-model="params.personName" type="text" placeholder="请输入" clearable />
                        </div>
                    </li>
                    <li>
                        <span>随访类型：</span>
                        <div class="selectWrap">
                            <el-select v-model="params.diseaseType" placeholder="全部" clearable filterable>
                                <el-option v-for="item in visitDiseaseTypeList" :key="item.key" :label="item.text" :value="item.key">
                                </el-option>
                            </el-select>
                        </div>
                    </li>
                    <li>
                        <span>随访方式：</span>
                        <div class="selectWrap">
                            <el-select v-model="params.visitType" placeholder="全部" clearable filterable>
                                <el-option v-for="item in visitTypeList" :key="item.key" :label="item.text" :value="item.key">
                                </el-option>
                            </el-select>
                        </div>
                    </li>
                </ol>
                <a class="search" @click="searchList">搜索</a>
            </div>
            <div class="main">
                <table>
                    <thead>
                        <tr>
                            <th v-for="item in ['姓名','性别','年龄','随访方式','随访类型','随访日期','填表日期','随访医生','状态','操作']">
                                <div class="item">
                                    {{item}}
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item,index) in dataList">
                            <td>
                                <div class="item">
                                    {{item.personName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.sex=='1'?'男':'女'}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.age}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.visitTypeText}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.diseaseTypeText}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.createDt|date('yyyy.MM.dd hh:mm')}}
                                </div>
                            </td>
                            <td>
                                <div class="item" v-if="item.visitStatus!='03'">
                                    {{item.endDt|date('yyyy.MM.dd hh:mm')}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.visitDoctorName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.visitStatusText}}
                                </div>
                            </td>
                            <td>
                                <div class="item handle blue">
                                    <a @click="toFollowUpQuestionnaire(item)" v-if="item.visitStatus!='03'">
                                        {{item.visitStatus=='01'?'填写问卷':'查看问卷'}}
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="end">
                <el-pagination layout="total,sizes,prev,pager,next,jumper" :total="total" :current-page="params.pageNo" :page-size="params.pageSize" :page-sizes="[10,20,50]" @size-change="sizeChange" @current-change="currentChange" />
            </div>
        </section>
    </div>
</template>
<script>
import { sStore, alerts, copyJson } from 'js/yydjs';
import { findDic, visitPageList } from 'services';

export default {
    data() {
        let doctorId = sStore.get('docId') || '';

        return {
            params: {
                doctorId, //医生id
                mpiId: '', //患者mpiId
                userId: '', //用户id(可不传)
                diseaseType: '', //随访慢病类型 1高血压随访 2糖尿病随访
                personName: '', //患者姓名
                visitType: '', //随访方式 01在线问卷
                pageNo: 1,
                pageSize: 10
            },
            dataList: [],
            total: 0,
            visitDiseaseTypeList: [],
            visitTypeList: [],
        }
    },
    mounted() {
        this.findDicFn();
        this.getDataList();
    },
    methods: {
        sizeChange(value) {
            this.params.pageSize = value;
            this.getDataList();
        },
        currentChange(value) {
            this.params.page = value;
            this.getDataList();
        },
        handleSizeChange(val) {
            this.params.pageSize = val;
            this.getDataList();
        },
        handleCurrentChange(val) {
            this.params.pageNo = val;
            this.getDataList();
        },
        searchList() {
            this.getDataList();
        },
        //随访类型、随访方式
        findDicFn() {
            findDic(['cfs.dic.base_visitDiseaseType', 'cfs.dic.base_visitType'], (res) => {
                if (res.body) {
                    let [item1 = {}, item2 = {}] = res.body;

                    this.visitDiseaseTypeList = item1.items || [];
                    this.visitTypeList = item2.items || [];
                }
            });
        },
        getDataList() {
            visitPageList([this.params], (res) => {
                let { data = [], total = 0 } = res.body || {};

                this.dataList = data;
                this.total = total;
            });
        },
        toFollowUpQuestionnaire(item) {
            let { mpiId, visitId } = item;

            this.$router.push({
                path: '/patientManage/followUpQuestionnaire',
                query: {
                    mpiId,
                    visitId,
                },
            });
        },
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.followUpRecords {
    .content {
        padding: 20px;
        .title {
            @include conditionSearch;
        }
        .main {
            table {
                @include table1;
            }
        }
        .end {
            display: flex;
            justify-content: flex-end;
            padding: 20px 0;
            /deep/ .el-input__inner {
                height: 28px !important;
                line-height: 28px !important;
            }
        }
    }
}

</style>

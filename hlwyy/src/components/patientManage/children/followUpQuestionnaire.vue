<template>
    <div class="followUpQuestionnaire" @click="validateFn">
        <div class="patientInfo">
            <div class="avatar" :style="{
                backgroundImage:detail.avatar?`url(${imgview}${detail.avatar})`:'',
            }"></div>
            <div class="rightContent">
                <h3>
                    <span>{{detail.personName}}</span>
                    <em :class="{female:detail.sex!='1'}">{{detail.age}}岁</em>
                </h3>
                <h4>手机号：{{detail.phoneNo}}</h4>
            </div>
        </div>
        <div class="infoWrap">
            <el-form :model="params" :rules="rules" ref="params">
                <ul class="infoList">
<!--                     <div class="title"></div>-->
                    <el-form-item class="infoList" :label="item.nodeName" :prop="item.nodeCode" v-for="(item,index) in nodes" :key="index">
                        <li v-if="item.nodeType=='1'">
                            <div class="rightContent">
                                <el-input type="number" v-model="params[item.nodeCode]" :disabled="isReadonly" clearable></el-input>
                                <span class="unit" v-if="item.number&&item.number.unitText">{{item.number.unitText}}</span>
                            </div>
                        </li>
                        <li v-else-if="item.nodeType=='2'">
                            <div class="rightContent">
                                <el-input type="text" v-model="params[item.nodeCode]" :maxlength="item.text.maxLimit" :disabled="isReadonly" clearable></el-input>
                                <span class="unit" v-if="item.text&&item.text.unitText">{{item.text.unitText}}</span>
                            </div>
                        </li>
                        <li v-else-if="item.nodeType=='3'">
                            <div class="rightContent">
                                <el-date-picker
                                        v-model="params[item.nodeCode]"
                                        type="date"
                                        :disabled="isReadonly"
                                        value-format="yyyy-MM-dd"
                                        :picker-options="pickerOptions">
                                </el-date-picker>
                            </div>
                        </li>

                        <li v-else-if="item.nodeType=='15'">
                            <div class="rightContent">
                                <el-input type="textarea" v-model="params[item.nodeCode]" :maxlength="item.text.maxLimit" :disabled="isReadonly" clearable></el-input>
                            </div>
                        </li>

                        <li v-else-if="item.nodeType=='4'||item.nodeType=='5'">
                            <div class="rightContent">
                                <el-select v-show="item.isWrite=='1'" v-model="params[item.nodeCode]" :placeholder="item.options.length ? '' : '其他选项可自行填写'" allow-create clearable filterable :multiple="item.nodeType=='5'" :disabled="isReadonly" v-if="item.options">
                                    <el-option v-for="(item1,index1) in item.options" :key="`${index}${index1}`" :label="item1.optionContent" :value="item1.optionCode" :disabled="disabledOption(item,item1)">
                                    </el-option>
                                </el-select>
                                <el-select v-show="item.isWrite!='1'" v-model="params[item.nodeCode]" placeholder="" clearable filterable :multiple="item.nodeType=='5'" :disabled="isReadonly" v-if="item.options">
                                    <el-option v-for="(item1,index1) in item.options" :key="`${index}${index1}`" :label="item1.optionContent" :value="item1.optionCode" :disabled="disabledOption(item,item1)">
                                    </el-option>
                                </el-select>
                            </div>
                        </li>

                        <li v-else-if="item.nodeType=='BMI'">
                            <div class="rightContent">
                                <el-input v-model="BMI" :disabled="true"></el-input>
                                <span class="unit" v-if="item.number&&item.number.unitText">{{item.number.unitText}}</span>
                            </div>
                        </li>
                    </el-form-item>
                </ul>
            </el-form>
        </div>
        <div class="buttonWrap" v-if="!isReadonly">
            <button type="button" @click="$router.go(-1)">取消</button>
            <button type="button" class="blue" @click="save">保存</button>
        </div>
    </div>
</template>
<script>
import { toFixed0, alerts, copyJson, sStore } from 'js/yydjs';
import { imgview, getPatientInfo, getIhForm, getVisitPaperInfo, writePaper } from 'services';

export default {
    data() {
        return {
            query: this.$router.currentRoute.query,
            params: {

            },
            detail: {},
            nodes: [],
            isReadonly: true,
            showOther: false,
            imgview,
            pickerOptions: {
                disabledDate: (date) => {
                    let minDate = (new Date(
                        new Date().getFullYear(),
                        new Date().getMonth() - 1,
                        0
                    ).getDate()) * 24 * 3600 * 1000

                    let startDate = new Date() - minDate
                    return date.getTime() < startDate || date.getTime() > Date.now()
                }
            }
        }
    },

    computed: {
        rules() {
            let { nodes } = this;
            let result = {};
            nodes.map((item) => {
                let { requiredFlag: required = false, nodeType, nodeName, nodeCode, number, text, visibleFlag } = item;
                let { limitLower = 0, limitUpper, precise } = number || {};
                let { miniLimit, maxLimit } = text || {};
                let message1 = `请${['4','5'].includes(nodeType)?'选择':'输入'}${nodeName}`;
                let message2 = `长度在${miniLimit}到${maxLimit}个字符`;
                let validateInt = (rule, value, callback) => {
                    if (value) {
                        let value1 = +value;
                        let value2 = value + '';
                        let arr = value2.split('.');

                        //console.log(arr, precise);
                        if (precise == 0 && parseInt(value1) != value1 || value1 < 0 || maxLimit > 999) {
                            callback(new Error(`请输入${limitLower}到${limitUpper}的${precise==0?'整数':'数字'}`));
                        } else if (precise && arr.length == 2 && arr[1].length > precise) {
                            callback(new Error(`小数点限制${precise}位`));
                        } else {
                            callback();
                        }
                    } else {
                        callback();
                    }
                };
                let rules = [];

                if (required) {
                    rules.push({ required, message: message1, trigger: 'blur' });
                }
                if (nodeType == '1') {
                    rules.push({ validator: validateInt, trigger: 'blur' });
                }
                if (nodeType == '2' || nodeType == '15') {
                    rules.push({ min: miniLimit, max: maxLimit, message: message2, trigger: 'blur' });
                }
                result[nodeCode] = rules;
                return result;
            });

            return result;
        },
        BMI() {
            let weight = this.params['07'];
            let height = this.params['08'];

            return height && weight ? toFixed0(weight / Math.pow(height / 100, 2), 2, true) : '';
        }
    },

    mounted() {
        this.getPatientInfoFn();
        this.getVisitPaperInfoFn();
    },

    beforeDestroy() {
        sStore.set('patientDetailTabIndex', 1);
    },

    methods: {
        validateFn() {
            this.$refs.params.validate((valid) => {});
        },
        disabledOption(item, item1) {
            let { nodeType, nodeCode } = item;
            let { mutex = '' } = item1;
            let current = this.params[nodeCode];
            let disabled = false;

            if (nodeType == '5' && current) {
                mutex = mutex.split('|');
                for (let item2 of current) {
                    if (mutex.includes(item2)) {
                        disabled = true;
                        break;
                    }
                }
                return disabled;
            } else {
                return false;
            }
        },
        getPatientInfoFn() {
            let { mpiId } = this.query;

            getPatientInfo([mpiId], (res) => {
                if (res.body) {
                    this.detail = res.body;
                }
            });
        },
        getIhFormFn() {
            let { visitId } = this.query;

            getIhForm([visitId], (res) => {
                if (res.body) {
                    let { nodes = [] } = res.body;

                    this.nodes = nodes;
                    this.isReadonly = false;
                }
            });
        },
        getVisitPaperInfoFn() {
            let { visitId } = this.query;

            getVisitPaperInfo([visitId], (res) => {
                if (res.body) {
                    let { content = '', questionnaireId } = res.body;

                    content = JSON.parse(content);
                    for (let item of content) {
                        let { nodeCode } = item;

                        if (item.value || item.value == 0) {
                            this.params[nodeCode] = item.value;
                        }
                    }
                    this.nodes = copyJson(content);
                   // console.log('getVisitPaperInfoFn', content);
                } else {
                    this.getIhFormFn();
                }
            }, () => {
                this.getIhFormFn();
            });
        },
        save() {
            this.$refs.params.validate((valid) => {
                if (valid) {
                    let { visitId } = this.query;
                    console.log(this.params)
                    let content = JSON.stringify(this.nodes.map((item) => {
                        let { nodeCode } = item;

                        item.value = this.params[nodeCode];
                        console.log(item.value)
                        return item;
                    }));

                    console.log(content)
                    writePaper([{ visitId, content }], (res) => {
                        if (res.code == 200) {
                            alerts('保存成功', 'success');
                            this.isReadonly = true;
                            this.getVisitPaperInfoFn();
                        }
                    });
                }
            });
        }
    },

    components: {

    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.followUpQuestionnaire {
    padding: 20px;
    background-color: #fff;
    .patientInfo {
        @include patientInfo1('../../../');
    }
    .infoWrap {
        .title {
            padding: 0 20px;
            line-height: 40px;
            font-size: 18px;
            background-color: #f5f5f5;
            color: #666;
            text-align: center;
        }
        .infoList {
            width: 1000px;
            padding: 0 20px;
            overflow: hidden;
            /deep/ .el-form-item {
                float: left;
                display: flex;
                justify-content: space-between;
                width: 50%;
                padding: 20px 0;
                padding-right: 100px;
                min-height: 80px;
                .el-input.is-disabled .el-input__inner, .el-textarea.is-disabled .el-textarea__inner {
                    background-color: transparent !important;
                }
                &.oneLine {
                    width: 100%;
                    justify-content: flex-start;
                    .rightContent {
                        max-width: 400px;
                        flex: 1;
                        padding-left: 50px;
                    }
                }
                >span {
                    float: left;
                }
                .rightContent {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    min-width: 150px;
                    >.el-input {
                        /*width: 100px;*/
                        .el-input__inner {
                            border-radius: 0;
                            border-top: none;
                            border-left: none;
                            border-right: none;
                            text-align: center;
                            width: auto;
                        }
                    }
                    .el-select, .el-textarea {
                        width: 250px;
                    }
                    .el-textarea.is-disabled .el-textarea__inner{
                        color: #666666;
                    }
                    .el-input.is-disabled .el-input__inner{
                        color: #666666;
                    }
                    .el-select .el-tag {
                        height: auto !important;
                        line-height: initial !important;
                        white-space: normal;
                        word-break: break-all;
                        color: #666666;
                    }

                    .el-date-editor input{
                        width: 200px;
                    }
                }
            }
        }
    }
    .buttonWrap {
        @include buttonWrap1;
        width: 600px;
    }
}

</style>

<template>
    <div class="patientDetail">
        <div class="patientInfo">
            <div class="avatar" :style="{
                backgroundImage:detail.avatar?`url(${imgview}${detail.avatar})`:'',
            }"></div>
            <div class="rightContent">
                <h3>
                    <span>{{detail.personName}}</span>
                    <em :class="{female:detail.sex!='1'}">{{detail.age}}岁</em>
<!--                    <a @click="getMDTUrlForWebFn">-->
<!--                        <span>申请远程会诊</span>-->
<!--                    </a>-->
                </h3>
                <h4>手机号：{{detail.phoneNo}}</h4>
            </div>
        </div>
        <el-tabs v-model="activeName1" @tab-click="handleClick1">
            <el-tab-pane label="基本信息" name="0">
                <div class="basicInfo">
                    <ul class="infoList">
                        <li>
                            <span>身高</span>
                            <em>{{detail.height}}cm</em>
                        </li>
                        <li>
                            <span>婚姻状况</span>
                            <em>{{detail.maritalStatusText}}</em>
                        </li>
                        <li>
                            <span>药物过敏史</span>
                            <em>{{detail.medicalAllergy}}</em>
                        </li>
                        <li>
                            <span>体重</span>
                            <em>{{detail.weight}}kg</em>
                        </li>
                        <li>
                            <span>生育状况</span>
                            <em>{{detail.procreateStatusText}}</em>
                        </li>
                        <li>
                            <span>食物/接触物过敏</span>
                            <em>{{detail.foodAllergy}}</em>
                        </li>
                        <li>
                            <span>BMI</span>
                            <em>{{BMI}}kg/m2</em>
                        </li>
                        <li>
                            <span>国籍</span>
                            <em>{{detail.nationalityText}}</em>
                        </li>
                        <li>
                            <span>家族病史</span>
                            <em>{{detail.familyDiseaseHistory}}</em>
                        </li>
                        <li>
                            <span>血型</span>
                            <em>{{detail.bloodTypeText}}</em>
                        </li>
                        <li>
                            <span>民族</span>
                            <em>{{detail.nationText}}</em>
                        </li>
                        <li>
                            <span>手术或外伤</span>
                            <em>{{detail.operationOrTrauma}}</em>
                        </li>
                    </ul>
                </div>
                <div class="tabWrap">
                    <el-tabs v-model="activeName2" type="border-card" @tab-click="handleClick2">
                        <el-tab-pane label="就诊记录">
                            <ul class="recordList" v-if="diagnosisList.length">
                                <li v-for="(item,index) in diagnosisList" :key="index">
                                    <span>就诊记录：{{item.createDt|date('yyyy-MM-dd')}}{{item.orgFullName}}</span>
                                    <em>{{item.deptName}}</em>
                                </li>
                            </ul>
                            <defaultImage :show="loaded1&&!diagnosisList.length" />
                        </el-tab-pane>
                        <el-tab-pane label="处方记录">
                            <el-tabs v-model="activeName3" @tab-click="handleClick3" v-if="recipeList.length">
                                <el-tab-pane v-for="(item,index) in recipeList" :key="index" :label="`处方${index+1}`" :name="`${index}`">
                                    <prescriptionRecord :detail="prescriptionDetail" v-if="index==activeName3" />
                                </el-tab-pane>
                            </el-tabs>
                            <defaultImage :show="loaded2&&!recipeList.length" />
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </el-tab-pane>
            <el-tab-pane label="慢病管理" name="1">
                <div class="chronicDiseaseManagement" v-if="activeName1=='1'">
                    <div class="chronicDiseaseWrap">
                        <chronicDisease />
                    </div>
<!--                    <div class="audioWrap">-->
<!--                        <voiceCall :patientInfo="detail" />-->
<!--                    </div>-->
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import prescriptionRecord from './prescriptionRecord';
import chronicDisease from './chronicDisease';
import voiceCall from './voiceCall';
import defaultImage from 'components/common/defaultImage';
import { toFixed0, sStore } from 'js/yydjs';
import { imgview, getPatientInfo, findDiagnosisList, findRecipeList, getMDTUrlForWeb } from 'services';

export default {
    data() {
        let activeName1 = sStore.get('patientDetailTabIndex') || '0';

        return {
            query: this.$router.currentRoute.query,
            activeName1,
            activeName2: '0',
            activeName3: '0',
            detail: {},
            diagnosisList: [],
            recipeList: [],
            loaded1: false,
            loaded2: false,
            imgview,
        }
    },

    computed: {
        BMI() {
            let { height, weight } = this.detail;

            return height && weight ? toFixed0(weight / Math.pow(height / 100, 2), 2, true) : '';
        },
        prescriptionDetail() {
            let { recipeList = [], activeName3 = '0' } = this;
            let result = recipeList[activeName3] || {};

            return result;
        },
    },

    mounted() {
        this.getPatientInfoFn();
        this.findDiagnosisListFn();
        this.findRecipeListFn();
    },

    beforeDestroy() {
        sStore.remove('patientDetailTabIndex');
    },

    methods: {
        handleClick1(tab, event) {
            console.log('handleClick1', tab, event);
        },
        handleClick2(tab, event) {
            console.log('handleClick2', tab, event);
        },
        handleClick3(tab, event) {
            console.log('handleClick3', tab, event);
        },
        getPatientInfoFn() {
            let { mpiId } = this.query;

            getPatientInfo([mpiId], (res) => {
                if (res.body) {
                    this.detail = res.body;
                    let { accId: to } = res.body;

                    sStore.set('nimChat', {
                        scene: 'p2p',
                        to,
                    });
                }
            });
        },
        findDiagnosisListFn() {
            let { mpiId } = this.query;

            findDiagnosisList([mpiId], (res) => {
                this.loaded1 = true;
                this.diagnosisList = res.body || {};
            }, () => {
                this.loaded1 = true;
            });
        },
        findRecipeListFn() {
            let { mpiId } = this.query;

            findRecipeList([mpiId, 1, 100], (res) => {
                console.log(res)
                this.loaded2 = true;
                let { items = [] } = res.body || {};

                this.recipeList = items;
            }, () => {
                this.loaded2 = true;
            });
        },
        getMDTUrlForWebFn() {
            let phoneNo = sStore.get('phoneNo');
            let doctorName = sStore.get('doctorName');

            getMDTUrlForWeb([phoneNo, doctorName], (res) => {
                if (res.body) {
                    window.open(res.body);
                }
            });
        },
    },

    components: {
        prescriptionRecord,
        chronicDisease,
        voiceCall,
        defaultImage,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.patientDetail {
    padding: 20px;
    background-color: #fff;
    .patientInfo {
        @include patientInfo1('../../../');
    }
    .basicInfo {
        .title {
            padding: 0 20px;
            line-height: 40px;
            font-size: 14px;
            background-color: #f5f5f5;
            color: #666;
        }
        .infoList {
            padding: 10px 20px;
            overflow: hidden;
            li {
                float: left;
                width: 33.3333%;
                line-height: 30px;
                span {
                    float: left;
                    width: 50px;
                }
                &:nth-of-type(3n+2) {
                    span {
                        width: 70px;
                    }
                }
                &:nth-of-type(3n) {
                    span {
                        width: 100px;
                    }
                }
            }
        }
    }
    .tabWrap {
        padding: 20px;
        background-color: #F8F8F8;
        /deep/ .el-tabs {
            border: none;
            box-shadow: none;
            .el-tabs__header {
                border: none;
            }
            .el-tabs__item {
                border: none;
                font-size: 13px !important;
            }
            .recordList {
                line-height: 30px;
                span {
                    padding-right: 20px;
                }
            }
        }
    }
    .chronicDiseaseManagement {
        display: flex;
        .chronicDiseaseWrap {
            flex: 1;
        }
        .audioWrap {
            width: 400px;
            min-width: 400px;
            padding: 0 20px;
            position: relative;
        }
    }
    /deep/ .el-tabs__content {
        min-height: 400px;
        .defaultImage {
            .defaultList {
                padding: 5% 0 !important;
            }
        }
    }
}

</style>

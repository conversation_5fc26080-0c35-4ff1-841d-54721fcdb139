<template>
    <div class="inq-list">
        <div class="list-main-head">
            <div  class="fieldName" >
                <label class="shopName" >咨询时间:</label>
                <p size="small" class="shopValue">{{evaDetail.consultStartAt}}</p>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >评价时间:</label>
                <p size="small" class="shopValue">{{evaDetail.createAt}}</p>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >患者姓名:</label>
                <p size="small" class="shopValue">{{evaDetail.patientName}}</p>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >手机号:</label>
                <p size="small" class="shopValue">{{evaDetail.patientPhone}}</p>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >医生姓名:</label>
                <p size="small" class="shopValue">{{evaDetail.doctorName}}</p>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >评价类型:</label>
                <p size="small"  class="shopValue" v-if="evaDetail.itemCode == '01'">图文咨询</p>

                <p size="small"  class="shopValue" v-if="evaDetail.itemCode == '02'">视频咨询</p>

            </div>

            <div  class="fieldName" >
                <label class="shopName" >评价星级:</label>
                <el-rate
                    v-model="evaDetail.score"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value}"
                    class="shopValue">
                </el-rate>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >评价标签:</label>
                <p size="small" clearable disabled class="shopValue conTag">
                    <el-tag style="margin-right:5px;" v-for="item in evaDetail.tagsContext" :key="item.id">{{item}}</el-tag>
                </p>
            </div>

            <div  class="fieldName" >
                <label class="shopName" >评价内容:</label>
                <p size="small"  class="shopValue conTag" >{{evaDetail.content?evaDetail.content:'无'}}</p>


            </div>
        </div>
    </div>
</template>


<script>
import { commonAjax } from '../../../api/api';
    export default {
        data() {
            return {
                params:{
                    "doctorId":sessionStorage.getItem("docId"),
                    "orgId":sessionStorage.getItem("orgId"),
                    "orderNo":sessionStorage.getItem("orderNo"),

                },
                evaDetail:{
                    consultStartAt:"",
                },
            }
        },
        methods:{
            getEvaDetail() {
                commonAjax("cas.foshan_searchPatientCommentService","searchCommentDetails",[this.params]).then(res => {
                    if(res && res.code == 200) {
                        this.evaDetail = res.body
                    } else {
                        this.$message({
                            type:"error",
                            message:res.msg,
                        })
                    }
                })
            }
        },
        mounted() {
            this.getEvaDetail();
        }
    }
</script>



<style lang="scss" scoped>
.inq-list{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;
    .list-main-head{
        width: 100%;
        background: white;
        height: 100%;
        // text-align: center;
        margin-bottom: 10px;
        .fieldName{
            margin-bottom: 20px;
            line-height: 30px;
            // height: 40px;
            // width: 500px;
            overflow: hidden;
            .shopName{
                float: left;
                height:30px;
                width:150px;
                line-height: 30px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue{
                width: 80%;
                min-width: 500px;
                height: auto;
                float: left;
                display: block;
                padding-left:10px;
            }
            .conTag{
                word-wrap: break-word;
                word-break: normal;
            }
        }
    }
}
</style>

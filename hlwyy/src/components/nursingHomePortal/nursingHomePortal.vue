<template>
    <div class="nursing-home-portal">
        <transition name="router-fade" mode="out-in">
            <router-view></router-view>
        </transition>
    </div>
</template>

<script>
export default {
    name: 'NursingHomePortal',
    data() {
        return {
            query: this.$router.currentRoute.query,
        }
    },
    mounted() {
        
    },
    methods: {
        
    },
    components: {
        
    },
}
</script>

<style lang="scss" scoped>
@import '~css/public.scss';

.nursing-home-portal {
    width: 100%;
    height: 100%;
}
</style> 
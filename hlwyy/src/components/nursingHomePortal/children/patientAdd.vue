<template>
    <div class="patient-add">
        <div class="page-header">
            <h3>新增患者</h3>
            <el-button @click="goBack">返回</el-button>
        </div>
        
        <el-form :model="patientForm" :rules="formRules" ref="patientForm" label-width="120px">
            <el-card class="form-card">
                <div slot="header">
                    <span>基本信息</span>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="患者姓名" prop="name">
                            <el-input v-model="patientForm.name" placeholder="请输入患者姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="性别" prop="sex">
                            <el-radio-group v-model="patientForm.sex">
                                <el-radio label="1">男</el-radio>
                                <el-radio label="2">女</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="年龄" prop="age">
                            <el-input-number v-model="patientForm.age" :min="0" :max="150" placeholder="请输入年龄"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="手机号" prop="phone">
                            <el-input v-model="patientForm.phone" placeholder="请输入手机号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="身份证号" prop="idCard">
                            <el-input v-model="patientForm.idCard" placeholder="请输入身份证号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            
            <el-card class="form-card">
                <div slot="header">
                    <span>入住信息</span>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="入住时间" prop="admissionDate">
                            <el-date-picker
                                v-model="patientForm.admissionDate"
                                type="date"
                                placeholder="选择入住时间"
                                value-format="yyyy-MM-dd">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="房间号" prop="roomNumber">
                            <el-input v-model="patientForm.roomNumber" placeholder="请输入房间号"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="护理等级" prop="careLevel">
                            <el-select v-model="patientForm.careLevel" placeholder="请选择护理等级">
                                <el-option label="自理" value="1"></el-option>
                                <el-option label="半自理" value="2"></el-option>
                                <el-option label="全护理" value="3"></el-option>
                                <el-option label="特护" value="4"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="健康状态" prop="healthStatus">
                            <el-select v-model="patientForm.healthStatus" placeholder="请选择健康状态">
                                <el-option label="良好" value="1"></el-option>
                                <el-option label="一般" value="2"></el-option>
                                <el-option label="较差" value="3"></el-option>
                                <el-option label="危重" value="4"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            
            <el-card class="form-card">
                <div slot="header">
                    <span>联系信息</span>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="紧急联系人" prop="emergencyContact">
                            <el-input v-model="patientForm.emergencyContact" placeholder="请输入紧急联系人"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话" prop="emergencyPhone">
                            <el-input v-model="patientForm.emergencyPhone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item label="家庭住址">
                            <el-input v-model="patientForm.address" type="textarea" :rows="2" placeholder="请输入家庭住址"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-card>
            
            <el-card class="form-card">
                <div slot="header">
                    <span>备注信息</span>
                </div>
                
                <el-form-item label="既往病史">
                    <el-input v-model="patientForm.medicalHistory" type="textarea" :rows="3" placeholder="请输入既往病史"></el-input>
                </el-form-item>
                
                <el-form-item label="用药情况">
                    <el-input v-model="patientForm.medications" type="textarea" :rows="3" placeholder="请输入用药情况"></el-input>
                </el-form-item>
                
                <el-form-item label="备注">
                    <el-input v-model="patientForm.remark" type="textarea" :rows="2" placeholder="其他需要说明的信息"></el-input>
                </el-form-item>
            </el-card>
            
            <div class="form-actions">
                <el-button @click="goBack">取消</el-button>
                <el-button type="primary" @click="savePatient">保存</el-button>
            </div>
        </el-form>
    </div>
</template>

<script>
import axios from 'axios';

export default {
    name: 'PatientAdd',
    data() {
        return {
            patientForm: {
                name: '',
                sex: '1',
                age: null,
                phone: '',
                idCard: '',
                admissionDate: '',
                roomNumber: '',
                careLevel: '',
                healthStatus: '',
                emergencyContact: '',
                emergencyPhone: '',
                address: '',
                medicalHistory: '',
                medications: '',
                remark: ''
            },
            formRules: {
                name: [{ required: true, message: '请输入患者姓名', trigger: 'blur' }],
                sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
                age: [{ required: true, message: '请输入年龄', trigger: 'blur' }],
                phone: [
                    { required: true, message: '请输入手机号', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                ],
                idCard: [
                    { required: true, message: '请输入身份证号', trigger: 'blur' },
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
                ],
                admissionDate: [{ required: true, message: '请选择入住时间', trigger: 'change' }],
                roomNumber: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
                careLevel: [{ required: true, message: '请选择护理等级', trigger: 'change' }],
                healthStatus: [{ required: true, message: '请选择健康状态', trigger: 'change' }],
                emergencyContact: [{ required: true, message: '请输入紧急联系人', trigger: 'blur' }],
                emergencyPhone: [
                    { required: true, message: '请输入联系电话', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
                ]
            }
        }
    },
    
    methods: {
        async savePatient() {
            this.$refs.patientForm.validate(async (valid) => {
                if (valid) {
                    try {
                        // 构造患者信息数据
                        const patientData = {
                            patientName: this.patientForm.name,
                            gender: this.patientForm.sex,
                            phone: this.patientForm.phone,
                            idCard: this.patientForm.idCard,
                            admissionDate: this.patientForm.admissionDate,
                            bedNumber: this.patientForm.roomNumber,
                            careLevel: this.patientForm.careLevel,
                            healthStatus: this.patientForm.healthStatus,
                            emergencyContact: this.patientForm.emergencyContact,
                            emergencyPhone: this.patientForm.emergencyPhone,
                            address: this.patientForm.address,
                            medicalHistory: this.patientForm.medicalHistory,
                            medications: this.patientForm.medications,
                            remark: this.patientForm.remark,
                            nursingHomeCode: sessionStorage.getItem("nursingHomeCode") || 'NH001' // 默认机构代码
                        };
                        
                        const response = await axios.post('/api/emr/nhpatient', patientData);
                        
                        if (response.data.code === 200) {
                            this.$message.success('患者信息保存成功');
                            this.goBack();
                        } else {
                            this.$message.error(response.data.message || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存患者信息失败:', error);
                        this.$message.error('保存失败，请稍后重试');
                    }
                }
            });
        },
        
        goBack() {
            this.$router.push({
                path: '/nursingHomePortal/patientList'
            });
        }
    }
}
</script>

<style lang="scss" scoped>
@import '~css/public.scss';

.patient-add {
    padding: 20px;
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
            margin: 0;
            color: #333;
        }
    }
    
    .form-card {
        margin-bottom: 20px;
    }
    
    .form-actions {
        text-align: center;
        margin-top: 30px;
        
        .el-button {
            margin: 0 10px;
            min-width: 100px;
        }
    }
}
</style> 
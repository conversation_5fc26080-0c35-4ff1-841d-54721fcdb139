<template>
    <div class="referral-detail">
        <div class="page-header">
            <h3>转介信详情</h3>
            <el-button @click="goBack">返回</el-button>
        </div>
        
                <div v-loading="loading">
            <!-- 状态和基本信息卡片 -->
            <el-card class="detail-card">
                <div slot="header">
                    <span>转介信概要</span>
                    <el-tag :type="getStatusTag(referralInfo.status)" style="float: right;">
                        {{ getStatusText(referralInfo.status) }}
                    </el-tag>
                </div>
                
                <el-row :gutter="20">
                    <el-col :span="8">
                        <div class="info-item">
                            <label>转介单号：</label>
                            <span>{{ referralInfo.referralNo }}</span>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="info-item">
                            <label>申请时间：</label>
                            <span>{{ referralInfo.createTime }}</span>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div class="info-item">
                            <label>申请人：</label>
                            <span>{{ referralInfo.applicant || '系统用户' }}</span>
                        </div>
                    </el-col>
                </el-row>
            </el-card>

            <!-- 完整转介信内容 -->
            <el-card class="detail-card referral-letter-content">
                <div slot="header">
                    <span>出院延续性服务转介信</span>
                </div>
                
                <!-- 患者基本信息 -->
                <div class="section">
                    <h3 class="section-title">患者基本信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>姓名：</label>
                            <span>{{ referralInfo.patientName }}</span>
                        </div>
                        <div class="info-item">
                            <label>性别：</label>
                            <span>☑{{ referralInfo.gender === '男' ? '男' : '' }} ☐{{ referralInfo.gender === '女' ? '女' : '' }}</span>
                        </div>
                        <div class="info-item">
                            <label>出生日期：</label>
                            <span>{{ referralInfo.birthDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>身份证后4位：</label>
                            <span>{{ referralInfo.idCardLast4 }}</span>
                        </div>
                        <div class="info-item">
                            <label>入院日期：</label>
                            <span>{{ referralInfo.admissionDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>出院日期：</label>
                            <span>{{ referralInfo.dischargeDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>入院科室：</label>
                            <span>{{ referralInfo.admissionDepartment }}</span>
                        </div>
                        <div class="info-item">
                            <label>出院科室：</label>
                            <span>{{ referralInfo.dischargeDepartment }}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>入院主诉：</label>
                            <span>{{ referralInfo.admissionChiefComplaint }}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>主要诊断：</label>
                            <span>{{ referralInfo.primaryDiagnosis }}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>其他诊断：</label>
                            <span>{{ referralInfo.otherDiagnosis }}</span>
                        </div>
                    </div>
                </div>

                <!-- 患者去向 -->
                <div class="section">
                    <h3 class="section-title">患者去向</h3>
                    <div class="checkbox-group">
                        <el-checkbox v-model="referralInfo.destinations.rehabilitation" disabled>康复护理床位</el-checkbox>
                        <el-checkbox v-model="referralInfo.destinations.palliative" disabled>安宁疗护床位</el-checkbox>
                        <el-checkbox v-model="referralInfo.destinations.daycare" disabled>日间照料托位</el-checkbox>
                        <el-checkbox v-model="referralInfo.destinations.nursing" disabled>养老机构床位</el-checkbox>
                        <el-checkbox v-model="referralInfo.destinations.homecare" disabled>家庭照护床位</el-checkbox>
                        <el-checkbox v-model="referralInfo.destinations.longcare" disabled>长护险服务</el-checkbox>
                        <el-checkbox v-model="referralInfo.destinations.homebed" disabled>家庭病床</el-checkbox>
                    </div>
                    <div class="contact-info">
                        <div class="info-item">
                            <label>机构名称：</label>
                            <span>{{ referralInfo.institutionName }}</span>
                        </div>
                        <div class="info-item">
                            <label>机构联系人：</label>
                            <span>{{ referralInfo.institutionContact }}</span>
                        </div>
                        <div class="info-item">
                            <label>联系电话：</label>
                            <span>{{ referralInfo.institutionPhone }}</span>
                        </div>
                    </div>
                </div>

                <!-- 居家照护情况 -->
                <div class="section">
                    <h3 class="section-title">居家照护情况</h3>
                    <div class="checkbox-group">
                        <el-checkbox v-model="referralInfo.homeCare.spouse" disabled>爱人/父母/子女</el-checkbox>
                        <el-checkbox v-model="referralInfo.homeCare.housekeeper" disabled>住家保姆</el-checkbox>
                        <el-checkbox v-model="referralInfo.homeCare.hourly" disabled>钟点工人</el-checkbox>
                        <el-checkbox v-model="referralInfo.homeCare.caregiver" disabled>护工照护</el-checkbox>
                        <el-checkbox v-model="referralInfo.homeCare.other" disabled>其他：{{ referralInfo.homeCare.otherDesc }}</el-checkbox>
                    </div>
                    <div class="contact-info">
                        <div class="info-item">
                            <label>本人联系方式：</label>
                            <span>{{ patientInfo.phone }}</span>
                        </div>
                        <div class="info-item">
                            <label>紧急联系人：</label>
                            <span>{{ patientInfo.emergencyContact }}</span>
                        </div>
                        <div class="info-item">
                            <label>紧急联系电话：</label>
                            <span>{{ patientInfo.emergencyPhone }}</span>
                        </div>
                    </div>
                </div>

                <!-- 院内评估结果 -->
                <div class="section">
                    <h3 class="section-title">院内评估结果</h3>
                    <div class="assessment-items">
                        <div class="info-item">
                            <label>ADL（Barthel指数）评分：</label>
                            <span>{{ referralInfo.adlScore }}分（详见附表）</span>
                        </div>
                        <div class="info-item">
                            <label>吞咽功能评估：</label>
                            <span>☑{{ referralInfo.swallowingNormal ? '正常' : '' }} ☐{{ referralInfo.swallowingAbnormal ? '异常' + referralInfo.swallowingAbnormalDesc : '' }}</span>
                        </div>
                        <div class="info-item">
                            <label>压疮风险：</label>
                            <span>☐低风险 ☑中风险 ☐高风险 {{ referralInfo.pressureRisk }}</span>
                        </div>
                        <div class="info-item">
                            <label>跌倒风险：</label>
                            <span>☐低风险 ☐中风险 ☑高风险 {{ referralInfo.fallRisk }}</span>
                        </div>
                        <div class="info-item">
                            <label>DVT风险：</label>
                            <span>☑低风险 ☐中风险 ☐高风险 {{ referralInfo.dvtRisk }}</span>
                        </div>
                        <div class="info-item">
                            <label>认知：</label>
                            <span>☐无 ☑轻度 ☐中度 ☐重度 {{ referralInfo.cognitionDesc }}</span>
                        </div>
                    </div>
                </div>

                <!-- 复诊安排 -->
                <div class="section">
                    <h3 class="section-title">复诊安排</h3>
                    <div class="followup-section">
                        <div class="followup-item">
                            <h4>已预约复诊：</h4>
                            <div class="info-item">
                                <label>时间：</label>
                                <span>{{ referralInfo.scheduledFollowUp.date }}</span>
                            </div>
                            <div class="info-item">
                                <label>科室：</label>
                                <span>{{ referralInfo.scheduledFollowUp.department }}</span>
                            </div>
                            <div class="info-item">
                                <label>预约号：</label>
                                <span>{{ referralInfo.scheduledFollowUp.appointmentNo }}</span>
                            </div>
                            <div class="info-item">
                                <label>接诊医生：</label>
                                <span>{{ referralInfo.scheduledFollowUp.doctor }}</span>
                            </div>
                        </div>
                        <div class="followup-item">
                            <h4>待预约复诊：</h4>
                            <div class="info-item">
                                <label>时间：</label>
                                <span>{{ referralInfo.pendingFollowUp.frequency }}</span>
                            </div>
                            <div class="info-item">
                                <label>已预约复诊：</label>
                                <span>{{ referralInfo.pendingFollowUp.date }}</span>
                            </div>
                            <div class="info-item">
                                <label>科室：</label>
                                <span>{{ referralInfo.pendingFollowUp.department }}</span>
                            </div>
                            <div class="info-item">
                                <label>预约号：</label>
                                <span>{{ referralInfo.pendingFollowUp.appointmentNo }}</span>
                            </div>
                            <div class="info-item">
                                <label>接诊医生：</label>
                                <span>{{ referralInfo.pendingFollowUp.doctor }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 随访要求 -->
                <div class="section">
                    <h3 class="section-title">随访要求</h3>
                    <div class="followup-requirements">
                        <div class="requirement-item">
                            <label>出院后后续随访要求：</label>
                            <span>{{ referralInfo.followUpRequirements.postDischarge }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>随访关键指标：</label>
                            <span>{{ referralInfo.followUpRequirements.keyIndicators }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>康复训练：</label>
                            <span>{{ referralInfo.followUpRequirements.rehabilitation }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>日常随访中向专科门急诊转诊指征：</label>
                            <span>{{ referralInfo.followUpRequirements.referralCriteria }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>紧急情况处理建议：</label>
                            <span>{{ referralInfo.followUpRequirements.emergencyHandling }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>长期用药管理：</label>
                            <span>{{ referralInfo.followUpRequirements.medicationManagement }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>居家期间可能存在的风险和举措：</label>
                            <span>{{ referralInfo.followUpRequirements.homeRisks }}</span>
                        </div>
                    </div>
                </div>

                <!-- 照护需求清单和执行 -->
                <div class="section">
                    <h3 class="section-title">照护需求清单和执行</h3>
                    <table class="care-needs-table">
                        <thead>
                            <tr>
                                <th>类型</th>
                                <th>需求项</th>
                                <th>频率/要求</th>
                                <th>执行方</th>
                                <th>指导</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(need, index) in referralInfo.careNeeds" :key="index">
                                <td>{{ need.type }}</td>
                                <td>{{ need.item }}</td>
                                <td>{{ need.frequency }}</td>
                                <td>{{ need.executor }}</td>
                                <td>{{ need.guidance }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 服务资源对接 -->
                <div class="section">
                    <h3 class="section-title">服务资源对接</h3>
                    <div class="service-resources">
                        <div class="resource-item">
                            <label>{{ referralInfo.serviceResources.hospitalService.name }}：</label>
                            <span>{{ referralInfo.serviceResources.hospitalService.contact }}（电话：{{ referralInfo.serviceResources.hospitalService.phone }}）</span>
                        </div>
                        <div class="resource-item">
                            <label>{{ referralInfo.serviceResources.communityService.name }}：</label>
                            <span>{{ referralInfo.serviceResources.communityService.contact }}（电话：{{ referralInfo.serviceResources.communityService.phone }}）</span>
                        </div>
                        <div class="resource-item">
                            <label>{{ referralInfo.serviceResources.healthCenter.name }}：</label>
                            <span>{{ referralInfo.serviceResources.healthCenter.contact }}（电话：{{ referralInfo.serviceResources.healthCenter.phone }}）</span>
                        </div>
                        <div class="resource-item">
                            <label>{{ referralInfo.serviceResources.specialistFollowUp.name }}：</label>
                            <span>{{ referralInfo.serviceResources.specialistFollowUp.contact }}（电话：{{ referralInfo.serviceResources.specialistFollowUp.phone }}）</span>
                        </div>
                    </div>
                </div>

                <!-- 责任签收与反馈 -->
                <div class="section">
                    <h3 class="section-title">责任签收与反馈</h3>
                    <div class="responsibility-section">
                        <div class="signature-item">
                            <label>社区医护签收（推送起72小时）：</label>
                            <span>_________ 日期：_________</span>
                        </div>
                        <div class="signature-item">
                            <label>街道社工签收（推送起72小时）：</label>
                            <span>_________ 日期：_________</span>
                        </div>
                        <div class="signature-item">
                            <label>医院联系人：</label>
                            <span>{{ referralInfo.responsibility.hospitalContact }}（电话：{{ referralInfo.responsibility.hospitalPhone }} | 平台ID：{{ referralInfo.responsibility.platformId }}）</span>
                        </div>
                    </div>
                </div>

                <!-- 附件和签章 -->
                <div class="section">
                    <div class="appendix-section">
                        <div class="appendix-item">
                            <strong>附：</strong>
                            <div>自理能力评估（Barthel指数）：详见附表。</div>
                        </div>
                        <div class="hospital-signature">
                            <div class="hospital-name">{{ referralInfo.hospitalName }}</div>
                            <div class="create-date">日期：{{ referralInfo.createTime.split(' ')[0] }}</div>
                        </div>
                    </div>
                </div>
            </el-card>
            
            <!-- 处理记录 -->
            <el-card class="detail-card" v-if="referralInfo.processLogs && referralInfo.processLogs.length > 0">
                <div slot="header">
                    <span>处理记录</span>
                </div>
                
                <el-timeline>
                    <el-timeline-item
                        v-for="log in referralInfo.processLogs"
                        :key="log.id"
                        :timestamp="log.processTime"
                        :type="getTimelineType(log.action)">
                        <div class="timeline-content">
                            <h4>{{ getActionText(log.action) }}</h4>
                            <p>处理人：{{ log.processor }}</p>
                            <p v-if="log.remark">备注：{{ log.remark }}</p>
                        </div>
                    </el-timeline-item>
                </el-timeline>
            </el-card>

            <div class="action-buttons" v-if="referralInfo.status == '1'">
                <el-button type="warning" @click="editReferral">编辑转介信</el-button>
                <el-button type="danger" @click="cancelReferral">取消转介</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import {commonAjax} from '../../../api/api.js';

export default {
    name: 'ReferralDetail',
    data() {
        return {
            loading: false,
            referralId: null,
            referralInfo: {},
            patientInfo: {}
        }
    },
    
    mounted() {
        this.referralId = this.$route.query.id;
        if (this.referralId) {
            this.loadReferralDetail();
        } else {
            this.$message.error('缺少转介信ID参数');
            this.goBack();
        }
    },
    
    methods: {
        loadReferralDetail() {
            this.loading = true;
            commonAjax("cas.nursingHome_referralService", "getReferralDetail", [{ id: this.referralId }]).then(res => {
                if(res.code == 200) {
                    this.referralInfo = res.body.referralInfo;
                    this.patientInfo = res.body.patientInfo;
                } else {
                    this.$message.error(res.msg);
                }
            }).catch(err => {
                // 模拟数据 - 按照新格式生成
                this.referralInfo = {
                    id: 1,
                    referralNo: "ZJ202312001",
                    status: "1",
                    createTime: "2024-06-01 10:30:00",
                    applicant: "护士长王某",
                    // 患者基本信息
                    patientName: "张老先生",
                    gender: "男",
                    birthDate: "1946年06月15日",
                    idCardLast4: "1234",
                    admissionDate: "2024年01月15日",
                    dischargeDate: "2024年06月01日",
                    admissionDepartment: "心血管科",
                    dischargeDepartment: "心血管科",
                    admissionChiefComplaint: "因胸闷气短入院",
                    primaryDiagnosis: "高血压病",
                    otherDiagnosis: "详见出院小结",
                    
                    // 患者去向
                    destinations: {
                        rehabilitation: false,
                        palliative: false,
                        daycare: false,
                        nursing: true,
                        homecare: false,
                        longcare: false,
                        homebed: false
                    },
                    
                    // 机构信息
                    institutionName: "上海XX养老机构",
                    institutionContact: "张护士长",
                    institutionPhone: "021-12345678",
                    
                    // 居家照护情况
                    homeCare: {
                        spouse: true,
                        housekeeper: false,
                        hourly: false,
                        caregiver: false,
                        other: false,
                        otherDesc: ''
                    },
                    
                    // 院内评估结果
                    adlScore: 75,
                    swallowingNormal: true,
                    swallowingAbnormal: false,
                    swallowingAbnormalDesc: '',
                    pressureRisk: "中风险",
                    fallRisk: "高风险",
                    dvtRisk: "低风险",
                    cognition: "轻度",
                    cognitionDesc: "轻度认知障碍",
                    
                    // 复诊安排
                    scheduledFollowUp: {
                        date: "2024年06月15日",
                        department: "心血管科",
                        appointmentNo: "A001234",
                        doctor: "李主任医师"
                    },
                    pendingFollowUp: {
                        frequency: "每周三",
                        date: "2024年06月22日",
                        department: "心血管科",
                        appointmentNo: "待定",
                        doctor: "待定"
                    },
                    
                    // 随访要求
                    followUpRequirements: {
                        postDischarge: "出院后两周门诊取病理报告",
                        keyIndicators: "血压、血糖、体重管理",
                        rehabilitation: "术后肌力训练",
                        referralCriteria: "运动障碍加重或不自主运动频繁",
                        emergencyHandling: "髋关节术后如跌倒，如伴明显疼痛立即就诊",
                        medicationManagement: "观察药物副作用，随访肝功能和血常规，胰岛素需家属监督注射，警惕低血糖反应",
                        homeRisks: "跌倒高风险：禁止独自行走，如厕需陪护"
                    },
                    
                    // 照护需求清单
                    careNeeds: [
                        {
                            type: "医疗",
                            item: "伤口护理",
                            frequency: "每日1次，持续7天",
                            executor: "社区护士",
                            guidance: "家庭医生"
                        },
                        {
                            type: "医疗",
                            item: "心脏康复",
                            frequency: "每周5次互联网医院，每周2次心内科心脏康复门诊",
                            executor: "心内科康复医生和治疗师",
                            guidance: "上海四院医生"
                        },
                        {
                            type: "医疗",
                            item: "血糖监测",
                            frequency: "空腹+三餐后，每周记录",
                            executor: "家属/社区医生",
                            guidance: "家庭医生"
                        },
                        {
                            type: "生活",
                            item: "助浴服务",
                            frequency: "每周2次",
                            executor: "养老机构",
                            guidance: "养老机构"
                        },
                        {
                            type: "生活",
                            item: "营养配餐",
                            frequency: "低盐糖尿病餐",
                            executor: "社区助餐中心",
                            guidance: "社工"
                        }
                    ],
                    
                    // 服务资源对接
                    serviceResources: {
                        hospitalService: {
                            name: "四院转介服务",
                            contact: "慢病管理中心全科医生：陈医生",
                            phone: "021-12345678"
                        },
                        communityService: {
                            name: "社区服务",
                            contact: "凉城新村街道社工：郑女士",
                            phone: "021-65284030"
                        },
                        healthCenter: {
                            name: "社区卫生服务中心",
                            contact: "家庭医生：李医生",
                            phone: "021-12333371111"
                        },
                        specialistFollowUp: {
                            name: "四院专科随访",
                            contact: "健康管理师：魏护士",
                            phone: "021-55603198"
                        }
                    },
                    
                    // 责任签收
                    responsibility: {
                        hospitalContact: "陈医生",
                        hospitalPhone: "021-12345678",
                        platformId: "HD001"
                    },
                    
                    // 其他信息
                    hospitalName: "上海市第四人民医院慢病管理中心",
                    
                    processLogs: [
                        {
                            id: 1,
                            action: "create",
                            processor: "护士长王某",
                            processTime: "2024-06-01 10:30:00",
                            remark: "创建转介申请"
                        }
                    ]
                };
                
                this.patientInfo = {
                    sex: "1",
                    age: 78,
                    phone: "13800138001",
                    roomNumber: "A101",
                    careLevel: "2",
                    healthStatus: "3",
                    emergencyContact: "张小明",
                    emergencyPhone: "13900139001"
                };
            }).finally(() => {
                this.loading = false;
            });
        },
        
        editReferral() {
            this.$router.push({
                path: '/nursingHomePortal/patientReferral',
                query: { edit: this.referralId }
            });
        },
        
        cancelReferral() {
            this.$confirm('确认取消此转介信吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                commonAjax("cas.nursingHome_referralService", "cancelReferral", [{ id: this.referralId }]).then(res => {
                    if(res.code == 200) {
                        this.$message.success('转介信取消成功');
                        this.goBack();
                    } else {
                        this.$message.error(res.msg);
                    }
                }).catch(err => {
                    this.$message.success('转介信取消成功（模拟）');
                    this.goBack();
                });
            });
        },
        
        goBack() {
            this.$router.push({
                path: '/nursingHomePortal/patientReferral'
            });
        },
        
        getStatusText(status) {
            const map = {
                '1': '待审核',
                '2': '已通过',
                '3': '已拒绝',
                '4': '已完成'
            };
            return map[status] || '';
        },
        
        getStatusTag(status) {
            const map = {
                '1': '',
                '2': 'success',
                '3': 'danger',
                '4': 'info'
            };
            return map[status] || '';
        },
        
        getReferralTypeText(type) {
            const map = {
                '1': '转医院',
                '2': '转机构',
                '3': '回家护理'
            };
            return map[type] || '';
        },
        
        getReferralTypeTag(type) {
            const map = {
                '1': 'danger',
                '2': 'warning',
                '3': 'success'
            };
            return map[type] || '';
        },
        
        getUrgencyText(urgency) {
            const map = {
                '1': '普通',
                '2': '紧急',
                '3': '特急'
            };
            return map[urgency] || '';
        },
        
        getUrgencyTag(urgency) {
            const map = {
                '1': '',
                '2': 'warning',
                '3': 'danger'
            };
            return map[urgency] || '';
        },
        
        getActionText(action) {
            const map = {
                'create': '创建转介申请',
                'approve': '审核通过',
                'reject': '审核拒绝',
                'complete': '转介完成',
                'cancel': '取消转介'
            };
            return map[action] || action;
        },
        
        getTimelineType(action) {
            const map = {
                'create': 'primary',
                'approve': 'success',
                'reject': 'danger',
                'complete': 'info',
                'cancel': 'warning'
            };
            return map[action] || 'primary';
        },
        
        getCareText(careLevel) {
            const map = {
                '1': '自理',
                '2': '半自理',
                '3': '全护理',
                '4': '特护'
            };
            return map[careLevel] || '';
        },
        
        getCareTagType(careLevel) {
            const map = {
                '1': 'success',
                '2': '',
                '3': 'warning',
                '4': 'danger'
            };
            return map[careLevel] || '';
        },
        
        getHealthText(healthStatus) {
            const map = {
                '1': '良好',
                '2': '一般',
                '3': '较差',
                '4': '危重'
            };
            return map[healthStatus] || '';
        },
        
        getHealthTagType(healthStatus) {
            const map = {
                '1': 'success',
                '2': '',
                '3': 'warning',
                '4': 'danger'
            };
            return map[healthStatus] || '';
        }
    }
}
</script>

<style lang="scss" scoped>
@import '~css/public.scss';

.referral-detail {
    padding: 20px;
    
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
            margin: 0;
            color: #333;
        }
    }
    
    .detail-card {
        margin-bottom: 20px;
        
        .info-item {
            margin-bottom: 15px;
            
            label {
                font-weight: bold;
                color: #606266;
                margin-right: 8px;
            }
        }
        
        .info-section {
            margin-bottom: 20px;
            
            h4 {
                color: #333;
                margin-bottom: 10px;
                font-size: 16px;
            }
            
            .content-text {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 4px;
                line-height: 1.6;
                color: #333;
                margin: 0;
            }
        }
        
        .timeline-content {
            h4 {
                margin: 0 0 8px 0;
                color: #333;
                font-size: 16px;
            }
            
            p {
                margin: 4px 0;
                color: #666;
                font-size: 14px;
            }
        }
    }
    
    .action-buttons {
        text-align: center;
        margin-top: 30px;
        
        .el-button {
            margin: 0 10px;
            min-width: 120px;
        }
    }
}

// 转介信内容样式 - 复用患者列表页面的样式
.referral-letter-content {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    
    .section {
        margin-bottom: 30px;
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409EFF;
        }
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
        
        .info-item {
            display: flex;
            align-items: center;
            
            &.full-width {
                grid-column: 1 / -1;
            }
            
            label {
                font-weight: bold;
                color: #333;
                margin-right: 8px;
                white-space: nowrap;
            }
            
            span {
                color: #666;
            }
        }
    }
    
    .content-block {
        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: flex-start;
            
            label {
                font-weight: bold;
                color: #333;
                margin-right: 8px;
                white-space: nowrap;
                min-width: 100px;
            }
            
            span, div {
                color: #666;
                flex: 1;
            }
        }
        
        .patient-destination,
        .home-care-situation,
        .long-term-needs {
            margin: 15px 0;
            
            label {
                font-weight: bold;
                color: #333;
                display: block;
                margin-bottom: 8px;
            }
            
            .checkbox-group {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                
                .el-checkbox {
                    margin-right: 0;
                }
            }
        }
        
        .contact-info {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            
            .info-item {
                margin-bottom: 5px;
            }
        }
        
        .assessment-date {
            font-style: italic;
            color: #666;
            margin-bottom: 15px;
        }
        
        .assessment-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .main-risks {
            padding: 15px;
            background-color: #fff2e8;
            border-left: 4px solid #fa8c16;
            margin: 15px 0;
            
            label {
                font-weight: bold;
                color: #fa8c16;
                display: block;
                margin-bottom: 8px;
            }
            
            div {
                color: #333;
            }
        }
        
        .recommendations {
            .info-item {
                margin-bottom: 20px;
                
                label {
                    display: block;
                    margin-bottom: 8px;
                }
                
                div {
                    background-color: #f8f9fa;
                    padding: 10px;
                    border-radius: 4px;
                    border-left: 3px solid #409EFF;
                }
            }
        }
        
        .follow-up-info,
        .service-resources {
            margin: 20px 0;
            
            label {
                font-weight: bold;
                color: #333;
                display: block;
                margin-bottom: 8px;
            }
            
            div {
                background-color: #f0f9ff;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #b3d8ff;
                white-space: pre-line;
            }
        }
        
        .date-signature {
            text-align: right;
            margin-top: 30px;
            
            .create-date {
                font-size: 16px;
                font-weight: bold;
                color: #333;
            }
        }
    }
    
    // 新增样式
    .followup-section {
        .followup-item {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #409EFF;
            
            h4 {
                margin: 0 0 10px 0;
                color: #409EFF;
                font-weight: bold;
            }
            
            .info-item {
                margin-bottom: 5px;
                
                label {
                    font-weight: bold;
                    color: #333;
                    margin-right: 8px;
                    min-width: 80px;
                    display: inline-block;
                }
                
                span {
                    color: #666;
                }
            }
        }
    }
    
    .followup-requirements {
        .requirement-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #52c41a;
            
            label {
                font-weight: bold;
                color: #333;
                display: block;
                margin-bottom: 5px;
            }
            
            span {
                color: #666;
                line-height: 1.6;
            }
        }
    }
    
    .care-needs-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        border: 1px solid #ddd;
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f5f7fa;
            font-weight: bold;
            color: #606266;
        }
        
        tbody tr:nth-child(even) {
            background-color: #fafafa;
        }
        
        tbody tr:hover {
            background-color: #f0f9ff;
        }
    }
    
    .service-resources {
        .resource-item {
            margin-bottom: 12px;
            padding: 10px;
            background-color: #f0f9ff;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
            
            label {
                font-weight: bold;
                color: #1890ff;
                margin-right: 8px;
            }
            
            span {
                color: #666;
            }
        }
    }
    
    .responsibility-section {
        .signature-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #fff7e6;
            border-radius: 4px;
            border-left: 3px solid #fa8c16;
            
            label {
                font-weight: bold;
                color: #fa8c16;
                margin-right: 8px;
            }
            
            span {
                color: #666;
            }
        }
    }
    
    .appendix-section {
        .appendix-item {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f6ffed;
            border-radius: 4px;
            border-left: 3px solid #52c41a;
            
            strong {
                color: #52c41a;
                font-weight: bold;
            }
            
            div {
                margin-top: 5px;
                color: #666;
            }
        }
        
        .hospital-signature {
            text-align: right;
            margin-top: 30px;
            
            .hospital-name {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
            
            .create-date {
                font-size: 14px;
                color: #666;
            }
        }
    }
}
</style> 
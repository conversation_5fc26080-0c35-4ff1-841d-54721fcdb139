<template>
    <div class="nursing-home-patient-list">
        <div class="list-main-head">
            <div class="fieldName">
                <el-input size="small" v-model="params.name" clearable class="shopValue" placeholder="患者姓名"></el-input>
            </div>
            <div class="fieldName">
                <el-input size="small" v-model="params.phone" clearable class="shopValue" placeholder="患者手机号"></el-input>
            </div>
            <div class="fieldName">
                <el-input size="small" v-model="params.idCard" clearable class="shopValue" placeholder="身份证号"></el-input>
            </div>
            <div class="fieldName">
                <el-select size="small" v-model="params.healthStatus" clearable placeholder="健康状态">
                    <el-option label="良好" value="1"></el-option>
                    <el-option label="一般" value="2"></el-option>
                    <el-option label="较差" value="3"></el-option>
                    <el-option label="危重" value="4"></el-option>
                </el-select>
            </div>
            <div class="fieldName">
                <el-select size="small" v-model="params.careLevel" clearable placeholder="护理等级">
                    <el-option label="自理" value="1"></el-option>
                    <el-option label="半自理" value="2"></el-option>
                    <el-option label="全护理" value="3"></el-option>
                    <el-option label="特护" value="4"></el-option>
                </el-select>
            </div>
            <div class="fieldName">
                <el-select size="small" v-model="params.nursingHomeCode" clearable placeholder="所属机构" style="width: 180px;">
                    <el-option 
                        v-for="institution in activeInstitutions" 
                        :key="institution.code" 
                        :label="institution.name" 
                        :value="institution.code">
                    </el-option>
                </el-select>
            </div>
            <div class="fieldName handleWrap">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
                <el-button type="success" size="small" @click="addPatient">新增患者</el-button>
            </div>
        </div>
        
        <el-table :data="patList" stripe style="width: 100%">
            <el-table-column label="患者姓名" prop="name" width="100">
            </el-table-column>
            <el-table-column label="性别" prop="sex" width="60">
                <template slot-scope="scope">
                    <span v-if="scope.row.sex == '1'">男</span>
                    <span v-if="scope.row.sex == '2'">女</span>
                </template>
            </el-table-column>
            <el-table-column label="年龄" prop="age" width="60">
            </el-table-column>
            <el-table-column label="手机号" prop="phone" width="120">
            </el-table-column>
            <el-table-column label="身份证号" prop="idCard" width="180">
                <template slot-scope="scope">
                    <span>{{ scope.row.idCard ? scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') : '' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="入住时间" prop="admissionDate" width="100">
            </el-table-column>
            <el-table-column label="房间号" prop="roomNumber" width="80">
            </el-table-column>
            <el-table-column label="护理等级" prop="careLevel" width="80">
                <template slot-scope="scope">
                    <el-tag :type="getCareTagType(scope.row.careLevel)" size="mini">
                        {{ getCareText(scope.row.careLevel) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="健康状态" prop="healthStatus" width="80">
                <template slot-scope="scope">
                    <el-tag :type="getHealthTagType(scope.row.healthStatus)" size="mini">
                        {{ getHealthText(scope.row.healthStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="所属机构" prop="nursingHomeName" width="150">
                <template slot-scope="scope">
                    <span>{{ scope.row.nursingHomeName || '未指定' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="紧急联系人" prop="emergencyContact" width="100">
            </el-table-column>
            <el-table-column label="联系电话" prop="emergencyPhone" min-width="120">
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <div class="operation-buttons">
                        <el-button type="success" size="mini" @click="viewReferralLetter(scope.row)">查看转介信</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        
        <!--分页-->
        <div class="pagination">
            <el-pagination 
                layout="total, sizes, prev, pager, next, jumper" 
                :total="total" 
                :current-page="params.pageNo" 
                :page-sizes="[10, 20, 50]" 
                @size-change="handleSizeChange" 
                @current-change="handleCurrentChange" 
                :page-size="params.pageSize">
            </el-pagination>
        </div>

        <!-- 转介信详情弹窗 -->
        <el-dialog
            title="出院延续性服务转介信"
            :visible.sync="referralDialogVisible"
            width="90%"
            :before-close="handleReferralDialogClose">
            <div class="referral-letter-content" v-if="currentReferralData">
                <!-- 患者基本信息 -->
                <div class="section">
                    <h3 class="section-title">患者基本信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>姓名：</label>
                            <span>{{ currentReferralData.patientName }}</span>
                        </div>
                        <div class="info-item">
                            <label>性别：</label>
                            <span>☑{{ currentReferralData.gender === '男' ? '男' : '' }} ☐{{ currentReferralData.gender === '女' ? '女' : '' }}</span>
                        </div>
                        <div class="info-item">
                            <label>出生日期：</label>
                            <span>{{ currentReferralData.birthDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>身份证后4位：</label>
                            <span>{{ currentReferralData.idCardLast4 }}</span>
                        </div>
                        <div class="info-item">
                            <label>入院日期：</label>
                            <span>{{ currentReferralData.admissionDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>出院日期：</label>
                            <span>{{ currentReferralData.dischargeDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>入院科室：</label>
                            <span>{{ currentReferralData.admissionDepartment }}</span>
                        </div>
                        <div class="info-item">
                            <label>出院科室：</label>
                            <span>{{ currentReferralData.dischargeDepartment }}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>入院主诉：</label>
                            <span>{{ currentReferralData.admissionChiefComplaint }}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>主要诊断：</label>
                            <span>{{ currentReferralData.primaryDiagnosis }}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>其他诊断：</label>
                            <span>{{ currentReferralData.otherDiagnosis }}</span>
                        </div>
                    </div>
                </div>

                <!-- 患者去向 -->
                <div class="section">
                    <h3 class="section-title">患者去向</h3>
                    <div class="checkbox-group">
                        <el-checkbox v-model="currentReferralData.destinations.rehabilitation" disabled>康复护理床位</el-checkbox>
                        <el-checkbox v-model="currentReferralData.destinations.palliative" disabled>安宁疗护床位</el-checkbox>
                        <el-checkbox v-model="currentReferralData.destinations.daycare" disabled>日间照料托位</el-checkbox>
                        <el-checkbox v-model="currentReferralData.destinations.nursing" disabled>养老机构床位</el-checkbox>
                        <el-checkbox v-model="currentReferralData.destinations.homecare" disabled>家庭照护床位</el-checkbox>
                        <el-checkbox v-model="currentReferralData.destinations.longcare" disabled>长护险服务</el-checkbox>
                        <el-checkbox v-model="currentReferralData.destinations.homebed" disabled>家庭病床</el-checkbox>
                    </div>
                    <div class="contact-info">
                        <div class="info-item">
                            <label>机构名称：</label>
                            <span>{{ currentReferralData.institutionName }}</span>
                        </div>
                        <div class="info-item">
                            <label>机构联系人：</label>
                            <span>{{ currentReferralData.institutionContact }}</span>
                        </div>
                        <div class="info-item">
                            <label>联系电话：</label>
                            <span>{{ currentReferralData.institutionPhone }}</span>
                        </div>
                    </div>
                </div>

                <!-- 居家照护情况 -->
                <div class="section">
                    <h3 class="section-title">居家照护情况</h3>
                    <div class="checkbox-group">
                        <el-checkbox v-model="currentReferralData.homeCare.spouse" disabled>爱人/父母/子女</el-checkbox>
                        <el-checkbox v-model="currentReferralData.homeCare.housekeeper" disabled>住家保姆</el-checkbox>
                        <el-checkbox v-model="currentReferralData.homeCare.hourly" disabled>钟点工人</el-checkbox>
                        <el-checkbox v-model="currentReferralData.homeCare.caregiver" disabled>护工照护</el-checkbox>
                        <el-checkbox v-model="currentReferralData.homeCare.other" disabled>其他：{{ currentReferralData.homeCare.otherDesc }}</el-checkbox>
                    </div>
                    <div class="contact-info">
                        <div class="info-item">
                            <label>本人联系方式：</label>
                            <span>{{ currentReferralData.patientPhone }}</span>
                        </div>
                        <div class="info-item">
                            <label>紧急联系人：</label>
                            <span>{{ currentReferralData.emergencyContact }}</span>
                        </div>
                        <div class="info-item">
                            <label>紧急联系电话：</label>
                            <span>{{ currentReferralData.emergencyPhone }}</span>
                        </div>
                    </div>
                </div>

                <!-- 院内评估结果 -->
                <div class="section">
                    <h3 class="section-title">院内评估结果</h3>
                    <div class="assessment-items">
                        <div class="info-item">
                            <label>ADL（Barthel指数）评分：</label>
                            <span>{{ currentReferralData.adlScore }}分（详见附表）</span>
                        </div>
                        <div class="info-item">
                            <label>吞咽功能评估：</label>
                            <span>☑{{ currentReferralData.swallowingNormal ? '正常' : '' }} ☐{{ currentReferralData.swallowingAbnormal ? '异常' + currentReferralData.swallowingAbnormalDesc : '' }}</span>
                        </div>
                        <div class="info-item">
                            <label>压疮风险：</label>
                            <span>☐低风险 ☐中风险 ☑高风险 {{ currentReferralData.pressureRisk }}</span>
                        </div>
                        <div class="info-item">
                            <label>跌倒风险：</label>
                            <span>☐低风险 ☐中风险 ☑高风险 {{ currentReferralData.fallRisk }}</span>
                        </div>
                        <div class="info-item">
                            <label>DVT风险：</label>
                            <span>☐低风险 ☐中风险 ☑高风险 {{ currentReferralData.dvtRisk }}</span>
                        </div>
                        <div class="info-item">
                            <label>认知：</label>
                            <span>☐无 ☐轻度 ☐中度 ☑重度 {{ currentReferralData.cognitionDesc }}</span>
                        </div>
                    </div>
                </div>

                <!-- 复诊安排 -->
                <div class="section">
                    <h3 class="section-title">复诊安排</h3>
                    <div class="followup-section">
                        <div class="followup-item">
                            <h4>已预约复诊：</h4>
                            <div class="info-item">
                                <label>时间：</label>
                                <span>{{ currentReferralData.scheduledFollowUp.date }}</span>
                            </div>
                            <div class="info-item">
                                <label>科室：</label>
                                <span>{{ currentReferralData.scheduledFollowUp.department }}</span>
                            </div>
                            <div class="info-item">
                                <label>预约号：</label>
                                <span>{{ currentReferralData.scheduledFollowUp.appointmentNo }}</span>
                            </div>
                            <div class="info-item">
                                <label>接诊医生：</label>
                                <span>{{ currentReferralData.scheduledFollowUp.doctor }}</span>
                            </div>
                        </div>
                        <div class="followup-item">
                            <h4>待预约复诊：</h4>
                            <div class="info-item">
                                <label>时间：</label>
                                <span>{{ currentReferralData.pendingFollowUp.frequency }}</span>
                            </div>
                            <div class="info-item">
                                <label>已预约复诊：</label>
                                <span>{{ currentReferralData.pendingFollowUp.date }}</span>
                            </div>
                            <div class="info-item">
                                <label>科室：</label>
                                <span>{{ currentReferralData.pendingFollowUp.department }}</span>
                            </div>
                            <div class="info-item">
                                <label>预约号：</label>
                                <span>{{ currentReferralData.pendingFollowUp.appointmentNo }}</span>
                            </div>
                            <div class="info-item">
                                <label>接诊医生：</label>
                                <span>{{ currentReferralData.pendingFollowUp.doctor }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 随访要求 -->
                <div class="section">
                    <h3 class="section-title">随访要求</h3>
                    <div class="followup-requirements">
                        <div class="requirement-item">
                            <label>出院后后续随访要求：</label>
                            <span>{{ currentReferralData.followUpRequirements.postDischarge }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>随访关键指标：</label>
                            <span>{{ currentReferralData.followUpRequirements.keyIndicators }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>康复训练：</label>
                            <span>{{ currentReferralData.followUpRequirements.rehabilitation }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>日常随访中向专科门急诊转诊指征：</label>
                            <span>{{ currentReferralData.followUpRequirements.referralCriteria }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>紧急情况处理建议：</label>
                            <span>{{ currentReferralData.followUpRequirements.emergencyHandling }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>长期用药管理：</label>
                            <span>{{ currentReferralData.followUpRequirements.medicationManagement }}</span>
                        </div>
                        <div class="requirement-item">
                            <label>居家期间可能存在的风险和举措：</label>
                            <span>{{ currentReferralData.followUpRequirements.homeRisks }}</span>
                        </div>
                    </div>
                </div>

                <!-- 照护需求清单和执行 -->
                <div class="section">
                    <h3 class="section-title">照护需求清单和执行</h3>
                    <table class="care-needs-table">
                        <thead>
                            <tr>
                                <th>类型</th>
                                <th>需求项</th>
                                <th>频率/要求</th>
                                <th>执行方</th>
                                <th>指导</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(need, index) in currentReferralData.careNeeds" :key="index">
                                <td>{{ need.type }}</td>
                                <td>{{ need.item }}</td>
                                <td>{{ need.frequency }}</td>
                                <td>{{ need.executor }}</td>
                                <td>{{ need.guidance }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 服务资源对接 -->
                <div class="section">
                    <h3 class="section-title">服务资源对接</h3>
                    <div class="service-resources">
                        <div class="resource-item">
                            <label>{{ currentReferralData.serviceResources.hospitalService.name }}：</label>
                            <span>{{ currentReferralData.serviceResources.hospitalService.contact }}（电话：{{ currentReferralData.serviceResources.hospitalService.phone }}）</span>
                        </div>
                        <div class="resource-item">
                            <label>{{ currentReferralData.serviceResources.communityService.name }}：</label>
                            <span>{{ currentReferralData.serviceResources.communityService.contact }}（电话：{{ currentReferralData.serviceResources.communityService.phone }}）</span>
                        </div>
                        <div class="resource-item">
                            <label>{{ currentReferralData.serviceResources.healthCenter.name }}：</label>
                            <span>{{ currentReferralData.serviceResources.healthCenter.contact }}（电话：{{ currentReferralData.serviceResources.healthCenter.phone }}）</span>
                        </div>
                        <div class="resource-item">
                            <label>{{ currentReferralData.serviceResources.specialistFollowUp.name }}：</label>
                            <span>{{ currentReferralData.serviceResources.specialistFollowUp.contact }}（电话：{{ currentReferralData.serviceResources.specialistFollowUp.phone }}）</span>
                        </div>
                    </div>
                </div>

                <!-- 责任签收与反馈 -->
                <div class="section">
                    <h3 class="section-title">责任签收与反馈</h3>
                    <div class="responsibility-section">
                        <div class="signature-item">
                            <label>社区医护签收（推送起72小时）：</label>
                            <span>_________ 日期：_________</span>
                        </div>
                        <div class="signature-item">
                            <label>街道社工签收（推送起72小时）：</label>
                            <span>_________ 日期：_________</span>
                        </div>
                        <div class="signature-item">
                            <label>医院联系人：</label>
                            <span>{{ currentReferralData.responsibility.hospitalContact }}（电话：{{ currentReferralData.responsibility.hospitalPhone }} | 平台ID：{{ currentReferralData.responsibility.platformId }}）</span>
                        </div>
                    </div>
                </div>

                <!-- 附件和签章 -->
                <div class="section">
                    <div class="appendix-section">
                        <div class="appendix-item">
                            <strong>附：</strong>
                            <div>自理能力评估（Barthel指数）：详见附表。</div>
                        </div>
                        <div class="hospital-signature">
                            <div class="hospital-name">{{ currentReferralData.hospitalName }}</div>
                            <div class="create-date">日期：{{ currentReferralData.createDate }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleReferralDialogClose">关 闭</el-button>
                <el-button type="primary" @click="printReferralLetter">打 印</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios'

// 配置axios默认值
axios.defaults.headers.post['Content-Type'] = 'application/json'

export default {
    name: 'NursingHomePatientList',
    data() {
        return {
            patList: [],
            params: {
                nursingHomeId: sessionStorage.getItem("nursingHomeId"), // 养老机构ID
                name: "",
                phone: "",
                idCard: "",
                healthStatus: "",
                careLevel: "",
                nursingHomeCode: "", // 新增：所属机构代码
                pageNo: 1,
                pageSize: 10,
            },
            total: 0,
            referralDialogVisible: false,
            currentReferralData: null,
            activeInstitutions: [], // 新增：可用的机构列表
        }
    },
    methods: {
        // 获取患者列表
        async getPatList() {
            try {
                const requestParams = {
                    patientName: this.params.name || null,
                    phone: this.params.phone || null,
                    nursingHomeCode: this.params.nursingHomeCode || null
                }

                const response = await axios.post('/api/emr/nhpatient/list', requestParams)
                if (response.data.code === 200) {
                    let filteredData = response.data.data.map(item => ({
                        ...item,
                        age: this.calculateAge(item.birthDate),
                        sex: item.gender || item.sex,
                        name: item.patientName || item.name,
                        phone: item.phone,
                        idCard: item.idCard,
                        // 新增：机构信息
                        nursingHomeCode: item.nursingHomeCode,
                        nursingHomeName: item.nursingHomeName,
                        // 为养老机构添加默认值或从其他字段映射
                        admissionDate: item.admissionDate || '2023-01-01',
                        roomNumber: item.bedNumber || item.roomNumber || 'A101',
                        careLevel: item.careLevel || '2',
                        healthStatus: item.healthStatus || '2', 
                        emergencyContact: item.emergencyContact || '家属',
                        emergencyPhone: item.emergencyPhone || item.phone
                    }))
                    
                    // 前端过滤其他搜索条件（因为patient360接口不支持这些参数）
                    if (this.params.idCard) {
                        filteredData = filteredData.filter(item => 
                            item.idCard && item.idCard.includes(this.params.idCard)
                        )
                    }
                    if (this.params.healthStatus) {
                        filteredData = filteredData.filter(item => 
                            item.healthStatus === this.params.healthStatus
                        )
                    }
                    if (this.params.careLevel) {
                        filteredData = filteredData.filter(item => 
                            item.careLevel === this.params.careLevel
                        )
                    }
                    
                    this.patList = filteredData
                    this.total = filteredData.length
                } else {
                    this.$message.error(response.data.message || '获取患者列表失败')
                }
            } catch (error) {
                console.error('获取患者列表失败:', error)
                this.$message.error('获取患者列表失败，使用模拟数据')
                
                // 模拟数据供测试使用
                this.patList = [
                    {
                        id: 1,
                        name: "张老先生",
                        sex: "1",
                        age: 78,
                        phone: "13800138001",
                        idCard: "310101194501015234",
                        admissionDate: "2023-01-15",
                        roomNumber: "A101",
                        careLevel: "2",
                        healthStatus: "2",
                        emergencyContact: "张小明",
                        emergencyPhone: "13900139001"
                    },
                    {
                        id: 2,
                        name: "李奶奶",
                        sex: "2",
                        age: 82,
                        phone: "13800138002",
                        idCard: "310101194101015678",
                        admissionDate: "2023-02-20",
                        roomNumber: "B205",
                        careLevel: "3",
                        healthStatus: "3",
                        emergencyContact: "李女士",
                        emergencyPhone: "13900139002"
                    }
                ];
                this.total = 2;
            }
        },

        // 计算年龄
        calculateAge(birthDate) {
            if (!birthDate) return ''
            const birth = new Date(birthDate)
            const today = new Date()
            let age = today.getFullYear() - birth.getFullYear()
            const monthDiff = today.getMonth() - birth.getMonth()
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                age--
            }
            return age
        },

        handleSizeChange(val) {
            this.params.pageSize = val;
            this.getPatList();
        },
        
        handleCurrentChange(val) {
            this.params.pageNo = val;
            this.getPatList();
        },
        
        searchList() {
            this.params.pageNo = 1;
            this.getPatList();
        },
        
        clearParams() {
            this.params = {
                nursingHomeId: sessionStorage.getItem("nursingHomeId"),
                name: "",
                phone: "",
                idCard: "",
                healthStatus: "",
                careLevel: "",
                nursingHomeCode: "",
                pageNo: 1,
                pageSize: 10,
            }
            this.getPatList();
        },

        // 新增：加载可用的机构列表
        async loadActiveInstitutions() {
            try {
                const response = await axios.post('/api/emr/nursing-home/getAllActiveInstitutions', {});
                if (response.data.success) {
                    this.activeInstitutions = response.data.data || [];
                } else {
                    console.error('获取机构列表失败:', response.data.message);
                    this.activeInstitutions = [];
                }
            } catch (error) {
                console.error('获取机构列表失败:', error);
                this.activeInstitutions = [];
            }
        },
        
        goDetail(row) {
            let {id} = row;
            this.$router.push({
                path: '/nursingHomePortal/patientDetail',
                query: {
                    id,
                },
            });
        },
        
        editPatient(row) {
            this.$router.push({
                path: '/nursingHomePortal/patientEdit',
                query: {
                    id: row.id,
                },
            });
        },
        
        addPatient() {
            this.$router.push({
                path: '/nursingHomePortal/patientAdd'
            });
        },
        
        createReferral(row) {
            this.$router.push({
                path: '/nursingHomePortal/patientReferral',
                query: {
                    patientId: row.id,
                    patientName: row.name
                }
            });
        },

        // 查看转介信
        viewReferralLetter(row) {
            this.currentReferralData = this.generateReferralData(row);
            this.referralDialogVisible = true;
        },

        // 生成转介信数据
        generateReferralData(patient) {
            const genderText = patient.sex === '1' ? '男' : '女';
            const birthYear = new Date().getFullYear() - patient.age;
            const idCardLast4 = patient.idCard ? patient.idCard.slice(-4) : '****';
            
            // 根据患者姓名和信息生成模拟数据
            const referralData = {
                // 患者基本信息
                patientName: patient.name,
                gender: genderText,
                birthDate: `${birthYear}年06月15日`,
                idCardLast4: idCardLast4,
                admissionDate: patient.admissionDate || '2024年01月15日',
                dischargeDate: '2024年06月01日',
                admissionDepartment: this.getDepartmentByPatient(patient),
                dischargeDepartment: this.getDepartmentByPatient(patient),
                admissionChiefComplaint: this.getChiefComplaint(patient),
                primaryDiagnosis: this.getPrimaryDiagnosis(patient),
                otherDiagnosis: '详见出院小结',
                
                // 患者去向
                destinations: {
                    rehabilitation: false,
                    palliative: false,
                    daycare: false,
                    nursing: true, // 养老机构床位
                    homecare: false,
                    longcare: false,
                    homebed: false
                },
                
                // 机构信息
                institutionName: '上海XX养老机构',
                institutionContact: '张护士长',
                institutionPhone: '021-12345678',
                
                // 居家照护情况
                homeCare: {
                    spouse: true,
                    housekeeper: false,
                    hourly: false,
                    caregiver: false,
                    other: false,
                    otherDesc: ''
                },
                
                // 联系方式
                patientPhone: patient.phone,
                emergencyContact: patient.emergencyContact,
                emergencyPhone: patient.emergencyPhone,
                
                // 院内评估结果
                adlScore: this.getADLScore(patient),
                swallowingAssessment: this.getSwallowingAssessment(patient),
                swallowingNormal: this.getSwallowingAssessment(patient) === '正常',
                swallowingAbnormal: this.getSwallowingAssessment(patient) !== '正常',
                swallowingAbnormalDesc: this.getSwallowingAssessment(patient) !== '正常' ? this.getSwallowingAssessment(patient) : '',
                pressureRisk: this.getPressureRisk(patient),
                fallRisk: this.getFallRisk(patient),
                dvtRisk: this.getDvtRisk(patient),
                cognition: this.getCognition(patient),
                cognitionDesc: this.getCognitionDesc(patient),
                
                // 复诊安排
                scheduledFollowUp: {
                    date: '2024年06月15日',
                    department: '心血管科',
                    appointmentNo: 'A001234',
                    doctor: '李主任医师'
                },
                pendingFollowUp: {
                    frequency: '每周三',
                    date: '2024年06月22日',
                    department: '心血管科',
                    appointmentNo: '待定',
                    doctor: '待定'
                },
                
                // 随访要求
                followUpRequirements: {
                    postDischarge: '出院后两周门诊取病理报告',
                    keyIndicators: '血压、血糖、体重管理',
                    rehabilitation: '术后肌力训练',
                    referralCriteria: '运动障碍加重或不自主运动频繁',
                    emergencyHandling: '髋关节术后如跌倒，如伴明显疼痛立即就诊',
                    medicationManagement: '观察药物副作用，随访肝功能和血常规，胰岛素需家属监督注射，警惕低血糖反应',
                    homeRisks: '跌倒高风险：禁止独自行走，如厕需陪护'
                },
                
                // 照护需求清单
                careNeeds: [
                    {
                        type: '医疗',
                        item: '伤口护理',
                        frequency: '每日1次，持续7天',
                        executor: '社区护士',
                        guidance: '家庭医生'
                    },
                    {
                        type: '医疗',
                        item: '心脏康复',
                        frequency: '每周5次互联网医院，每周2次心内科心脏康复门诊',
                        executor: '心内科康复医生和治疗师',
                        guidance: '上海四院医生'
                    },
                    {
                        type: '医疗',
                        item: '血糖监测',
                        frequency: '空腹+三餐后，每周记录',
                        executor: '家属/社区医生',
                        guidance: '家庭医生'
                    },
                    {
                        type: '生活',
                        item: '助浴服务',
                        frequency: '每周2次',
                        executor: '养老机构',
                        guidance: '养老机构'
                    },
                    {
                        type: '生活',
                        item: '营养配餐',
                        frequency: '低盐糖尿病餐',
                        executor: '社区助餐中心',
                        guidance: '社工'
                    }
                ],
                
                // 服务资源对接
                serviceResources: {
                    hospitalService: {
                        name: '四院转介服务',
                        contact: '慢病管理中心全科医生：陈医生',
                        phone: '021-12345678'
                    },
                    communityService: {
                        name: '社区服务',
                        contact: '凉城新村街道社工：郑女士',
                        phone: '021-65284030'
                    },
                    healthCenter: {
                        name: '社区卫生服务中心',
                        contact: '家庭医生：李医生',
                        phone: '021-12333371111'
                    },
                    specialistFollowUp: {
                        name: '四院专科随访',
                        contact: '健康管理师：魏护士',
                        phone: '021-55603198'
                    }
                },
                
                // 责任签收
                responsibility: {
                    hospitalContact: '陈医生',
                    hospitalPhone: '021-12345678',
                    platformId: 'HD001'
                },
                
                // 其他信息
                createDate: '2024年06月01日',
                hospitalName: '上海市第四人民医院慢病管理中心'
            };
            
            return referralData;
        },

        // 根据患者信息获取科室
        getDepartmentByPatient(patient) {
            const departments = ['神经内科', '心血管科', '内分泌科', '康复科', '老年医学科'];
            return departments[Math.floor(Math.random() * departments.length)];
        },

        // 根据患者信息获取主要诊断
        getPrimaryDiagnosis(patient) {
            const diagnoses = {
                '1': ['高血压病', '冠心病', '心房颤动', '慢性心力衰竭'],
                '2': ['帕金森病', '阿尔茨海默病', '脑血管病后遗症', '糖尿病'],
                '3': ['慢性肾病', '慢性阻塞性肺疾病', '骨关节炎', '高血压病'],
                '4': ['脑梗死', '重症肺炎', '急性心肌梗死', '多器官功能衰竭']
            };
            const categoryDiagnoses = diagnoses[patient.healthStatus] || diagnoses['2'];
            return categoryDiagnoses[Math.floor(Math.random() * categoryDiagnoses.length)];
        },

        // 获取入院主诉
        getChiefComplaint(patient) {
            const complaints = [
                '因"头晕伴行走不稳"入院',
                '因"胸闷气短"入院',
                '因"反复咳嗽咳痰"入院',
                '因"血糖控制不佳"入院',
                '因"血压波动"入院'
            ];
            return complaints[Math.floor(Math.random() * complaints.length)];
        },

        // 获取ADL评分
        getADLScore(patient) {
            const scores = {
                '1': 90 + Math.floor(Math.random() * 10), // 良好: 90-100
                '2': 70 + Math.floor(Math.random() * 20), // 一般: 70-90
                '3': 40 + Math.floor(Math.random() * 30), // 较差: 40-70
                '4': 10 + Math.floor(Math.random() * 30)  // 危重: 10-40
            };
            return scores[patient.healthStatus] || 60;
        },

        // 获取吞咽功能评估
        getSwallowingAssessment(patient) {
            const assessments = ['正常', '轻度异常', '中度异常', '重度异常'];
            const index = parseInt(patient.healthStatus) - 1;
            return assessments[index] || '正常';
        },

        // 获取跌倒风险
        getFallRisk(patient) {
            const risks = ['低风险', '中风险', '高风险', '极高风险'];
            const index = parseInt(patient.healthStatus) - 1;
            return risks[index] || '低风险';
        },

        // 获取主要风险
        getMainRisks(patient) {
            const risks = [
                '观察术后并发症，观察药物副作用。',
                '观察血压波动，预防心血管意外。',
                '观察呼吸功能，预防肺部感染。',
                '观察生命体征，预防多器官功能衰竭。'
            ];
            const index = parseInt(patient.healthStatus) - 1;
            return risks[index] || risks[0];
        },

        // 获取自我管理建议
        getSelfManagementAdvice(patient) {
            return `出院后随访关键指标。长期口服药物治疗，随访肝功能和血常规，预防体位性高血压。肢体功能训练（如康复训练）。居家安全防护（防跌倒措施）。（护理部患者教育和自我管理知识库）`;
        },

        // 获取随访信息
        getFollowUpInfo(patient) {
            return `除常规诊疗外，出现以下情况应及时就诊：复诊随访：术后两周门诊复查，门诊解读报告。随时转诊：症状加重或药物副作用时，建议至专科门诊。主诊医生门诊：周五上午专家门诊。`;
        },

        // 获取服务资源
        getServiceResources(patient) {
            return `医院转介服务：慢病管理中心全科医生：陈医生（13817993555），负责出院后1个月内转诊衔接。社区服务：社区街道社工：郑女士（65284030），负责整合街道资源及出院探望。社区卫生服务中心：家庭医生：李医生（12333371111）。专科随访：健康管理师：魏护士（55603198），负责专科门诊时间对接。`;
        },

        // 获取压疮风险
        getPressureRisk(patient) {
            const risks = ['低风险', '中风险', '高风险'];
            const index = Math.min(parseInt(patient.healthStatus) - 1, 2);
            return risks[index] || '低风险';
        },

        // 获取DVT风险
        getDvtRisk(patient) {
            const risks = ['低风险', '中风险', '高风险'];
            const index = Math.min(parseInt(patient.healthStatus) - 1, 2);
            return risks[index] || '低风险';
        },

        // 获取认知状态
        getCognition(patient) {
            const cognitions = ['无', '轻度', '中度', '重度'];
            const index = Math.min(parseInt(patient.healthStatus) - 1, 3);
            return cognitions[index] || '无';
        },

        // 获取认知描述
        getCognitionDesc(patient) {
            const descs = ['', '轻度认知障碍', '中度认知障碍', '重度认知障碍'];
            const index = Math.min(parseInt(patient.healthStatus) - 1, 3);
            return descs[index] || '';
        },

        // 关闭转介信弹窗
        handleReferralDialogClose() {
            this.referralDialogVisible = false;
            this.currentReferralData = null;
        },

        // 打印转介信
        printReferralLetter() {
            this.$message.success('转介信打印功能开发中...');
        },
        
        getCareText(careLevel) {
            const map = {
                '1': '自理',
                '2': '半自理',
                '3': '全护理',
                '4': '特护'
            };
            return map[careLevel] || '';
        },
        
        getCareTagType(careLevel) {
            const map = {
                '1': 'success',
                '2': '',
                '3': 'warning',
                '4': 'danger'
            };
            return map[careLevel] || '';
        },
        
        getHealthText(healthStatus) {
            const map = {
                '1': '良好',
                '2': '一般',
                '3': '较差',
                '4': '危重'
            };
            return map[healthStatus] || '';
        },
        
        getHealthTagType(healthStatus) {
            const map = {
                '1': 'success',
                '2': '',
                '3': 'warning',
                '4': 'danger'
            };
            return map[healthStatus] || '';
        }
    },
    
    mounted() {
        // 检查是否从管理页面传入了机构代码参数
        if (this.$route.query.nursingHomeCode) {
            this.params.nursingHomeCode = this.$route.query.nursingHomeCode;
        }
        
        this.getPatList();
        this.loadActiveInstitutions(); // 新增：加载可用的机构列表
    }
}
</script>

<style lang="scss" scoped>
@import '~css/public.scss';

.nursing-home-patient-list {
    @include titleAndTable;
    
    .list-main-head {
        .fieldName {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .handleWrap {
            .el-button {
                margin-right: 10px;
            }
        }
    }

    .operation-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        
        /deep/ .el-button {
            margin: 0 !important;
            padding: 5px 10px !important;
            font-size: 13px !important;
            min-width: 80px;
            text-align: center;
        }
        
        /deep/ .el-button + .el-button {
            margin-left: 0 !important;
        }
    }

    // 覆盖公共样式中表格按钮的margin设置
    /deep/ .el-table {
        tr {
            .cell {
                button {
                    margin: 0 1px !important;
                }
            }
        }
    }
}

// 转介信弹窗样式
::v-deep .el-dialog {
    .el-dialog__body {
        max-height: 70vh;
        overflow-y: auto;
        padding: 20px;
    }
}

.referral-letter-content {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    
    .section {
        margin-bottom: 30px;
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #409EFF;
        }
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
        
        .info-item {
            display: flex;
            align-items: center;
            
            &.full-width {
                grid-column: 1 / -1;
            }
            
            label {
                font-weight: bold;
                color: #333;
                margin-right: 8px;
                white-space: nowrap;
            }
            
            span {
                color: #666;
            }
        }
    }
    
    .content-block {
        .info-item {
            margin-bottom: 10px;
            display: flex;
            align-items: flex-start;
            
            label {
                font-weight: bold;
                color: #333;
                margin-right: 8px;
                white-space: nowrap;
                min-width: 100px;
            }
            
            span, div {
                color: #666;
                flex: 1;
            }
        }
        
        .patient-destination,
        .home-care-situation,
        .long-term-needs {
            margin: 15px 0;
            
            label {
                font-weight: bold;
                color: #333;
                display: block;
                margin-bottom: 8px;
            }
            
            .checkbox-group {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                
                .el-checkbox {
                    margin-right: 0;
                }
            }
        }
        
        .contact-info {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            
            .info-item {
                margin-bottom: 5px;
            }
        }
        
        .assessment-date {
            font-style: italic;
            color: #666;
            margin-bottom: 15px;
        }
        
        .assessment-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .main-risks {
            padding: 15px;
            background-color: #fff2e8;
            border-left: 4px solid #fa8c16;
            margin: 15px 0;
            
            label {
                font-weight: bold;
                color: #fa8c16;
                display: block;
                margin-bottom: 8px;
            }
            
            div {
                color: #333;
            }
        }
        
        .recommendations {
            .info-item {
                margin-bottom: 20px;
                
                label {
                    display: block;
                    margin-bottom: 8px;
                }
                
                div {
                    background-color: #f8f9fa;
                    padding: 10px;
                    border-radius: 4px;
                    border-left: 3px solid #409EFF;
                }
            }
        }
        
        .follow-up-info,
        .service-resources {
            margin: 20px 0;
            
            label {
                font-weight: bold;
                color: #333;
                display: block;
                margin-bottom: 8px;
            }
            
            div {
                background-color: #f0f9ff;
                padding: 15px;
                border-radius: 4px;
                border: 1px solid #b3d8ff;
                white-space: pre-line;
            }
        }
        
        .date-signature {
            text-align: right;
            margin-top: 30px;
            
            .create-date {
                font-size: 16px;
                font-weight: bold;
                color: #333;
            }
        }
    }
    
    // 新增样式
    .followup-section {
        .followup-item {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #409EFF;
            
            h4 {
                margin: 0 0 10px 0;
                color: #409EFF;
                font-weight: bold;
            }
            
            .info-item {
                margin-bottom: 5px;
                
                label {
                    font-weight: bold;
                    color: #333;
                    margin-right: 8px;
                    min-width: 80px;
                    display: inline-block;
                }
                
                span {
                    color: #666;
                }
            }
        }
    }
    
    .followup-requirements {
        .requirement-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #52c41a;
            
            label {
                font-weight: bold;
                color: #333;
                display: block;
                margin-bottom: 5px;
            }
            
            span {
                color: #666;
                line-height: 1.6;
            }
        }
    }
    
    .care-needs-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        border: 1px solid #ddd;
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f5f7fa;
            font-weight: bold;
            color: #606266;
        }
        
        tbody tr:nth-child(even) {
            background-color: #fafafa;
        }
        
        tbody tr:hover {
            background-color: #f0f9ff;
        }
    }
    
    .service-resources {
        .resource-item {
            margin-bottom: 12px;
            padding: 10px;
            background-color: #f0f9ff;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
            
            label {
                font-weight: bold;
                color: #1890ff;
                margin-right: 8px;
            }
            
            span {
                color: #666;
            }
        }
    }
    
    .responsibility-section {
        .signature-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #fff7e6;
            border-radius: 4px;
            border-left: 3px solid #fa8c16;
            
            label {
                font-weight: bold;
                color: #fa8c16;
                margin-right: 8px;
            }
            
            span {
                color: #666;
            }
        }
    }
    
    .appendix-section {
        .appendix-item {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f6ffed;
            border-radius: 4px;
            border-left: 3px solid #52c41a;
            
            strong {
                color: #52c41a;
                font-weight: bold;
            }
            
            div {
                margin-top: 5px;
                color: #666;
            }
        }
        
        .hospital-signature {
            text-align: right;
            margin-top: 30px;
            
            .hospital-name {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }
            
            .create-date {
                font-size: 14px;
                color: #666;
            }
        }
    }
}
</style> 
<template>
  <div class="medical-record-container">
    <el-form :model="recordForm" label-width="100px" class="record-form">
      <el-form-item label="病历类型">
        <el-select v-model="recordForm.recordType" placeholder="请选择病历类型">
          <el-option label="入院记录" value="admission"></el-option>
          <el-option label="病程记录" value="course"></el-option>
          <el-option label="出院记录" value="discharge"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="主诉">
        <el-input
          type="textarea"
          v-model="recordForm.complaint"
          :rows="3"
          placeholder="请输入主诉">
        </el-input>
      </el-form-item>

      <el-form-item label="现病史">
        <el-input
          type="textarea"
          v-model="recordForm.presentIllness"
          :rows="6"
          placeholder="请输入现病史">
        </el-input>
      </el-form-item>

      <el-form-item label="既往史">
        <el-input
          type="textarea"
          v-model="recordForm.pastHistory"
          :rows="4"
          placeholder="请输入既往史">
        </el-input>
      </el-form-item>

      <el-form-item label="体格检查">
        <el-input
          type="textarea"
          v-model="recordForm.physicalExam"
          :rows="4"
          placeholder="请输入体格检查结果">
        </el-input>
      </el-form-item>

      <el-form-item label="诊断">
        <el-input
          type="textarea"
          v-model="recordForm.diagnosis"
          :rows="3"
          placeholder="请输入诊断">
        </el-input>
      </el-form-item>

      <el-form-item label="处理意见">
        <el-input
          type="textarea"
          v-model="recordForm.treatment"
          :rows="4"
          placeholder="请输入处理意见">
        </el-input>
      </el-form-item>

      <el-form-item label="医师签名">
        <el-input v-model="recordForm.doctorName" disabled></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitRecord">保存</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'MedicalRecord',
  props: {
    patientInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      recordForm: {
        recordType: '',
        complaint: '',
        presentIllness: '',
        pastHistory: '',
        physicalExam: '',
        diagnosis: '',
        treatment: '',
        doctorName: ''
      }
    }
  },
  created() {
    this.recordForm.doctorName = this.patientInfo.attendingDoctor || '未指定医生'
  },
  methods: {
    submitRecord() {
      if (!this.recordForm.recordType) {
        this.$message.warning('请选择病历类型');
        return;
      }
      if (!this.recordForm.complaint) {
        this.$message.warning('请输入主诉');
        return;
      }
      if (!this.recordForm.presentIllness) {
        this.$message.warning('请输入现病史');
        return;
      }

      // 这里添加提交病历的逻辑
      console.log('提交病历:', this.recordForm);
      this.$message.success('病历保存成功');
    },
    resetForm() {
      this.recordForm = {
        recordType: '',
        complaint: '',
        presentIllness: '',
        pastHistory: '',
        physicalExam: '',
        diagnosis: '',
        treatment: '',
        doctorName: this.patientInfo.attendingDoctor || '未指定医生'
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.medical-record-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .record-form {
    max-width: 800px;
    margin: 0 auto;
  }
}
</style> 
<template>
    <div class="nurse-succession">
        <div class="header">
            <div class="time-range">
                <span class="label">交接班时间：</span>
                <el-date-picker
                    v-model="startTime"
                    type="datetime"
                    placeholder="开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
                <span class="separator">至</span>
                <el-date-picker
                    v-model="endTime"
                    type="datetime"
                    placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                />
                <el-button type="primary" @click="search">查询</el-button>
            </div>
        </div>
        
        <div class="table-container">
            <el-table
                :data="tableData"
                style="width: 100%"
                border
                @selection-change="handleSelectionChange"
            >
                <el-table-column
                    type="selection"
                    width="55"
                    :selectable="isSelectable"
                />
                <el-table-column
                    prop="shiftTime"
                    label="交班时间"
                    width="180"
                />
                <el-table-column
                    prop="shiftPerson"
                    label="交班人"
                    width="120"
                />
                <el-table-column
                    prop="content"
                    label="交班内容"
                    min-width="200"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="status"
                    label="交班状态"
                    width="100"
                >
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.status === '未接班' ? 'warning' : 'success'">
                            {{ scope.row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="successionPerson"
                    label="接班人"
                    width="120"
                />
                <el-table-column
                    prop="successionTime"
                    label="接班时间"
                    width="180"
                />
            </el-table>
        </div>

        <div class="footer">
            <el-button 
                type="primary" 
                :disabled="!selectedRows.length"
                @click="handleSuccession"
            >
                接班
            </el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'NurseSuccession',
    data() {
        return {
            startTime: '',
            endTime: '',
            tableData: [],
            selectedRows: [],
            currentUser: {
                name: '当前用户' // TODO: 从登录信息中获取
            }
        }
    },
    created() {
        // 设置默认时间范围（最近7天）
        const end = new Date()
        const start = new Date()
        start.setDate(start.getDate() - 7)
        this.startTime = this.formatDateTime(start)
        this.endTime = this.formatDateTime(end)
        this.search()
    },
    methods: {
        formatDateTime(date) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            const seconds = String(date.getSeconds()).padStart(2, '0')
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        },
        isSelectable(row) {
            return row.status === '未接班'
        },
        search() {
            // TODO: 调用后端接口获取数据
            // 模拟数据
            this.tableData = [
                {
                    shiftTime: '2024-03-20 08:00:00',
                    shiftPerson: '陈晓',
                    content: '患者A今日体温正常，血压偏高，已给予降压药',
                    status: '未接班',
                    successionPerson: '',
                    successionTime: ''
                },
                {
                    shiftTime: '2024-03-20 08:05:00',
                    shiftPerson: '王雄',
                    content: '患者B今日手术，术后恢复良好',
                    status: '已接班',
                    successionPerson: '张雨欣',
                    successionTime: '2024-03-20 08:30:00'
                }
            ]
        },
        handleSuccession() {
            if (!this.selectedRows.length) {
                this.$message.warning('请选择要接班的记录')
                return
            }

            const currentTime = this.formatDateTime(new Date())
            // 更新选中行的数据
            this.selectedRows.forEach(row => {
                row.status = '已接班'
                row.successionPerson = this.currentUser.name
                row.successionTime = currentTime
            })

            // 更新表格数据
            this.tableData = this.tableData.map(item => {
                const selectedRow = this.selectedRows.find(row => row.shiftTime === item.shiftTime)
                if (selectedRow) {
                    return selectedRow
                }
                return item
            })

            // TODO: 调用后端接口更新数据
            this.$message.success('接班成功')
            // 清空选中行
            this.selectedRows = []
        },
        handleSelectionChange(selection) {
            this.selectedRows = selection;
        }
    }
}
</script>

<style lang="scss" scoped>
.nurse-succession {
    padding: 20px;
    
    .header {
        margin-bottom: 20px;
        
        .time-range {
            display: flex;
            align-items: center;
            
            .label {
                margin-right: 10px;
            }
            
            .separator {
                margin: 0 10px;
            }
        }
    }
    
    .table-container {
        margin-bottom: 20px;
    }
    
    .footer {
        text-align: right;
    }
}
</style> 
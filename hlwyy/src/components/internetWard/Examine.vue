<template>
	<div class="examine-container">

		<!-- 工具栏 -->
		<!-- <div class="toolbar">
			<div class="title">检查申请</div>
			<div class="right-buttons">
			</div>
		</div> -->

		<el-tabs v-model="activeTab" type="card">
			<!-- 新开申请 tab -->
			<el-tab-pane label="新开申请单" name="new">
				<el-button type="primary" icon="el-icon-check" @click="handleSubmit" :disabled="!canSubmit">
					提交
				</el-button>
				<div class="new-application">
					<!-- 左侧检查项目 -->
					<div class="left-panel">
						<div class="panel-title">检查类别</div>
						<el-tree :data="examineItems" 
							show-checkbox 
							node-key="id" 
							:props="defaultProps"
							:check-strictly="true"
							:expand-on-click-node="false"
							:default-expanded-keys="defaultExpandedKeys"
							:auto-expand-parent="true"
							@check="handleItemCheck" 
							@node-click="handleNodeClick"
							ref="examineTree">
						</el-tree>
					</div>

					<!-- 二级项目 -->
					<div class="middle-panel">
						<div class="panel-title">检查项目</div>
						<el-table :data="secondLevelItems" border size="small">
							<el-table-column type="index" width="50" label="序号">
							</el-table-column>
							<el-table-column prop="label" label="项目名称">
							</el-table-column>
							<el-table-column width="80" label="操作">
								<template slot-scope="scope">
									<el-button type="text" size="small" @click="handleAddToSelected(scope.row)">
										添加
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<!-- 部位选择 -->
					<div class="middle-panel">
						<div class="panel-title">部位选择</div>
						<el-table :data="selectedItems" border size="small">
							<el-table-column type="index" width="50" label="序号">
							</el-table-column>
							<el-table-column prop="label" label="检查项目">
							</el-table-column>
							<el-table-column prop="parts" label="部位">
								<template slot-scope="scope">
									<div v-if="scope.row.hasParts">
										<div class="part-item">
											<div class="part-header">
												<span>{{ scope.row.partLabel }}</span>
											</div>
											<el-select 
												v-model="scope.row.selectedSmallParts" 
												multiple 
												size="small" 
												placeholder="请选择具体部位"
												@focus="() => saveCurrentSmallParts(scope.row)"
												@change="(val) => handleSmallPartChange(scope.row, val)">
												<el-option 
													v-for="part in scope.row.parts"
													:key="part.value" 
													:label="part.label" 
													:value="part.value">
												</el-option>
											</el-select>
										</div>
									</div>
									<span v-else>-</span>
								</template>
							</el-table-column>
							<el-table-column width="80" label="操作">
								<template slot-scope="scope">
									<el-button type="text" size="small" @click="handleDeletePart(scope.row)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<!-- 右侧收费项目 -->
					<!-- <div class="right-panel">
						<div class="panel-title">收费项目</div>
						<el-table :data="chargeItems" border size="small">
							<el-table-column type="index" width="50" label="序号">
							</el-table-column>
							<el-table-column prop="name" label="项目名称">
							</el-table-column>
							<el-table-column prop="unit" label="单位" width="80">
							</el-table-column>
							<el-table-column prop="quantity" label="数量" width="80">
							</el-table-column>
							<el-table-column prop="price" label="单价" width="100">
								<template slot-scope="scope">
									¥{{ scope.row.price }}
								</template>
							</el-table-column>
							<el-table-column prop="amount" label="金额" width="100">
								<template slot-scope="scope">
									¥{{ scope.row.amount }}
								</template>
							</el-table-column>
						</el-table>
						<div class="total-amount">
							合计金额：<span class="price">¥{{ totalAmount }}</span>
						</div>
					</div> -->
				</div>
			</el-tab-pane>

			<!-- 已开申请单 tab -->
			<el-tab-pane label="已开申请单" name="history">
				<div class="history-application">
					<!-- 左侧已申请项目列表 -->
					<div class="history-left">
						<el-table :data="historyApplications" border @row-click="handleHistoryClick">
							<el-table-column prop="status" label="状态" width="100">
								<template slot-scope="scope">
									<el-tag :type="getStatusType(scope.row.status)">
										{{ scope.row.status }}
									</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="applicationNo" label="申请单号" width="150">
							</el-table-column>
							<el-table-column prop="applyTime" label="申请时间" width="150">
							</el-table-column>
							<el-table-column prop="doctor" label="申请医生" width="120">
							</el-table-column>
							<el-table-column prop="examineItems" label="检查项目">
							</el-table-column>
						</el-table>
					</div>

					<!-- 右侧检查结果 -->
					<div class="history-right">
						<el-table :data="examineResults" border size="small">
							<el-table-column prop="itemName" label="检查项目">
							</el-table-column>
							<el-table-column prop="parts" label="检查部位" width="150">
							</el-table-column>
							<el-table-column prop="result" label="检查结果">
							</el-table-column>
							<el-table-column prop="doctor" label="检查医生" width="120">
							</el-table-column>
							<el-table-column prop="checkTime" label="检查时间" width="150">
							</el-table-column>
						</el-table>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
	import { sStore } from 'js/yydjs';
	import { commonAjax, commonAjaxNoEncrypt } from '@/api/api';
	
	export default {
		name: 'Examine',
		props: {
			patientInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			console.log('Examine组件 data初始化');
			return {
				activeTab: 'new',
				defaultProps: {
					children: 'children',
					label: 'label'
				},
				examineItems: [], // 改为空数组，将从API获取数据
				secondLevelItems: [], // 新增：二级项目列表
				selectedItems: [],
				chargeItems: [],
				previousSmallPartsCache: {}, // 缓存之前选中的小部位
				expandedNodes: [], // 当前展开的节点ID列表
				defaultExpandedKeys: [], // Element UI 树形控件的默认展开节点
				historyApplications: [], // 改为空数组，将从API获取数据
				examineResults: []
			}
		},
		created() {
			console.log('Examine组件 created钩子执行');
			// 确保patientInfo中有必要的属性
			if (!this.patientInfo.orgId) {
				this.patientInfo.orgId = "45958b0c-d384-4dae-a1ee-5080cf9327b6";
			}
			
			this.fetchExamineItems();
			this.fetchHistoryApplications();
		},
		mounted() {
		},
		methods: {
			async fetchExamineItems() {
				try {
					const orgId = this.patientInfo.orgId || "45958b0c-d384-4dae-a1ee-5080cf9327b6";
					
					const response = await commonAjax(
						'cas_ih_foshan.medicalSkillItemService',
						'getMedicalSkillTmpl',
						[orgId, "2"]
					);

					if (response.code === 200) {
						// 将API返回的数据转换为树形结构
						this.examineItems = response.body.map(item => ({
							id: item.tempId.toString(),
							label: item.tempName,
							children: [], // 初始化为空数组，后续可以通过其他API获取子项目
							rawData: item
						}));
					} else {
						this.$message.error(response.msg || '获取检查项目失败');
					}
				} catch (error) {
					this.$message.error('获取检查项目失败，请重试');
					console.error('获取检查项目失败:', error);
				}
			},

			async handleItemCheck(checkedNode, checkedStatus) {
				console.log('勾选事件触发:', checkedNode, checkedStatus);
				
				// 获取当前选中的节点
				const selectedNode = checkedStatus.checkedNodes[0];
				if (selectedNode) {
					try {
						// 调用接口获取子项目
						const response = await commonAjax(
							'cas_ih_foshan.medicalSkillItemService',
							'findMedicalSkillItem',
							[selectedNode.id, "2", null, String(this.patientInfo.revisitId || "16790")]
						);

						if (response.code === 200 && Array.isArray(response.body)) {
							// 将返回的数据转换为列表结构
							const items = response.body.map(item => {
								// 确保 item 和必要的属性存在
								if (!item || !item.itemId || !item.itemName) {
									console.warn('Invalid item data:', item);
									return null;
								}

								return {
									id: String(item.itemId),
									label: item.itemName,
									parts: Array.isArray(item.medPlaceList) ? item.medPlaceList.map(part => {
										// 确保 part 和必要的属性存在
										if (!part || !part.bwid || !part.bwmc) {
											console.warn('Invalid part data:', part);
											return null;
										}

										return {
											value: String(part.bwid),
											label: part.bwmc,
											ffid: part.ffid || null,
											zxid: part.zxid || null
										};
									}).filter(Boolean) : [],
									rawData: item
								};
							}).filter(Boolean); // 过滤掉无效的项目
							
							// 更新二级项目列表
							this.secondLevelItems = items;
						} else {
							this.$message.error(response.msg || '获取检查项目详情失败');
						}
					} catch (error) {
						this.$message.error('获取检查项目详情失败，请重试');
						console.error('获取检查项目详情失败:', error);
					}
				} else {
					// 清空二级项目列表
					this.secondLevelItems = [];
				}
			},

			// 修改添加项目到已选列表的方法
			handleAddToSelected(item) {
				// 检查是否已经添加过
				const exists = this.selectedItems.some(selected => selected.id === item.id);
				if (!exists) {
					// 保存完整的原始数据，包括子项目
					this.selectedItems.push({
						id: item.id,
						label: item.label,
						hasParts: item.parts && item.parts.length > 0,
						partId: item.id,
						partLabel: item.label,
						selectedSmallParts: [],
						parts: item.parts || [], // 直接保存部位信息
						rawData: item.rawData // 使用原始数据
					});
					
					// 初始化小部位缓存
					const cacheKey = `${item.id}_${item.id}`;
					this.previousSmallPartsCache[cacheKey] = [];
					
					// 获取收费项目
					this.fetchAndAddChargeItems(item.id, 0, item.label, item.label);
				} else {
					this.$message.warning('该项目已添加');
				}
			},

			handleNodeClick(data, node) {
				console.log('节点点击:', data, node);
				// 如果点击的是一级节点，更新二级项目列表
				if (node.level === 1) {
					if (data.children) {
						this.secondLevelItems = data.children.map(child => ({
							id: child.id,
							label: child.label,
							parts: child.parts,
							rawData: child
						}));
					} else {
						this.secondLevelItems = [];
					}
				}
			},

			// 新增：更新二级项目列表的方法
			updateSecondLevelItems(checkedNode) {
				if (checkedNode && checkedNode.children) {
					this.secondLevelItems = checkedNode.children.map(child => ({
						id: child.id,
						label: child.label,
						parts: child.parts,
						rawData: child
					}));
				} else {
					this.secondLevelItems = [];
				}
			},

			// 修改查找节点的方法
			findNodeById(nodeId) {
				// 在二级项目列表中查找指定ID的项目
				return this.secondLevelItems.find(item => item.id === nodeId);
			},

			async fetchAndAddChargeItems(itemId, hasParts, itemLabel, partLabel = '', smallPartLabel = '') {
				try {
					// hasParts: 0=检查项目或部位, 1=小部位
					const hasPartsValue = hasParts === 1 ? 1 : 0;
					const apiUrl = `/api/base/examineitem/getexaminechargeitems/${itemId}/${hasPartsValue}`;
										
					const response = await this.$axios.get(apiUrl);
										
					if (response.data.code === 200) {
						const chargeItems = response.data.data.map(item => {
							const quantity = parseInt(item.chargeItemCnt);
							const price = parseFloat(item.chargeItemPrice);
							return {
								id: item.chargeItemId.toString(),
								itemId: itemId,
								hasParts: hasPartsValue,
								name: item.chargeItemName,
								price: price,
								unit: item.chargeItemUnit,
								quantity: quantity,
								amount: price * quantity
							};
						});
						// 检查是否已存在相同的收费项目，避免重复添加
						const newChargeItems = chargeItems.filter(newItem => {
							return !this.chargeItems.some(existingItem => 
								existingItem.itemId === newItem.itemId && 
								existingItem.hasParts === newItem.hasParts &&
								existingItem.id === newItem.id
							);
						});
						
						
						this.chargeItems.push(...newChargeItems);
						
					}
				} catch (error) {
					this.$message.error('获取收费项目失败，请重试');
				}
			},

			handleNodeExpand(data, node, instance) {				
				// 只对一级节点实现手风琴效果
				if (node.level === 1) {
					// 更新默认展开键为当前节点
					this.defaultExpandedKeys = [data.id];
					
					// 在下一个tick中处理其他节点的折叠
					this.$nextTick(() => {
						if (this.$refs.examineTree && this.$refs.examineTree.store) {
							const allNodes = this.$refs.examineTree.store.nodesMap;
							
							// 遍历所有节点，关闭其他一级节点
							Object.keys(allNodes).forEach(nodeId => {
								const targetNode = allNodes[nodeId];
								
								// 只处理一级节点（level === 1）且不是当前展开的节点
								if (targetNode.level === 1 && nodeId !== data.id) {
									targetNode.expanded = false;
								}
							});
							
							// 确保当前节点保持展开状态
							if (allNodes[data.id]) {
								allNodes[data.id].expanded = true;
							}
						}
					});
				}
				
				// 将展开的节点ID添加到列表中
				if (!this.expandedNodes.includes(data.id)) {
					this.expandedNodes.push(data.id);
				}
			},

			handleNodeCollapse(data, node, instance) {
				
				// 从展开节点列表中移除
				const index = this.expandedNodes.indexOf(data.id);
				if (index > -1) {
					this.expandedNodes.splice(index, 1);
				}
				
				// 如果是一级节点被折叠，清空默认展开键
				if (node.level === 1) {
					this.defaultExpandedKeys = [];
				}
				
			},

			saveCurrentSmallParts(item) {
				// 在用户开始操作下拉框时保存当前状态
				const cacheKey = `${item.id}_${item.partId}`;
				this.previousSmallPartsCache[cacheKey] = [...(item.selectedSmallParts || [])];
			},

			async handleSmallPartChange(item, selectedParts) {
				// 从缓存中获取之前选中的小部位
				const cacheKey = `${item.id}_${item.partId}`;
				let previousParts = this.previousSmallPartsCache[cacheKey];
				
				// 如果缓存中没有，使用当前的 item.selectedSmallParts 作为之前的状态
				// 注意：这里 item.selectedSmallParts 可能已经被 v-model 更新了，但我们先试试
				if (!previousParts) {
					// 第一次操作，没有缓存，使用空数组
					previousParts = [];
				}
				
				// 找出被取消选中的小部位
				const uncheckedParts = previousParts.filter(part => !selectedParts.includes(part));
				

				// 删除被取消选中的小部位对应的收费项目（hasParts=1的小部位收费项目）
				if (uncheckedParts.length > 0) {
					uncheckedParts.forEach(partId => {
						const beforeCount = this.chargeItems.length;
						this.chargeItems = this.chargeItems.filter(chargeItem => {
							const shouldKeep = !(chargeItem.itemId === partId && chargeItem.hasParts === 1);
							if (!shouldKeep) {
								
							}
							return shouldKeep;
						});
						const afterCount = this.chargeItems.length;
					});
				}

				// 如果当前没有选择任何小部位，但之前有选择，删除所有该部位的小部位收费项目
				if (selectedParts.length === 0 && previousParts.length > 0) {
					previousParts.forEach(partId => {
						const beforeCount = this.chargeItems.length;
						this.chargeItems = this.chargeItems.filter(chargeItem => {
							const shouldKeep = !(chargeItem.itemId === partId && chargeItem.hasParts === 1);
							if (!shouldKeep) {
							}
							return shouldKeep;
						});
						const afterCount = this.chargeItems.length;
					});
				}

				// 更新选中项目的小部位选择
				item.selectedSmallParts = selectedParts;
				
				// 强制触发Vue响应式更新
				this.$set(item, 'selectedSmallParts', selectedParts);
				
				// 更新缓存，为下次操作做准备
				this.previousSmallPartsCache[cacheKey] = [...selectedParts];

				// 获取当前选中的所有小部位对应的收费项目
				const partNode = this.findNodeById(item.partId);
				
				if (partNode) {
					// 找出新选中的小部位（之前没有选中的）
					const newSelectedParts = selectedParts.filter(part => !previousParts.includes(part));
					
					// 如果没有新增的小部位，但有选中的小部位，说明是替换选择
					// 这种情况下我们需要为所有当前选中的小部位获取收费项目
					const partsToProcess = newSelectedParts.length > 0 ? newSelectedParts : selectedParts;
					
					for (const smallPartId of partsToProcess) {
						const smallPart = partNode.parts.find(p => p.value === smallPartId);
						
						if (smallPart) {
							// 检查是否已经存在该小部位的收费项目
							const existingChargeItem = this.chargeItems.find(chargeItem => 
								chargeItem.itemId === smallPartId && chargeItem.hasParts === 1
							);
							
							if (!existingChargeItem) {
								// 选择小部位时：使用 hasParts=1 和 minorBodyPartId 获取收费项目
								await this.fetchAndAddChargeItems(smallPartId, 1, item.label, item.partLabel, smallPart.label);
							} else {
								
							}
						} else {
							
						}
					}
				} else {
					
				}
			},

			handleDeleteCharge(index) {
				// 从收费项目列表中删除
				this.chargeItems.splice(index, 1);
			},

			handleDeletePart(item) {
				if (item.hasParts) {
					// 从树形控件中取消勾选该部位
					this.$refs.examineTree.setChecked(item.partId, false);
					// 删除该部位相关的收费项目（hasParts=0的部位收费项目）
					this.chargeItems = this.chargeItems.filter(chargeItem => 
						!(chargeItem.itemId === item.partId && chargeItem.hasParts === 0)
					);
					// 删除该部位下所有小部位的收费项目（hasParts=1的小部位收费项目）
					if (item.selectedSmallParts && item.selectedSmallParts.length > 0) {
						item.selectedSmallParts.forEach(smallPartId => {
							this.chargeItems = this.chargeItems.filter(chargeItem => 
								!(chargeItem.itemId === smallPartId && chargeItem.hasParts === 1)
							);
						});
					}
				} else {
					// 从树形控件中取消勾选该检查项目
					this.$refs.examineTree.setChecked(item.id, false);
					// 删除该检查项目相关的收费项目（hasParts=0的检查项目收费项目）
					this.chargeItems = this.chargeItems.filter(chargeItem => 
						!(chargeItem.itemId === item.id && chargeItem.hasParts === 0)
					);
				}

				// 从选中项目列表中删除
				const index = this.selectedItems.indexOf(item);
				if (index !== -1) {
					this.selectedItems.splice(index, 1);
				}
			},

			handleHistoryClick(row) {
				
				
				// 清空之前的检查结果
				this.examineResults = [];
				
				// 根据申请单状态显示相应信息
				if (row.jczt >= 3 && row.jczt <= 7) { // 已检查、已报告、已审核、再审、打印状态
					// 这里可以调用具体的检查结果API
					// 暂时使用模拟数据展示检查结果结构
					this.examineResults = [
						{
							itemName: row.examineItems,
							parts: '-',
							result: row.jczt >= 4 ? '检查已完成，请查看详细报告' : '检查已完成，报告生成中',
							doctor: row.doctor,
							checkTime: row.applyTime
						}
					];
				} else if (row.jczt === 8 || row.jczt === 9) { // 医技取消、RIS退回状态
					this.examineResults = [
						{
							itemName: row.examineItems,
							parts: '-',
							result: `申请已${row.status}`,
							doctor: row.doctor,
							checkTime: row.applyTime
						}
					];
				} else {
					// 对于其他状态的申请单，显示申请信息
					this.examineResults = [
						{
							itemName: row.examineItems,
							parts: '-',
							result: `状态：${row.status}`,
							doctor: row.doctor,
							checkTime: row.applyTime
						}
					];
				}
			},

			getStatusType(status) {
				const statusMap = {
					'新增': 'info',
					'提交': 'primary',
					'预约': 'warning',
					'已检查': 'success',
					'已报告': 'success',
					'已审核': 'success',
					'再审': 'warning',
					'打印': 'success',
					'医技取消': 'danger',
					'RIS退回': 'danger',
					'未知': 'info'
				};
				return statusMap[status] || 'info';
			},

			handleClose() {
				this.$router.push('/doctorPatientList');
			},

			generateApplicationNo() {
				const date = new Date();
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0');
				return `EX${year}${month}${day}${random}`;
			},

			async handleSubmit() {
				try {
					// 构建提交的数据结构
					const submitData = {
						applyName: "检查申请单",
						applyType: "2",
						clinicInfo: "复诊配药。。",
						examPurpose: "明确诊断",
						revisitId: String(this.patientInfo.revisitId || "16845"),
						skillItems: this.selectedItems.map(item => {
							// 构建基本项目信息
							const skillItem = {
								itemCode: item.id,
								itemCount: 1,
								itemId: item.id,
								itemName: item.label,
								itemPrice: 0.0,
								itemType: "0",
								medPlaceList: []
							};

							// 如果有部位信息，添加选中的部位
							if (item.hasParts && item.selectedSmallParts && item.selectedSmallParts.length > 0) {
								skillItem.medPlaceList = item.selectedSmallParts.map(smallPartId => {
									// 在 parts 中找到对应的部位信息
									const part = item.parts.find(p => p.value === smallPartId);
									if (part) {
										return {
											bwid: part.value,
											bwmc: part.label,
											ffid: part.ffid || "",
											isSelect: true,
											tzzh: "1",
											zxid: part.zxid || ""
										};
									}
									return null;
								}).filter(Boolean); // 过滤掉无效的部位
							}

							return skillItem;
						})
					};

					// 显示确认对话框
					await this.$confirm('确认提交检查申请？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					});

					// 调用后端API保存数据
					const response = await commonAjax(
						'cas_ih_foshan.medicalSkillItemService',
						'applyMedicalSkill',
						[submitData]
					);

					if (response.code === 200) {
						// 提示保存成功
						this.$message({
							type: 'success',
							message: '检查申请提交成功！'
						});

						// 清空选择
						this.clearSelection();

						// 切换到已开申请单标签
						this.activeTab = 'history';
						
						// 刷新历史申请单列表
						this.fetchHistoryApplications();
					} else {
						this.$message({
							type: 'error',
							message: response.msg || '提交失败，请重试'
						});
					}

				} catch (error) {
					if (error !== 'cancel') {
						this.$message({
							type: 'error',
							message: '提交失败，请重试'
						});
						console.error('提交检查申请失败:', error);
					}
				}
			},

			clearSelection() {
				// 清空树形控件的选中状态
				this.$refs.examineTree.setCheckedKeys([]);
				// 清空已选项目列表
				this.selectedItems = [];
				// 清空收费项目列表
				this.chargeItems = [];
				// 重置所有检查项目的选中状态
				this.examineItems.forEach(item => {
					if (item.hasParts) {
						item.selectedParts = [];
					}
				});
			},

			findNodeById(nodeId) {
				// 在所有检查项目中查找指定ID的节点
				for (const item of this.examineItems) {
					if (item.children) {
						for (const child of item.children) {
							if (child.id === nodeId) {
								return child;
							}
						}
					}
				}
				return null;
			},

			// 获取已开申请单数据
			async fetchHistoryApplications() {
				try {
					const orgId = this.patientInfo.orgId || "45958b0c-d384-4dae-a1ee-5080cf9327b6";
					const revisitId = this.patientInfo.revisitId;
					
					if (!revisitId) {
						console.warn('复诊ID不存在，无法获取历史申请单');
						this.historyApplications = [];
						return;
					}
					
					// 使用复诊ID调用API获取历史申请单，确保revisitId是字符型
					const response = await commonAjaxNoEncrypt(
						'cas_ih_foshan.medicalSkillItemService',
						'findMedicalSkillInfoByRevisitId',
						[String(revisitId)]
					);
					
					if (response.code === 200) {
						// 过滤出检查类型的申请单（applyType === "2"）
						this.historyApplications = (response.body || []).filter(item => 
							item.applyType === "2"
						).map(item => ({
							status: this.getStatusText(item.status),
							applicationNo: item.applyNo || '未知',
							applyTime: item.applyTime || '未知',
							doctor: item.applyDoctor || '未知',
							examineItems: item.skillItems ? item.skillItems.map(skill => skill.itemName).join(', ') : '未知',
							rawData: item // 保存原始数据用于详情展示
						}));
					} else {
						this.$message.error(response.msg || '获取历史申请单失败');
						this.historyApplications = [];
					}
				} catch (error) {
					console.error('获取历史申请单失败:', error);
					this.$message.error('获取历史申请单失败');
					this.historyApplications = [];
				}
			},
			
			// 添加状态文本转换方法
			getStatusText(status) {
				const statusMap = {
					'1': '待检查',
					'2': '检查中',
					'3': '已完成',
					'4': '已作废'
				};
				return statusMap[status] || '未知状态';
			}
		},
		computed: {
			totalAmount() {
				return this.chargeItems.reduce((sum, item) => sum + item.price, 0);
			},
			canSubmit() {
				// 检查是否有选中的项目
				if (this.selectedItems.length === 0) {
					return false;
				}

				// 检查每个项目是否满足条件
				return this.selectedItems.every(item => {
					if (item.hasParts) {
						// 如果有部位的项目，必须选择至少一个部位
						return item.selectedSmallParts && item.selectedSmallParts.length > 0;
					} else {
						// 如果没有部位的项目，只要有选中就可以
						return true;
					}
				});
			}
		},
		watch: {
			// 监听patientInfo的变化
			'patientInfo.revisitId': {
				handler(newVal, oldVal) {
					if (newVal && newVal !== oldVal) {
						console.log('Examine: 复诊ID变化，重新获取数据', newVal);
						// 重新获取历史申请单
						this.fetchHistoryApplications();
					}
				},
				immediate: false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.examine-container {
		padding: 20px;
		background-color: #f5f7fa;
		min-height: calc(100vh - 60px);
	}

	.patient-info-card {
		margin-bottom: 20px;
	}

	.patient-info-header {
		margin-bottom: 20px;

		h3 {
			margin: 0;
			color: #303133;
		}
	}

	.info-item {
		display: flex;
		align-items: center;

		.label {
			color: #606266;
			margin-right: 8px;
			min-width: 80px;
		}

		.value {
			color: #303133;
		}
	}

	/* 添加工具栏样式 */
	.toolbar {
		background-color: #fff;
		padding: 10px 20px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title {
			font-size: 16px;
			font-weight: bold;
			color: #303133;
		}

		.right-buttons {
			display: flex;
			gap: 10px;

			.el-button {
				padding: 9px 15px;
			}
		}
	}

	.new-application {
		display: flex;
		gap: 20px;
		margin-top: 20px;
	}

	.left-panel {
		width: 25%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.middle-panel {
		width: 35%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.right-panel {
		width: 40%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.history-application {
		display: flex;
		gap: 20px;
		margin-top: 20px;
	}

	.history-left {
		width: 66.67%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.history-right {
		width: 33.33%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.panel-title {
		font-size: 16px;
		font-weight: bold;
		margin-bottom: 15px;
		color: #303133;
	}

	::v-deep .el-tree-node__content {
		height: 32px;
	}

	::v-deep .el-table {
		margin-top: 10px;
	}

	.total-amount {
		margin-top: 15px;
		text-align: right;
		padding-right: 20px;
		font-size: 14px;
		color: #606266;

		.price {
			font-size: 16px;
			font-weight: bold;
			color: #f56c6c;
			margin-left: 5px;
		}
	}

	.part-item {
		margin-bottom: 8px;

		.part-header {
			margin-bottom: 4px;

			span {
				font-weight: bold;
			}
		}

		.el-select {
			width: 100%;

			::v-deep .el-select__tags {
				display: flex;
				flex-wrap: wrap;
				gap: 4px;
			}

			::v-deep .el-tag {
				margin: 2px;
				max-width: none;
			}
		}
	}

	.delete-button {
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}
	}
</style>
<template>
  <div class="doctor-station-container">
    <div class="filter-container">
      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="养老机构">
            <el-select v-model="searchForm.nursingHome" placeholder="请选择养老机构" clearable style="width: 150px;">
              <el-option
                v-for="home in nursingHomeList"
                :key="home.id"
                :label="home.name"
                :value="home.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="科室">
            <el-select v-model="searchForm.department" placeholder="请选择科室" clearable style="width: 150px;">
              <el-option
                v-for="dept in departmentList"
                :key="dept.id"
                :label="dept.name"
                :value="dept.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="医生">
            <el-select v-model="searchForm.doctor" placeholder="请选择医生" clearable filterable style="width: 150px;">
              <el-option
                v-for="doctor in doctorList"
                :key="doctor.id"
                :label="doctor.name"
                :value="doctor.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="护士">
            <el-select v-model="searchForm.nurse" placeholder="请选择护士" clearable filterable style="width: 150px;">
              <el-option
                v-for="nurse in nurseList"
                :key="nurse.id"
                :label="nurse.name"
                :value="nurse.name">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px;">
              <el-option label="在院" value="在院"></el-option>
              <el-option label="出院" value="出院"></el-option>
              <el-option label="全部" value="全部"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- <div class="patient-stats">
        <el-checkbox-group v-model="filterOptions">
          <span class="stat-count">患者总数: 17</span>
          <el-checkbox label="危重">
            <span class="danger-tag">危重: 0</span>
          </el-checkbox>
          <el-checkbox label="特级护理">
            <span class="special-tag">特级护理: 14</span>
          </el-checkbox>
          <el-checkbox label="一级护理">
            <span class="level1-tag">一级护理: 0</span>
          </el-checkbox>
          <el-checkbox label="二级护理">
            <span class="level2-tag">二级护理: 1</span>
          </el-checkbox>
          <el-checkbox label="三级护理">
            <span class="level3-tag">三级护理: 0</span>
          </el-checkbox>
          <el-checkbox label="已转出病人">
            <span>已转出病人</span>
          </el-checkbox>
          <el-button type="primary" @click="handlePatientInfo" :disabled="!currentRow">患者信息</el-button>
          <el-button type="primary" @click="handleOrderClick" :disabled="!currentRow">医嘱处理</el-button>
          <el-button type="primary" @click="handleExamineClick" :disabled="!currentRow">检查处理</el-button>
          <el-button type="primary" @click="handleLabTestClick" :disabled="!currentRow">检验处理</el-button>
          <el-button type="primary" @click="handleDoctorRounds" :disabled="!currentRow">查房记录</el-button>

        </el-checkbox-group>
        <div class="view-options">
          <el-radio-group v-model="viewOption" size="small">
            <el-radio-button label="列表">列表</el-radio-button>
            <el-radio-button label="卡片">卡片</el-radio-button>
          </el-radio-group>
          <el-radio-group v-model="statusOption" size="small" style="margin-left: 10px;">
            <el-radio-button label="在院">在院</el-radio-button>
            <el-radio-button label="出院">出院</el-radio-button>
            <el-radio-button label="全部">全部</el-radio-button>
          </el-radio-group>
        </div>
      </div> -->
    </div>

    <!-- 表格视图 -->
    <div v-if="viewOption === '列表'" class="table-container">
      <el-table :data="patientList" :row-class-name="getRowClassName" border
        :header-cell-style="{ background: '#f5f7fa', color: '#333' }" height="calc(100vh - 220px)"
        ref="patientTable">
        <el-table-column prop="bedNumber" label="床号" width="80" align="center">
        </el-table-column>
        <el-table-column prop="patientName" label="姓名" width="80" align="center">
        </el-table-column>
        <el-table-column prop="gender" label="性别" width="80" align="center">
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.age }}岁</span>
          </template>
        </el-table-column>
        <el-table-column prop="hospitalId" label="住院号码" width="80" align="center">
        </el-table-column>
        <!-- <el-table-column prop="nature" label="性质" width="150" align="center">
        </el-table-column> -->
        <el-table-column prop="admissionDate" label="入院日期" width="150" align="center">
        </el-table-column>
        <el-table-column prop="stayDays" label="住院天数" width="80" align="center">
        </el-table-column>
        <!-- <el-table-column prop="department" label="所在科室" width="200" align="center">
        </el-table-column> -->
        <el-table-column prop="nurseLevel" label="护理级别" width="100" align="center">
        </el-table-column>
        <el-table-column prop="attendingDoctor" label="主诊医师" width="80" align="center">
        </el-table-column>
        <el-table-column prop="responsibleNurse" label="护师" width="80" align="center">
        </el-table-column>
        <el-table-column prop="diagnosis" label="入院诊断" width="180" align="center">
        </el-table-column>
        <el-table-column prop="ADLSelfCareAbility" label="ADL自理能力" width="180" align="center">
        </el-table-column>
        <el-table-column prop="specialTreatment" label="特殊治疗" width="180" align="center">
        </el-table-column>
        <!-- 添加一个填充列 -->
        <el-table-column min-width="1">
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDoctorRounds(scope.row)">查房记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="card-container">
      <div class="card-grid">
        <div v-for="(patient, index) in patientList" :key="index" class="patient-card"
          :class="getCardClassName(patient)">
          <div class="card-header">
            <div class="bed-number">{{ patient.bedNumber }}</div>
            <div class="patient-name">{{ patient.patientName }}</div>
          </div>
          <div class="card-body">
            <div class="avatar-section">
              <div class="avatar">
                <img :src="getAvatarUrl(patient)" alt="病人头像">
              </div>
              <div class="patient-info">
                <div>{{ patient.age }}岁</div>
                <div>{{ patient.gender }}</div>
                <div>{{ getShortDepartment(patient.department) }}</div>
              </div>
            </div>
            <div class="nature-badge">{{ patient.nature }}</div>
            <div class="diagnosis-info" v-if="patient.diagnosis">{{ patient.diagnosis }}</div>
            <div class="status-section">
              <div class="status-row">
                <div class="status-label">AB:</div>
                <div class="status-value"></div>
              </div>
              <div class="status-row">
                <div class="status-label">CP</div>
                <div :class="['status-indicator', getStatusClass(patient)]">{{ getStatusText(patient) }}</div>
              </div>
            </div>
          </div>
          <div class="card-footer">
            <div>{{ formatAdmissionDate(patient.admissionDate) }} 入院</div>
            <div>{{ patient.stayDays }} 天</div>
          </div>
          <div class="card-alert" v-if="hasAlert(patient)">⚠</div>
        </div>
      </div>
    </div>

    <div class="pagination-container">
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="pagination.currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'

  export default {
    name: 'DoctorStationPatientList',
    data() {
      return {
        filterOptions: [],
        viewOption: '列表',
        searchForm: {
          department: '',
          status: '在院',
          patientName: '',
          nursingHome: '',
          doctor: '',
          nurse: ''
        },
        patientList: [],
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0
        },
        // 搜索选项数据
        nursingHomeList: [
          { id: 1, name: '彩虹湾' },
          { id: 2, name: '阳光家园' },
          { id: 3, name: '康乐养老院' }
        ],
        departmentList: [
          { id: 1, name: '内科' },
          { id: 2, name: '外科' },
          { id: 3, name: '妇产科' },
          { id: 4, name: '儿科' },
          { id: 5, name: '急诊科' },
          { id: 6, name: '康复科' }
        ],
        doctorList: [
          { id: 1, name: '张医生' },
          { id: 2, name: '李医生' },
          { id: 3, name: '王医生' },
          { id: 4, name: '刘医生' },
          { id: 5, name: '陈医生' }
        ],
        nurseList: [
          { id: 1, name: '张护士' },
          { id: 2, name: '李护士' },
          { id: 3, name: '王护士' },
          { id: 4, name: '刘护士' },
          { id: 5, name: '陈护士' }
        ]
      };
    },
    created() {
      this.fetchPatientList()
    },
    methods: {
      // 获取患者列表数据
      async fetchPatientList() {
        try {
          const params = {
            current: this.pagination.currentPage,
            size: this.pagination.pageSize
          }
          
          // 添加搜索参数
          if (this.searchForm.nursingHome) {
            params.nursingHome = this.searchForm.nursingHome
          }
          if (this.searchForm.department) {
            params.department = this.searchForm.department
          }
          if (this.searchForm.doctor) {
            params.attendingDoctor = this.searchForm.doctor
          }
          if (this.searchForm.nurse) {
            params.responsibleNurse = this.searchForm.nurse
          }
          if (this.searchForm.patientName) {
            params.patientName = this.searchForm.patientName
          }
          if (this.searchForm.status && this.searchForm.status !== '全部') {
            params.status = this.searchForm.status
          }
          
          const response = await axios.get('api/emr/hospitalization/page', {
            // const response = await axios.get('http://localhost:18923/emr/hospitalization/page', {
            params: params
          })

          if (response.data.code === 200) {
            const { records, total } = response.data.data
            this.patientList = records.map(item => ({
              ...item,
              gender: item.gender === '2' ? '女' : '男',
              stayDays: this.calculateStayDays(item.admissionDate),
              sickDays: this.calculateStayDays(item.admissionDate),
              rowType: this.getRowType(item.nurseLevel)
            }))
            this.pagination.total = total
          } else {
            this.$message.error(response.data.message || '获取数据失败')
          }
        } catch (error) {
          console.error('获取患者列表失败:', error)
          this.$message.error('获取患者列表失败')
        }
      },

      // 计算住院天数
      calculateStayDays(admissionDate) {
        if (!admissionDate) return 0
        const admission = new Date(admissionDate)
        const today = new Date()
        const diffTime = Math.abs(today - admission)
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      },

      // 根据护理级别获取行类型
      getRowType(nurseLevel) {
        if (!nurseLevel) return ''
        if (nurseLevel.includes('特级')) return 'light-blue'
        if (nurseLevel.includes('一级')) return 'blue'
        if (nurseLevel.includes('二级')) return 'purple'
        if (nurseLevel.includes('三级')) return 'green'
        return ''
      },

      // 处理分页大小变化
      handleSizeChange(val) {
        this.pagination.pageSize = val
        this.fetchPatientList()
      },

      // 处理页码变化
      handleCurrentChange(val) {
        this.pagination.currentPage = val
        this.fetchPatientList()
      },

      getRowClassName({ row }) {
        return row.rowType || '';
      },
      getCardClassName(patient) {
        const classMap = {
          'blue': 'card-blue',
          'light-blue': 'card-light-blue',
          'purple': 'card-purple',
          'green': 'card-green',
          'yellow': 'card-yellow'
        };
        return classMap[patient.rowType] || '';
      },
      getAvatarUrl(patient) {
        // 默认使用性别区分头像
        return patient.gender === '男'
          ? 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzQwOUVGRiIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgM2MxLjY2IDAgMyAxLjM0IDMgM3MtMS4zNCAzLTMgMy0zLTEuMzQtMy0zIDEuMzQtMyAzLTN6bTAgMTQuMmMtMi41IDAtNC43MS0xLjI4LTYtMy4yMi4wMy0xLjk5IDQtMy4wOCA2LTMuMDggMS45OSAwIDUuOTcgMS4wOSA2IDMuMDgtMS4yOSAxLjk0LTMuNSAzLjIyLTYgMy4yMnoiLz48L3N2Zz4='
          : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI0U2QTIzQyIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgM2MxLjY2IDAgMyAxLjM0IDMgM3MtMS4zNCAzLTMgMy0zLTEuMzQtMy0zIDEuMzQtMyAzLTN6bTAgMTQuMmMtMi41IDAtNC43MS0xLjI4LTYtMy4yMi4wMy0xLjk5IDQtMy4wOCA2LTMuMDggMS45OSAwIDUuOTcgMS4wOSA2IDMuMDgtMS4yOSAxLjk0LTMuNSAzLjIyLTYgMy4yMnoiLz48L3N2Zz4=';
      },
      getShortDepartment(department) {
        // 获取科室简称，例如"急诊医学三科(EIC)" 显示为 "急诊医学三科"
        if (department) {
          const match = department.match(/^([^(]+)/);
          return match ? match[1] : department;
        }
        return '';
      },
      formatAdmissionDate(dateStr) {
        // 格式化日期，例如 "2023-03-17 11:09" 显示为 "2023/03/17"
        if (dateStr) {
          const parts = dateStr.split(' ')[0].split('-');
          return parts.join('/');
        }
        return '';
      },
      getStatusClass(patient) {
        if (patient.status === '危') return 'danger';
        if (patient.rowType === 'green') return 'success';
        if (patient.rowType === 'yellow') return 'warning';
        return '';
      },
      getStatusText(patient) {
        return patient.status || (patient.rowType === 'green' ? '在' : '危');
      },
      hasAlert(patient) {
        return patient.hasAlert;
      },
      handleSearch() {
        // 重置到第一页并执行搜索
        this.pagination.currentPage = 1
        this.fetchPatientList()
      },
      resetSearch() {
        this.searchForm = {
          department: '',
          status: '在院',
          patientName: '',
          nursingHome: '',
          doctor: '',
          nurse: ''
        };
        // 重置后重新获取数据
        this.pagination.currentPage = 1
        this.fetchPatientList()
      },
      handleView(row) {
        console.log('查看病人:', row);
        // 跳转到病人详情页
      },
      exportPatientList() {
        console.log('导出病人列表');
        // 导出病人列表功能
      },
      handleDoctorRounds(row) {
        if (row) {
          this.$router.push({
            path: '/doctorRounds',
            query: {
              patientName: row.patientName,
              patientId: row.patientId,
              patientIds: row.patientIds,
              bedNumber: row.bedNumber,
              gender: row.gender,
              age: row.age,
              department: row.department,
              nurseLevel: row.nurseLevel,
              responsibleNurse: row.responsibleNurse,
              diagnosis: row.diagnosis,
              attendingDoctor: row.attendingDoctor,
              residentDoctor: row.residentDoctor
            }
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .doctor-station-container {
    padding: 15px;
    background-color: #f5f7fa;
    height: calc(100vh - 60px);
    display: flex;
    flex-direction: column;
  }

  .filter-container {
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }

  .search-form {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
  }

  .search-form .el-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
  }

  .search-form .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  .search-form .el-button {
    margin-left: 10px;
  }

  .search-form .el-radio-group {
    margin-left: 0;
  }

  .search-form .el-radio-button__inner {
    padding: 8px 15px;
    font-size: 14px;
  }

  .patient-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  .stat-count {
    font-weight: bold;
    margin-right: 15px;
  }

  .danger-tag {
    color: #f56c6c;
    margin-left: 5px;
  }

  .special-tag {
    color: #f56c6c;
    margin-left: 5px;
  }

  .level1-tag {
    color: #e6a23c;
    margin-left: 5px;
  }

  .level2-tag {
    color: #409EFF;
    margin-left: 5px;
  }

  .level3-tag {
    color: #67c23a;
    margin-left: 5px;
  }

  .table-container {
    background-color: #fff;
    flex: 1;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }

  /* 卡片视图样式 */
  .card-container {
    background-color: #fff;
    flex: 1;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
    overflow-y: auto;
  }

  .card-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .patient-card {
    width: calc(16.666% - 10px);
    min-width: 150px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f0f3f6;
    padding: 5px 10px;
    border-bottom: 1px solid #dcdfe6;
  }

  .bed-number {
    font-weight: bold;
    color: #303133;
  }

  .patient-name {
    font-weight: bold;
  }

  .card-body {
    padding: 10px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .avatar-section {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f5f7fa;
  }

  .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .patient-info {
    font-size: 12px;
    color: #606266;
  }

  .nature-badge {
    padding: 2px 6px;
    background-color: #f5f7fa;
    border-radius: 2px;
    font-size: 12px;
    color: #606266;
    text-align: center;
  }

  .diagnosis-info {
    font-size: 12px;
    color: #606266;
    padding: 2px 6px;
    margin-top: 2px;
    border-radius: 2px;
    background-color: #f9f9f9;
    text-align: center;
    min-height: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .status-section {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .status-row {
    display: flex;
    align-items: center;
  }

  .status-label {
    width: 30px;
    font-size: 12px;
    color: #606266;
  }

  .status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    background-color: #909399;
  }

  .status-indicator.danger {
    background-color: #F56C6C;
  }

  .status-indicator.success {
    background-color: #67C23A;
  }

  .status-indicator.warning {
    background-color: #E6A23C;
  }

  .card-footer {
    padding: 5px 10px;
    background-color: #f5f7fa;
    border-top: 1px solid #dcdfe6;
    font-size: 12px;
    color: #606266;
    display: flex;
    justify-content: space-between;
  }

  .card-alert {
    position: absolute;
    top: 5px;
    right: 5px;
    color: #F56C6C;
    font-size: 16px;
  }

  /* 卡片颜色样式 */
  .card-blue {
    border-left: 4px solid #409EFF;
  }

  .card-light-blue {
    border-left: 4px solid #79bbff;
  }

  .card-purple {
    border-left: 4px solid #b983ff;
  }

  .card-green {
    border-left: 4px solid #67C23A;
  }

  .card-yellow {
    border-left: 4px solid #E6A23C;
  }

  /* 设置表格滚动条样式 */
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #409EFF;
    border-radius: 6px;
    border: 3px solid #f5f7fa;
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    background-color: #ebeef5;
    border-radius: 6px;
  }

  .pagination-container {
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    text-align: right;
  }

  /* 表格颜色样式 */
  ::v-deep .el-table .blue td {
    background-color: #e6f1ff !important;
  }

  ::v-deep .el-table .light-blue td {
    background-color: #ecf5ff !important;
  }

  ::v-deep .el-table .purple td {
    background-color: #f5e6ff !important;
  }

  ::v-deep .el-table .green td {
    background-color: #e6fff1 !important;
  }

  /* 表头样式 */
  ::v-deep .el-table th {
    background-color: #eef1f6 !important;
    color: #606266;
    font-weight: bold;
    padding: 8px 0;
  }

  /* 单元格内容样式 */
  ::v-deep .el-table {
    width: 100%;
  }

  ::v-deep .el-table .cell {
    padding-left: 5px;
    padding-right: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    line-height: 40px;
  }

  ::v-deep .el-table__row td {
    height: 40px !important;
    padding: 5px 0;
  }

  /* 确保滚动时边框对齐 */
  ::v-deep .el-table--border th:first-child .cell,
  ::v-deep .el-table--border td:first-child .cell {
    padding-left: 5px;
  }

  ::v-deep .el-table--border th:last-child .cell,
  ::v-deep .el-table--border td:last-child .cell {
    padding-right: 5px;
  }
</style>
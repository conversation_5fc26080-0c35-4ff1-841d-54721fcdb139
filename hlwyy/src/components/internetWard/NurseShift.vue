<template>
  <div class="nurse-shift-container">
    <div class="left-panel">
      <div class="form-group">
        <span class="label">交班时间：</span>
        <el-date-picker
          v-model="currentTime"
          type="datetime"
          placeholder="选择时间"
          :default-value="new Date()"
        />
      </div>
      <div class="form-group">
        <el-input
          v-model="shiftContent"
          type="textarea"
          :rows="4"
          placeholder="请输入交班内容"
        />
      </div>
      <div class="form-group">
        <el-button type="primary" @click="openPatientDialog">选择患者</el-button>
        <div class="selected-patients">
          <div v-for="patient in selectedPatients" :key="patient.id" class="patient-tag">
            {{ getPatientInfo(patient) }}
            <i class="el-icon-close" @click="removePatient(patient)"></i>
          </div>
        </div>
      </div>
      <div class="form-group">
        <div v-for="patient in selectedPatients" :key="patient.id" class="patient-events">
          <div class="patient-info">
            {{ getPatientInfo(patient) }}
          </div>
          <el-checkbox-group v-model="patientEvents[patient.id]" @change="updateShiftContent">
            <el-checkbox label="入院">入院</el-checkbox>
            <el-checkbox label="出院">出院</el-checkbox>
            <el-checkbox label="转入">转入</el-checkbox>
            <el-checkbox label="转出">转出</el-checkbox>
            <el-checkbox label="手术">手术</el-checkbox>
            <el-checkbox label="危重">危重</el-checkbox>
            <el-checkbox label="抢救">抢救</el-checkbox>
            <el-checkbox label="死亡">死亡</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="form-group">
        <el-button type="primary" @click="submitShift">提交交班</el-button>
      </div>
    </div>
    <div class="right-panel">
      <div class="search-group">
        <span class="label">交班时间：</span>
        <el-date-picker
          v-model="startDate"
          type="date"
          placeholder="开始日期"
          :default-value="getDefaultStartDate()"
        />
        <el-date-picker
          v-model="endDate"
          type="date"
          placeholder="结束日期"
          :default-value="new Date()"
        />
        <el-button type="primary" @click="refreshList">刷新</el-button>
      </div>
      <el-table :data="shiftList" style="width: 100%">
        <el-table-column prop="shiftTime" label="交班时间" />
        <el-table-column prop="shiftPerson" label="交班人" />
        <el-table-column prop="content" label="交班内容" />
        <el-table-column prop="receiveTime" label="接班时间" />
        <el-table-column prop="receivePerson" label="接班人" />
      </el-table>
    </div>

    <!-- 患者选择对话框 -->
    <el-dialog
      title="选择患者"
      :visible.sync="patientDialogVisible"
      width="70%"
      :before-close="handleDialogClose"
    >
      <div class="dialog-search">
        <el-input
          v-model="searchForm.bedNo"
          placeholder="请输入床号"
          style="width: 200px; margin-right: 10px;"
        />
        <el-input
          v-model="searchForm.hospitalNo"
          placeholder="请输入住院号"
          style="width: 200px; margin-right: 10px;"
        />
        <el-button type="primary" @click="searchPatients">搜索</el-button>
      </div>
      <el-table
        :data="filteredPatients"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        ref="patientTable"
      >
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          prop="bedNo"
          label="床号"
          width="80">
        </el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          width="100">
        </el-table-column>
        <el-table-column
          prop="hospitalNo"
          label="住院号">
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalPatients">
        </el-pagination>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button type="primary" @click="confirmSelection">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'NurseShift',
  data() {
    return {
      currentTime: new Date(),
      shiftContent: '无异常',
      selectedPatients: [],
      patientEvents: {},
      patients: [
        { id: 1, bedNo: '101', name: '陈晓', hospitalNo: '2024001' },
        { id: 2, bedNo: '102', name: '王雄', hospitalNo: '2024002' },
        { id: 3, bedNo: '103', name: '张雨欣', hospitalNo: '2024003' },
        { id: 4, bedNo: '104', name: '林晓华', hospitalNo: '2024004' },
        { id: 5, bedNo: '105', name: '黄志强', hospitalNo: '2024005' },
        { id: 6, bedNo: '106', name: '杨丽娟', hospitalNo: '2024006' },
        { id: 7, bedNo: '107', name: '周明远', hospitalNo: '2024007' },
        { id: 8, bedNo: '108', name: '吴晓峰', hospitalNo: '2024008' },
        { id: 9, bedNo: '109', name: '郑美玲', hospitalNo: '2024009' },
        { id: 10, bedNo: '110', name: '王建国', hospitalNo: '2024010' }
      ],
      startDate: this.getDefaultStartDate(),
      endDate: new Date(),
      shiftList: [
        {
          shiftTime: '2024-03-20 08:00',
          shiftPerson: '王护士',
          content: '患者情况稳定',
          receiveTime: '2024-03-20 08:30',
          receivePerson: '李护士'
        }
      ],
      // 对话框相关数据
      patientDialogVisible: false,
      searchForm: {
        bedNo: '',
        hospitalNo: ''
      },
      currentPage: 1,
      pageSize: 10,
      totalPatients: 0,
      tempSelectedPatients: []
    }
  },
  computed: {
    filteredPatients() {
      let result = this.patients
      if (this.searchForm.bedNo) {
        result = result.filter(p => p.bedNo.includes(this.searchForm.bedNo))
      }
      if (this.searchForm.hospitalNo) {
        result = result.filter(p => p.hospitalNo.includes(this.searchForm.hospitalNo))
      }
      this.totalPatients = result.length
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return result.slice(start, end)
    }
  },
  watch: {
    selectedPatients: {
      handler(newVal) {
        newVal.forEach(patient => {
          if (!this.patientEvents[patient.id]) {
            this.$set(this.patientEvents, patient.id, [])
          }
        })
        this.updateShiftContent()
      },
      deep: true
    }
  },
  methods: {
    openPatientDialog() {
      this.patientDialogVisible = true
      this.tempSelectedPatients = [...this.selectedPatients]
      this.$nextTick(() => {
        this.selectedPatients.forEach(patient => {
          const row = this.filteredPatients.find(p => p.id === patient.id)
          if (row) {
            this.$refs.patientTable.toggleRowSelection(row, true)
          }
        })
      })
    },
    handleDialogClose() {
      this.patientDialogVisible = false
      this.searchForm.bedNo = ''
      this.searchForm.hospitalNo = ''
      this.currentPage = 1
    },
    handleSelectionChange(selection) {
      this.tempSelectedPatients = selection
    },
    confirmSelection() {
      this.selectedPatients = this.tempSelectedPatients
      this.patientDialogVisible = false
    },
    removePatient(patient) {
      const index = this.selectedPatients.findIndex(p => p.id === patient.id)
      if (index > -1) {
        this.selectedPatients.splice(index, 1)
        delete this.patientEvents[patient.id]
        this.updateShiftContent()
      }
    },
    searchPatients() {
      this.currentPage = 1
    },
    handleSizeChange(val) {
      this.pageSize = val
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    getDefaultStartDate() {
      const date = new Date()
      date.setDate(date.getDate() - 7)
      return date
    },
    getPatientInfo(patient) {
      return `${patient.bedNo}床，住院号${patient.hospitalNo}，姓名${patient.name}`
    },
    updateShiftContent() {
      let content = []
      this.selectedPatients.forEach(patient => {
        const events = this.patientEvents[patient.id] || []
        if (events.length > 0) {
          content.push(`${patient.bedNo}床，住院号${patient.hospitalNo}，姓名${patient.name}发生了${events.join('、')}`)
        }
      })
      this.shiftContent = content.length > 0 ? content.join('\n') : '无异常'
    },
    submitShift() {
      // 获取当前用户信息（这里假设从sessionStorage获取）
      const currentUser = JSON.parse(sessionStorage.getItem('userInfo')) || { name: '当前用户' }
      
      // 创建新记录
      const newRecord = {
        shiftTime: this.formatDateTime(this.currentTime),
        shiftPerson: currentUser.name,
        content: this.shiftContent,
        receiveTime: '-',
        receivePerson: '-'
      }

      // 添加到列表
      this.shiftList.unshift(newRecord)

      // 清空表单
      this.selectedPatients = []
      this.patientEvents = {}
      this.shiftContent = '无异常'
      this.currentTime = new Date()

      // TODO: 提交到后端
      console.log('提交交班信息：', {
        time: this.currentTime,
        content: this.shiftContent,
        patients: this.selectedPatients,
        events: this.patientEvents
      })
    },
    formatDateTime(date) {
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    },
    refreshList() {
      // TODO: 从后端获取数据
    }
  }
}
</script>

<style scoped>
.nurse-shift-container {
  display: flex;
  height: 100%;
  padding: 20px;
}

.left-panel {
  width: 33.33%;
  padding-right: 20px;
}

.right-panel {
  width: 66.67%;
}

.form-group {
  margin-bottom: 20px;
}

.label {
  display: inline-block;
  width: 80px;
  text-align: right;
  margin-right: 10px;
}

.search-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.el-date-picker {
  width: 200px;
}

.el-select {
  width: 100%;
}

.patient-events {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.patient-info {
  margin-bottom: 10px;
  font-weight: bold;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.selected-patients {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.patient-tag {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  font-size: 14px;
}

.patient-tag .el-icon-close {
  margin-left: 5px;
  cursor: pointer;
}

.dialog-search {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 
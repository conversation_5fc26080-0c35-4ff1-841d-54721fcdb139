<template>
  <div class="lab-test-results">
    <h2>检验结果</h2>
    <div class="content-area">
      <div class="filter-container">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions">
        </el-date-picker>
        <el-select v-model="testType" placeholder="检验类型" clearable>
          <el-option v-for="item in testTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
      </div>
      
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%">
        <el-table-column prop="testName" label="检验名称" width="180"></el-table-column>
        <el-table-column prop="testDate" label="检验日期" width="180"></el-table-column>
        <el-table-column prop="sampleType" label="样本类型"></el-table-column>
        <el-table-column prop="departmentName" label="送检科室"></el-table-column>
        <el-table-column prop="doctorName" label="送检医生"></el-table-column>
        <el-table-column label="结果状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>
    
    <!-- 检验结果详情弹窗 -->
    <el-dialog title="检验结果详情" :visible.sync="detailDialogVisible" width="70%">
      <div v-if="selectedTest" class="detail-container">
        <div class="detail-header">
          <div class="detail-item">
            <span class="label">检验名称：</span>
            <span class="value">{{ selectedTest.testName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">检验日期：</span>
            <span class="value">{{ selectedTest.testDate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">样本类型：</span>
            <span class="value">{{ selectedTest.sampleType }}</span>
          </div>
          <div class="detail-item">
            <span class="label">送检科室：</span>
            <span class="value">{{ selectedTest.departmentName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">送检医生：</span>
            <span class="value">{{ selectedTest.doctorName }}</span>
          </div>
        </div>
        
        <el-table :data="selectedTest.results" border style="width: 100%; margin-top: 20px;">
          <el-table-column prop="itemName" label="检验项目" width="180"></el-table-column>
          <el-table-column prop="result" label="结果" width="120"></el-table-column>
          <el-table-column prop="unit" label="单位" width="80"></el-table-column>
          <el-table-column prop="referenceRange" label="参考范围" width="150"></el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.resultStatus === '正常' ? 'success' : 'danger'">
                {{ scope.row.resultStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handlePrint">打印结果</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'LabTestResults',
  data() {
    return {
      loading: false,
      dateRange: [],
      testType: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      detailDialogVisible: false,
      selectedTest: null,
      testTypes: [
        { label: '血常规', value: 'BLOOD' },
        { label: '生化检验', value: 'BIOCHEMICAL' },
        { label: '尿常规', value: 'URINE' },
        { label: '免疫学检验', value: 'IMMUNE' }
      ],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      tableData: [
        {
          id: '1',
          testName: '血常规',
          testDate: '2023-04-20 09:30',
          sampleType: '静脉血',
          departmentName: '内科',
          doctorName: '陈明宇',
          status: '异常',
          results: [
            { itemName: '白细胞计数(WBC)', result: '10.5', unit: '10^9/L', referenceRange: '4.0-10.0', resultStatus: '异常', remark: '' },
            { itemName: '红细胞计数(RBC)', result: '4.5', unit: '10^12/L', referenceRange: '3.5-5.5', resultStatus: '正常', remark: '' },
            { itemName: '血红蛋白(HGB)', result: '140', unit: 'g/L', referenceRange: '110-160', resultStatus: '正常', remark: '' },
            { itemName: '血小板计数(PLT)', result: '230', unit: '10^9/L', referenceRange: '100-300', resultStatus: '正常', remark: '' }
          ]
        },
        {
          id: '2',
          testName: '肝功能',
          testDate: '2023-04-15 14:20',
          sampleType: '静脉血',
          departmentName: '消化内科',
          doctorName: '王俊杰',
          status: '正常',
          results: [
            { itemName: '总蛋白(TP)', result: '72', unit: 'g/L', referenceRange: '60-80', resultStatus: '正常', remark: '' },
            { itemName: '白蛋白(ALB)', result: '42', unit: 'g/L', referenceRange: '35-55', resultStatus: '正常', remark: '' },
            { itemName: '谷丙转氨酶(ALT)', result: '25', unit: 'U/L', referenceRange: '5-40', resultStatus: '正常', remark: '' },
            { itemName: '谷草转氨酶(AST)', result: '28', unit: 'U/L', referenceRange: '8-40', resultStatus: '正常', remark: '' }
          ]
        },
        {
          id: '3',
          testName: '尿常规',
          testDate: '2023-04-10 10:15',
          sampleType: '中段尿',
          departmentName: '泌尿外科',
          doctorName: '刘伟',
          status: '正常',
          results: [
            { itemName: '尿比重', result: '1.020', unit: '', referenceRange: '1.010-1.025', resultStatus: '正常', remark: '' },
            { itemName: '尿蛋白', result: '阴性', unit: '', referenceRange: '阴性', resultStatus: '正常', remark: '' },
            { itemName: '尿糖', result: '阴性', unit: '', referenceRange: '阴性', resultStatus: '正常', remark: '' },
            { itemName: '尿红细胞', result: '0-2', unit: '/HP', referenceRange: '0-3', resultStatus: '正常', remark: '' }
          ]
        }
      ]
    }
  },
  methods: {
    handleSearch() {
      this.loading = true;
      // 模拟API请求
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.handleSearch();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.handleSearch();
    },
    viewDetail(row) {
      this.selectedTest = row;
      this.detailDialogVisible = true;
    },
    handlePrint() {
      this.$message({
        message: '打印功能开发中',
        type: 'info'
      });
    }
  },
  created() {
    // 初始化时设置数据总条数
    this.total = this.tableData.length;
  }
}
</script>

<style scoped>
.lab-test-results {
  padding: 20px;
}

.content-area {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-top: 20px;
}

.filter-container {
  display: flex;
  margin-bottom: 20px;
}

.filter-container .el-date-editor,
.filter-container .el-select {
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-container {
  padding: 0 20px;
}

.detail-header {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.detail-item {
  width: 33%;
  margin-bottom: 10px;
}

.detail-item .label {
  font-weight: bold;
  color: #606266;
}

.detail-item .value {
  margin-left: 5px;
}
</style> 
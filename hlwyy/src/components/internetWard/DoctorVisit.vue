<template>
  <div class="doctor-visit">
    <div class="content">
      <div class="chat-wrap">
        <!-- 患者信息区域 -->
        <div class="patient-info">
          <div class="patient-wrap">
            <div class="patient-info-card">
              <div class="portrait" :style="{
                backgroundImage: patientInfo.avatar ? `url(${patientInfo.avatar})` : ''
              }"></div>
              <div class="right-content">
                <h3>
                  <span>{{ patientInfo.patientName }}</span>
                  <em :class="{ female: patientInfo.gender !== '男' }">{{ patientInfo.age }}岁</em>
                  <a @click="viewMedicalRecords">查看病历</a>
                </h3>
                <h4>
                  <em>住院号：{{ patientInfo.patientId }}</em>
                  <em>床号：{{ patientInfo.bedNumber }}</em>
                  <em>科室：{{ patientInfo.department }}</em>
                </h4>
              </div>
            </div>
            <div class="right-content">
              <el-button type="primary" @click="startVideoCall" :disabled="isVideoCalling">
                {{ isVideoCalling ? '视频通话中' : '发起视频' }}
              </el-button>
              <a class="finish" @click="finishVisit">结束查房</a>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <div class="chat-messages" ref="chatMessages">
            <div v-for="(message, index) in chatMessages" :key="index" 
                 :class="['message-item', message.type === 'doctor' ? 'doctor-message' : 'patient-message']">
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-time">{{ message.time }}</div>
              </div>
            </div>
          </div>
          <div class="chat-input">
            <el-input
              type="textarea"
              v-model="messageInput"
              :rows="3"
              placeholder="请输入消息"
              @keyup.enter.native="sendMessage"
            ></el-input>
            <el-button type="primary" @click="sendMessage">发送</el-button>
          </div>
        </div>

        <!-- 查房记录区域 -->
        <div class="visit-record">
          <!-- <div class="record-header">
            <h3>查房记录</h3>
            <el-button type="primary" size="small" @click="addRecord">新增记录</el-button>
          </div> -->
          <div class="record-list">
            <el-timeline>
              <el-timeline-item
                v-for="(record, index) in visitRecords"
                :key="index"
                :timestamp="record.time"
                :type="record.type"
                :color="record.color"
              >
                <el-card>
                  <h4>{{ record.title }}</h4>
                  <p>{{ record.content }}</p>
                  <div class="record-footer">
                    <span>记录医生：{{ record.doctor }}</span>
                    <el-button type="text" @click="editRecord(record)">编辑</el-button>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>

        <!-- 视频通话对话框 -->
        <el-dialog
          title="视频通话"
          :visible.sync="videoDialogVisible"
          width="70%"
          :before-close="handleVideoClose"
          custom-class="video-dialog"
        >
          <div class="video-container">
            <div class="video-box">
              <video ref="localVideo" autoplay muted></video>
              <div class="video-label">本地视频</div>
            </div>
            <div class="video-box">
              <video ref="remoteVideo" autoplay></video>
              <div class="video-label">患者视频</div>
            </div>
          </div>
          <div class="video-controls">
            <el-button type="danger" @click="endVideoCall">结束通话</el-button>
          </div>
        </el-dialog>

        <!-- 新增/编辑记录对话框 -->
        <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogVisible"
          width="50%"
          :before-close="handleDialogClose"
        >
          <el-form :model="recordForm" :rules="rules" ref="recordForm" label-width="100px">
            <el-form-item label="记录类型" prop="type">
              <el-select v-model="recordForm.type" placeholder="请选择记录类型">
                <el-option label="查房记录" value="visit"></el-option>
                <el-option label="病情记录" value="condition"></el-option>
                <el-option label="医嘱记录" value="order"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="记录内容" prop="content">
              <el-input
                type="textarea"
                v-model="recordForm.content"
                :rows="6"
                placeholder="请输入记录内容"
              ></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitRecord">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DoctorVisit',
  props: {
    patientInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chatMessages: [
        // {
        //   type: 'doctor',
        //   content: '您好，我是您的主治医生，现在开始查房。',
        //   time: '09:30'
        // },
        // {
        //   type: 'patient',
        //   content: '医生您好，我今天感觉好多了。',
        //   time: '09:31'
        // }
      ],
      messageInput: '',
      isVideoCalling: false,
      videoDialogVisible: false,
      localStream: null,
      remoteStream: null,
      peerConnection: null,
      visitRecords: [
        // {
        //   time: '2024-03-20 09:30',
        //   type: 'visit',
        //   color: '#409EFF',
        //   title: '查房记录',
        //   content: '患者精神状态良好，生命体征平稳，血压130/80mmHg，心率75次/分。',
        //   doctor: '王医生'
        // },
        // {
        //   time: '2024-03-19 15:20',
        //   type: 'condition',
        //   color: '#67C23A',
        //   title: '病情记录',
        //   content: '患者主诉头痛症状减轻，无其他不适。',
        //   doctor: '李医生'
        // }
      ],
      dialogVisible: false,
      dialogTitle: '新增记录',
      recordForm: {
        type: '',
        content: ''
      },
      rules: {
        type: [
          { required: true, message: '请选择记录类型', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入记录内容', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    viewMedicalRecords() {
      // 查看病历逻辑
      console.log('查看病历');
    },
    finishVisit() {
      this.$confirm('确定结束本次查房吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '查房已结束'
        });
      }).catch(() => {});
    },
    sendMessage() {
      if (!this.messageInput.trim()) return;
      
      const message = {
        type: 'doctor',
        content: this.messageInput,
        time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      };
      
      this.chatMessages.push(message);
      this.messageInput = '';
      
      // 模拟患者回复
      setTimeout(() => {
        this.chatMessages.push({
          type: 'patient',
          content: '好的，我明白了。',
          time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        });
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, 1000);
      
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    scrollToBottom() {
      const container = this.$refs.chatMessages;
      container.scrollTop = container.scrollHeight;
    },
    async startVideoCall() {
      try {
        this.isVideoCalling = true;
        this.videoDialogVisible = true;
        
        // 获取本地视频流
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        this.localStream = stream;
        this.$refs.localVideo.srcObject = stream;
        
        // 这里应该添加WebRTC相关的代码来建立连接
        // 由于是模拟环境，我们只显示本地视频
        
      } catch (error) {
        console.error('获取视频流失败:', error);
        this.$message.error('获取视频流失败，请检查摄像头和麦克风权限');
        this.endVideoCall();
      }
    },
    endVideoCall() {
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop());
        this.localStream = null;
      }
      if (this.remoteStream) {
        this.remoteStream.getTracks().forEach(track => track.stop());
        this.remoteStream = null;
      }
      this.isVideoCalling = false;
      this.videoDialogVisible = false;
    },
    handleVideoClose(done) {
      this.$confirm('确定结束视频通话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.endVideoCall();
        done();
      }).catch(() => {});
    },
    addRecord() {
      this.dialogTitle = '新增记录';
      this.recordForm = {
        type: '',
        content: ''
      };
      this.dialogVisible = true;
    },
    editRecord(record) {
      this.dialogTitle = '编辑记录';
      this.recordForm = {
        type: record.type,
        content: record.content
      };
      this.dialogVisible = true;
    },
    handleDialogClose(done) {
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    submitRecord() {
      this.$refs.recordForm.validate((valid) => {
        if (valid) {
          // 提交记录逻辑
          this.dialogVisible = false;
          this.$message({
            type: 'success',
            message: '记录已保存'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.doctor-visit {
  height: 100%;
  .content {
    height: 100%;
    display: flex;
    .chat-wrap {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .patient-info {
        padding: 20px;
        .patient-wrap {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #e4e4e4;
          padding: 15px 0;
          .patient-info-card {
            display: flex;
            flex: 1;
            .portrait {
              width: 64px;
              height: 64px;
              border-radius: 50%;
              background-color: #f5f7fa;
              background-size: cover;
              background-position: center;
            }
            .right-content {
              margin-left: 20px;
              h3 {
                margin: 0;
                font-size: 16px;
                span {
                  margin-right: 10px;
                }
                em {
                  font-style: normal;
                  color: #666;
                  &.female {
                    color: #ff8081;
                  }
                }
                a {
                  float: right;
                  color: #409EFF;
                  cursor: pointer;
                }
              }
              h4 {
                margin: 5px 0 0;
                color: #666;
                em {
                  font-style: normal;
                  margin-right: 20px;
                }
              }
            }
          }
          .right-content {
            display: flex;
            align-items: center;
            gap: 10px;
            .finish {
              display: inline-block;
              width: 100px;
              height: 32px;
              line-height: 32px;
              text-align: center;
              background-color: #ff8081;
              color: #fff;
              border-radius: 16px;
              cursor: pointer;
            }
          }
        }
      }

      .chat-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        border-bottom: 1px solid #e4e4e4;
        
        .chat-messages {
          flex: 1;
          padding: 20px;
          overflow-y: auto;
          
          .message-item {
            margin-bottom: 20px;
            display: flex;
            
            &.doctor-message {
              justify-content: flex-end;
              .message-content {
                background-color: #409EFF;
                color: #fff;
                border-radius: 10px 0 10px 10px;
              }
            }
            
            &.patient-message {
              justify-content: flex-start;
              .message-content {
                background-color: #f5f7fa;
                color: #333;
                border-radius: 0 10px 10px 10px;
              }
            }
            
            .message-content {
              max-width: 70%;
              padding: 10px 15px;
              
              .message-text {
                word-break: break-all;
              }
              
              .message-time {
                font-size: 12px;
                margin-top: 5px;
                opacity: 0.7;
              }
            }
          }
        }
        
        .chat-input {
          padding: 20px;
          display: flex;
          gap: 10px;
          
          .el-textarea {
            flex: 1;
          }
        }
      }
      
      .visit-record {
        padding: 20px;
        .record-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          h3 {
            margin: 0;
            font-size: 16px;
          }
        }
        .record-list {
          max-height: 300px;
          overflow-y: auto;
          .record-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            color: #666;
          }
        }
      }
    }
  }
}

.video-dialog {
  .video-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .video-box {
      width: 48%;
      position: relative;
      
      video {
        width: 100%;
        height: auto;
        background-color: #000;
        border-radius: 4px;
      }
      
      .video-label {
        position: absolute;
        bottom: 10px;
        left: 10px;
        color: #fff;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 5px 10px;
        border-radius: 4px;
      }
    }
  }
  
  .video-controls {
    text-align: center;
  }
}
</style> 
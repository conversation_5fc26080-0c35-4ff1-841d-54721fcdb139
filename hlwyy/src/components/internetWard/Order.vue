<template>
  <div class="order-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="left-buttons">
        <el-button-group>
          <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
          <el-button type="primary" icon="el-icon-folder-add" @click="handleAddGroup">新组</el-button>
          <el-button type="primary" icon="el-icon-delete" @click="handleDelete">删除</el-button>
          <el-button type="primary" icon="el-icon-check" @click="handleSave">保存</el-button>
          <el-button type="primary" icon="el-icon-close" @click="handleInvalidate">作废</el-button>
        </el-button-group>
      </div>
      <div class="right-buttons">
        <!--<el-button type="info" icon="el-icon-back" @click="handleClose">关闭</el-button>-->
      </div>
    </div>

    <!-- 医嘱表格 -->
    <el-table
      :data="orderList"
      border
      stripe
      height="calc(100vh - 180px)"
      :header-cell-style="{
        background: '#f5f7fa',
        color: '#606266',
        fontWeight: 'bold',
        textAlign: 'center'
      }"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column
        prop="name"
        label="医嘱名称"
        min-width="150">
        <template slot-scope="scope">
          <el-autocomplete
            v-model="scope.row.name"
            :fetch-suggestions="querySearch"
            placeholder="请输入药品名称或拼音"
            @select="(item) => handleSelect(item, scope.row)"
            :trigger-on-focus="false"
            popper-class="my-autocomplete">
            <template slot-scope="{ item }">
              <div class="medicine-item">
                <span class="name">{{ item.name }}</span>
                <span class="spec">{{ item.specification }}</span>
                <span class="unit">{{ item.unit }}</span>
                <span class="price">¥{{ item.price }}</span>
              </div>
            </template>
          </el-autocomplete>
        </template>
      </el-table-column>
      <el-table-column
        prop="specification"
        label="规格"
        width="120">
      </el-table-column>
      <el-table-column
        prop="unit"
        label="单位"
        width="80">
      </el-table-column>
      <el-table-column
        prop="usage"
        label="用法"
        width="120">
        <template slot-scope="scope">
          <el-select v-model="scope.row.usage" placeholder="请选择">
            <el-option label="口服" value="口服"></el-option>
            <el-option label="静脉注射" value="静脉注射"></el-option>
            <el-option label="肌肉注射" value="肌肉注射"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        prop="dosage"
        label="剂量"
        width="100">
      </el-table-column>
      <el-table-column
        prop="frequency"
        label="频次"
        width="120">
        <template slot-scope="scope">
          <el-select v-model="scope.row.frequency" placeholder="请选择">
            <el-option label="qd" value="qd"></el-option>
            <el-option label="bid" value="bid"></el-option>
            <el-option label="tid" value="tid"></el-option>
            <el-option label="qid" value="qid"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        prop="days"
        label="天数"
        width="150">
        <template slot-scope="scope">
          <el-input-number 
            v-model="scope.row.days" 
            :min="1"
            :max="999"
            size="small"
            controls-position="right">
          </el-input-number>
        </template>
      </el-table-column>
      <el-table-column
        prop="quantity"
        label="数量"
        width="80">
      </el-table-column>
      <el-table-column
        prop="price"
        label="单价"
        width="80">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'Order',
  data() {
    return {
      patientInfo: {}, // 添加患者信息对象
      orderList: [
        {
          name: '阿莫西林胶囊',
          specification: '0.25g*24粒/盒',
          unit: '盒',
          usage: '口服',
          dosage: '1',
          frequency: 'tid',
          days: 3,
          quantity: 1,
          price: 15.6
        },
        {
          name: '布洛芬缓释胶囊',
          specification: '0.3g*12粒/盒',
          unit: '盒',
          usage: '口服',
          dosage: '1',
          frequency: 'bid',
          days: 3,
          quantity: 1,
          price: 22.8
        }
      ],
      selectedRows: [],
      medicineList: [
        { 
          name: '阿莫西林胶囊', 
          specification: '0.25g*24粒/盒',
          unit: '盒',
          price: 15.6,
          pinyin: 'amoxilin' 
        },
        { 
          name: '布洛芬缓释胶囊', 
          specification: '0.3g*12粒/盒',
          unit: '盒',
          price: 22.8,
          pinyin: 'buluofen' 
        },
        { 
          name: '头孢克肟胶囊', 
          specification: '0.1g*12粒/盒',
          unit: '盒',
          price: 35.6,
          pinyin: 'toubaokefen' 
        },
        { 
          name: '奥美拉唑肠溶胶囊', 
          specification: '20mg*14粒/盒',
          unit: '盒',
          price: 28.5,
          pinyin: 'aomeilazuo' 
        },
        { 
          name: '盐酸左氧氟沙星片', 
          specification: '0.1g*12片/盒',
          unit: '盒',
          price: 42.0,
          pinyin: 'zuoyangfushaxing' 
        },
        { 
          name: '维生素B族片', 
          specification: '100片/瓶',
          unit: '瓶',
          price: 18.5,
          pinyin: 'weishengsuB' 
        },
        { 
          name: '复方甘草片', 
          specification: '100片/瓶',
          unit: '瓶',
          price: 15.0,
          pinyin: 'fugangancao' 
        },
        { 
          name: '对乙酰氨基酚片', 
          specification: '0.5g*12片/盒',
          unit: '盒',
          price: 12.5,
          pinyin: 'duiyixiananjifen' 
        },
        { 
          name: '硫酸氨基葡萄糖片', 
          specification: '0.75g*24片/盒',
          unit: '盒',
          price: 68.0,
          pinyin: 'liusuananjiputaotang' 
        },
        { 
          name: '马来酸氯雷他定片', 
          specification: '10mg*6片/盒',
          unit: '盒',
          price: 25.8,
          pinyin: 'malaisuanlvleitading' 
        }
      ]
    }
  },
  created() {
    // 从路由参数中获取患者信息
    this.patientInfo = this.$route.query;
  },
  methods: {
    // 搜索建议方法
    querySearch(queryString, cb) {
      const results = queryString ? this.medicineList.filter(medicine => {
        // 匹配药品名称或拼音
        return medicine.name.toLowerCase().includes(queryString.toLowerCase()) ||
               medicine.pinyin.toLowerCase().includes(queryString.toLowerCase())
      }) : this.medicineList;
      
      cb(results);
    },

    // 选择药品后的处理
    handleSelect(item, row) {
      // 找到当前编辑的行索引
      const index = this.orderList.findIndex(r => r === row);
      if (index > -1) {
        // 使用 $set 确保视图更新
        this.$set(this.orderList, index, {
          ...this.orderList[index],
          name: item.name,
          specification: item.specification,
          unit: item.unit,
          price: item.price,
          // 保持其他字段不变
          dosage: this.orderList[index].dosage || '',
          frequency: this.orderList[index].frequency || '',
          days: this.orderList[index].days || 1,
          quantity: this.orderList[index].quantity || 0,
          usage: this.orderList[index].usage || ''
        });
      }
    },
    // 新增一行
    handleAdd() {
      this.orderList.push({
        name: '',
        specification: '',
        unit: '',
        usage: '',
        dosage: '',
        frequency: '',
        days: 1,
        quantity: 0,
        price: 0
      });
    },

    // 新增一组
    handleAddGroup() {
      // 实现新增组的逻辑
    },

    // 删除选中行
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的医嘱');
        return;
      }
      this.$confirm('确认删除选中的医嘱?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const selectedIds = this.selectedRows.map(row => row.id);
        this.orderList = this.orderList.filter(item => !selectedIds.includes(item.id));
        this.$message.success('删除成功');
      }).catch(() => {});
    },

    // 保存医嘱
    handleSave() {
      // 实现保存逻辑
      this.$message.success('保存成功');
    },

    // 作废医嘱
    handleInvalidate() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要作废的医嘱');
        return;
      }
      this.$confirm('确认作废选中的医嘱?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 实现作废逻辑
        this.$message.success('作废成功');
      }).catch(() => {});
    },

    // 选择行变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    handleClose() {
      this.$router.push('/doctorPatientList'); // 替换成你的患者列表路由路径
    }
  }
}
</script>

<style lang="scss" scoped>
.order-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.patient-info-card {
  margin-bottom: 20px;
}

.patient-info-header {
  margin-bottom: 20px;
  h3 {
    margin: 0;
    color: #303133;
  }
}

.info-item {
  display: flex;
  align-items: center;
  .label {
    color: #606266;
    margin-right: 8px;
    min-width: 80px;
  }
  .value {
    color: #303133;
  }
}

.toolbar {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;  // 使左右按钮分开
  align-items: center;
}

.left-buttons {
  // 左侧按钮组样式
}

.right-buttons {
  // 右侧关闭按钮样式
}

.el-table {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.my-autocomplete {
  /deep/ .el-autocomplete-suggestion__wrap {
    padding: 0;
  }

  /deep/ .medicine-item {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 30px;  // 将间距从 12px 增加到 30px
    
    &:hover {
      background-color: #f5f7fa;
    }

    .name {
      font-weight: bold;
      color: #303133;
      min-width: 150px;  // 从 120px 增加到 150px
    }

    .spec {
      color: #606266;
      min-width: 150px;  // 从 120px 增加到 150px
    }

    .unit {
      color: #606266;
      min-width: 80px;   // 从 60px 增加到 80px
    }

    .price {
      color: #f56c6c;
      min-width: 100px;  // 从 80px 增加到 100px
    }
  }
}

/deep/ .el-autocomplete {
  width: 100%;
}

/deep/ .el-input__inner {
  height: 28px;
  line-height: 28px;
}

/deep/ .el-input-number {
  width: 100%;
}
</style> 
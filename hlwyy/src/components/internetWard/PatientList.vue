<template>
  <div class="nurse-station-container">
    <div class="filter-container">
      <div class="top-controls">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="科室">
            <el-select v-model="searchForm.department" placeholder="请选择科室">
              <el-option label="全部" value=""></el-option>
              <el-option label="呼吸内科" value="呼吸内科"></el-option>
              <el-option label="心内科" value="心内科"></el-option>
              <el-option label="神经内科" value="神经内科"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="住院状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态">
              <el-option label="全部" value=""></el-option>
              <el-option label="在院" value="在院"></el-option>
              <el-option label="出院" value="出院"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="病人姓名">
            <el-input v-model="searchForm.patientName" placeholder="请输入病人姓名"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        
        <div class="view-options">
          <el-radio-group v-model="viewOption" size="small">
            <el-radio-button label="列表">列表</el-radio-button>
            <el-radio-button label="卡片">卡片</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    
    <!-- <div class="table-operations">
      <el-button type="primary" class="operation-btn" @click="showBedAssignmentDialog">床位分配</el-button>
    </div> -->
    
    <!-- 表格视图 -->
    <div v-if="viewOption === '列表'" class="table-container">
      <el-table
        :data="patientList"
        stripe
        border
        :header-cell-style="{ background: '#f5f7fa' }"
        :row-class-name="tableRowClassName"
        height="calc(100vh - 220px)">
        <el-table-column
          type="index"
          width="50"
          align="center"
          label="序号">
        </el-table-column>
        <el-table-column
          prop="bedNumber"
          label="床号"
          width="70"
          align="center">
        </el-table-column>
        <el-table-column
          prop="patientName"
          label="姓名" 
          width="80"
          align="center">
        </el-table-column>
        <el-table-column
          prop="gender"
          label="性别"
          width="50"
          align="center">
        </el-table-column>
        <el-table-column
          prop="age"
          label="年龄"
          width="60"
          align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.age }}岁</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="hospitalId"
          label="住院号码"
          width="90"
          align="center">
        </el-table-column>
        <el-table-column
          prop="admissionDate"
          label="入院日期"
          width="100"
          align="center">
        </el-table-column>
        <el-table-column
          prop="nature"
          label="性质"
          width="120"
          align="center">
        </el-table-column>
        <el-table-column
          prop="condition"
          label="情况"
          width="130"
          align="center">
        </el-table-column>
        <el-table-column
          prop="nurseCategory"
          label="护理级别"
          width="80"
          align="center">
        </el-table-column>
        <el-table-column
          prop="admissionStatus"
          label="病人情况"
          width="90"
          align="center">
        </el-table-column>
        <el-table-column
          prop="attendingDoctor"
          label="主诊医生"
          width="80"
          align="center">
        </el-table-column>
        <el-table-column
          label="入院诊断"
          width="260"
          align="center"
          prop="admissionDiagnosis">
        </el-table-column>
        <el-table-column
          label="病人状态"
          width="100"
          align="center"
          prop="patientStatus">
          <template slot-scope="scope">
            <div class="status-icon" :class="scope.row.patientStatus === '在院' ? 'in-hospital' : 'discharged'">
              {{ scope.row.patientStatus === '在院' ? '在' : '出' }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 卡片视图 -->
    <div v-else class="card-container">
      <div class="card-grid">
        <div 
          v-for="(patient, index) in patientList" 
          :key="index" 
          class="patient-card"
          :class="getCardClassName(index)">
          <div class="card-header">
            <div class="bed-number">{{ patient.bedNumber }}</div>
            <div class="patient-name">{{ patient.patientName }}</div>
          </div>
          <div class="card-body">
            <div class="avatar-section">
              <div class="avatar">
                <img :src="getAvatarUrl(patient)" alt="病人头像">
              </div>
              <div class="patient-info">
                <div>{{ patient.age }}岁</div>
                <div>{{ patient.gender }}</div>
                <div>{{ patient.nurseCategory }}</div>
              </div>
            </div>
            <div class="nature-badge">{{ patient.nature }}</div>
            <div class="diagnosis-info" v-if="patient.admissionDiagnosis">{{ patient.admissionDiagnosis }}</div>
            <div class="status-section">
              <div class="status-row">
                <div class="status-label">AB:</div>
                <div class="status-value"></div>
              </div>
              <div class="status-row">
                <div class="status-label">CP</div>
                <div :class="['status-indicator', patient.patientStatus === '在院' ? 'success' : '']">
                  {{ patient.patientStatus === '在院' ? '在' : '出' }}
                </div>
              </div>
            </div>
          </div>
          <div class="card-footer">
            <div>{{ formatAdmissionDate(patient.admissionDate) }} 入院</div>
            <div>{{ getDaysSinceAdmission(patient.admissionDate) }} 天</div>
          </div>
        </div>
      </div>
    </div>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 床位分配对话框 -->
    <el-dialog
      title="分配床位 (12楼病区)"
      :visible.sync="bedAssignmentVisible"
      width="80%"
      :before-close="handleDialogClose"
      class="bed-assignment-dialog">
      <div class="dialog-content">
        <p class="bed-assignment-prompt">请选择哪位病人分配到1221床（12楼病区）：</p>
        <el-table
          :data="assignmentPatientList"
          stripe
          border
          highlight-current-row
          @current-change="handlePatientSelect"
          :header-cell-style="{ background: '#f5f7fa', color: '#333', fontWeight: 'bold' }"
          height="400px">
          <el-table-column
            prop="hospitalId"
            label="住院号码"
            width="80"
            align="center">
          </el-table-column>
          <el-table-column
            prop="patientName"
            label="姓名" 
            width="80"
            align="center">
          </el-table-column>
          <el-table-column
            prop="gender"
            label="性别"
            width="50"
            align="center">
          </el-table-column>
          <el-table-column
            prop="patientStatus"
            label="病人状态"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            prop="nature"
            label="性质"
            width="120"
            align="center">
          </el-table-column>
          <el-table-column
            prop="admissionDate"
            label="入院日期"
            width="150"
            align="center">
          </el-table-column>
          <el-table-column
            prop="department"
            label="病人科室"
            width="100"
            align="center">
          </el-table-column>
          <el-table-column
            prop="admissionDiagnosis"
            label="入院诊断"
            align="center">
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="confirmAssignment" icon="el-icon-check">确认(O)</el-button>
        <el-button @click="bedAssignmentVisible = false" icon="el-icon-close">关闭(X)</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'NurseStationPatientList',
  data() {
    return {
      viewOption: '列表',
      searchForm: {
        department: '',
        status: '',
        patientName: ''
      },
      patientList: [
        { bedNumber: '12+1', patientName: '杨泽进', gender: '男', age: 31, hospitalId: '500994', admissionDate: '2025.03.05', nature: '异地医保人入院', condition: '异常恶性病人史', nurseCategory: '一级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '神经性神经退行性疾病', patientStatus: '在院' },
        { bedNumber: '12+2', patientName: '陈少云', gender: '男', age: 35, hospitalId: '502001', admissionDate: '2025.03.18', nature: '城镇职工', condition: '糖尿病', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '刘伟', admissionDiagnosis: '面瘫玻疹', patientStatus: '在院' },
        { bedNumber: '12+3', patientName: '朱志祺', gender: '男', age: 75, hospitalId: '502196', admissionDate: '2025.03.15', nature: '城镇职工', condition: '糖尿病', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '吴涛', admissionDiagnosis: '周围神经病', patientStatus: '在院' },
        { bedNumber: '12+4', patientName: '解阿嬷', gender: '女', age: 67, hospitalId: '502064', admissionDate: '2025.03.14', nature: '城镇职工', condition: '失病', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '刘伟', admissionDiagnosis: '中风', patientStatus: '在院' },
        { bedNumber: '1201', patientName: '韦玉兰', gender: '女', age: 63, hospitalId: '502304', admissionDate: '2025.03.17', nature: '异地医保人入院', condition: '慢性肾衰病', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '帕金森病', patientStatus: '在院' },
        { bedNumber: '1204', patientName: '彭彩虹', gender: '女', age: 56, hospitalId: '501553', admissionDate: '2025.03.17', nature: '自费(外地)', condition: '异常（外地）', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '下肢神经病变', patientStatus: '在院' },
        { bedNumber: '1205', patientName: '沈月琴', gender: '女', age: 61, hospitalId: '501553', admissionDate: '2025.03.11', nature: '城镇职工', condition: '糖尿病', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '糖尿病压迫性周围病变', patientStatus: '在院' },
        { bedNumber: '1206', patientName: '魏延林', gender: '男', age: 80, hospitalId: '500991', admissionDate: '2025.03.06', nature: '城镇职工', condition: '肺炎性出血', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '脑叶出血', patientStatus: '在院' },
        { bedNumber: '1207', patientName: '刘从林', gender: '男', age: 55, hospitalId: '502530', admissionDate: '2025.03.17', nature: '异地医保人入院', condition: '异常恶性病人史', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '多处骨折', patientStatus: '在院' },
        { bedNumber: '1208', patientName: '王孝平', gender: '女', age: 56, hospitalId: '502394', admissionDate: '2025.03.17', nature: '城镇职工', condition: '异地医保人入院', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '骨质疏松症', patientStatus: '在院' },
        { bedNumber: '1211', patientName: '王翠兰', gender: '女', age: 72, hospitalId: '501618', admissionDate: '2025.03.12', nature: '城镇职工', condition: '创伤性脑垂体下出血', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '张海涛', admissionDiagnosis: '脑部损伤', patientStatus: '在院' },
        { bedNumber: '1212', patientName: '叶维新', gender: '男', age: 61, hospitalId: '501514', admissionDate: '2025.03.10', nature: '城镇职工', condition: '脑内出血', nurseCategory: '二级', admissionStatus: '在院', attendingDoctor: '韩国明', admissionDiagnosis: '创伤性脑内出血', patientStatus: '在院' }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 32
      },
      bedAssignmentVisible: false,
      selectedPatient: null,
      assignmentPatientList: [
        { hospitalId: '332971', patientName: '尚怀勤', gender: '男', patientStatus: '出院结算', nature: '城镇职工', admissionDate: '2020. 09. 08 15', department: '神经内科', admissionDiagnosis: '脑血管病' },
        { hospitalId: '332272', patientName: '王晓红', gender: '女', patientStatus: '出院结算', nature: '自费(本地)', admissionDate: '2021. 01. 18 11', department: '产科', admissionDiagnosis: '妊娠状态' },
        { hospitalId: '333545', patientName: '王晓红之子', gender: '男', patientStatus: '出院结算', nature: '自费(外地)', admissionDate: '2021. 02. 05 16', department: '儿科', admissionDiagnosis: '新生儿黄疸' },
        { hospitalId: '333984', patientName: '张喜娟', gender: '女', patientStatus: '出院结算', nature: '自费(本地)', admissionDate: '2021. 02. 27 19', department: '产科', admissionDiagnosis: '先兆临产' },
        { hospitalId: '334040', patientName: '严干超', gender: '男', patientStatus: '出院结算', nature: '自费(本地)', admissionDate: '2021. 03. 01 21', department: '骨科', admissionDiagnosis: '小汽车和两轮或三轮机动车之间碰撞造成的人员损伤(交通性)' },
        { hospitalId: '334042', patientName: '罗铜', gender: '男', patientStatus: '出院结算', nature: '自费(本地)', admissionDate: '2021. 03. 01 21', department: '神经外科', admissionDiagnosis: '交通事故中脚踏车与其他机动车碰撞造成乘脚踏车人员的损伤' },
        { hospitalId: '334045', patientName: '宋豪杰', gender: '男', patientStatus: '出院结算', nature: '自费(本地)', admissionDate: '2021. 03. 01 21', department: '急诊医学三科(EICU)', admissionDiagnosis: '小汽车乘员在小汽车与小汽车、货型货车或客车碰撞中的损伤' },
        { hospitalId: '338105', patientName: '宋豪杰', gender: '男', patientStatus: '出院结算', nature: '自费(外地)', admissionDate: '2021. 04. 08 15', department: '康复医学科', admissionDiagnosis: '腰椎术后' },
        { hospitalId: '338666', patientName: '郝振干', gender: '男', patientStatus: '出院结算', nature: '自费(本地)', admissionDate: '2021. 04. 14 12', department: '神经内科', admissionDiagnosis: '出血性脑梗死' }
      ]
    };
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 0) {
        return 'even-row';
      }
      return 'odd-row';
    },
    getCardClassName(index) {
      // 根据索引交替卡片颜色
      const colorClasses = ['card-blue', 'card-yellow', 'card-green', 'card-light-blue', 'card-purple'];
      return colorClasses[index % colorClasses.length];
    },
    getAvatarUrl(patient) {
      // 默认使用性别区分头像
      return patient.gender === '男' 
        ? 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzQwOUVGRiIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgM2MxLjY2IDAgMyAxLjM0IDMgM3MtMS4zNCAzLTMgMy0zLTEuMzQtMy0zIDEuMzQtMyAzLTN6bTAgMTQuMmMtMi41IDAtNC43MS0xLjI4LTYtMy4yMi4wMy0xLjk5IDQtMy4wOCA2LTMuMDggMS45OSAwIDUuOTcgMS4wOSA2IDMuMDgtMS4yOSAxLjk0LTMuNSAzLjIyLTYgMy4yMnoiLz48L3N2Zz4=' 
        : 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI0U2QTIzQyIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgM2MxLjY2IDAgMyAxLjM0IDMgM3MtMS4zNCAzLTMgMy0zLTEuMzQtMy0zIDEuMzQtMyAzLTN6bTAgMTQuMmMtMi41IDAtNC43MS0xLjI4LTYtMy4yMi4wMy0xLjk5IDQtMy4wOCA2LTMuMDggMS45OSAwIDUuOTcgMS4wOSA2IDMuMDgtMS4yOSAxLjk0LTMuNSAzLjIyLTYgMy4yMnoiLz48L3N2Zz4=';
    },
    formatAdmissionDate(dateStr) {
      // 格式化日期，例如 "2025.03.05" 显示为 "2025/03/05"
      if (dateStr) {
        return dateStr.replace(/\./g, '/');
      }
      return '';
    },
    getDaysSinceAdmission(dateStr) {
      if (!dateStr) return 0;
      
      // 估算住院天数
      const parts = dateStr.split('.');
      if (parts.length !== 3) return 0;
      
      const admissionDate = new Date(parts[0], parts[1] - 1, parts[2]);
      const currentDate = new Date();
      const diffTime = Math.abs(currentDate - admissionDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      return diffDays;
    },
    handleSearch() {
      // 实际应用中这里会调用API获取数据
      console.log('搜索条件:', this.searchForm);
    },
    resetSearch() {
      this.searchForm = {
        department: '',
        status: '',
        patientName: ''
      };
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      // 重新获取数据
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      // 重新获取数据
    },
    handleView(row) {
      console.log('查看病人:', row);
      // 跳转到病人详情页
    },
    handleEdit(row) {
      console.log('编辑病人:', row);
      // 打开编辑对话框
    },
    handleMore(row) {
      console.log('更多操作:', row);
      // 显示更多操作菜单
    },
    showBedAssignmentDialog() {
      this.bedAssignmentVisible = true;
      this.selectedPatient = null;
    },
    handleDialogClose(done) {
      this.selectedPatient = null;
      done();
    },
    handlePatientSelect(row) {
      this.selectedPatient = row;
    },
    confirmAssignment() {
      if (this.selectedPatient) {
        // 实际应用中这里会调用API进行床位分配
        this.$message.success(`已将患者 ${this.selectedPatient.patientName} 分配到1221床位`);
        this.bedAssignmentVisible = false;
      } else {
        this.$message.warning('请先选择一位患者');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.nurse-station-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
}

.filter-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-options {
  margin-left: auto;
}

.table-operations {
  display: flex;
  gap: 5px;
  background-color: #f0f3f6;
  padding: 10px;
  margin-bottom: 0;
  border-radius: 4px 4px 0 0;
}

.table-container {
  background-color: #fff;
  flex: 1;
  padding: 20px;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 卡片视图样式 */
.card-container {
  background-color: #fff;
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow-y: auto;
}

.card-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.patient-card {
  width: calc(16.666% - 10px);
  min-width: 150px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f3f6;
  padding: 5px 10px;
  border-bottom: 1px solid #dcdfe6;
}

.bed-number {
  font-weight: bold;
  color: #303133;
}

.patient-name {
  font-weight: bold;
}

.card-body {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f7fa;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.patient-info {
  font-size: 12px;
  color: #606266;
}

.nature-badge {
  padding: 2px 6px;
  background-color: #f5f7fa;
  border-radius: 2px;
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.diagnosis-info {
  font-size: 12px;
  color: #606266;
  padding: 2px 6px;
  margin-top: 2px;
  border-radius: 2px;
  background-color: #f9f9f9;
  text-align: center;
  min-height: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-section {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.status-row {
  display: flex;
  align-items: center;
}

.status-label {
  width: 30px;
  font-size: 12px;
  color: #606266;
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  background-color: #909399;
}

.status-indicator.success {
  background-color: #67C23A;
}

.card-footer {
  padding: 5px 10px;
  background-color: #f5f7fa;
  border-top: 1px solid #dcdfe6;
  font-size: 12px;
  color: #606266;
  display: flex;
  justify-content: space-between;
}

/* 卡片颜色样式 */
.card-blue {
  border-left: 4px solid #409EFF;
}

.card-light-blue {
  border-left: 4px solid #79bbff;
}

.card-purple {
  border-left: 4px solid #b983ff;
}

.card-green {
  border-left: 4px solid #67C23A;
}

.card-yellow {
  border-left: 4px solid #E6A23C;
}

.card-red {
  border-left: 4px solid #F56C6C;
}

.table-scroll-wrapper::after {
  display: none;
}

.even-row {
  background-color: #f9fcff !important;
}

.odd-row {
  background-color: #ffffff !important;
}

.pagination-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: right;
}

/* 病人状态图标 */
.status-icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  line-height: 30px;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
  margin: 0 auto;
}

.in-hospital {
  background-color: #67c23a;
}

.discharged {
  background-color: #909399;
}

/* 行高亮显示 */
/deep/ .el-table__row:hover {
  cursor: pointer;
  background-color: #e6f7ff !important;
}

/* 表头样式 */
/deep/ .el-table th {
  background-color: #eef1f6 !important;
  color: #606266;
  font-weight: bold;
}

/* 确保行样式有更高的优先级 */
/deep/ .el-table .even-row td {
  background-color: #f9fcff !important;
}

/deep/ .el-table .odd-row td {
  background-color: #ffffff !important;
}

/* 单元格内容样式 */
/deep/ .el-table {
  width: 100%;
}

/deep/ .el-table .cell {
  padding-left: 5px;
  padding-right: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  line-height: 40px;
}

/* 修复固定列与非固定列的高度不一致问题 */
/deep/ .el-table__fixed,
/deep/ .el-table__fixed-right {
  height: auto !important;
  box-shadow: none !important;
}

/deep/ .el-table__fixed::before,
/deep/ .el-table__fixed-right::before {
  display: none;
}

/deep/ .el-table__row td {
  height: 50px !important;
}

/deep/ .el-table__row td.is-hidden > * {
  visibility: visible !important;
}

/* 确保滚动时边框对齐 */
/deep/ .el-table--border th:first-child .cell,
/deep/ .el-table--border td:first-child .cell {
  padding-left: 5px;
}

/deep/ .el-table--border th:last-child .cell,
/deep/ .el-table--border td:last-child .cell {
  padding-right: 5px;
}

/* 设置表格滚动条样式 */
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #409EFF;
  border-radius: 6px;
  border: 3px solid #f5f7fa;
}

/deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #ebeef5;
  border-radius: 6px;
}

/* 操作按钮样式 */
.operation-btn {
  padding: 7px 15px;
  font-size: 13px;
  border-radius: 3px;
  font-weight: normal;
}

/* 为表格按钮覆盖默认样式 */
/deep/ .table-operations .el-button {
  border: none;
  box-shadow: none;
}

/deep/ .table-operations .el-button:focus,
/deep/ .table-operations .el-button:hover {
  opacity: 0.9;
  transform: none;
}

/* 床位分配对话框样式 */
.bed-assignment-dialog {
  /deep/ .el-dialog__header {
    background: #fff;
    padding: 10px 20px;
    border-radius: 4px 4px 0 0;
  }
  
  /deep/ .el-dialog__title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
  }
  
  /deep/ .el-dialog__body {
    padding: 0;
  }
  
  .bed-assignment-prompt {
    padding: 10px 20px;
    background: #fff;
    margin: 0;
    font-size: 14px;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }
  
  /deep/ .el-table .el-table__row.current-row {
    background-color: #409EFF !important;
    color: white;
  }
  
  /deep/ .el-table .el-table__row.current-row td {
    background-color: #409EFF !important;
    color: white;
  }
  
  /deep/ .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ddd;
  }
}
</style> 
<template>
  <div class="doctor-rounds-container">
    <!-- 患者信息卡片 -->
    <el-card class="patient-info-card">
      <div class="patient-info-header">
        <div class="patient-info-left">
          <h3>患者信息</h3>
          <div class="patient-basic-info">
            <span class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ patientInfo.patientName }}</span>
            </span>
            <span class="info-item">
              <span class="label">住院号：</span>
              <span class="value">{{ patientInfo.patientId }}</span>
            </span>
            <span class="info-item">
              <span class="label">床号：</span>
              <span class="value">{{ patientInfo.bedNumber }}</span>
            </span>
          </div>
        </div>
        <div class="header-buttons">
          <el-button type="primary" @click="showPatient360">患者360</el-button>
          <el-button type="info" icon="el-icon-back" @click="handleClose">关闭</el-button>
        </div>
      </div>
      
    </el-card>

    <!-- Tab页 -->
    <el-tabs v-model="activeTab" type="card" class="rounds-tabs">
      <el-tab-pane label="医生查房" name="visit">
        <doctor-visit :patient-info="patientInfo" />
      </el-tab-pane>
      <el-tab-pane label="病历书写" name="record">
        <medical-record :patient-info="patientInfo" />
      </el-tab-pane>
      <el-tab-pane label="开处方" name="order">
        <order :patient-info="patientInfo" />
      </el-tab-pane>
      <el-tab-pane label="开检查" name="examine">
        <examine :patient-info="patientInfo" />
      </el-tab-pane>
      <el-tab-pane label="开检验" name="labtest">
        <lab-test :patient-info="patientInfo" />
      </el-tab-pane>
    </el-tabs>

    <!-- 患者360视图对话框 -->
    <el-dialog
      title="患者360视图"
      :visible.sync="patient360Visible"
      width="90%"
      :before-close="handlePatient360Close"
      custom-class="patient360-dialog"
    >
      <Dashboard v-if="patient360Visible" :patientInfo="patientInfo" />
    </el-dialog>
  </div>
</template>

<script>
import Order from './Order.vue'
import Examine from './Examine.vue'
import LabTest from './LabTest.vue'
import DoctorVisit from './DoctorVisit.vue'
import MedicalRecord from './MedicalRecord.vue'
import Dashboard from '../patient360/dashboard.vue'

export default {
  name: 'DoctorRounds',
  components: {
    Order,
    Examine,
    LabTest,
    DoctorVisit,
    MedicalRecord,
    Dashboard
  },
  data() {
    return {
      activeTab: 'visit',
      patientInfo: {},
      patient360Visible: false
    }
  },
  created() {
    // 从路由参数中获取患者信息，如果没有则使用默认数据
    this.patientInfo = this.$route.query || {
      patientName: '陈晓',
      patientId: '20240001',
      patientIds: '20240001',
      bedNumber: 'A101',
      gender: '女',
      age: 45,
      department: '内科',
      nurseLevel: '一级护理',
      responsibleNurse: '李护士',
      diagnosis: '高血压',
      attendingDoctor: '刘伟',
      residentDoctor: '赵医生'
    }
    console.log('DoctorRounds 接收到的患者信息:', this.patientInfo);
  },
  methods: {
    handleClose() {
      this.$router.push('/doctorPatientList')
    },
    showPatient360() {
      this.patient360Visible = true
    },
    handlePatient360Close(done) {
      this.$confirm('确定关闭患者360视图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.patient360Visible = false
        done()
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.doctor-rounds-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.patient-info-card {
  margin-bottom: 20px;
}

.patient-info-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .patient-info-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .patient-basic-info {
    display: flex;
    gap: 20px;
    
    .info-item {
      display: flex;
      align-items: center;
      
      .label {
        color: #606266;
        font-size: 14px;
      }
      
      .value {
        color: #303133;
        font-size: 14px;
      }
    }
  }
  
  .header-buttons {
    display: flex;
    gap: 10px;
  }
}

.rounds-tabs {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.patient360-dialog {
  .el-dialog__body {
    padding: 0;
    height: calc(90vh - 100px);
    overflow: auto;
  }
}					   
</style> 
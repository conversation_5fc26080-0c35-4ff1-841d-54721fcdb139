<template>
	<div class="lab-test-container">
		<!-- 修改顶部工具栏 -->
		<!-- <div class="toolbar">
			<div class="title">检验申请</div>
			<div class="right-buttons">
			</div>
		</div> -->

		<el-tabs v-model="activeTab" type="card">
			<!-- 新开申请 tab -->
			<el-tab-pane label="新开申请单" name="new">
				<el-button type="primary" icon="el-icon-check" @click="handleSubmit" :disabled="!canSubmit">
					提交
				</el-button>
				<div class="new-application">
					<!-- 左侧检验项目组套 -->
					<div class="left-panel">
						<div class="panel-title">检验单类别</div>
						<el-tree :data="testGroups" show-checkbox node-key="id" @check="handleGroupCheck"
							ref="testGroupTree">
						</el-tree>
					</div>

					<!-- 中间检验项目 -->
					<div class="middle-panel">
						<div class="panel-title">检验项目</div>
						<el-table :data="testItems" border size="small">
							<el-table-column type="index" width="50" label="序号">
							</el-table-column>
							<el-table-column prop="name" label="项目名称">
							</el-table-column>
							<el-table-column prop="price" label="单价" width="100">
								<template slot-scope="scope">
									¥{{ scope.row.price }}
								</template>
							</el-table-column>
							<el-table-column width="80" label="操作">
								<template slot-scope="scope">
									<el-button type="text" size="small" @click="handleAddToSelected(scope.row)">
										添加
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<!-- 申请项目 -->
					<div class="middle-panel">
						<div class="panel-title">申请项目</div>
						<el-table :data="selectedGroups" border size="small">
							<el-table-column type="index" width="50" label="序号">
							</el-table-column>
							<el-table-column prop="name" label="项目名称">
							</el-table-column>
							<el-table-column width="80" label="操作">
								<template slot-scope="scope">
									<el-button type="text" size="small" @click="handleDeleteGroup(scope.$index)">
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<!-- 右侧检验明细 -->
					<div class="right-panel">
						<div class="panel-title">申请明细</div>
						<el-table :data="testDetails" border size="small">
							<el-table-column prop="name" label="检验项目">
							</el-table-column>
							<el-table-column prop="type" label="类型" width="120">
							</el-table-column>
							<el-table-column prop="count" label="数量" width="80">
							</el-table-column>
							<el-table-column prop="price" label="单价" width="100">
								<template slot-scope="scope">
									¥{{ scope.row.price }}
								</template>
							</el-table-column>
							<el-table-column label="小计" width="100">
								<template slot-scope="scope">
									¥{{ (scope.row.price * scope.row.count).toFixed(2) }}
								</template>
							</el-table-column>
						</el-table>
						<!-- 添加合计金额显示 -->
						<div class="total-amount">
							合计金额：<span class="price">¥{{ totalAmount }}</span>
						</div>
					</div>
				</div>
			</el-tab-pane>

			<!-- 已开申请单 tab -->
			<el-tab-pane label="已开申请单" name="history">
				<div class="history-application">
					<!-- 左侧已申请项目列表 -->
					<div class="history-left">
						<el-table :data="historyApplications" border @row-click="handleHistoryClick">
							<el-table-column prop="status" label="状态" width="100">
								<template slot-scope="scope">
									<el-tag :type="getStatusType(scope.row.status)">
										{{ scope.row.status }}
									</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="applicationNo" label="申请单号" width="150">
							</el-table-column>
							<el-table-column prop="applyTime" label="申请时间" width="150">
							</el-table-column>
							<el-table-column prop="doctor" label="申请医生" width="120">
							</el-table-column>
							<el-table-column prop="testItems" label="检验项目">
							</el-table-column>
						</el-table>
					</div>

					<!-- 右侧检验结果 -->
					<div class="history-right">
						<el-table :data="testResults" border size="small">
							<el-table-column prop="itemName" label="明细项目名称">
							</el-table-column>
							<!-- <el-table-column prop="result" label="结果" width="100">
							</el-table-column>
							<el-table-column prop="unit" label="单位" width="80">
							</el-table-column> -->
							<el-table-column prop="price" label="单价" width="80">
								<template slot-scope="scope">
									¥{{ scope.row.price }}
								</template>
							</el-table-column>
							<el-table-column prop="count" label="数量" width="60">
							</el-table-column>
							<!-- <el-table-column prop="abnormalTip" label="异常提示" width="100">
								<template slot-scope="scope">
									<span :class="scope.row.abnormalTip ? 'abnormal' : ''">
										{{ scope.row.abnormalTip }}
									</span>
								</template>
							</el-table-column>
							<el-table-column prop="referenceRange" label="参考范围" width="120">
							</el-table-column> -->
						</el-table>
					</div>
				</div>
			</el-tab-pane>

			<!-- 检验报告 tab -->
			<el-tab-pane label="检验报告" name="report">
				<div class="report-application">
					<!-- 左侧检验报告列表 -->
					<div class="report-left">
						<div class="panel-title">检验报告列表</div>
						<el-table :data="labReports" border @row-click="handleReportClick">
							<el-table-column prop="reportStatus" label="状态" width="100">
								<template slot-scope="scope">
									<el-tag :type="getReportStatusType(scope.row.reportStatus)">
										{{ scope.row.reportStatus }}
									</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="reportNo" label="报告单号" width="150">
							</el-table-column>
							<el-table-column prop="reportTime" label="报告时间" width="150">
							</el-table-column>
							<el-table-column prop="reportDoctor" label="报告医生" width="120">
							</el-table-column>
							<el-table-column prop="testItems" label="检验项目">
							</el-table-column>
						</el-table>
					</div>

					<!-- 右侧报告详情 -->
					<div class="report-right">
						<div class="panel-title">报告详情</div>
						<el-table :data="reportDetails" border size="small">
							<el-table-column prop="itemName" label="检验项目名称">
							</el-table-column>
							<el-table-column prop="result" label="结果" width="100">
							</el-table-column>
							<el-table-column prop="unit" label="单位" width="80">
							</el-table-column>
							<el-table-column prop="abnormalTip" label="异常提示" width="100">
								<template slot-scope="scope">
									<span :class="scope.row.abnormalTip ? 'abnormal' : ''">
										{{ scope.row.abnormalTip }}
									</span>
								</template>
							</el-table-column>
							<el-table-column prop="referenceRange" label="参考范围" width="150">
							</el-table-column>
						</el-table>
						
						<!-- 报告总结 -->
						<!-- <div class="report-summary" v-if="selectedReport">
							<div class="summary-title">检验结论</div>
							<div class="summary-content">
								{{ selectedReport.conclusion || '暂无结论' }}
							</div>
							<div class="summary-title">医生建议</div>
							<div class="summary-content">
								{{ selectedReport.suggestion || '暂无建议' }}
							</div>
						</div> -->
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
	import { sStore } from 'js/yydjs';
	import { commonAjax, commonAjaxNoEncrypt } from '@/api/api';
	
	export default {
		name: 'LabTest',
		props: {
			patientInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				activeTab: 'new',
				testGroups: [], // 改为空数组，将从API获取数据
				testItems: [], // 新增：检验项目列表
				selectedGroups: [],
				testDetails: [],
				historyApplications: [], // 改为空数组，将从API获取数据
				testResults: [],
				// 新增检验报告相关数据
				labReports: [], // 改为空数组，将从API获取数据
				reportDetails: [],
				selectedReport: null
			}
		},
		created() {
			// 确保patientInfo中有必要的属性
			if (!this.patientInfo.orgId) {
				this.patientInfo.orgId = "45958b0c-d384-4dae-a1ee-5080cf9327b6";
			}
			
			// 获取检验项目数据
			this.fetchLabTestItems();
			// 获取已开申请单数据
			this.fetchHistoryApplications();
			// 获取检验报告数据
			this.fetchLabReports();
		},
		methods: {
			// 修改获取检验项目的方法
			async fetchLabTestItems() {
				try {
					const orgId = this.patientInfo.orgId || "45958b0c-d384-4dae-a1ee-5080cf9327b6";
					
					const response = await commonAjax(
						'cas_ih_foshan.medicalSkillItemService',
						'getMedicalSkillTmpl',
						[orgId, "3"]
					);

					if (response.code === 200) {
						this.testGroups = response.body.map(item => ({
							id: item.tempId.toString(),
							label: item.tempName,
							rawData: item
						}));
					} else {
						this.$message.error(response.msg || '获取检验项目失败');
					}
				} catch (error) {
					this.$message.error('获取检验项目失败，请重试');
					console.error('获取检验项目失败:', error);
				}
			},

			// 修改handleGroupCheck方法
			async handleGroupCheck(checkedNode, checkedStatus) {
				if (checkedStatus.checkedNodes.length > 0) {
					// 获取选中的组套ID
					const selectedGroupId = checkedStatus.checkedNodes[0].id;
					// 获取该组套下的检验项目
					await this.fetchTestItems(selectedGroupId);
				} else {
					// 清空检验项目列表
					this.testItems = [];
				}
			},

			// 修改获取检验项目的方法
			async fetchTestItems(groupId) {
				try {
					const response = await commonAjax(
						'cas_ih_foshan.medicalSkillItemService',
						'findMedicalSkillItem',
						[groupId, "3", null, String(this.patientInfo.revisitId || "16790")]
					);

					if (response.code === 200) {
						// 只处理主项目，但保存完整的原始数据
						this.testItems = response.body.map(item => ({
							id: item.itemId.toString(),
							name: item.itemName,
							price: parseFloat(item.itemPrice) || 0,
							yblx: item.yblx || '',
							ybmc: item.ybmc || '',
							rawData: item // 保存完整的原始数据，包括子项目
						}));
					} else {
						this.$message.error(response.msg || '获取检验项目失败');
					}
				} catch (error) {
					this.$message.error('获取检验项目失败，请重试');
					console.error('获取检验项目失败:', error);
				}
			},

			// 修改添加项目到已选列表的方法
			handleAddToSelected(item) {
				// 检查是否已经添加过
				const exists = this.selectedGroups.some(selected => selected.id === item.id);
				if (!exists) {
					// 保存完整的原始数据，包括子项目
					this.selectedGroups.push({
						id: item.id,
						name: item.name,
						rawData: item.rawData // 使用原始数据
					});
					// 更新检验明细
					this.updateTestDetails();
				} else {
					this.$message.warning('该项目已添加');
				}
			},

			// 修改updateTestDetails方法
			async updateTestDetails() {
				// 清空当前明细
				this.testDetails = [];

				// 遍历选中的项目，构建检验明细
				for (const item of this.selectedGroups) {
					const rawData = item.rawData;
					if (rawData && rawData.medicalSkillItemChildren && rawData.medicalSkillItemChildren.length > 0) {
						// 只添加子项目到检验明细
						rawData.medicalSkillItemChildren.forEach(child => {
							this.testDetails.push({
								id: child.itemId.toString(),
								name: child.itemName,
								type: '检验',
								price: parseFloat(child.itemPrice) || 0,
								count: 1,
								yblx: child.medOpts && child.medOpts[0] ? child.medOpts[0].yblx : '',
								ybmc: child.medOpts && child.medOpts[0] ? child.medOpts[0].ybmc : '',
								ybCode: child.ybCode || '' // 添加ybCode
							});
						});
					} else {
						console.log('No children found for item:', item);
					}
				}

				// 打印检验明细数据，用于调试
				console.log('更新后的检验明细:', this.testDetails);
			},

			// 根据分组ID获取检验类型
			getTestTypeByGroupId(itemId) {
				// 在testGroups中查找该项目属于哪个分组
				for (const group of this.testGroups) {
					if (group.children) {
						const foundItem = group.children.find(child => child.id === itemId);
						if (foundItem) {
							return group.label; // 返回分组名称作为类型
						}
					}
				}
				return '检验'; // 默认类型
			},

			async handleDeleteGroup(index) {
				// 获取要删除的项目名称
				const groupToDelete = this.selectedGroups[index];

				// 从树形控件中取消勾选
				this.$refs.testGroupTree.setChecked(groupToDelete.id, false);

				// 从选中项目列表中删除
				this.selectedGroups.splice(index, 1);

				// 更新检验明细
				await this.updateTestDetails();
			},

			handleHistoryClick(row) {
				// 使用API返回的明细数据
				if (row.rawData && row.rawData.llisSqdmxList) {
					this.testResults = row.rawData.llisSqdmxList.map(item => ({
						itemName: item.ylmc,
						result: '-', // API中没有检验结果，显示为待检验
						unit: '-',
						abnormalTip: '',
						referenceRange: '-',
						price: item.dj,
						count: item.sl
					}));
				} else {
					this.testResults = [];
				}
			},

			getStatusType(status) {
				const statusMap = {
					'已完成': 'success',
					'待采集': 'warning',
					'已采集': 'primary',
					'已作废': 'info'
				};
				return statusMap[status] || 'info';
			},

			// 添加关闭方法
			handleClose() {
				this.$router.push('/doctorPatientList');
			},

			// 修改提交方法
			async handleSubmit() {
				try {
					// 构建提交的数据结构
					const submitData = [{
						applyName: "检验申请单",
						applyType: "3",
						revisitId: String(this.patientInfo.revisitId || "16790"),
						skillItems: this.testDetails.map(item => ({
							itemCode: item.id,
							itemCount: 1,
							itemId: item.id,
							itemName: item.name,
							itemPrice: item.price,
							itemType: "0",
							ybCode: item.ybCode || "",
							yblx: item.yblx || "",
							ybmc: item.ybmc || ""
						}))
					}];

					// 打印提交的数据
					console.log('提交的数据结构:', JSON.stringify(submitData, null, 2));
					console.log('检验明细数据:', JSON.stringify(this.testDetails, null, 2));

					// 显示确认对话框
					await this.$confirm('确认提交检验申请？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					});

					// 调用后端API保存数据
					console.log('开始调用API，参数:', {
						service: 'cas_ih_foshan.medicalSkillItemService',
						method: 'applyMedicalSkill',
						params: submitData
					});

					const response = await commonAjax(
						'cas_ih_foshan.medicalSkillItemService',
						'applyMedicalSkill',
						submitData
					);

					console.log('API响应:', response);

					if (response.code === 200) {
						// 提示保存成功
						this.$message({
							type: 'success',
							message: '检验申请提交成功！'
						});

						// 清空选择
						this.clearSelection();

						// 切换到已开申请单标签
						this.activeTab = 'history';
						
						// 刷新历史申请单列表
						this.fetchHistoryApplications();
					} else {
						this.$message({
							type: 'error',
							message: response.msg || '提交失败，请重试'
						});
					}

				} catch (error) {
					if (error !== 'cancel') {
						console.error('提交失败，错误详情:', error);
						this.$message({
							type: 'error',
							message: '提交失败，请重试'
						});
					}
				}
			},

			// 生成申请单号
			generateApplicationNo() {
				const date = new Date();
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0');
				return `LT${year}${month}${day}${random}`;
			},

			// 清空选择
			clearSelection() {
				this.$refs.testGroupTree.setCheckedKeys([]); // 清空树选择
				this.selectedGroups = [];
				this.testDetails = [];
			},

			// 修改检验报告相关方法
			handleReportClick(row) {
				this.selectedReport = row;
				// 使用API返回的检验结果数据
				if (row.rawData && row.rawData.testResultList) {
					this.reportDetails = row.rawData.testResultList.map(item => ({
						itemName: item.chinesename,
						result: item.testresult,
						unit: item.unit,
						abnormalTip: item.hint === '↑' || item.hint === '↓' ? item.hint : '',
						referenceRange: item.ckfw || `${item.reflo || ''}-${item.refhi || ''}`,
						measuretime: item.measuretime,
						operator: item.operator
					}));
				} else {
					this.reportDetails = [];
				}
			},

			getReportStatusType(status) {
				const statusMap = {
					'已审核': 'success',
					'待审核': 'warning',
					'已退回': 'danger',
					'已作废': 'info',
					'待检验': 'info',
					'检验中': 'primary',
					'待复核': 'warning'
				};
				return statusMap[status] || 'info';
			},

			// 添加获取历史申请单的方法
			async fetchHistoryApplications() {
				try {
					const orgId = this.patientInfo.orgId || "45958b0c-d384-4dae-a1ee-5080cf9327b6";
					const revisitId = this.patientInfo.revisitId;
					
					console.log('orgId', orgId);
					console.log('revisitId', revisitId);
					
					if (!revisitId) {
						console.warn('复诊ID不存在，无法获取历史申请单');
						this.historyApplications = [];
						return;
					}
					
					// 使用复诊ID调用API获取历史申请单，确保revisitId是字符型
					const response = await commonAjaxNoEncrypt(
						'cas_ih_foshan.medicalSkillItemService',
						'findMedicalSkillInfoByRevisitId',
						[String(revisitId)]
					);
					
					if (response.code === 200) {
						// 过滤出检验类型的申请单（applyType === "3"）
						this.historyApplications = (response.body || []).filter(item => 
							item.applyType === "3"
						).map(item => ({
							status: this.getStatusText(item.status),
							applicationNo: item.applyNo || '未知',
							applyTime: item.applyTime || '未知',
							doctor: item.applyDoctor || '未知',
							testItems: item.skillItems ? item.skillItems.map(skill => skill.itemName).join(', ') : '未知',
							rawData: item // 保存原始数据用于详情展示
						}));
					} else {
						this.$message.error(response.msg || '获取历史申请单失败');
						this.historyApplications = [];
					}
				} catch (error) {
					console.error('获取历史申请单失败:', error);
					this.$message.error('获取历史申请单失败');
					this.historyApplications = [];
				}
			},
			
			// 添加状态文本转换方法
			getStatusText(status) {
				const statusMap = {
					'1': '待采集',
					'2': '已采集',
					'3': '已完成',
					'4': '已作废'
				};
				return statusMap[status] || '未知状态';
			},
			
			// 添加获取检验报告的方法
			async fetchLabReports() {
				try {
					const revisitId = this.patientInfo.revisitId;
					
					if (!revisitId) {
						console.warn('复诊ID不存在，无法获取检验报告');
						this.labReports = [];
						return;
					}
					
					// 使用复诊ID调用API获取检验报告，确保revisitId是字符型
					const response = await commonAjax(
						'cas_ih_foshan.medicalSkillItemService',
						'findLabReportByRevisitId',
						[String(revisitId)]
					);
					
					if (response.code === 200) {
						this.labReports = (response.body || []).map(item => ({
							reportStatus: this.getReportStatusText(item.reportStatus),
							reportNo: item.reportNo || '未知',
							reportTime: item.reportTime || '未知',
							reportDoctor: item.reportDoctor || '未知',
							testItems: item.testItems ? item.testItems.join(', ') : '未知',
							rawData: item // 保存原始数据用于详情展示
						}));
					} else {
						this.$message.error(response.msg || '获取检验报告失败');
						this.labReports = [];
					}
				} catch (error) {
					console.error('获取检验报告失败:', error);
					this.$message.error('获取检验报告失败');
					this.labReports = [];
				}
			},
			
			// 添加报告状态文本转换方法
			getReportStatusText(status) {
				const statusMap = {
					'1': '待检验',
					'2': '检验中',
					'3': '待复核',
					'4': '已审核',
					'5': '已退回',
					'6': '已作废'
				};
				return statusMap[status] || '未知状态';
			}
		},
		computed: {
			totalAmount() {
				return this.testDetails.reduce((sum, item) => sum + (item.price * (item.count || 1)), 0).toFixed(2);
			},
			// 添加提交按钮的禁用状态计算属性
			canSubmit() {
				return this.selectedGroups.length > 0 && this.testDetails.length > 0;
			}
		},
		watch: {
			// 监听patientInfo的变化
			'patientInfo.revisitId': {
				handler(newVal, oldVal) {
					if (newVal && newVal !== oldVal) {
						console.log('LabTest: 复诊ID变化，重新获取数据', newVal);
						// 重新获取历史申请单和检验报告
						this.fetchHistoryApplications();
						this.fetchLabReports();
					}
				},
				immediate: false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.lab-test-container {
		padding: 20px;
		background-color: #f5f7fa;
		min-height: calc(100vh - 60px);
	}

	.patient-info-card {
		margin-bottom: 20px;
	}

	.patient-info-header {
		margin-bottom: 20px;

		h3 {
			margin: 0;
			color: #303133;
		}
	}

	.info-item {
		display: flex;
		align-items: center;

		.label {
			color: #606266;
			margin-right: 8px;
			min-width: 80px;
		}

		.value {
			color: #303133;
		}
	}

	/* 添加工具栏样式 */
	.toolbar {
		background-color: #fff;
		padding: 10px 20px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title {
			font-size: 16px;
			font-weight: bold;
			color: #303133;
		}

		.right-buttons {
			display: flex;
			gap: 10px;

			.el-button {
				padding: 9px 15px;
			}
		}
	}

	.new-application {
		display: flex;
		gap: 20px;
		margin-top: 20px;
	}

	.left-panel {
		width: 25%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.middle-panel {
		width: 25%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.right-panel {
		width: 50%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.history-application {
		display: flex;
		gap: 20px;
		margin-top: 20px;
	}

	.history-left {
		width: 66.67%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.history-right {
		width: 33.33%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.panel-title {
		font-size: 16px;
		font-weight: bold;
		margin-bottom: 15px;
		color: #303133;
	}

	.abnormal {
		color: #f56c6c;
	}

	::v-deep .el-tree-node__content {
		height: 32px;
	}

	::v-deep .el-table {
		margin-top: 10px;
	}

	.total-amount {
		margin-top: 15px;
		text-align: right;
		padding-right: 20px;
		font-size: 14px;
		color: #606266;

		.price {
			font-size: 16px;
			font-weight: bold;
			color: #f56c6c;
			margin-left: 5px;
		}
	}

	.report-application {
		display: flex;
		gap: 20px;
		margin-top: 20px;
	}

	.report-left {
		width: 66.67%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.report-right {
		width: 33.33%;
		background-color: #fff;
		padding: 15px;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.report-summary {
		margin-top: 20px;
		padding: 15px;
		background-color: #f8f9fa;
		border-radius: 4px;

		.summary-title {
			font-size: 14px;
			font-weight: bold;
			color: #303133;
			margin-bottom: 8px;
			margin-top: 15px;

			&:first-child {
				margin-top: 0;
			}
		}

		.summary-content {
			font-size: 13px;
			color: #606266;
			line-height: 1.5;
			padding: 8px;
			background-color: #fff;
			border-radius: 3px;
			min-height: 40px;
		}
	}
</style>
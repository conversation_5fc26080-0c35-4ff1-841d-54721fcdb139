<template>
    <div class="inq-list">
         <div class="list-main-head">
            <div  class="fieldName" >
                <el-input size="small" v-model="patientName" clearable  class="shopValue" placeholder="发起人姓名"></el-input>
            </div>
            <div  class="fieldName" >
                <el-date-picker
                    v-model="startTime"
                    type="date"
                    placeholder="发起时间"
                    size="small"
                    value-format="yyyy-MM-dd"
                    class="shopValue"
                    :picker-options="pickerOptions0">
                </el-date-picker>
                <span style="float:left;margin-right:10px;">至</span>
                <el-date-picker
                    v-model="endTime"
                    type="date"
                    placeholder="结束时间"
                    size="small"
                    value-format="yyyy-MM-dd"
                    class="shopValue"
                    :picker-options="pickerOptions1">
                </el-date-picker>
            </div>
            <div  class="fieldName" >
                <el-select size="small" clearable class="shopValue" v-model="type" placeholder="启用情况">
                    <el-option label="全部类型" value=""></el-option>
                    <el-option v-for="item in typeList" :label="item.text" :value="item.key" :key="item.key"></el-option>
                </el-select>
            </div>
            <div  class="fieldName" >
                <el-select size="small" clearable class="shopValue" v-model="status" placeholder="消息状态">
                    <el-option label="全部状态" value=""></el-option>
                    <el-option label="未读" value="0"></el-option>
                    <el-option label="已读" value="1"></el-option>
                </el-select>
            </div>

            <div class="fieldName handleWrap">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
            </div>
            <div class="fieldName allRead">
                <el-button type="primary" size="small" @click="updateAllMsg">全部标记为已读</el-button>
            </div>
        </div>
        <el-table :data="msgList">
            <el-table-column label="消息类型" prop="businessTypeText" width="150">
            </el-table-column>
            <el-table-column label="发起对象" prop="fromName">
            </el-table-column>
            <el-table-column label="发起时间" prop="sendTime">
            </el-table-column>
            <el-table-column label="消息标题" prop="title">
                <template slot-scope="scope">
                    <span  @click="routerReplace(scope.row)" style="cursor: pointer;">{{scope.row.title}}</span>
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="status">
                <template slot-scope="scope">
                    <span v-if='scope.row.status==1' style="cursor: pointer;">已读</span>
                    <span v-else @click="updateMsgStatus(scope.row)" style="color: #52a3fc; cursor: pointer;">未读</span>
                </template>
            </el-table-column>
        </el-table>
        <!--分页-->
        <div class="pagination">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :current-page="pageNo" :page-sizes="[10, 20, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="pageSize">
            </el-pagination>
        </div>
    </div>
</template>
<script>
import {commonAjax,imgview} from '../../api/api.js';
import patientAvatar from '../../assets/img/pat_avatar.png';
export default {
    data() {
        let that = this;
        return {
            pickerOptions0: {
                disabledDate(time) {
                    if(new Date(that.endTime).getTime()){
                        return time.getTime() > (new Date(that.endTime)).getTime() - 1*60*60*24*1000;
                    }

                }
            },
            pickerOptions1: {
                disabledDate(time) {
                    if(new Date(that.startTime).getTime()){
                        return time.getTime() < (new Date(that.startTime)).getTime();
                    }
                }
            },
            typeList:[],
            msgList:[],
            patientName:"",
            startTime:"",
            endTime:"",
            type:"",
            status:"",
            total:0,
            pageNo:1,
            pageSize:10,

        }
    },
    methods:{
        //获取字典
        dictionaryRequest() {
            let arr = ["cfs.dic.ih_messageType"];
            commonAjax("cas.multipleDictionaryService", "findDic", '[' + JSON.stringify(arr) + ']').then(res => {
                if (res.code == 200) {
                    this.typeList = res.body[0].items;
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    });
                }
            });
        },
        //获取消息列表
        getMsgList() {
            let params = {
                "type":this.type,   //
                "patientName":this.patientName,
                "startTime":this.startTime,
                "endTime":this.endTime,
                "status":this.status,
                "pageNo":this.pageNo,
                "pageSize":this.pageSize,
            }
            commonAjax("cas.foshan_messageAdminService","messageAllList",[params]).then(res => {
                if(res.code == 200) {
                    this.msgList = res.body.data;
                    this.total = res.body.total;
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    })
                }
            })
        },

        handleSizeChange(val) {
            this.pageSize = val;
            this.getMsgList();
        },
        handleCurrentChange(val) {
            this.pageNo = val;
            this.getMsgList();
        },
        searchList() {
            this.getMsgList();
        },
        clearParams() {
            this.patientName = "";
            this.startTime = "";
            this.endTime = "";
            this.type = "";
            this.status = "";
            this.pageNo = 1;
            this.pageSize = 10;
            this.getMsgList();
        },
        //回复咨询
        showChat(row) {
            console.log(row)
            let buzType = '';
            if(row.itemCode == '01') {
                buzType = '30';
            } else if(row.itemCode == '02') {
                buzType = '40';
            } else if(row.itemCode == '03') {
                buzType = '60';
            };
            sessionStorage.setItem("accId",row.patientAccId)
            sessionStorage.setItem("buzType",buzType);   //30是图文，40是视频，60是会诊
            sessionStorage.setItem("buzId",row.orderDetailId);
            sessionStorage.setItem("patientName",row.userName);
            let avatarUrl = row.avatar?row.avatar:patientAvatar; //本地处理用户头像
            sessionStorage.setItem("avatarUrl",avatarUrl);

            window.yunXin.openChatBox(row.patientAccId,"p2p",row.itemCode);

        },
        //点击标题跳转
        routerReplace(row) {
            if(row.status == 0) {
                this.updateMsgStatus(row);
                this.findBynotificationId(row.notificationId,row.businessType);

            } else {
                //如果是已读消息，直接跳转
                this.findBynotificationId(row.notificationId,row.businessType);
            }
        },
        //更新未读消息状态
        updateMsgStatus(row) {

                let params = [row.notificationId];
                commonAjax("cas.foshan_messageAdminService","updateAllReadFlag",[params]).then(res => {
                    if(res && res.code == 200) {
                        this.getMsgList();
                    } else {
                        this.$message({
                            type:"error",
                            message:res.msg,
                        })
                    }
                })

        },
        //通过消息id找到咨询，会诊信息
        findBynotificationId(id,type) {
            commonAjax("cas.foshan_messageAdminService","findBynotificationId",`[${id}]`).then(res => {
                if(res && res.code == 200) {
                    //判断是否为评价消息
                    if(type && type == '0609') {
                        if(res.body.orderNo) {  //评价     //预约时间未到的订单不做处理
                            sessionStorage.setItem("orderNo",res.body.orderNo);
                            this.$router.push("patientEvaDetail");
                        }

                    } else {
                        let {orderDetailId:buzId}=res.body;
                    //非评价消息
                        if(res.body.itemCode == '02') {  //视频咨询     //预约时间未到的订单不做处理
                            if(res.body.currentStatus == '53') {
                                //已结束状态跳转到咨询记录
                                this.$router.push({
                                    path:'iquRecord',
                                    query:{
                                        buzId,
                                    },
                                })
                            }else if(res.body.currentStatus == '45') {
                                //已取消
                                this.$router.push({
                                    path:'iquRecord',
                                    query:{
                                        buzId,
                                    },
                                })
                            } else if(res.body.currentStatus == '52') {
                                //咨询中状态打开对应聊天面板
                                this.showChat(res.body)
                            } else if(res.body.currentStatus == '51') {
                                this.$message({
                                    type:'warning',
                                    message:'该患者预约视频时间未到',
                                })
                            }
                        } else if(res.body.itemCode == '03'){//基层会诊
                            if(res.body.currentStatus == '58') {  //等待咨询状态就打开聊天窗口
                                this.showChat(res.body)
                            } else {
                                this.$router.push({
                                    path:'iquRecord',
                                    query:{
                                        buzId,
                                    },
                                })
                            }

                        }

                    }
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    })
                }
            });
        },

        //全部未读消息变为已读
        updateAllMsg() {
            this.$confirm('是否将全部消息标记为已读?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                commonAjax("cas.foshan_messageAdminService","updateAllUnReadStatus",[]).then(res => {
                    if(res && res.code == 200) {
                        this.getMsgList();
                        vm.$emit('onHeaderNum',0)
                    } else {
                        this.$message({
                            type:"error",
                            message:res.msg,
                        })
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消'
                });
            });
        },
    },
    mounted() {
        this.dictionaryRequest();
        this.getMsgList();
    }
}
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .inq-list{
        @include titleAndTable;
    }
</style>


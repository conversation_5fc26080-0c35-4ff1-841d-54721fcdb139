<template>
    <div style="width: 100%;position: relative;">
        <el-input placeholder="请输入" v-model="doctorName" @input="searchSelect"></el-input>
        <div :class="{ searchSelect: true, active: show }" :style="{ left:`${left}px`, top:`${top}px`}">
            <ul class="dataList">
                <li
                    v-for="(item,index) in dataList"
                    :key="index"
                    :class="{
                        active:index==mouseoverIndex,
                    }"
                    @click.stop="selectItem(item,index)"
                    @mouseover="mouseoverIndex=index"
                >
                    <!-- <div class="hospName">医院</div> -->
                    <!-- <div class="deptName"></div> -->
                    <div class="docName">{{item.orgName}}——{{item.standardDeptName}}——{{item.doctorName}}.</div>
                </li>
            </ul>
            <div v-show="!dataList.length" class="noData">
                未搜索到数据
            </div>
        </div>
    </div>
</template>

<script>
    // 用法： 
    // import searchSelect from 'components/common/searchSelect'
    // show: false,
    // components:{
    //     searchSelect
    // },
    // <search-select
    //     v-if="show"
    //     @emitSelectItem="setelectItem"
    //     :show="true"
    //     :top="40"
    //     :left="10"
    //     :params="params"
    // ></search-select>  
    // searchSelect (val) {
    //   if (val) {
    //     this.show = true
    //     console.log(val, 'searchSelect')
    //   } else {
    //     this.show = false
    //   }
    // },
    // setelectItem (val) {
    //   console.log(val, 'search')
    //   this.params.orderNo = val.value
    //   this.show = false
    // },
    import { commonAjax } from '../../api/api'
    function bind (obj, evname, fn) {
        if (obj.addEventListener) {
            obj.addEventListener(evname,fn,false)
        } else {
            obj.attachEvent('on'+evname,function () {
                fn.call(obj)
            })
        }
    }
    //取消绑定，可重复取消('事件名称'必须加引号)
    function unbind (obj, evname, fn){
        if (obj.removeEventListener) {
            obj.removeEventListener(evname, fn, false)
        } else {
            obj.detachEvent('on'+evname,fn)
        }
    }
    export default{
        //绑定事件，可重复绑定('事件名称'必须加引号)
        data(){
            return{
                mouseoverIndex: -1,
                dataList: [],
                show: false,
                doctorName: '',
                doctorId: ''
            }
        },
        props:{
            left:{//偏移父元素左边距离
                type:Number,
                default:0,
            },
            top:{//偏移父元素顶部距离
                type:Number,
                default:50,
            },
            params:{//接口传参
                type:[Object,Array],
                default:null,
            },
        },
        mounted(){
            //请求数据列表
            this.getDataList();
            bind(document,'click',this.close)
        },
        beforeDestroy(){
            unbind(document,'click',this.close)
        },
        watch: {
        　　params: {
        　　　　handler(newValue, oldValue) {
                   this.doctorName = newValue.doctorName
        　　　　　　this.getDataList()
        　　　　},
        　　　　deep: true
        　　}
        },
        methods:{
            close () {
                this.emit({doctorName: this.doctorName, doctorId: this.doctorId})
                this.show = false
            },
            searchSelect (val) {
                if (val) {
                    this.show = true
                    this.getDataList()
                } else {
                    this.show = false
                }
            },
            getDataList(){
                let orgId = this.params.orgId ? this.params.orgId : ''
                let deptId = this.params.deptId ? this.params.deptId : ''
                commonAjax('cas.szmb_orderManageService', 'queryOpenDoctor', [orgId, deptId, this.doctorName]).then(res => {
                    if (res && res.code == 200) {
                        this.dataList = res.body
                    } else {
                        this.dataList = []
                        this.$message({type: 'error', message: res.msg})
                    }
                })
            },
            selectItem(item, index){
                this.doctorId = item.doctorId
                this.doctorName = item.doctorName
                this.emit(item)
            },
            emit(val) {
                let ret = this.checkPropHas(this.dataList, ['doctorId', 'doctorName'], [this.doctorId, this.doctorName])
                if (!ret) {
                    this.doctorId = ''
                    this.doctorName = ''
                    val.doctorId = ''
                    val.doctorName = ''
                } else {
                    this.doctorName = val.doctorName
                    this.doctorId = val.doctorId
                }
                this.$emit('emitSelectItem', val)
                this.show = false
            },
            checkPropHas (arr = [], prop = [], propValue = []) {
                let ret = false
                arr.map((v) => {
                    if (v[prop[0]] == propValue[0] && v[prop[1]] == propValue[1]) {
                        ret = true
                    }
                })
                return ret
            }
        },
    }
</script>

<style lang="less" scoped>
.searchSelect{
    overflow-y: auto;
    width: 400px;
    position: absolute;
    background-color: #fff;
    left: 0;
    top: 30px;
    z-index: 1000;
    display: none;
    overflow: hidden;
    border: 1px solid rgb(191, 217, 216);
    border-radius: 10px !important;
    &.active{
        display: block;
    }
    .dataList{
        max-height: 340px;
        overflow-y: auto;
        li{
            padding: 5px 20px;
            line-height: 24px;
            cursor: pointer;
            color: #606266;
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            &.active{
                background-color: #e2f2ff;
                color: #52a3fc;
            }
            .hospName {
                text-overflow: ellipsis;
                overflow: hidden;
            }
            .deppName {
                text-overflow: ellipsis;
                overflow: hidden;
            }
            .docName {
                font-size: 12px;
                text-overflow: ellipsis;
                overflow: hidden;
            }
        }
    }
    .noData{
        padding: 20px 0;
        line-height: 30px;
        text-align: center;
    }
}
</style>
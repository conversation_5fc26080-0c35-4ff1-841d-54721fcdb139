<template>
    <div class="hospitalization-form">
        <el-dialog
            title="入院登记表"
            :visible.sync="dialogVisible"
            width="80%"
            :before-close="handleDialogClose">
            <el-form :model="form" label-width="120px" :rules="rules" ref="hospitalizationForm">
                <!-- 基本信息 -->
                <el-divider content-position="left">基本信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="患者姓名" prop="patientName">
                            <el-input v-model="form.patientName" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="性别" prop="gender">
                            <el-input v-model="form.gender" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="年龄" prop="age">
                            <el-input v-model="form.age" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="出生年月" prop="birthDate">
                            <el-input v-model="form.birthDate" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="婚姻状况" prop="maritalStatus">
                            <el-select v-model="form.maritalStatus" placeholder="请选择婚姻状况">
                                <el-option label="未婚" value="未婚"></el-option>
                                <el-option label="已婚" value="已婚"></el-option>
                                <el-option label="离异" value="离异"></el-option>
                                <el-option label="丧偶" value="丧偶"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="国籍" prop="nationality">
                            <el-input v-model="form.nationality" placeholder="请输入国籍"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="民族" prop="ethnicity">
                            <el-input v-model="form.ethnicity" placeholder="请输入民族"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="身份证号" prop="idCardNumber">
                            <el-input 
                                v-model="form.idCardNumber" 
                                placeholder="请输入身份证号，将自动填充性别和出生年月"
                                @blur="handleIdCardBlur">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="出生地" prop="birthPlace">
                            <el-input v-model="form.birthPlace" placeholder="请输入出生地"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="籍贯" prop="nativePlace">
                            <el-input v-model="form.nativePlace" placeholder="请输入籍贯"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="手机号码" prop="mobilePhone">
                            <el-input v-model="form.mobilePhone" placeholder="请输入手机号码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系电话" prop="contactPhone">
                            <el-input v-model="form.contactPhone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 现住址信息 -->
                <el-divider content-position="left">现住址信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="现住址省" prop="currentAddressProvince">
                            <el-select v-model="form.currentAddressProvince" placeholder="请选择省" @change="handleProvinceChange">
                                <el-option
                                    v-for="province in provinceList"
                                    :key="province.code"
                                    :label="province.name"
                                    :value="province.name">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现住址市" prop="currentAddressCity">
                            <el-select v-model="form.currentAddressCity" placeholder="请选择市" @change="handleCityChange">
                                <el-option
                                    v-for="city in cityList"
                                    :key="city.code"
                                    :label="city.name"
                                    :value="city.name">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现住址县" prop="currentAddressCounty">
                            <el-select v-model="form.currentAddressCounty" placeholder="请选择县">
                                <el-option
                                    v-for="county in countyList"
                                    :key="county.code"
                                    :label="county.name"
                                    :value="county.name">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="现住址邮编" prop="currentAddressPostcode">
                            <el-input v-model="form.currentAddressPostcode" placeholder="请输入邮编"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="现住址" prop="currentAddress">
                    <el-input type="textarea" v-model="form.currentAddress" placeholder="请输入详细地址"></el-input>
                </el-form-item>

                <!-- 户口地址信息 -->
                <el-divider content-position="left">户口地址信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="户口地址" prop="householdAddress">
                            <el-input type="textarea" v-model="form.householdAddress" placeholder="请输入户口地址"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="户口邮编" prop="householdPostcode">
                            <el-input v-model="form.householdPostcode" placeholder="请输入邮编"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 单位信息 -->
                <el-divider content-position="left">单位信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="单位地址" prop="workAddress">
                            <el-input type="textarea" v-model="form.workAddress" placeholder="请输入单位地址"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="单位电话" prop="workPhone">
                            <el-input v-model="form.workPhone" placeholder="请输入单位电话"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="单位邮编" prop="workPostcode">
                            <el-input v-model="form.workPostcode" placeholder="请输入邮编"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 联系人信息 -->
                <el-divider content-position="left">联系人信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="联系人姓名" prop="contactPersonName">
                            <el-input v-model="form.contactPersonName" placeholder="请输入联系人姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系人电话" prop="contactPersonPhone">
                            <el-input v-model="form.contactPersonPhone" placeholder="请输入联系人电话"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系人关系" prop="contactPersonRelation">
                            <el-select v-model="form.contactPersonRelation" placeholder="请选择关系">
                                <el-option label="父亲" value="父亲"></el-option>
                                <el-option label="母亲" value="母亲"></el-option>
                                <el-option label="配偶" value="配偶"></el-option>
                                <el-option label="子女" value="子女"></el-option>
                                <el-option label="兄弟姐妹" value="兄弟姐妹"></el-option>
                                <el-option label="朋友" value="朋友"></el-option>
                                <el-option label="其他" value="其他"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="联系人邮编" prop="contactPersonPostcode">
                            <el-input v-model="form.contactPersonPostcode" placeholder="请输入邮编"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="联系人地址" prop="contactPersonAddress">
                    <el-input type="textarea" v-model="form.contactPersonAddress" placeholder="请输入联系人地址"></el-input>
                </el-form-item>

                <!-- 监护人信息 -->
                <el-divider content-position="left">监护人信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="监护人姓名" prop="guardianName">
                            <el-input v-model="form.guardianName" placeholder="请输入监护人姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="监护人电话" prop="guardianPhone">
                            <el-input v-model="form.guardianPhone" placeholder="请输入监护人电话"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="监护人关系" prop="guardianRelation">
                            <el-select v-model="form.guardianRelation" placeholder="请选择关系">
                                <el-option label="父亲" value="父亲"></el-option>
                                <el-option label="母亲" value="母亲"></el-option>
                                <el-option label="配偶" value="配偶"></el-option>
                                <el-option label="子女" value="子女"></el-option>
                                <el-option label="兄弟姐妹" value="兄弟姐妹"></el-option>
                                <el-option label="朋友" value="朋友"></el-option>
                                <el-option label="其他" value="其他"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 住院信息 -->
                <el-divider content-position="left">住院信息</el-divider>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="住院号" prop="inHospitalId">
                            <el-input v-model="form.inHospitalId" placeholder="请输入住院号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="床号" prop="bedNumber" required>
                            <el-input v-model="form.bedNumber" placeholder="请输入床号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="科室" prop="department" required>
                            <el-input v-model="form.department" placeholder="请输入科室"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="养老机构" prop="nursingHome">
                            <el-select v-model="form.nursingHome" placeholder="请选择养老机构">
                                <el-option
                                    v-for="home in nursingHomeList"
                                    :key="home.id"
                                    :label="home.name"
                                    :value="home.name">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="护理等级" prop="nurseLevel" required>
                            <el-select v-model="form.nurseLevel" placeholder="请选择护理等级">
                                <el-option label="特级护理" value="特级护理"></el-option>
                                <el-option label="一级护理" value="一级护理"></el-option>
                                <el-option label="二级护理" value="二级护理"></el-option>
                                <el-option label="三级护理" value="三级护理"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="责任护士" prop="responsibleNurse" required>
                            <el-input v-model="form.responsibleNurse" placeholder="请输入责任护士"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="主治医师" prop="attendingDoctor" required>
                            <el-input v-model="form.attendingDoctor" placeholder="请输入主治医师"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="入院日期" prop="admissionDate" required>
                            <el-date-picker
                                v-model="form.admissionDate"
                                type="datetime"
                                placeholder="选择入院日期时间"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="开单日期" prop="orderDate">
                            <el-date-picker
                                v-model="form.orderDate"
                                type="datetime"
                                placeholder="选择开单日期时间"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="入院诊断" prop="diagnosis" required>
                    <el-input type="textarea" v-model="form.diagnosis" placeholder="请输入入院诊断"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleDialogClose">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios'

export default {
    name: 'HospitalizationForm',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            dialogVisible: false,
            form: {
                id: null,
                patientId: '',
                patientName: '',
                gender: '',
                age: '',
                bedNumber: '',
                cardNo: '',
                diagnosis: '',
                department: '',
                nurseLevel: '',
                responsibleNurse: '',
                attendingDoctor: '',
                admissionDate: '',
                birthDate: '',
                maritalStatus: '',
                nationality: '',
                ethnicity: '',
                birthPlace: '',
                nativePlace: '',
                idCardNumber: '',
                mobilePhone: '',
                contactPhone: '',
                currentAddressProvince: '',
                currentAddressCity: '',
                currentAddressCounty: '',
                currentAddress: '',
                currentAddressPostcode: '200000',
                householdAddress: '',
                householdPostcode: '200000',
                workAddress: '',
                workPhone: '',
                workPostcode: '200000',
                contactPersonName: '',
                contactPersonPhone: '',
                contactPersonRelation: '',
                contactPersonAddress: '',
                contactPersonPostcode: '200000',
                guardianName: '',
                guardianPhone: '',
                guardianRelation: '',
                inHospitalId: '',
                nursingHome: '',
                orderDate: ''
            },
            rules: {
                bedNumber: [
                    { required: true, message: '请输入床号', trigger: 'blur' }
                ],
                department: [
                    { required: true, message: '请输入科室', trigger: 'blur' }
                ],
                diagnosis: [
                    { required: true, message: '请输入入院诊断', trigger: 'blur' }
                ],
                nurseLevel: [
                    { required: true, message: '请选择护理等级', trigger: 'change' }
                ],
                responsibleNurse: [
                    { required: true, message: '请输入责任护士', trigger: 'blur' }
                ],
                attendingDoctor: [
                    { required: true, message: '请输入主治医师', trigger: 'blur' }
                ],
                admissionDate: [
                    { required: true, message: '请选择入院日期', trigger: 'change' }
                ],
                mobilePhone: [
                    { 
                        pattern: /^$|^1[3-9]\d{9}$/, 
                        message: '请输入正确的手机号码', 
                        trigger: 'blur' 
                    }
                ],
                contactPhone: [
                    { 
                        pattern: /^$|^1[3-9]\d{9}$|^(\d{3,4}-)?\d{7,8}$/, 
                        message: '请输入正确的电话号码（支持手机号或固定电话）', 
                        trigger: 'blur' 
                    }
                ],
                contactPersonPhone: [
                    { 
                        pattern: /^$|^1[3-9]\d{9}$|^(\d{3,4}-)?\d{7,8}$/, 
                        message: '请输入正确的电话号码（支持手机号或固定电话）', 
                        trigger: 'blur' 
                    }
                ],
                idCardNumber: [
                    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
                ],
                workPhone: [
                    { 
                        pattern: /^$|^1[3-9]\d{9}$|^(\d{3,4}-)?\d{7,8}$/, 
                        message: '请输入正确的电话号码（支持手机号或固定电话）', 
                        trigger: 'blur' 
                    }
                ],
                guardianPhone: [
                    { 
                        pattern: /^$|^1[3-9]\d{9}$|^(\d{3,4}-)?\d{7,8}$/, 
                        message: '请输入正确的电话号码（支持手机号或固定电话）', 
                        trigger: 'blur' 
                    }
                ]
            },
            provinceList: [
                { code: '110000', name: '北京市' },
                { code: '120000', name: '天津市' },
                { code: '130000', name: '河北省' },
                { code: '140000', name: '山西省' },
                { code: '150000', name: '内蒙古自治区' },
                { code: '210000', name: '辽宁省' },
                { code: '220000', name: '吉林省' },
                { code: '230000', name: '黑龙江省' },
                { code: '310000', name: '上海市' },
                { code: '320000', name: '江苏省' },
                { code: '330000', name: '浙江省' },
                { code: '340000', name: '安徽省' },
                { code: '350000', name: '福建省' },
                { code: '360000', name: '江西省' },
                { code: '370000', name: '山东省' },
                { code: '410000', name: '河南省' },
                { code: '420000', name: '湖北省' },
                { code: '430000', name: '湖南省' },
                { code: '440000', name: '广东省' },
                { code: '450000', name: '广西壮族自治区' },
                { code: '460000', name: '海南省' },
                { code: '500000', name: '重庆市' },
                { code: '510000', name: '四川省' },
                { code: '520000', name: '贵州省' },
                { code: '530000', name: '云南省' },
                { code: '540000', name: '西藏自治区' },
                { code: '610000', name: '陕西省' },
                { code: '620000', name: '甘肃省' },
                { code: '630000', name: '青海省' },
                { code: '640000', name: '宁夏回族自治区' },
                { code: '650000', name: '新疆维吾尔自治区' }
            ],
            cityList: [],
            countyList: [],
            nursingHomeList: [
                { id: 1, name: '彩虹湾' }
            ]
        }
    },
    watch: {
        visible(val) {
            this.dialogVisible = val
            if (val) {
                this.initForm()
            }
        },
        dialogVisible(val) {
            this.$emit('update:visible', val)
        },
        // 监听身份证号码变化
        'form.idCardNumber': {
            handler(newVal) {
                if (newVal && newVal.length === 18) {
                    const { gender, birthDate, age } = this.parseIdCard(newVal)
                    // 只有当当前字段为空时才自动填充
                    if (!this.form.gender) {
                        this.form.gender = gender
                    }
                    if (!this.form.birthDate) {
                        this.form.birthDate = birthDate
                    }
                    if (!this.form.age) {
                        this.form.age = age
                    }
                }
            },
            immediate: false
        }
    },
    methods: {
        initForm() {
            // 获取当前时间并格式化为 yyyy-MM-dd HH:mm:ss
            const now = new Date()
            const formattedDate = now.getFullYear() + '-' + 
                String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                String(now.getDate()).padStart(2, '0') + ' ' + 
                String(now.getHours()).padStart(2, '0') + ':' + 
                String(now.getMinutes()).padStart(2, '0') + ':' + 
                String(now.getSeconds()).padStart(2, '0')
            
            // 根据身份证号码获取性别和出生年月
            const idCard = this.patientInfo.idCard || ''
            const { gender, birthDate, age } = this.parseIdCard(idCard)
            
            this.form = {
                id: null,
                patientId: this.patientInfo.patientId || '',
                patientName: this.patientInfo.patientName || '',
                gender: this.patientInfo.gender || gender || '',
                age: this.patientInfo.age || age || '',
                cardNo: this.patientInfo.idCard || '',
                bedNumber: '',
                diagnosis: '',
                department: '',
                nurseLevel: '',
                responsibleNurse: '',
                attendingDoctor: '',
                admissionDate: formattedDate,
                birthDate: this.patientInfo.birthDate || birthDate || '',
                maritalStatus: '',
                nationality: '中国',
                ethnicity: '',
                birthPlace: '',
                nativePlace: '',
                idCardNumber: this.patientInfo.idCard || '',
                mobilePhone: this.patientInfo.phone || '',
                contactPhone: this.patientInfo.phone || '',
                currentAddressProvince: '',
                currentAddressCity: '',
                currentAddressCounty: '',
                currentAddress: this.patientInfo.address || '',
                currentAddressPostcode: '200000',
                householdAddress: '',
                householdPostcode: '200000',
                workAddress: '',
                workPhone: '',
                workPostcode: '200000',
                contactPersonName: '',
                contactPersonPhone: '',
                contactPersonRelation: '',
                contactPersonAddress: '',
                contactPersonPostcode: '200000',
                guardianName: '',
                guardianPhone: '',
                guardianRelation: '',
                inHospitalId: '',
                nursingHome: this.nursingHomeList.length > 0 ? this.nursingHomeList[0].name : '',
                orderDate: formattedDate
            }
            
            if (this.$refs.hospitalizationForm) {
                this.$refs.hospitalizationForm.clearValidate()
            }
        },
        
        // 根据身份证号码解析性别和出生年月
        parseIdCard(idCard) {
            if (!idCard || idCard.length !== 18) {
                return { gender: '', birthDate: '', age: '' }
            }
            
            try {
                // 提取出生年月日
                const year = idCard.substring(6, 10)
                const month = idCard.substring(10, 12)
                const day = idCard.substring(12, 14)
                const birthDate = `${year}-${month}-${day}`
                
                // 计算年龄
                const birth = new Date(year, month - 1, day)
                const today = new Date()
                let age = today.getFullYear() - birth.getFullYear()
                const monthDiff = today.getMonth() - birth.getMonth()
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                    age--
                }
                
                // 提取性别（第17位数字，奇数为男，偶数为女）
                const genderCode = parseInt(idCard.substring(16, 17))
                const gender = genderCode % 2 === 1 ? '男' : '女'
                
                return {
                    gender,
                    birthDate,
                    age: age.toString()
                }
            } catch (error) {
                console.error('解析身份证号码失败:', error)
                return { gender: '', birthDate: '', age: '' }
            }
        },
        handleDialogClose() {
            this.dialogVisible = false
            this.$emit('close')
        },
        async submitForm() {
            try {
                // 表单验证
                const valid = await this.$refs.hospitalizationForm.validate()
                if (!valid) {
                    return
                }
                
                // 确保时间格式正确
                const formData = { ...this.form }
                
                // 如果入院日期是Date对象，转换为字符串格式
                if (formData.admissionDate instanceof Date) {
                    formData.admissionDate = formData.admissionDate.getFullYear() + '-' + 
                        String(formData.admissionDate.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(formData.admissionDate.getDate()).padStart(2, '0') + ' ' + 
                        String(formData.admissionDate.getHours()).padStart(2, '0') + ':' + 
                        String(formData.admissionDate.getMinutes()).padStart(2, '0') + ':' + 
                        String(formData.admissionDate.getSeconds()).padStart(2, '0')
                }
                
                // 如果开单日期是Date对象，转换为字符串格式
                if (formData.orderDate instanceof Date) {
                    formData.orderDate = formData.orderDate.getFullYear() + '-' + 
                        String(formData.orderDate.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(formData.orderDate.getDate()).padStart(2, '0') + ' ' + 
                        String(formData.orderDate.getHours()).padStart(2, '0') + ':' + 
                        String(formData.orderDate.getMinutes()).padStart(2, '0') + ':' + 
                        String(formData.orderDate.getSeconds()).padStart(2, '0')
                }
                
                console.log('提交的住院信息：', formData)
                const response = await axios.post('/api/emr/hospitalization/save', formData)
                if (response.data.code === 200) {
                    this.$message.success('住院信息保存成功')
                    this.dialogVisible = false
                    this.$emit('success')
                } else {
                    this.$message.error(response.data.message || '保存失败')
                }
            } catch (error) {
                console.error('保存住院信息失败:', error)
                this.$message.error('保存住院信息失败')
            }
        },
        handleProvinceChange() {
            // 清空市和县的选择
            this.form.currentAddressCity = ''
            this.form.currentAddressCounty = ''
            this.cityList = []
            this.countyList = []
            
            // 根据选择的省加载对应的市
            if (this.form.currentAddressProvince) {
                this.loadCityList(this.form.currentAddressProvince)
            }
        },
        handleCityChange() {
            // 清空县的选择
            this.form.currentAddressCounty = ''
            this.countyList = []
            
            // 根据选择的市加载对应的县
            if (this.form.currentAddressCity) {
                this.loadCountyList(this.form.currentAddressCity)
            }
        },
        loadCityList(provinceName) {
            // 这里可以根据实际需求从API获取数据，暂时使用模拟数据
            const cityData = {
                '上海市': [
                    { code: '310100', name: '上海市' }
                ],
                '北京市': [
                    { code: '110100', name: '北京市' }
                ],
                '广东省': [
                    { code: '440100', name: '广州市' },
                    { code: '440300', name: '深圳市' },
                    { code: '440600', name: '佛山市' },
                    { code: '441900', name: '东莞市' }
                ],
                '江苏省': [
                    { code: '320100', name: '南京市' },
                    { code: '320200', name: '无锡市' },
                    { code: '320500', name: '苏州市' },
                    { code: '320600', name: '南通市' }
                ]
            }
            
            this.cityList = cityData[provinceName] || []
        },
        loadCountyList(cityName) {
            // 这里可以根据实际需求从API获取数据，暂时使用模拟数据
            const countyData = {
                '上海市': [
                    { code: '310101', name: '黄浦区' },
                    { code: '310104', name: '徐汇区' },
                    { code: '310105', name: '长宁区' },
                    { code: '310106', name: '静安区' },
                    { code: '310107', name: '普陀区' },
                    { code: '310109', name: '虹口区' },
                    { code: '310110', name: '杨浦区' },
                    { code: '310112', name: '闵行区' },
                    { code: '310113', name: '宝山区' },
                    { code: '310114', name: '嘉定区' },
                    { code: '310115', name: '浦东新区' },
                    { code: '310116', name: '金山区' },
                    { code: '310117', name: '松江区' },
                    { code: '310118', name: '青浦区' },
                    { code: '310120', name: '奉贤区' },
                    { code: '310151', name: '崇明区' }
                ],
                '北京市': [
                    { code: '110101', name: '东城区' },
                    { code: '110102', name: '西城区' },
                    { code: '110105', name: '朝阳区' },
                    { code: '110106', name: '丰台区' },
                    { code: '110107', name: '石景山区' },
                    { code: '110108', name: '海淀区' },
                    { code: '110109', name: '门头沟区' },
                    { code: '110111', name: '房山区' },
                    { code: '110112', name: '通州区' },
                    { code: '110113', name: '顺义区' },
                    { code: '110114', name: '昌平区' },
                    { code: '110115', name: '大兴区' },
                    { code: '110116', name: '怀柔区' },
                    { code: '110117', name: '平谷区' },
                    { code: '110118', name: '密云区' },
                    { code: '110119', name: '延庆区' }
                ]
            }
            
            this.countyList = countyData[cityName] || []
        },
        handleIdCardBlur() {
            // 身份证号码失焦时自动填充性别和出生年月
            const idCard = this.form.idCardNumber
            if (idCard && idCard.length === 18) {
                const { gender, birthDate, age } = this.parseIdCard(idCard)
                
                // 只有当当前字段为空时才自动填充
                if (!this.form.gender) {
                    this.form.gender = gender
                }
                if (!this.form.birthDate) {
                    this.form.birthDate = birthDate
                }
                if (!this.form.age) {
                    this.form.age = age
                }
                
                // 显示提示信息
                if (gender && birthDate) {
                    this.$message.success(`已自动填充：性别 ${gender}，出生日期 ${birthDate}，年龄 ${age}岁`)
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.hospitalization-form {
    ::v-deep .el-dialog__body {
        padding: 10px 20px;
    }

    ::v-deep .el-divider__text {
        background-color: #f5f7fa;
        color: #409eff;
        font-weight: bold;
        font-size: 14px;
    }

    ::v-deep .el-form-item__label {
        font-weight: 500;
    }

    ::v-deep .el-dialog {
        max-height: 90vh;
        overflow-y: auto;
    }

    ::v-deep .el-dialog__body {
        max-height: calc(90vh - 120px);
        overflow-y: auto;
    }

    ::v-deep .el-select {
        width: 100%;
    }

    ::v-deep .el-form-item__content {
        line-height: 32px;
    }

    ::v-deep .el-input.is-disabled .el-input__inner {
        background-color: #f5f7fa;
        border-color: #e4e7ed;
        color: #606266;
    }
}
</style> 
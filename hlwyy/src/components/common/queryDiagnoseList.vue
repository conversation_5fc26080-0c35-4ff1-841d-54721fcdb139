<template>
    <div
        :class="{
            queryDiagnoseList:true,
            readonly,
        }"
        :style="{
            zIndex,
        }"
        ref="queryDiagnoseList"
    >
        <template v-if="!readonly">
            <div class="list">
                <ul
                    v-for="(item,index) in 2"
                    :key="index"
                    :class="{
                        visible:index==0,
                    }"
                >
                    <li
                        v-for="(item1,index1) in list"
                        :key="index1"
                        @click="deleteItem(item1,index1)"
                    >
                        {{item1&&item1.icdName}}
                    </li>
                </ul>
            </div>
            <div class="inputWrap">
                <input
                    v-model.trim="params[0].keyWord"
                    type="text"
                    @input="search($event)"
                    @blur="params[0].keyWord=''"
                    @keydown.prevent.enter="enterSelect"
                    @keydown.prevent.up="changeSelectIndex('up')"
                    @keydown.prevent.down="changeSelectIndex('down')"
                />
            </div>

            <pagingPopup
                v-if="showPopup"
                :api="findDiseases"
                :params="params"
                :show="true"
                :top="34"
                :selectFn="selectItem"
            />
        </template>
        <input
            v-else
            v-model="icdNames"
            readonly="true"
            type="text"
            class="icdNames"
        />
    </div>
</template>

<script>
    import vm from 'src/main';
    import pagingPopup from 'components/common/pagingPopup';
    import {findDiseases,findMzDiagnose,saveMzDiagnose,removeMzDiagnose} from 'services';
    import {copyJson,alerts,sStore,bind,unbind} from 'js/yydjs';

    export default{
        data(){
            let diagnoseJson=sStore.get('diagnoseJson')||{};
            let {idList=[],list=[]}=diagnoseJson;

            return{
                showPopup:false,
                idList,
                list,
                params:[{
                    icdVersion:'',
                    keyWord:'',
                }],
                params1:[{
                    diagnoseRecordId:'',// 主键，有则为修改，无则为新增
                    mpiId:'',
                    revisitId:'',
                    masterFlag:1,
                    icdCode:'',
                    icdName:'',
                }],
                delayTimer:null,
                searchTimer:null,
                saveDataList:[],
                deleteCount:0,
                findDiseases,
            }
        },

        computed:{
            icdNames(){
                let {list=[]}=this;

                return list.map((item)=>item.icdName).join('|');
            },
        },

        /*
            <queryDiagnoseList
                :changeFn="changeDiagnoseList"
            />
        */

        props:{
            zIndex:{//显示层级
                type:Number,
                default:10,
            },
            revisitId:{//复诊id
                type:Number,
                default(){
                    return -1;
                },
            },
            mpiId:{//患者id
                type:String,
                default(){
                    return '';
                },
            },
            deleteFn:{//删除时触发的回调函数
                type:Function,
                default:(list,item,index)=>{},//1、当前选中列表数据，2、删除的数据，3、删除数据的索引
            },
            selectFn:{//选择时触发的回调函数
                type:Function,
                default:(list,item,index)=>{},//1、当前选中列表数据，2、选择的数据，3、选择数据的索引
            },
            changeFn:{//列表改变时触发的回调函数
                type:Function,
                default:(list,item,index)=>{},//1、当前选中列表数据，2、改变的数据，3、改变数据的索引
            },
            readonly:{//是否为只读属性
                type:Boolean,
                default:false,
            },
            componentId:{//组件id，用来判断这个组件是否可以用来执行保存数据的函数queryDiagnoseListSave
                type:String,
                default:'',
            },
        },

        watch:{
            revisitId(newVal,oldVal){
                if(newVal!=oldVal){
                    //获取诊断
                    this.getDiagnose();
                }
            },
        },

        created(){
            vm.$on('queryDiagnoseListUpdate',this.queryDiagnoseListUpdate);
            vm.$on('queryDiagnoseListSave',this.queryDiagnoseListSave);
            bind(window,'beforeunload',this.removeRevisitId);
        },

        mounted(){
            //session有数据则触发changeFn函数
            this.sessionToChangeFn();

            //document点击非组件区域则关闭弹窗
            bind(document,'click',this.closePopup);
        },

        beforeDestroy(){
            vm.$off('queryDiagnoseListUpdate',this.queryDiagnoseListUpdate);
            vm.$off('queryDiagnoseListSave',this.queryDiagnoseListSave);
            unbind(window,'beforeunload',this.removeRevisitId);
            unbind(document,'click',this.closePopup);
            sStore.remove('revisitId');
            sStore.remove('diagnoseJson');
        },

        methods:{
            closePopup(ev){
                let {queryDiagnoseList}=this.$refs;

                if(!queryDiagnoseList.contains(ev.target)){
                    this.showPopup=false;
                }
            },
            sessionToChangeFn(){
                let {list,idList,changeFn}=this;

                if(list.length&&idList.length){
                    changeFn&&changeFn(list,list[0],0);
                }
            },
            queryDiagnoseListUpdate(list,idList){
                let {changeFn}=this;

                this.list=list;
                this.idList=idList;
                changeFn&&changeFn(list,list[0],0);
            },
            removeRevisitId(){
                sStore.remove('revisitId');
            },
            queryDiagnoseListSave(dataList=[]){
                let whiteList=['recordCaseHistory1','caseHistoryEditDialog1','prescriptionEditDialog1'];

                if(!~whiteList.indexOf(this.componentId))return;
                let {list}=this;

                this.saveDataList=dataList;
                if(list.length){
                    for(let i=0;i<list.length;i++){
                        this.deleteItem(list[i],i);
                    }
                }else{
                    this.saveDataListFn();
                }
            },
            saveDataListFn(){
                let {list}=this;
                let saveDataList=copyJson(this.saveDataList);

                this.deleteCount++;
                if(!list.length||saveDataList.length){
                    this.deleteCount=0;
                    this.diagnoseListUpdate([],[]);
                    for(let i=0;i<saveDataList.length;i++){
                        this.selectItem([],saveDataList[i],i,i);
                        this.saveDataList.shift();
                    }
                }
            },
            enterSelect(){
                vm.$emit('pagingPopupSelect');
            },
            changeSelectIndex(key){
                vm.$emit('pagingPopupUpOrDown',key);
            },
            search(){
                let {keyWord}=this.params[0];

                this.showPopup=false;
                if(keyWord){
                    clearTimeout(this.searchTimer);
                    this.searchTimer=setTimeout(()=>{
                        this.showPopup=true;
                    },300);
                }
            },
            selectItem(dataList,item,index,sortIndex){
                let {icdCode}=item;
                let posIndex=this.idList.indexOf(icdCode);
                let list=[];

                if(!~posIndex){
                    this.selectDiagnoseList(list,item,index,posIndex,sortIndex);
                }else{
                    alerts('请勿重复添加');
                }
                this.params[0].keyWord='';
                this.showPopup=false;
            },
            deleteItem(item,index){
                if(this.readonly)return;
                let {icdCode}=item;
                let posIndex=this.idList.indexOf(icdCode);
                let list=[];

                if(~posIndex){
                    this.deleteDiagnoseList(list,item,index,posIndex);
                }
            },
            diagnoseListUpdate(list,idList){
                sStore.set('diagnoseJson',{
                    list,
                    idList,
                });
                vm.$emit('queryDiagnoseListUpdate',list,idList);
            },
            getDiagnose(){
                if(!this.revisitId||this.revisitId==-1)return;

                if(this.revisitId!=sStore.get('revisitId')){
                    this.idList=[];
                    sStore.set('revisitId',this.revisitId);
                    findMzDiagnose([this.revisitId],(res)=>{
                        if(res.body){
                            let list=[];
                            let idList=[];

                            list=res.body;
                            idList=res.body.map((item,index)=>item.icdCode);
                            this.diagnoseListUpdate(list,idList);
                        }
                    });
                }
            },
            selectDiagnoseList(list,item,index,posIndex,sortIndex){
                let {revisitId,mpiId}=this;
                let {selectFn,changeFn}=this;
                let {icdCode,icdName}=item;
                let params1=copyJson(this.params1);

                if(revisitId!=-1&&mpiId){
                    params1[0].revisitId=revisitId;
                    params1[0].mpiId=mpiId;
                    params1[0].icdCode=icdCode;
                    params1[0].icdName=icdName;
                    saveMzDiagnose(params1,(res)=>{
                        if(res.body){
                            let list=copyJson(this.list);
                            let idList=copyJson(this.idList);

                            if(sortIndex||sortIndex===0){
                                idList[sortIndex]=icdCode;
                                list[sortIndex]=item;
                                list[sortIndex].diagnoseRecordId=res.body;
                            }else{
                                idList.push(icdCode);
                                list.push(item);
                                list[list.length-1].diagnoseRecordId=res.body;
                            }

                            this.list=list;
                            this.idList=idList;

                            this.diagnoseListUpdate(list,idList);
                            selectFn&&selectFn(list,item,index);
                            changeFn&&changeFn(list,item,index);
                            alerts('诊断保存成功','success');
                        }
                    });
                }else{
                    let list=[];
                    let idList=[];

                    this.idList.push(icdCode);
                    this.list.push(item);
                    list=copyJson(this.list);
                    idList=copyJson(this.idList);
                    selectFn&&selectFn(list,item,index);
                    changeFn&&changeFn(list,item,index);
                }

            },
            deleteDiagnoseList(list,item,index,posIndex){
                let {deleteFn,changeFn}=this;

                if(item.diagnoseRecordId){
                    removeMzDiagnose([item.diagnoseRecordId],(res)=>{
                        let list=[];
                        let idList=[];

                        this.idList.splice(posIndex,1);
                        this.list.splice(posIndex,1);
                        list=copyJson(this.list);
                        idList=copyJson(this.idList);

                        this.diagnoseListUpdate(list,idList);
                        this.saveDataListFn();
                        deleteFn&&deleteFn(list,item,index);
                        changeFn&&changeFn(list,item,index);
                        alerts('诊断删除成功','success');
                    });
                }else{
                    let list=[];
                    let idList=[];

                    this.idList.splice(posIndex,1);
                    this.list.splice(posIndex,1);
                    list=copyJson(this.list);
                    idList=copyJson(this.idList);
                    this.saveDataListFn();
                    deleteFn&&deleteFn(list,item,index);
                    changeFn&&changeFn(list,item,index);
                }
            },
        },

        components:{
            pagingPopup,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .queryDiagnoseList{
        display: flex;
        align-items: center;
        width: 624px;
        padding: 5px 10px;
        height: 34px;
        border: $border1;
        border-radius: $borderRadius1;
        position: relative;
        z-index: 10;
        .list{
            max-width: 70%;
            line-height: 24px;
            position: relative;
            overflow: hidden;
            >ul{
                overflow: hidden;
                white-space: nowrap;
                position: absolute;
                right: 0;
                top: 0;
                &.visible{
                    opacity: 0;
                    filter: alpha(opacity=0);
                    position: relative;
                    z-index: -1;
                }
                >li{
                    display: inline-block;
                    padding-right: 22px;
                    margin-right: 20px;
                    background: url('../../assets/img/icon_del.png') no-repeat right center;
                    background-size: 14px;
                    font-size: 12px;
                    cursor: pointer;
                }
            }
        }
        .inputWrap{
            flex: 1;
            input{
                width: 100%;
                border: none;
            }
        }
        .icdNames{
            width: 100%;
            background-color: transparent;
            border: none;
            font-size: 12px;
            color: #999;
        }
        &.readonly{
            background-color: #f7f7f7;
            color: #999;
            .list{
                max-width: unset;
                >ul{
                    >li{
                        padding-right: 0;
                        background: none;
                        cursor: unset;
                    }
                }
            }
            .inputWrap{
                display: none;
            }
        }
    }
</style>
<template>
    <div
        :class="{
            pagingPopup:true,
            active:show,
        }"
        :style="{
            left:`${left}px`,
            top:`${top}px`,
        }"
    >
        <ul class="dataList">
            <li
                v-for="(item,index) in dataList"
                :class="{
                    active:index==mouseoverIndex,
                }"
                @click="selectItem(item,index)"
                @mouseover="mouseoverIndex=index"
            >
                <span>{{index+1}}.</span>
                <!-- 诊断 -->
                <span v-if="item.icdName">{{item.icdName}}</span>
                <!-- 药品 -->
                <span v-if="item.drugName">{{item.drugName}}</span>
                <span v-if="item.drugSpecifications">{{item.drugSpecifications}}</span>
                <span v-if="item.drugPackSpecificationUnitText">{{item.drugPackSpecificationUnitText}}</span>
                <!-- 取药点 -->
                <span v-if="item.storeName">{{item.storeName}}</span>
                <!-- 药品生产厂家 -->
                <span v-if="item.drugProductionPlaceName">{{item.drugProductionPlaceName}}</span>
                <span v-if="item.price">¥{{item.price}}</span>
            </li>
        </ul>
        <div v-show="!dataList.length&&loaded" class="noData">
            未搜索到数据
        </div>
        <div class="handleWrap">
            <div class="left">
                <a
                    v-for="item in [-10,-1,1,10]"
                    @click="prevOrBack(item)"
                ></a>
            </div>
            <div class="right">
                <span>跳至第</span>
                <input
                    v-model.trim="insideParams[0].page"
                    type="text"
                    @input="insideParams[0].page=insideParams[0].page.replace(/[^\d]/g,'')"
                    @keydown.enter="refresh"
                />
                <span>页/共{{maxPage}}页</span>
                <a
                    class="refresh"
                    @click="refresh"
                ></a>
            </div>
        </div>
    </div>
</template>

<script>
    import vm from 'src/main';
    import {Type,copyJson,alerts} from 'js/yydjs';

    export default{
        data(){
            return{
                mouseoverIndex:-1,
                insideParams:[{
                    page:1,
                    pageNo:1,
                    pageSize:10,
                }],
                maxPage:0,
                dataList:[],
                loaded:false,
            }
        },

        /*

        */

        props:{
            haveDrugList:{//是否有药品列表
                type:Boolean,
                default:false,
            },
            api:{//接口函数，只接受如下形式的接口，第一个参数是传参，第二个参数是回调函数，回参返回请求结果
                required:true,
                type:Function,
                default:null,
            },
            params:{//接口传参
                type:[Object,Array],
                default:null,
            },
            show:{//是否显示该分页弹窗
                type:Boolean,
                default:false,
            },
            left:{//偏移父元素左边距离
                type:Number,
                default:0,
            },
            top:{//偏移父元素顶部距离
                type:Number,
                default:50,
            },
            selectFn:{//选择时触发的回调函数
                type:Function,
                default:(item,index)=>{},
            },
        },

        created(){
            //更新内部传参
            this.updateInsideParams();

            //订阅向上或向下事件
            vm.$on('pagingPopupUpOrDown',this.outerChangeIndex);

            //订阅选中事件
            vm.$on('pagingPopupSelect',this.outerSelect);
        },

        beforeDestroy(){
            vm.$off('pagingPopupUpOrDown',this.outerChangeIndex);
            vm.$off('pagingPopupSelect',this.outerSelect);
        },

        mounted(){
            //请求数据列表
            this.getDataList();
        },

        methods:{
            checkDrugList (drugList) {
                for(let i = 0; i < drugList.length; i++) {
                    if (!drugList[i].drugSendNumber) {
                        alerts(`第${i + 1}行药品总量不能为空`)
                        return false
                    }
                    if (+drugList[i].drugSendNumber < 0) {
                        alerts(`第${i + 1}行药品总量需要大于0`)
                        return false
                    }
                }
                return true
            },
            outerChangeIndex(key){
                let {mouseoverIndex,dataList=[]}=this;
                let {length}=dataList;

                length--;
                key=='up'?mouseoverIndex--:mouseoverIndex++;
                if(mouseoverIndex<0){
                    mouseoverIndex=length;
                }else if(mouseoverIndex>length){
                    mouseoverIndex=0;
                }
                this.mouseoverIndex=mouseoverIndex;
            },
            outerSelect(){
                let {dataList,mouseoverIndex:index}=this;
                let item=dataList[index];

                this.selectItem(item,index);
                this.mouseoverIndex=0;
            },
            updateInsideParams(){
                let {params}=this;

                if(params){
                    for(let attr in params[0]){
                        this.insideParams[0][attr]=params[0][attr];
                    }
                }
            },
            getDataList(){
                let {api,insideParams,dataList}=this;
                let {page}=insideParams[0];

                if(api){
                    this.loaded=false;
                    this.insideParams[0].pageNo=page;
                    let params = JSON.parse(JSON.stringify(this.insideParams))
                    if (this.haveDrugList) {
                        delete params[0].pageNo
                        delete params[0].pageSize
                        if (params[0].drugList.length) {
                            let ret = this.checkDrugList(params[0].drugList)
                            if (!ret) {
                                return false
                            }
                        }
                    }
                    api(params,(res)=>{
                        this.loaded=true;
                        if(res.body){
                            let {data=[],total=0}=res.body;
                            if(Type(res.body)=='array'){
                                this.dataList=res.body;
                            }else{
                                this.dataList=data;
                            }
                            this.maxPage=Math.ceil(total/insideParams[0].pageSize);
                        }
                    });
                }
            },
            prevOrBack(item){
                let {maxPage}=this;
                let {page}=this.insideParams[0];

                page+=item;
                if(page<1){
                    page=1;
                }else if(page>maxPage){
                    page=maxPage;
                }
                this.insideParams[0].page=page;
                this.getDataList();
            },
            refresh(){
                this.getDataList();
            },
            selectItem(item,index){
                if(index==-1)return;
                let {selectFn}=this;

                selectFn&&selectFn(copyJson(this.dataList),item,index);
            },
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .pagingPopup{
        width: 530px;
        background-color: #fff;
        box-shadow: 0 0 10px rgba(82,163,252,.6);
        border-radius: $borderRadius1;
        position: absolute;
        left: 0;
        top: 34px;
        z-index: 10;
        display: none;
        &.active{
            display: block;
        }
        .dataList{
            max-height: 340px;
            overflow-y: auto;
            li{
                padding: 5px 20px;
                line-height: 24px;
                text-align: left;
                cursor: pointer;
                &.active{
                    background-color: $blueBg;
                    color: $main;
                }
                span{
                    padding-right: 10px;
                    &:last-of-type{
                        padding-right: 0;
                    }
                }
            }
        }
        .noData{
            padding: 20px 0;
            line-height: 30px;
            text-align: center;
        }
        .handleWrap{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            height: 36px;
            line-height: 36px;
            background-color: #e4e4e4;
            .left{
                display: flex;
                width: 100px;
                a{
                    flex: 1;
                    background: url('../../assets/img/icon_back.png') no-repeat center center;
                    background-size: 8px;
                    &:nth-of-type(2){
                        background-image: url('../../assets/img/icon_back2.png');
                        background-size: 6px;
                    }
                    &:nth-of-type(3){
                        background-image: url('../../assets/img/icon_go.png');
                        background-size: 6px;
                    }
                    &:nth-of-type(4){
                        background-image: url('../../assets/img/icon_go2.png');
                    }
                }
            }
            .right{
                display: flex;
                align-items: center;
                input{
                    width: 36px;
                    padding: 0 5px;
                    margin: 0 5px;
                    height: 26px;
                    text-align: center;
                    background-color: #fff;
                    border: none;
                }
                .refresh{
                    width: 50px;
                    height: 100%;
                    background: url('../../assets/img/ico_shuaxin.png') no-repeat center center;
                    background-size: 12px;
                }
            }
        }
    }
</style>
<template>
    <div :class="{previewPrescription:true,active:innerPreviewDetail.recipeCheckStatus=='5'}">
        <yydDialog :parent="parent" :show="show" :showName="showName" :maskClose="false" :width="750" :height="600">
            <span class="disabled">已作废</span>
            <div class="wrap">
                <div class="title">
                    <div class="top">
                        <h3>{{innerPreviewDetail.orgFullName}}</h3>
                        <h3>处方笺</h3>
                        <div class="right">
                            <h3>
                                <span>{{innerPreviewDetail.recipeFreeStatus=='1'?'免费':'普通'}}</span>
                            </h3>
                        </div>
                    </div>
                    <div class="bottom">
                        <h3>
                            <div class="left">
                                <span>门</span>
                                <div class="barCodeWrap">
                                    <svg v-if="innerPreviewDetail.recipeBarCode" class="barCode" :jsbarcode-width="1" :jsbarcode-height="50" :jsbarcode-fontsize="0" :jsbarcode-value="innerPreviewDetail.recipeBarCode"></svg>
                                </div>
                            </div>
                            <div class="right">
                                开具日期：{{new Date()|date('yyyy-MM-dd')}}
                            </div>
                        </h3>
                        <h4>
                            <span>费别：{{innerPreviewDetail.feibie ||'自费'}}</span>
                            <span>身份证号：{{innerPreviewDetail.idCard}}</span>
                            <span v-if="innerPreviewDetail.recipeNo">处方编号：{{innerPreviewDetail.recipeNo}}</span>
                        </h4>
                    </div>
                </div>
                <div class="main">
                    <ul class="infoList">
                        <li>
                            <span>姓名：</span>
                            <em class="multiLine">{{innerPreviewDetail.patientName||innerPreviewDetail.personName}}</em>
                        </li>
                        <li>
                            <span>性别：</span>
                            <em class="multiLine">{{innerPreviewDetail.sexText}}</em>
                        </li>
                        <li>
                            <span>年龄：</span>
                            <em class="multiLine">{{innerPreviewDetail.age}}岁</em>
                        </li>
                        <li>
                            <span>电话：</span>
                            <em>{{innerPreviewDetail.phoneNo}}</em>
                        </li>
                        <li>
                            <span>门诊号码：</span>
                            <em>{{innerPreviewDetail.outpatientCode}}</em>
                        </li>
                        <li>
                            <span>科室：</span>
                            <em>{{innerPreviewDetail.deptName||innerPreviewDetail.revisitStandardDeptName}}</em>
                        </li>
                        <li v-show="innerPreviewDetail.address">
                            <span>联系地址：</span>
                            <em>{{innerPreviewDetail.address}}</em>
                        </li>
                        <li>
                            <span>临床诊断：</span>
                            <em>{{diagnoseName}}</em>
                        </li>
                    </ul>
                    <ul class="drugList">
                        <li v-for="(item, index) in innerPreviewDetail.drugs" :key="index">
                            <div class="drugCont">
                                <div class="left">
                                    <div class="sort">{{`${index + 1}、`}}</div>
                                    <div>
                                        <h3>
                                            <span>{{item.drugName}}</span>
                                            <span>{{item.drugSpecifications}}</span>
                                        </h3>
                                        <h4>
                                            <span>用法：每次{{item.drugUseDose}}{{item.drugUseDoseUnitText}}</span>
                                            <span>{{item.drugUsingRate&&drugUsingRate_gdJson[item.drugUsingRate] || item.drugUsingRateText}}</span>
                                            <span>{{item.drugUsePathwaysCode&&drugUsePathways_gdJson[item.drugUsePathwaysCode] || item.drugUsePathwaysCodeText}}</span>
                                        </h4>
                                    </div>
                                </div>
                                <div class="right">
                                    <h3>〤{{item.drugSendNumber}}{{item.drugPackSpecificationUnitText}}</h3>
                                    <!-- <h3>〤{{item.drugUseDays}}天</h3> -->
                                </div>
                            </div>
                            <div class="remark">备注：{{item.remark||'无'}}</div>
                        </li>
                    </ul>
                    <div class="lineDeg">
                        <div class="lineDeg45"></div>
                    </div>
                    <div class="priceWrap">
                        <div class="left">
                            处方{{innerPreviewDetail.recipeEffectiveDays||3}}日有效
                        </div>
                        <div v-if="innerPreviewDetail.orderAmount&&innerPreviewDetail.orderAmount!=0" class="right">
                            药费：<span>{{innerPreviewDetail.orderAmount}}</span>元
                        </div>
                    </div>
                    <ul class="signatureWrap">
                        <li>
                            <span>处方医师：</span>
                            <div class="right">
                                <a v-if="signature&&!signed" @click="signatureFn">签名确认</a>
                                <span class="redLine" v-else>{{doctorName}}</span>
                            </div>
                        </li>
                        <li>
                            <span>审核药师：</span>
                            <div class="right">
                                <span v-if="orderNo&&~recipeId&&innerPreviewDetail.reviewDoctorName" class="redLine">{{innerPreviewDetail.reviewDoctorName}}</span>
                            </div>
                        </li>
                        <li>
                            <span>调配药师：</span>
                            <div class="right">
                                <span v-if="orderNo&&~recipeId&&innerPreviewDetail.dispenseDoctorName" class="redLine">{{innerPreviewDetail.dispenseDoctorName}}</span>
                            </div>
                        </li>
                    </ul>
                    <ul class="signatureWrap">
                        <li>
                            <span>核对药师：</span>
                            <div class="right">
                                <span v-if="orderNo&&~recipeId&&innerPreviewDetail.checkDoctorName" class="redLine">{{innerPreviewDetail.checkDoctorName}}</span>
                            </div>
                        </li>
                        <li>
                            <span>发药药师：</span>
                            <div class="right">
                                <span v-if="orderNo&&~recipeId&&innerPreviewDetail.sendDoctorName" class="redLine">{{innerPreviewDetail.sendDoctorName}}</span>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="end">
                    <h3>温馨提示：</h3>
                    <h4>1.取药时请仔细核对清单、点齐药品；</h4>
                    <h4>2.依《中华人民共和国药品管理法》，药品一经发出，一律不得退换。</h4>
                </div>
            </div>
        </yydDialog>
    </div>
</template>
<script>
import yydDialog from 'components/common/yydDialog';
import jsbarcode from 'jsbarcode';
import { sStore } from 'js/yydjs';
import { getDictionaries, getRecipeInfo } from 'services';
import dictionaries from 'src/dictionaries';

export default {
    data() {
        let doctorName = sStore.get('doctorName');

        return {
            drugUsePathways_gd: [],
            drugUsePathways_gdJson: {},
            drugUsingRate_gd: [],
            drugUsingRate_gdJson: {},
            piecesChineseMedicineMethod: [],
            piecesChineseMedicineMethodJson: {},
            agentNumbPerDay: [],
            agentNumbPerDayJson: {},
            drugUsePathwaysCodeChina: [],
            drugUsePathwaysCodeChinaJson: {},
            drugTakeDemand: [],
            drugTakeDemandJson: {},
            frequencyJson: {},
            signed: false,
            innerPreviewDetail: {},
            doctorName,
        }
    },

    computed: {
        diagnoseName() {
            let result = '';
            let { innerPreviewDetail = {} } = this;
            let { mzDiagnoses = [] } = innerPreviewDetail;

            result = mzDiagnoses.map((item) => item.icdName);
            return result.join('|');
        },
    },

    /*

    */

    props: {
        parent: { //父组件的this
            type: Object,
            default: null,
            required: true,
        },
        show: { //是否显示组件
            type: Boolean,
            default: false,
        },
        showName: { //控制显示组件的名称
            type: String,
            default: '',
        },
        orderNo: { //订单号
            type: String,
            default: '',
        },
        recipeId: { //处方id
            type: Number,
            default: -1,
        },
        previewDetail: { //处方预览详情
            type: Object,
            default () {
                return {};
            },
        },
        signature: { //点击签名触发的回调函数
            type: Function,
            default: null,
        },
    },

    mounted() {
        //获取字典
        this.getDictionariesFn();

        //外部更新内部详情
        this.outerUpdateInnerDetail();

        //获取处方详情
        this.getPrescription();
    },

    methods: {
        getDictionariesFn() {
            getDictionaries((res) => {
                console.log('字典', res);
                for (let attr in res) {
                    this[attr] = res[attr];
                }
            });
        },
        outerUpdateInnerDetail() {
            let { previewDetail = {} } = this;
            let { drugs = [] } = previewDetail;

            previewDetail.drugs = drugs;
            this.innerPreviewDetail = previewDetail;
        },
        barCodeInit() {
            setTimeout(() => {
                jsbarcode('.barCode').init();
            }, 300);
        },
        getPrescription() {
            if (!this.orderNo || !~this.recipeId) return;
            let { orderNo, recipeId } = this;
            let params = [
                orderNo,
                recipeId,
            ];

            getRecipeInfo(params, (res) => {
                if (res.body) {
                    let { recipeBarCode } = res.body[0];

                    this.innerPreviewDetail = res.body[0];
                    if (recipeBarCode) {
                        this.barCodeInit();
                    }
                }
            });
        },
        signatureFn() {
            let { signature } = this;

            signature && signature(() => {
                this.signed = true;
            });
        },
    },

    components: {
        yydDialog,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.previewPrescription {
    .wrap {
        padding: 20px 36px;
        .title {
            border-bottom: 3px solid #666;
            .top {
                padding-bottom: 20px;
                text-align: center;
                position: relative;
                >h3 {
                    line-height: 30px;
                    font-size: 22px;
                    color: #666;
                }
                .right {
                    padding-top: 10px;
                    padding-right: 50px;
                    line-height: 24px;
                    position: absolute;
                    right: 0;
                    top: 0;
                    h3 {
                        span {
                            display: block;
                            width: 84px;
                            border: $border1;
                        }
                    }
                    h4 {
                        color: #999;
                    }
                }
            }
            .bottom {
                h3 {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .left {
                        display: flex;
                        align-items: center;
                        span {
                            font-size: 22px;
                            color: #666;
                        }
                        .barCodeWrap {
                            display: flex;
                            align-items: center;
                            margin-left: 12px;
                            svg {
                                max-width: 300px;
                            }
                        }
                    }
                    .right {
                        color: #666;
                    }
                }
                h4 {
                    display: flex;
                    line-height: 48px;
                    color: #666;
                    span {
                        flex: 1;
                        &:last-of-type {
                            text-align: right;
                        }
                    }
                }
            }
        }
        .main {
            .infoList {
                padding-top: 20px;
                border-bottom: 1px solid #666;
                overflow: hidden;
                li {
                    display: flex;
                    float: left;
                    width: 35%;
                    padding-bottom: 10px;
                    line-height: 20px;
                    &:last-of-type, &:nth-last-of-type(2) {
                        width: 100%;
                    }
                    &:nth-of-type(2), &:nth-of-type(5) {
                        width: 30%;
                    }
                    span {
                        width: 90px;
                        text-align: right;
                        color: #666;
                    }
                    em {
                        flex: 1;
                    }
                }
            }
            .drugList {
                padding: 20px;
                padding-top: 80px;
                background: url('../../assets/img/RP.png') no-repeat 15px 32px;
                background-size: 30px;
                li {
                    padding-bottom: 10px;
                    line-height: 30px;
                    .drugCont {
                        display: flex;
                        justify-content: space-between;
                        // align-items: center;
                        .left {
                            display: flex;
                            .sort {
                                width: 30px;
                            }
                            h3 {
                                display: flex;
                                font-size: 14px;
                                span {
                                    padding-right: 12px;
                                }
                            }
                            h4 {
                                display: flex;
                                span {
                                    padding-right: 24px;
                                }
                            }
                        }
                        .right {
                            width: 70px;
                            text-align: right;
                        }
                    }
                    .remark {
                        padding-left: 30px;
                        font-size: 12px;
                    }
                }
            }
            .lineDeg {
                width: 100%;
                height: 190px;
                .lineDeg45 {
                    margin: 0 auto;
                    width: 200px;
                    height: 200px;
                    border-bottom: 1px solid #787878;
                    transform: rotate(-45deg) translate(0px, -110px);
                }
            }
            .priceWrap {
                display: flex;
                justify-content: space-between;
                line-height: 30px;
                padding: 0 20px;
                // padding-bottom: 80px;
                .left {
                    color: #ff6600;
                }
                .right {
                    span {
                        color: #ff6600;
                        font-size: 22px;
                    }
                }
            }
            .signatureWrap {
                display: flex;
                padding: 0 20px;
                overflow: hidden;
                li {
                    display: flex;
                    float: left;
                    width: 33.3333%;
                    line-height: 20px;
                    padding: 8px 0;
                    border-right: 30px solid transparent;
                    position: relative;
                    >span {
                        width: 70px;
                        color: #666;
                    }
                    .right {
                        flex: 1;
                        span {
                            color: #333;
                        }
                        a {
                            width: 120px;
                            height: 36px;
                            line-height: 36px;
                            text-align: center;
                            background-color: $main;
                            color: #fff;
                            border-radius: 18px;
                            position: absolute;
                            right: 0;
                            top: 0;
                        }
                    }
                }
            }
        }
        .end {
            padding: 0 20px;
            line-height: 24px;
            color: #666;
            h3 {
                font-weight: bold;
            }
        }
    }
    .scrollWrap {
        position: relative;
        .disabled {
            width: 120px;
            height: 50px;
            line-height: 46px;
            text-align: center;
            font-size: 26px;
            font-weight: bold;
            color: #ff0000;
            border: 2px solid #ff0000;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            transform: rotate3d(0, 0, 1, -45deg);
            display: none;
        }
    }
    &.active {
        .wrap {
            filter: blur(4px);
        }
        .disabled {
            display: block;
        }
    }
}
.redLine {
    border: 1px solid red;
    padding: 5px;
    color: red !important
}

</style>

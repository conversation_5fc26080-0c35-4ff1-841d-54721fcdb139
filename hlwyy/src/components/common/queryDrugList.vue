<template>
    <div
        class="queryDrugList"
        :style="{
            zIndex,
        }"
        ref="queryDrugList"
    >
        <div class="inputWrap">
            <input
                v-model.trim="params[0].keyWord"
                type="text"
                ref="input"
                :readonly="readonly"
                @input="search($event)"
                @focus="delayChange(true)"
                @blur="delayChange(false)"
                @keydown.enter="enterSelect"
                @keydown.prevent.up="changeSelectIndex('up')"
                @keydown.prevent.down="changeSelectIndex('down')"
            />
        </div>
        <pagingPopup
            v-if="showPopup"
            :api="findDrugStandardCatalog"
            :params="params"
            :show="true"
            :top="40"
            :selectFn="selectItem"
        />
    </div>
</template>

<script>
    import vm from 'src/main';
    import pagingPopup from 'components/common/pagingPopup';
    import {findDrugStandardCatalog} from 'services';
    import {copyJson,sStore,bind,unbind} from 'js/yydjs';

    export default{
        data(){
            return{
                showPopup:false,
                currentItem:{},
                params:[{
                    orgId:sStore.get('orgId'),
                    drugType:'',
                    keyWord:'',
                }],
                delayTimer:null,
                searchTimer:null,
                findDrugStandardCatalog,
            }
        },

        /*
            <queryDrugList
                :changeFn="changeDrugList"
            />
        */

        props:{
            zIndex:{//显示层级
                type:Number,
                default:10,
            },
            componentIndex:{//组件索引
                type:Number,
                default:-1,
            },
            focusFn:{//获取焦点时触发的回调函数
                type:Function,
                default:(index)=>{},//1、组件索引
            },
            selectFn:{//选择时触发的回调函数
                type:Function,
                default:(componentIndex,item,index)=>{},//1、组件索引，2、选择的数据，3、选择数据的索引
            },
            changeFn:{//列表改变时触发的回调函数
                type:Function,
                default:(componentIndex,item,index)=>{},//1、组件索引，2、改变的数据，3、改变数据的索引
            },
            enterFn:{//按下enter键触发的回调函数
                type:Function,
                default:()=>{},
            },
            readonly:{//是否为只读属性
                type:Boolean,
                default:false,
            },
            keyWord:{//keyWord传值
                type:String,
                default:'',
            },
        },

        mounted(){
            //输入框自动获取焦点
            this.autoFocus();

            //keyWord传值
            this.setkeyWord();

            //document点击非组件区域则关闭弹窗
            bind(document,'click',this.closePopup);
        },

        beforeDestroy(){
            unbind(document,'click',this.closePopup);
        },

        methods:{
            closePopup(ev){
                let {queryDrugList}=this.$refs;

                if(!queryDrugList.contains(ev.target)){
                    this.showPopup=false;
                }
            },
            enterSelect(){
                let {enterFn}=this;

                enterFn&&enterFn();
                vm.$emit('pagingPopupSelect');
            },
            setkeyWord(){
                if(this.keyWord){
                    this.params[0].keyWord=this.keyWord;
                }
            },
            changeSelectIndex(key){
                vm.$emit('pagingPopupUpOrDown',key);
            },
            autoFocus(){
                this.$refs.input.focus();
            },
            delayChange(bool){
                let {focusFn,componentIndex}=this;

                if(bool){
                    focusFn&&focusFn(componentIndex);
                }
            },
            search(){
                let {keyWord}=this.params[0];

                this.showPopup=false;
                if(keyWord){
                    clearTimeout(this.searchTimer);
                    this.searchTimer=setTimeout(()=>{
                        this.showPopup=true;
                    },300);
                }
            },
            selectItem(dataList,item,index){
                let {selectFn,changeFn,componentIndex}=this;
                let {drugName}=item;
                const clearKeyWord=()=>{
                    this.params[0].keyWord='';
                    setTimeout(()=>{
                        this.autoFocus();
                    },300);
                };

                this.currentItem=item;
                this.params[0].keyWord=drugName;
                selectFn&&selectFn(componentIndex,item,index,clearKeyWord);
                changeFn&&changeFn(componentIndex,item,index,clearKeyWord);

                this.showPopup=false;
            },
        },

        components:{
            pagingPopup,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .queryDrugList{
        display: flex;
        align-items: center;
        width: 100%;
        position: relative;
        z-index: 10;
        .inputWrap{
            flex: 1;
            input{
                width: 100%;
                font-size: 12px;
                text-align: center;
                border: none;
                background-color: transparent;
            }
        }
    }
</style>
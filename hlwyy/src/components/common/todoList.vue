<template>
    <div
        :class="{
            todoList:true,
            active:show,
        }"
        ref="todoList"
    >
        <a
            class="leftContent"
            @click="show=!show"
        >
            <span>
                待办
                <i>{{msgList.length}}</i>
            </span>
        </a>
        <div class="rightContent">
            <div class="title">
                您有<span>{{msgList.length}}</span>条待处理事项，请尽快处理！
            </div>
            <ul class="msgList">
                <li v-for="(item,index) in msgList">
                    <a
                        class="leftContent"
                        @click="checkMsg(item)"
                    >
                        <span class="msg multiLine">
                            <em>{{item.patientName}}</em>向您申请了{{codeJson[item.code].text}}
                        </span>
                        <span class="time">
                            {{item.orderTime|date('MM-dd hh:mm')}}
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
    import {copyJson,notification,bind,unbind} from 'js/yydjs';
    import {getSchedule} from 'services';

    export default{
        data(){
            return{
                msgList:[],
                buzIdList:[],
                notificationJson:{},
                show:false,
                codeJson:{
                    '1000':{
                        text:'图文咨询',
                        path:'/imgTextConsultation',
                    },
                    '1001':{
                        text:'视频咨询',
                        path:'/videoConsultation',
                    },
                    '1002':{
                        text:'复诊配药',
                        path:'/visitDispensing',
                    },
                },
            }
        },

        /*
            <todoList/>
        */

        props:{

        },

        created(){
            vm.$on('nimOnMsg',this.todoListChange);
            bind(document,'click',this.outerClose);
        },

        mounted(){
            //获取待办事项
            this.getMsgList();
        },

        beforeDestroy(){
            vm.$off('nimOnMsg',this.todoListChange);
            unbind(document,'click',this.outerClose);
        },

        methods:{
            outerClose(ev){
                let {target}=ev;
                let {todoList}=this.$refs;

                if(!todoList.contains(target)){
                    this.show=false;
                }
            },
            getMsgList(){
                getSchedule([],(res)=>{
                    if(res.body){
                        this.msgList=res.body;
                    }
                });
            },
            todoListChange(res){
                let msgList=[];

                for(let attr in res){
                    msgList=[].concat(res[attr]);
                }

                msgList=msgList.filter((item)=>{
                    let {type,from,content={}}=item;
                    let {type:type1}=content;

                    return type=='custom'&&type1=='99';
                });

                if(msgList.length){
                    this.getMsgList();
                }
            },
            notificationFn(item){
                let {codeJson}=this;
                let {patientName,code,orderDetailId,revisitId}=item;
                let buzId=orderDetailId||revisitId;

                notification({
                    title:`${patientName}向您申请了${codeJson[code].text}`,
                    body:'请点击查看',
                    tag:buzId,
                    icon:require('img/title.png'),
                },{
                    click:(ev,oN)=>{
                        this.checkMsg(item);
                    },
                });
            },
            checkMsg(item){
                let {code,orderDetailId,revisitId}=item;
                let buzId=orderDetailId||revisitId;

                this.$router.push({
                    path:this.codeJson[code].path,
                    query:{
                        tabIndex:0,
                        buzId,
                    },
                });
            },
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .todoList{
        display: flex;
        height: 200px;
        position: absolute;
        right: 0;
        bottom: 10px;
        >.leftContent{
            width: 80px;
            padding-left: 30px;
            height: 50px;
            line-height: 50px;
            background: #fff url('../../assets/img/arrow_index.png') no-repeat 10px center;
            background-size: 5px;
            border-radius: 8px 0  0 8px;
            box-shadow: 0 0 4px 0 rgba(0,0,0,.2);
            position: relative;
            span{
                position: relative;
                i{
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    text-align: center;
                    font-size: 16px;
                    background-color: $red;
                    border-radius: 50%;
                    color: #fff;
                    position: absolute;
                    right: -15px;
                    top: -15px;
                    z-index: 10;
                    transform: scale(.5,.5);
                }
            }
            &:before{
                content: "";
                width: 10px;
                height: 50px;
                background-color: #fff;
                position: absolute;
                right: -5px;
                top: 0;
                z-index: 10;
                display: none;
            }
        }
        .rightContent{
            width: 0;
            background-color: #fff;
            border-radius: 0 0 0 8px;
            box-shadow: 0 0 4px 0 rgba(0,0,0,.2);
            overflow: hidden;
            transition: width .3s ease-out;
            .title{
                padding: 10px 20px;
                line-height: 20px;
                font-size: 14px;
                color: $gray;
                span{
                    color: $main;
                }
            }
            .msgList{
                height: 150px;
                padding: 0 20px;
                overflow-y: auto;
                li{
                    display: flex;
                    padding: 5px 0;
                    line-height: 20px;
                    font-size: 12px;
                    .leftContent{
                        flex: 1;
                        display: flex;
                        .msg{
                            flex: 1;
                            color: #666;
                            em{
                                color: #333;
                                font-weight: bold;
                            }
                        }
                        .time{
                            padding-left: 14px;
                            width: 105px;
                            color: $gray;
                        }
                    }
                    .delete{
                        width: 20px;
                        height: 20px;
                        background: url('../../assets/img/icon_close1.png') no-repeat center center;
                        background-size: 8px;
                    }
                }
            }
        }
        &.active{
            >.leftContent{
                background-image: url('../../assets/img/arrow2_index.png');
                &:before{
                    display: block;
                }
            }
            .rightContent{
                width: 310px;
            }
        }
    }
</style>
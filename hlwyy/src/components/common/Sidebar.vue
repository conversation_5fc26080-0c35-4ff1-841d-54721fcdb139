<template>
    <div
        :class="{
            sidebar: true,
            active: showSidebar,
        }"
    >
        <a
            :class="{
                onOff: true,
                active: showSidebar,
            }"
            @click="controlSidebar"
        ></a>
        <el-menu
            ref="menu"
            default-active="workbench"
            unique-opened
            @open="navToPage"
            @close="navToPage"
            @select="navToPage"
            class="sidebar-menu"
        >
            <!-- First level items -->
            <el-submenu
                v-for="item in menuList"
                :index="item.id"
                :key="item.id"
                class="first-level-menu"
            >
                <template slot="title">
                    <i class="menu-icon"></i>
                    <span>{{item.name}}</span>
                </template>
                
                <!-- Second level items -->
                <template v-for="item1 in item.children">
                    <!-- Second level items with children (third level) -->
                    <el-submenu 
                        v-if="item1.children && item1.children.length" 
                        :index="item1.id" 
                        :key="item1.id"
                        class="second-level-menu"
                    >
                        <template slot="title">
                            <i class="menu-icon"></i>
                            <span>{{item1.name}}</span>
                        </template>
                        
                        <!-- Third level menu items -->
                        <el-menu-item
                            v-for="item2 in item1.children"
                            :index="item2.id"
                            :key="item2.id"
                            class="third-level-menu"
                        >
                            <span>{{item2.name}}</span>
                        </el-menu-item>
                    </el-submenu>
                    
                    <!-- Second level items without children -->
                    <el-menu-item
                        v-else
                        :index="item1.id"
                        :key="'menu-'+item1.id"
                        class="second-level-menu-item"
                    >
                        <i class="menu-icon"></i>
                        <span>{{item1.name}}</span>
                    </el-menu-item>
                </template>
            </el-submenu>
        </el-menu>
    </div>
</template>

<style lang="scss" scoped>
    .sidebar {
        width: 0;
        height: 100%;
        background-color: #fff;
        color: #333;
        position: relative;
        transition: width .25s ease-out;
        overflow: hidden;
        
        .onOff {
            width: 36px;
            height: 36px;
            background: url('../../assets/img/openMenu.png') no-repeat center center;
            background-size: cover;
            border-radius: 2px;
            position: absolute;
            right: -26px;
            top: -26px;
            z-index: 1000;
            cursor: pointer;
            
            &.active {
                background-image: url('../../assets/img/closeMenu.png');
            }
        }
        
        &.active {
            width: 180px;
        }
    }
    
    /deep/ .sidebar-menu {
        border-right: none;
        background-color: #eef5ff;
        height: 100%;
        
        .menu-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 6px;
            background: url('../../assets/img/icon_page.png') no-repeat center center;
            background-size: 14px;
            vertical-align: middle;
            opacity: 0.8;
        }
        
        .el-submenu__title {
            height: 44px;
            line-height: 44px;
            padding-left: 20px !important;
            color: #303133;
            
            span {
                font-size: 14px;
                vertical-align: middle;
            }
            
            .el-submenu__icon-arrow {
                margin-top: -3px;
                font-size: 12px;
                color: #909399;
            }
            
            &:hover {
                background-color: #dbeaff;
            }
        }
        
        .el-menu {
            background-color: transparent;
        }
        
        // First level menu
        .first-level-menu {
            & > .el-submenu__title {
                font-weight: 500;
            }
        }
        
        // Second level menu
        .second-level-menu {
            & > .el-submenu__title {
                padding-left: 35px !important;
                height: 40px;
                line-height: 40px;
                
                &:hover {
                    background-color: #dbeaff;
                }
            }
            
            .el-menu {
                padding-left: 10px;
            }
        }
        
        .second-level-menu-item {
            height: 40px;
            line-height: 40px;
            padding-left: 35px !important;
            
            &:hover, &.is-active {
                background-color: #dbeaff !important;
            }
        }
        
        // Third level menu
        .third-level-menu {
            height: 36px;
            line-height: 36px;
            padding-left: 48px !important;
            position: relative;
            font-size: 13px;
            
            &:hover, &.is-active {
                background-color: #dbeaff !important;
            }
            
            &:before {
                content: "";
                position: absolute;
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background-color: #909399;
                left: 38px;
                top: 50%;
                margin-top: -2px;
            }
        }
        
        .is-active {
            color: #409EFF !important;
        }
    }
</style>

<script>
    import vm from 'src/main';

    const menuList=[
    {
        name: "互联网门诊",
        children: [
            {
                name:'工作台',
                path:'/workbench',
            },
            {
                name: "咨询/配药",
                children: [
                    {
                        name:'图文咨询',
                        path:'/imgTextConsultation',
                    },
                    {
                        name:'视频咨询',
                        path:'/videoConsultation',
                    },
                    {
                        name:'复诊配药',
                        path:'/visitDispensing',
                    },
                ]
            },
            {
                name: "监护人管理",
                children: [
                    {
                        name:'监护人认证',
                        path:'/guardianAuth',
                    },
                    {
                        name:'监护人审核',
                        path:'/guardianReview',
                    },
                ]
            },
            {
                name:'患者管理',
                path:'/patient360',
            },
            {
                name:'订单管理',
                path:'/orderManage',
            },
            {
                name: "门诊模板",
                children: [
                    {
                        name:'病历模板',
                        path:'/caseHistoryTemplate',
                    },
                    {
                        name:'处方模板',
                        path:'/prescriptionTemplate',
                    },
                ]
            },
            {
                name: "服务管理",
                children: [
                    {
                        name:'排班一览',
                        path:'/schedulingPreview',
                    },
                    {
                        name:'服务管理',
                        path:'/serviceManagement',
                    },
                ]
            },
        ]
    },
    {
        name: "互联网病房",
        children: [
            {
                name: "会诊管理",
                children: [
                    {
                        name:'会诊申请',
                        path:'/internet-clinic/consultation/apply',
                    },
                    {
                        name:'我的会诊',
                        path:'/internet-clinic/consultation/list',
                    },
                    {
                        name:'会诊团队',
                        path:'/internet-clinic/consultation/team',
                    },
                ]
            },
            {
                name: "护士站病人列表",
                path: "/nursePatientList",
            },
            {
                name: "医生站患者列表",
                path: "/doctorPatientList",
            },
            {
                name: "护士交接班管理",
                children: [
                    {
                        name: "护士交班",
                        path: "/nurse-shift",
                    },
                    {
                        name: "护士接班",
                        path: "/nurse-succession",
                    }
                ]
            },
        ],
    },
    {
        name: "随访系统",
        children: [
            {
                name: "随访用户列表",
                path: "/followUp/cUserList",
            },
            {
                name: "患者入组管理",
                path: "/followUp/patientGroup",
            },
            {
                name: "分组列表",
                path: "/followUp/groupList",
            },
            {
                name: "患者随访计划",
                path: "/followUp/plan",
            },
            // {
            //     name: "患者调査问卷",
            //     path: "",
            // },
            // {
            //     name: "患者诊疗评估",
            //     path: "",
            // },
            // {
            //     name: "患者教育推送",
            //     path: "",
            // },
            // {
            //     name: "综合问卷分析",
            //     path: "",
            // },
            {
                name: "模板管理",
                path: "/followUp/templateManage",
            },
        ],
    },
    {
        name: "警示系统",
        children: [
            {
                name: "警示知识库建立",
                path: "/warningKnowledge",
            },
            {
                name: "个人的专项警示",
                path: "/personalWarning",
            },
            // {
            //     name: "病种专向警示",
            //     path: "",
            // },
            // {
            //     name: "警示等级分级",
            //     path: "",
            // },
            // {
            //     name: "警示责任分级",
            //     path: "",
            // },
            // {
            //     name: "患者主动报警",
            //     path: "",
            // },
        ],
    },
    {
        name: "养老机构端",
        children: [
            {
                name:'机构管理',
                path:'/nursingHomePortal/manage',
            },
            // {
            //     name:'机构首页',
            //     path:'/nursingHomePortal',
            // },
            {
                name:'患者信息列表',
                path:'/nursingHomePortal/patientList',
            },
            // {
            //     name:'患者转介信',
            //     path:'/nursingHomePortal/patientReferral',
            // },
            {
                name:'体征数据采集',
                path:'/nursingHomePortal/deviceData',
            },
            {
                name:'体征数据分析',
                path:'/followUp/wearableData',
            },
        ],
    },
    {
        name: "出院延续性服务",
        children: [
            {
                name:'转介信审核',
                path:'/dischargeService/referralAudit',
            },
        ],
    },
    {
        name: "个人管理",
        children: [
            {
                name:'个人资料',
                path:'/personInfo',
            },
            {
                name:'账号信息',
                path:'/accountInfo',
            },
            {
                name:'密码修改',
                path:'/pwdManage',
            },
        ],
    },
    ];

    export default {
        data() {
            return {
                showSidebar: true,
                menuList: [],
            };
        },
        created() {
            // Create a copy of the original menuList
            let enhancedMenuList = JSON.parse(JSON.stringify(menuList));
            
            // Process all menu items for proper IDs at all levels
            for (let index = 0; index < enhancedMenuList.length; index++) {
                const element = enhancedMenuList[index];
                element.id = (index + 1) + "";
                if(element.children){
                    let array = element.children;
                    for (let index1 = 0; index1 < array.length; index1++) {
                        const item = array[index1];
                        item.id = element.id + "-" + (index1+1);
                        
                        // Handle third level items
                        if(item.children) {
                            let subArray = item.children;
                            for (let index2 = 0; index2 < subArray.length; index2++) {
                                const subItem = subArray[index2];
                                subItem.id = item.id + "-" + (index2+1);
                            }
                        }
                    }
                }
            }
            
            this.menuList = enhancedMenuList;
            console.log("menuList=====", this.menuList);
            vm.$on('closeSidebar',this.closeSidebar);
            
            // Add global style for the third-level popper
            this.addGlobalStyle();
        },
        beforeDestroy() {
            vm.$off('closeSidebar',this.closeSidebar);
            
            // Remove the global style element when component is destroyed
            const styleElement = document.getElementById('third-level-popper-style');
            if (styleElement) {
                document.head.removeChild(styleElement);
            }
        },
        methods: {
            controlSidebar() {
                this.showSidebar = !this.showSidebar;
            },
            closeSidebar() {
                this.showSidebar = false;
            },
            addGlobalStyle() {
                // Add global style for third-level popper (since scoped styles won't apply to it)
                const style = document.createElement('style');
                style.id = 'third-level-popper-style';
                style.innerHTML = `
                    .third-level-popper {
                        min-width: 150px !important;
                    }
                    
                    .third-level-popper .el-menu {
                        background-color: #fff;
                        border: 1px solid #ebeef5;
                        border-radius: 4px;
                        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                    }
                    
                    .third-level-popper .el-menu-item {
                        padding: 0 20px !important;
                        border-bottom: 1px solid #f0f0f0;
                    }
                    
                    .third-level-popper .el-menu-item:last-child {
                        border-bottom: none;
                    }
                    
                    .third-level-popper .el-menu-item.is-active {
                        color: #409EFF;
                        background-color: #ecf5ff !important;
                    }
                `;
                document.head.appendChild(style);
            },
            navToPage(index, indexPath) {
                console.log(index, "=======", indexPath);
                let {menuList}=this;
                let path='';
                
                try {
                    // Parse the index path to determine the level
                    if (typeof index === 'string' && index.split('-').length === 3) {
                        // This is a third-level menu item
                        const [firstLevel, secondLevel, thirdLevel] = index.split('-').map(i => parseInt(i) - 1);
                        path = menuList[firstLevel].children[secondLevel].children[thirdLevel].path;
                    } else if (typeof index === 'string' && index.split('-').length === 2) {
                        // This is a second-level menu item
                        const [firstLevel, secondLevel] = index.split('-').map(i => parseInt(i) - 1);
                        if (menuList[firstLevel].children[secondLevel].path) {
                            path = menuList[firstLevel].children[secondLevel].path;
                        }
                    } else if (typeof index === 'string' && !index.includes('-')) {
                        // This is a first-level menu item
                        const firstLevel = parseInt(index) - 1;
                        if (menuList[firstLevel].path) {
                            path = menuList[firstLevel].path;
                        }
                    }

                    // If this is just opening a submenu, return
                    if (!path) {
                        return;
                    }

                    this.$router.push(path);
                } catch (error) {
                    console.error('Navigation error:', error);
                    this.$message({
                        type: 'error',
                        message: '菜单导航错误，请联系管理员'
                    });
                }
            }
        },
    };

    export {menuList};
</script>

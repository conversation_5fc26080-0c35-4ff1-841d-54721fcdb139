<template>
    <el-breadcrumb class="app-breadcrumb" separator="/">
        <transition-group name="breadcrumb">
            <el-breadcrumb-item
                v-for="(item,index) in breadcrumbList"
                :key="item.path || item.name"
                :to="{
                    path:item.path||'',
                }"
            >
                {{item.name}}
            </el-breadcrumb-item>
        </transition-group>
    </el-breadcrumb>
</template>

<script>
    import sidebar,{menuList} from 'components/common/Sidebar';

    export default {
        data() {
            return {

            }
        },
        computed:{
            breadcrumbList(){
                let {$route}=this;
                let {path}=$route;
                let result=[];

                for(let item of menuList){
                    if(item.path==path){
                        result.push(item);
                        break;
                    }else if(item.children){
                        for(let item1 of item.children){
                            if(item1.path==path){
                                result.push(item);
                                result.push(item1);
                                break;
                            }
                        }
                    }
                }

                return result;
            },
        },
        methods: {

        }
    }
</script>

<style rel="stylesheet/scss" lang="scss" type="text/scss" scoped>
    .app-breadcrumb.el-breadcrumb {
        display: inline-block;
        font-size: 14px;
        line-height: 50px;
        margin-left: 10px;

        .no-redirect {
            color: #ccc;
            cursor: text;
        }

        /deep/ .el-breadcrumb__item {
            font-size: 14px;
        }

        /deep/ .el-breadcrumb__inner,
        .el-breadcrumb__inner a {
            color: #ccc;
            font-weight: normal;
        }

        /*fade*/
        .breadcrumb-enter-active,
        .breadcrumb-leave-active {
            transition: all .5s;
        }

        .breadcrumb-enter,
        .breadcrumb-leave-active {
            opacity: 0;
            transform: translateX(20px);
        }

        .breadcrumb-move {
            transition: all .5s;
        }

        .breadcrumb-leave-active {
            position: absolute;
        }

    }
</style>

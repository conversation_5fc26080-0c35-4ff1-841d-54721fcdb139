<template>
    <div class="previewInsApp">
        <yydDialog :parent="parent" :show="show" :showName="showName" :maskClose="false" :width="550" :height="600">
            <div class="wrap">
                <div class="title">
                    <div class="top">
                        <h3>{{innerPreviewDetail.orgFullName||innerPreviewDetail.orgName}}</h3>
                        <h3>{{applyType == 2 ? '检查' : '检验'}}申请单</h3>
                    </div>
                    <div class="bottom">
                        <h4>申请单号：{{!~applyId ? '暂无' : applyId}}</h4>
                        <h4>申请科室：{{innerPreviewDetail.deptName||innerPreviewDetail.revisitStandardDeptName}}</h4>
                        <h4>申请时间：{{innerPreviewDetail.applyTime||'暂无'}}</h4>
                        <h4>门诊病历号：{{innerPreviewDetail.outpatientCode}}</h4>
                        <h4>患者信息：
                            <em style="padding: 0 5px;">{{innerPreviewDetail.patientName||innerPreviewDetail.personName}}</em>
                            <em style="padding: 0 5px;">{{innerPreviewDetail.sexText}}</em>
                            <em style="padding: 0 5px;">{{innerPreviewDetail.age}}岁</em>
                        </h4>
                        <div>临床诊断：{{diagnoseName || innerPreviewDetail.mzDiagnoseName}}</div>
                    </div>
                </div>
                <div class="main">
                    <div class="insTitle">{{applyType == 2 ? '检查' : '检验'}}项目</div>
                    <ul class="drugList">
                        <li v-for="(item, index) in medicalSkillApplyDetailsList" :key="index">
                            <div class="drugCont">
                                <div class="name">{{item.itemName}}</div>
                                <div class="price">{{(item.attachItemAmount?addFn(item.itemPrice,item.attachItemAmount):item.itemPrice) + '元'}}</div>
                            </div>
                            <!-- <div class="remark">备注：{{item.remark||'无'}}</div> -->
                        </li>
                    </ul>
                    <div class="signatureWrap">
                        <div style="padding-right: 20px;font-size: 16px;"><span>申请医生：{{doctorName}}</span></div>
                        <div class="handleWrap" v-if="!~applyId">
                            <a @click="signatureFn">确认提交</a>
                        </div>
                    </div>
                </div>
                <div class="end">
                    <h3>{{applyType == 2 ? '检查' : '检验'}}须知：</h3>
                    <p>{{innerPreviewDetail.currentTab&&innerPreviewDetail.currentTab.remark || innerPreviewDetail.tmplRemark}}</p>
                </div>
            </div>
        </yydDialog>
    </div>
</template>
<script>
import yydDialog from 'components/common/yydDialog';
import { sStore, Dec } from 'js/yydjs';
import { getDictionaries, getRecipeInfo, findMzDiagnose, getMedicalSkillApplyDetail } from 'services';
import dictionaries from 'src/dictionaries';

export default {
    data() {
        let doctorName = sStore.get('doctorName');

        return {
            drugUsePathways_gd: [],
            drugUsePathways_gdJson: {},
            drugUsingRate_gd: [],
            drugUsingRate_gdJson: {},
            piecesChineseMedicineMethod: [],
            piecesChineseMedicineMethodJson: {},
            agentNumbPerDay: [],
            agentNumbPerDayJson: {},
            drugUsePathwaysCodeChina: [],
            drugUsePathwaysCodeChinaJson: {},
            drugTakeDemand: [],
            drugTakeDemandJson: {},
            frequencyJson: {},
            signed: false,
            innerPreviewDetail: {},
            doctorName,
        }
    },

    computed: {
        applyType() {
            if (!~this.applyId) {
                return this.innerPreviewDetail.currentTab && this.innerPreviewDetail.currentTab.tempCatalog
            } else {
                return this.innerPreviewDetail.applyType
            }
        },
        diagnoseName() {
            let result = '';
            let { innerPreviewDetail = {} } = this;
            let { mzDiagnoses = [] } = innerPreviewDetail;

            result = mzDiagnoses.map((item) => item.icdName);
            return result.join('|');
        },
        medicalSkillApplyDetailsList() {
            if (!~this.applyId) {
                return this.innerPreviewDetail.hasTableList
            } else {
                return this.innerPreviewDetail.medicalSkillApplyDetails
            }
        },
        remark() {
            if (!~this.applyId) {
                return this.innerPreviewDetail.remark
            } else {
                return this.innerPreviewDetail.medicalSkillApplyDetails
            }
        }
    },

    /*

    */

    props: {
        parent: { //父组件的this
            type: Object,
            default: null,
            required: true,
        },
        show: { //是否显示组件
            type: Boolean,
            default: false,
        },
        showName: { //控制显示组件的名称
            type: String,
            default: '',
        },
        orderNo: { //订单号
            type: String,
            default: '',
        },
        applyId: { //处方id
            type: Number,
            default: -1,
        },
        previewDetail: { //处方预览详情
            type: Object,
            default () {
                return {};
            },
        },
        signature: { //点击签名触发的回调函数
            type: Function,
            default: null,
        },
    },

    mounted() {
        //外部更新内部详情
        this.outerUpdateInnerDetail();
        this.findMzDiagnose()
        this.getMedicalSkillApplyDetail()
    },

    methods: {
        addFn(a, b) {
            return new Dec(a).add(new Dec(b)).toNumber();
        },
        findMzDiagnose() {
            if (!~this.applyId) {
                findMzDiagnose([this.previewDetail.revisitId], (res) => {
                    if (res.body) {
                        this.innerPreviewDetail.mzDiagnoses = res.body
                    }
                })
            }
        },
        outerUpdateInnerDetail() {
            let { previewDetail = {} } = this;
            let { drugs = [] } = previewDetail;
            previewDetail.drugs = drugs;
            this.innerPreviewDetail = previewDetail;
        },
        getMedicalSkillApplyDetail() {
            if (!~this.applyId) return;
            getMedicalSkillApplyDetail([this.applyId], (res) => {
                if (res.body) {
                    this.innerPreviewDetail = res.body
                }
            });
        },
        signatureFn() {
            let { signature } = this;

            signature && signature(() => {
                this.signed = true;
            });
        },
    },

    components: {
        yydDialog,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.previewInsApp {
    .wrap {
        padding: 20px 36px;
        .title {
            border-bottom: 3px solid #666;
            .top {
                padding-bottom: 20px;
                text-align: center;
                position: relative;
                >h3 {
                    line-height: 30px;
                    font-size: 22px;
                    color: #666;
                }
            }
            .bottom {
                padding-left: 20px;
                h4, div {
                    display: flex;
                    line-height: 24px;
                    color: #666;
                }
            }
        }
        .main {
            .insTitle {
                padding-top: 20px;
                line-height: 30px;
                font-size: 16px;
                color: #666;
            }
            .drugList {
                padding-top: 10px;
                max-height: 230px;
                min-height: 120px;
                overflow-y: scroll;
                li {
                    padding-bottom: 10px;
                    padding-left: 10px;
                    line-height: 24px;
                    .drugCont {
                        font-size: 12px;
                        display: flex;
                        .name {
                            padding: 0 10px;
                        }
                        .price {
                            padding: 0 10px;
                        }
                    }
                    .remark {
                        padding-left: 10px;
                        font-size: 12px;
                    }
                }
            }
            .signatureWrap {
                display: flex;
                align-items: center;
                span {
                    width: 70px;
                    color: #666;
                }
                .handleWrap {
                    @include handleWrap;
                }
            }
        }
        .end {
            padding: 20px;
            line-height: 24px;
            color: #666;
            h3 {
                font-weight: bold;
            }
            p {
                text-indent: 24px;
            }
        }
    }
}

</style>

<template>
  <el-select v-model="doctorName" filterable remote :remote-method="remoteMethod" :loading="loading">
    <el-option v-for="item in doctorList" :key="item.id" :label="item.doctorName" :value="item.doctorName">
      <span>{{item.doctorName}}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">{{item.deptName}}</span>
    </el-option>
  </el-select>
</template>

<script>
import { commonAjax } from '@/api/api'

export default {
  name: 'searchSelect',
  props: {
    api: {
      type: Object,
      required: true 
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      doctorName: '',
      doctorList: []
    }
  },
  methods: {
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        commonAjax(this.api.service, this.api.method, ['', this.params.deptId, query]).then(res => {
          this.loading = false
          if (res && res.code === 200) {
            // 使用 api.filterDepts 过滤科室
            if (this.api.filterDepts) {
              this.doctorList = res.body.filter(doc => this.api.filterDepts.includes(doc.deptName))
            } else {
              this.doctorList = res.body
            }
          }
        })
      } else {
        this.doctorList = []
      }
    }
  },
  watch: {
    doctorName(val) {
      const doctor = this.doctorList.find(d => d.doctorName === val)
      if (doctor) {
        this.$emit('emitSelectItem', doctor)
      }
    }
  }
}
</script>

<style scoped>
</style>
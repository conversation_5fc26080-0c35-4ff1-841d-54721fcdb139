<template>
  <div class="header">
    <div class="logo">
      <span class="ft_14"></span>
      <span class="min"></span>
    </div>
    <div class="left">
      <div>
        <i class="el-icon-menu"></i>
      </div>
      <breadcrump></breadcrump>
    </div>
    <div class="right">
      <div class="avatar">
        <img :src="userAvatar" alt="">
      </div>
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="el-dropdown-link doctorName">
          {{doctorName}}<i class="el-icon-arrow-down" style="margin-left:10px;"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="goPerInfo">个人资料</el-dropdown-item>
          <el-dropdown-item command="refreshToken">刷新会话</el-dropdown-item>
          <el-dropdown-item command="loginout">退出系统</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
import BreadCrump from './Breadcrumb'
import { imgview, commonAjax } from '../../api/api.js'
import docAvatar from '@/assets/img/doc_avatar.png'
import cookie from 'static/js/util.js'
import { lStore, sStore } from 'js/yydjs';
import { socketApi } from 'services';

export default {
  data() {
    return {
      doctorName: '',
      userAvatar: '',
    }
  },
  components: {
    breadcrump: BreadCrump,
  },
  created() {
    //修改头像后更新头像
    vm.$on('updataAvatar', data => {
      this.userAvatar = imgview + data;
    });
  },
  methods: {
    username() {
      let doctorName = sStore.get('doctorName')
      let avatar = sStore.get('userAvatar')
      this.doctorName = doctorName ? doctorName : this.doctorName
      this.userAvatar = +avatar ? imgview + avatar : docAvatar
    },
    loginout() {
      let peoCode = sStore.get('peoCode');

      delCookie('uid')
      delCookie('sdktoken')
      lStore.remove('nimAccount');
      lStore.remove('nimToken');
      sStore.clear();
      sStore.set('peoCode', peoCode);

      this.$router.replace('/login');
    },
    // 刷新会话token方法
    refreshToken() {
      // 从localStorage获取备份token
      const backupToken = localStorage.getItem('backup_token');
      if (backupToken) {
        // 更新会话token
        sStore.set('accessToken', backupToken);
        // 重置错误计数
        localStorage.setItem('api_error_count', 0);
        
        this.$message({
          type: 'success',
          message: '会话已刷新'
        });
      } else {
        this.$message({
          type: 'warning',
          message: '无可用会话信息，请重新登录'
        });
        // 如果没有备份token，可能需要重新登录
        this.loginout();
      }
    },
    handleCommand(command) {
      if (command == 'loginout') {
        this.loginout();
      } else if (command == 'goPerInfo') {
        this.$router.push('personInfo')
      } else if (command == 'refreshToken') {
        this.refreshToken();
      }
    },
    //获取医生ID和机构ID
    getDocIdAndOrgId() {
      commonAjax('cas.foshan_doctorOnlineAdminService', 'doctorInfo', []).then(res => {
        if (res.code == 200) {
          let { doctorId, standardDeptId, orgId, avatarFileId } = res.body[0];

          sStore.set('docId', doctorId)
          sStore.set('standardDeptId', standardDeptId)
          sStore.set('orgId', orgId)
          sStore.set('userAvatar', avatarFileId)
          sStore.set('doctorInfo', res.body[0]);
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      })
    },
  },
  mounted() {
    this.username();
    this.getDocIdAndOrgId();
  }
}

</script>
<style lang="scss" scoped>
@import '../../assets/css/var.scss';
.header {
  width: 100%;
  top: 0;
  left: 0;
  position: fixed;
  height: 60px;
  background-color: #fff;
  z-index: 10;
  font-size: 16px;
  box-shadow: 5px;
  .logo {
    width: 180px;
    height: 60px;
    text-align: center;
    color: #333;
    background: #fff url('../../assets/img/title.png') no-repeat center;
    transition: width 0.35s;
  }
  .right {
    position: absolute;
    right: 30px;
    top: 0;
    display: flex;
    align-items: center;
    min-width: 230px;
    height: 60px;
    line-height: 60px;
    color: #333;
    font-size: 16px;
    .badgeWrap {
      width: 30px;
      height: 100%;
      background: url('../../assets/img/icon_news.png') no-repeat center center;
      background-size: 20px;
      position: relative;
      /deep/ .el-badge__content {
        position: absolute;
        top: 10px !important;
        right: -15px !important;
      }
    }
    .avatar {
      width: 48px;
      height: 48px;
      overflow: hidden;
      border-radius: 50%;
      margin-left: 30px;
      img {
        width: 100%;
      }
    }

    /deep/ .el-dropdown {
      font-size: 16px;
      min-width: 100px;
      margin-left: 30px;
      cursor: pointer;
    }
    /deep/ .el-badge {
      font-size: 20px;
      cursor: pointer;
      // right: 80px;
      /deep/ .el-badge__content {
        top: 25px;
        font-size: 10px;
      }
    }
  }
  .left {
    position: absolute;
    left: 180px;
    height: 60px;
    min-width: 500px;
    display: inline-block;
    top: 0;
    padding-left: 10px;
    font-size: 0px;
    >div {
      color: 333;
      line-height: 60px;
      font-size: 14px;
      display: inline-block;
      vertical-align: middle;
    }
    >div:first-child {
      font-size: 24px;
    }
    >a {
      line-height: 60px;
      color: white;
    }
    >span {
      font-size: 14px;
      line-height: 60px;
      color: #333;
    }
  }
}

</style>
<style>
.el-dropdown-menu {
  top: 47px !important;
  border: #333 !important;
}
.popper__arrow {
  border-bottom-color: #ccc !important;
}

</style>

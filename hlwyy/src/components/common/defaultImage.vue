<template>
    <div
        :class="{
            defaultImage:true,
            active:show,
        }"
    >
        <ul class="defaultList fullColor">
            <li
                v-for="(item,index) in defaultList"
                :style="{
                    backgroundImage:'url('+require(`img/${item.src}`)+')',
                }"
                :class="{
                    active:showIndex==index,
                }"
            >
                <div
                    v-if="item.text"
                    v-html="item.text"
                    class="hintText"
                ></div>
                <div
                    v-if="item.html"
                    v-html="item.html.text"
                    @click="item.html.click"
                    class="hintText"
                ></div>
            </li>
        </ul>
    </div>
</template>

<script>
    export default{
        data(){
            return{
                query:this.$router.currentRoute.query,
                defaultList:[
                    {
                        src:'no_result.png',
                        text:'<h3>暂无数据</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无患者数据</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无患者数据</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无处方信息</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无就诊记录</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无药品处方</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无检查</h3>',
                    },
                    {
                        src:'no_result.png',
                        text:'<h3>暂无检验</h3>',
                    },
                ],
            }
        },

        /*
            <defaultImage
                :show="showDefaultImage"
            />
        */

        props:{
            show:{//控制缺省图的显示
                type:Boolean,
                default:true,
            },
            showIndex:{//根据索引显示定义的缺省图
                type:Number,
                default:0,
            },
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .defaultImage{
        position: relative;
        display: none;
        &.active{
            display: block;
        }
        .defaultList{
            width: 100%;
            position: absolute;
            padding: 20% 0;
            background-color: #fff;
            li{
                padding-top: 200px;
                background: no-repeat top center;
                background-size: 300px;
                display: none;
                &.active{
                    display: block;
                }
                .hintText{
                    line-height: 24px;
                    text-align: center;
                    font-size: 18px;
                    color: #999;
                    /deep/ *{
                        font-weight: normal;
                    }
                }
                &:nth-of-type(3){
                    padding-top: 150px;
                    background-size: 200px;
                }
            }
        }
    }
</style>

<template>
    <div class="exam-appointment">
        <div class="search-bar">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="患者姓名">
                    <el-input v-model="searchForm.patientName" placeholder="请输入患者姓名"></el-input>
                </el-form-item>
                <el-form-item label="预约状态">
                    <el-select v-model="searchForm.status" placeholder="请选择预约状态">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="待审核" value="0"></el-option>
                        <el-option label="已通过" value="1"></el-option>
                        <el-option label="已拒绝" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="预约时间">
                    <el-date-picker
                        v-model="searchForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="table-container">
            <el-table
                :data="tableData"
                border
                style="width: 100%">
                <el-table-column
                    prop="patientName"
                    label="患者姓名"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="gender"
                    label="性别"
                    width="80">
                    <template slot-scope="scope">
                        {{scope.row.gender === '1' ? '男' : '女'}}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="age"
                    label="年龄"
                    width="80">
                </el-table-column>
                <el-table-column
                    prop="phone"
                    label="联系电话"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="examType"
                    label="检查类型"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="examItem"
                    label="检查项目"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="appointmentTime"
                    label="预约时间"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="状态"
                    width="100">
                    <template slot-scope="scope">
                        <el-tag :type="getStatusType(scope.row.status)">{{getStatusText(scope.row.status)}}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="createTime"
                    label="申请时间"
                    width="160">
                </el-table-column>
                <el-table-column
                    label="操作"
                    width="200">
                    <template slot-scope="scope">
                        <el-button
                            size="mini"
                            @click="handleView(scope.row)">查看</el-button>
                        <el-button
                            v-if="scope.row.status === '0'"
                            size="mini"
                            type="primary"
                            @click="handleApprove(scope.row)">通过</el-button>
                        <el-button
                            v-if="scope.row.status === '0'"
                            size="mini"
                            type="danger"
                            @click="handleReject(scope.row)">拒绝</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>
        </div>

        <!-- 修改预览弹窗组件的props -->
        <previewInsApp 
            v-if="showpreviewInsApp" 
            :show="showpreviewInsApp" 
            :parent="this"
            :showName="'showpreviewInsApp'"
            :previewDetail="previewDetail"
            :applyId="applyId"
            :signature="null">
        </previewInsApp>

        <!-- 申请单详情弹窗 -->
        <el-dialog
            title="检查申请单详情"
            :visible.sync="dialogVisible"
            width="60%"
            :before-close="handleClose">
            <div class="application-form">
                <div class="form-header">
                    <h2>{{orgName}}</h2>
                    <h3>检查申请单</h3>
                </div>
                <div class="form-content">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <div class="form-item">
                                <label>患者姓名：</label>
                                <span>{{currentDetail.patientName}}</span>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="form-item">
                                <label>性别：</label>
                                <span>{{currentDetail.gender === '1' ? '男' : '女'}}</span>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="form-item">
                                <label>年龄：</label>
                                <span>{{currentDetail.age}}岁</span>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <div class="form-item">
                                <label>联系电话：</label>
                                <span>{{currentDetail.phone}}</span>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="form-item">
                                <label>检查类型：</label>
                                <span>{{currentDetail.examType}}</span>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="form-item">
                                <label>检查项目：</label>
                                <span>{{currentDetail.examItem}}</span>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <div class="form-item">
                                <label>预约时间：</label>
                                <span>{{currentDetail.appointmentTime}}</span>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="form-item">
                                <label>申请时间：</label>
                                <span>{{currentDetail.createTime}}</span>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="form-item">
                                <label>状态：</label>
                                <el-tag :type="getStatusType(currentDetail.status)">
                                    {{getStatusText(currentDetail.status)}}
                                </el-tag>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">关 闭</el-button>
                <el-button type="primary" @click="printForm">打 印</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import previewInsApp from 'components/common/previewInsApp.vue'
import yydDialog from 'components/common/yydDialog'
import { sStore } from 'js/yydjs'

export default {
    data() {
        return {
            searchForm: {
                patientName: '',
                status: '',
                dateRange: []
            },
            tableData: [], // 表格数据
            currentPage: 1,
            pageSize: 10,
            total: 0,
            showpreviewInsApp: false, // 控制预览弹窗显示
            previewDetail: {}, // 预览的申请单详情
            applyId: -1, // 申请单ID
            dialogVisible: false, // 控制弹窗显示
            currentDetail: {}, // 当前查看的申请单详情
            orgName: '', // 医院名称
            deptName: '' // 科室名称
        }
    },
    mounted() {
        this.loadData()
        // 在mounted时获取sStore的值
        this.orgName = sStore.get('orgName') || ''
        this.deptName = sStore.get('deptName') || ''
    },
    methods: {
        // 加载表格数据
        loadData() {
            // TODO: 调用后端API获取数据
            // 这里暂时使用模拟数据
            this.tableData = [
                {
                    patientName: '黄翠娥',
                    gender: '1',
                    age: 30,
                    phone: '13800138000',
                    examType: 'CT',
                    examItem: '胸部CT',
                    appointmentTime: '2024-03-20 09:30',
                    status: '0',
                    createTime: '2024-03-18 14:30'
                },
                // 更多数据...
            ]
            this.total = this.tableData.length
        },
        // 处理查询
        handleSearch() {
            this.currentPage = 1
            this.loadData()
        },
        // 重置查询条件
        resetSearch() {
            this.searchForm = {
                patientName: '',
                status: '',
                dateRange: []
            }
            this.handleSearch()
        },
        // 获取状态标签类型
        getStatusType(status) {
            const statusMap = {
                '0': 'warning',
                '1': 'success',
                '2': 'danger'
            }
            return statusMap[status]
        },
        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                '0': '待审核',
                '1': '已通过',
                '2': '已拒绝'
            }
            return statusMap[status]
        },
        // 查看详情
        handleView(row) {
            this.currentDetail = {
                ...row,
                orgName: this.orgName,
                deptName: this.deptName
            };
            this.dialogVisible = true;
        },
        // 通过申请
        handleApprove(row) {
            // TODO: 实现通过申请功能
            this.$confirm('确认通过该预约申请?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 调用通过API
                this.$message({
                    type: 'success',
                    message: '已通过预约申请'
                })
                this.loadData()
            }).catch(() => {})
        },
        // 拒绝申请
        handleReject(row) {
            // TODO: 实现拒绝申请功能
            this.$confirm('确认拒绝该预约申请?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 调用拒绝API
                this.$message({
                    type: 'success',
                    message: '已拒绝预约申请'
                })
                this.loadData()
            }).catch(() => {})
        },
        // 处理分页大小变化
        handleSizeChange(val) {
            this.pageSize = val
            this.loadData()
        },
        // 处理页码变化
        handleCurrentChange(val) {
            this.currentPage = val
            this.loadData()
        },
        // 关闭弹窗前的回调
        handleClose(done) {
            done();
        },
        // 打印申请单
        printForm() {
            window.print();
        }
    },
    components: {
        previewInsApp,
        yydDialog
    }
}
</script>

<style lang="scss" scoped>
.exam-appointment {
    padding: 20px;

    .search-bar {
        margin-bottom: 20px;
        background: #fff;
        padding: 20px;
        border-radius: 4px;
    }

    .table-container {
        background: #fff;
        padding: 20px;
        border-radius: 4px;

        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }
    }

    .application-form {
        padding: 20px;
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
            
            h2 {
                font-size: 24px;
                margin-bottom: 10px;
            }
            
            h3 {
                font-size: 20px;
                color: #666;
            }
        }
        
        .form-content {
            .el-row {
                margin-bottom: 20px;
            }
            
            .form-item {
                display: flex;
                align-items: center;
                
                label {
                    width: 100px;
                    color: #666;
                }
                
                span {
                    flex: 1;
                }
            }
        }
    }
}
</style> 
<template>
    <div class="patient-group">
        <!-- 页面标题 -->
        <div class="page-title" v-if="groupName">
            <h2>{{ groupName }}</h2>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="病种">
                    <el-select v-model="searchForm.diseaseType" placeholder="全部">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="手术患者出院后随访" value="手术患者出院后随访"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="姓名">
                    <el-input v-model="searchForm.name" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="证件号码">
                    <el-input v-model="searchForm.idCard" placeholder="请输入证件号码"></el-input>
                </el-form-item>
                <el-form-item label="手机号">
                    <el-input v-model="searchForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                </el-form-item>
            </el-form>
            <div class="tool-buttons">
                <el-button type="primary" @click="goToGroupList">分组列表</el-button>
            </div>
        </div>

        <!-- 表格 -->
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="name" label="姓名" width="100"></el-table-column>
            <el-table-column prop="age" label="年龄" width="80"></el-table-column>
            <el-table-column prop="gender" label="性别" width="80"></el-table-column>
            <el-table-column prop="phone" label="联系电话" width="120"></el-table-column>
            <el-table-column prop="idCard" label="证件号码" width="180"></el-table-column>
            <el-table-column prop="disease" label="所患疾病" width="180"></el-table-column>
            <el-table-column label="申请入组时间" width="180">
                <template slot-scope="scope">
                    <span>{{ formatDate(scope.row.joinTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="patientId" label="患者ID" width="120" v-if="false"></el-table-column>
            <el-table-column prop="status" label="入组状态" width="100">
                <template slot-scope="scope">
                    <span :class="{'status-reviewed': scope.row.status === 1}">
                        {{ statusText(scope.row.status) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="300">
                <template slot-scope="scope">
                    <el-button type="primary" size="small" @click="handleDetail(scope.row)">详情</el-button>
                    <el-button type="success" size="small" @click="handleApprove(scope.row)" v-if="scope.row.status === 0 || scope.row.status === '0' || scope.row.status === 'pending'">审核入组</el-button>
                    <el-button type="danger" size="small" @click="handleReject(scope.row)" v-if="scope.row.status === 0 || scope.row.status === '0' || scope.row.status === 'pending'">不同意</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>

        <!-- 详情弹窗 -->
        <el-dialog
            title="患者详情"
            :visible.sync="detailDialogVisible"
            width="80%"
            :before-close="handleDetailClose">
            <div class="patient-detail">
                <!-- 患者基本信息 -->
                <div class="basic-info">
                    <div class="info-row">
                        <span class="label">姓名：</span>
                        <span class="value">{{currentPatient.name}}</span>
                        <span class="label">性别：</span>
                        <span class="value">{{currentPatient.gender}}</span>
                        <span class="label">出生日期：</span>
                        <span class="value">{{currentPatient.birthDate || '1954-06-06'}}</span>
                        <span class="label">病历号：</span>
                        <span class="value">{{currentPatient.medicalNo || '636468776'}}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">身份证号：</span>
                        <span class="value">{{currentPatient.idCard}}</span>
                        <span class="label">联系电话：</span>
                        <span class="value">{{currentPatient.phone}}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">入组病种：</span>
                        <span class="value">{{currentPatient.disease}}</span>
                    </div>
                </div>

                <!-- 健康档案标签页 -->
                <div class="health-records">
                    <el-tabs v-model="activeHealthTab">
                        <el-tab-pane label="专科病历(4)" name="medicalRecords">
                            <div class="record-list">
                                <div class="record-item">
                                    <div class="record-type">入组评估</div>
                                    <div class="record-content">
                                        <div class="record-title">手术患者出院后随访（赵宝骏团队：上海第四人民医院）</div>
                                        <div class="record-info">
                                            <span>入组方式：院内导入</span>
                                            <span>申请时间：2024-12-27 15:24:37</span>
                                            <span>审核时间：2024-12-27 15:24:37</span>
                                        </div>
                                        <div class="record-info">
                                            <span>审核医生：赵宝骏</span>
                                            <span>审核结果：审核通过</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="record-item">
                                    <div class="record-type">随访记录</div>
                                    <div class="record-content">
                                        <div class="record-title">手术患者出院后随访</div>
                                        <div class="record-status-group">
                                            <span>已执行(0)</span>
                                            <span>已终止(0)</span>
                                            <span>待确认(0)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="record-item">
                                    <div class="record-type">健康记录</div>
                                    <div class="record-content">
                                        <div class="record-title">手术患者出院后随访</div>
                                        <div class="record-status-group">
                                            <span>已执行(0)</span>
                                            <span>已终止(0)</span>
                                            <span>待确认(0)</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="record-item">
                                    <div class="record-type">健康活动</div>
                                    <div class="record-content">
                                        <div class="record-title">手术患者出院后随访</div>
                                        <div class="record-status-group">
                                            <span>已执行(0)</span>
                                            <span>已终止(0)</span>
                                            <span>待确认(0)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="门诊记录(1)" name="outpatientRecords">
                            <div class="record-list">
                                <div class="record-item">
                                    <div class="record-type">就诊记录</div>
                                    <div class="record-content">
                                        <div class="record-title">门诊就诊记录</div>
                                        <div class="record-info">
                                            <span>就诊时间：2024-12-27</span>
                                            <span>就诊科室：普外科</span>
                                            <span>就诊医生：赵宝骏</span>
                                        </div>
                                        <div class="record-info">
                                            <span>诊断：术后随访</span>
                                            <span>就诊状态：已完成</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="住院记录(1)" name="inpatientRecords">
                            <div class="record-list">
                                <div class="record-item">
                                    <div class="record-type">就诊记录</div>
                                    <div class="record-content">
                                        <div class="record-title">住院就诊记录</div>
                                        <div class="record-info">
                                            <span>入院时间：2024-12-20</span>
                                            <span>出院时间：2024-12-27</span>
                                            <span>住院科室：普外科</span>
                                        </div>
                                        <div class="record-info">
                                            <span>主治医生：赵宝骏</span>
                                            <span>入院诊断：腹部手术</span>
                                            <span>住院状态：已出院</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="检验记录(0)" name="testRecords"></el-tab-pane>
                        <el-tab-pane label="检查记录(0)" name="examRecords"></el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios';

export default {
    name: 'PatientGroup',
    data() {
        return {
            // 搜索表单
            searchForm: {
                diseaseType: '',
                name: '',
                idCard: '',
                phone: '',
                groupId: ''
            },
            // 表格数据
            tableData: [],
            // 分页
            currentPage: 1,
            pageSize: 10,
            total: 0,
            // 详情弹窗
            detailDialogVisible: false,
            currentPatient: {},
            activeHealthTab: 'medicalRecords',
            loading: false,
            groupName: '患者分组'
        }
    },
    mounted() {
        // 检查是否从分组列表页面传递了分组ID和名称
        if (this.$route.query.groupId) {
            this.searchForm.groupId = this.$route.query.groupId;
            // 加载标题
            this.groupName = this.$route.query.groupName || '患者分组';
            // 查询指定分组的患者列表
            this.getPatientList();
        } else {
            // 如果没有分组ID，这是正常的患者入组管理页面
            // 可以查询所有已入组的患者，或者显示空状态
            this.groupName = '患者入组管理';
            // 加载所有已入组患者
            this.getPatientList();
        }
    },
    methods: {
        // 获取患者列表
        getPatientList() {
            this.loading = true;
            
            // 构建查询参数
            const params = {
                page: this.currentPage - 1, // 后端分页从0开始
                size: this.pageSize
            };
            
            // 添加其他搜索条件
            if (this.searchForm.name) params.name = this.searchForm.name;
            if (this.searchForm.idCard) params.idCard = this.searchForm.idCard;
            if (this.searchForm.phone) params.phone = this.searchForm.phone;
            if (this.searchForm.diseaseType) params.diseaseType = this.searchForm.diseaseType;
            
            // 根据是否有分组ID决定调用不同的API
            let apiUrl;
            if (this.searchForm.groupId) {
                // 查询指定分组的患者
                apiUrl = `/api/emr/patient/group/${this.searchForm.groupId}/patients`;
                params.groupId = this.searchForm.groupId;
            } else {
                // 查询所有已入组的患者
                apiUrl = `/api/emr/patient/group/patients`;
            }
            
            axios({
                method: 'get',
                url: apiUrl,
                params: params,
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code === 200) {
                    // 确保每条记录都有patientId字段
                    this.tableData = res.data.data.records.map(item => ({
                        ...item,
                        patientId: item.patientId || item.id
                    }));
                    this.total = res.data.data.total;
                } else {
                    this.$message.error(res.data.message || '获取患者列表失败');
                }
            }).catch(err => {
                console.error('获取患者列表出错', err);
                this.$message.error('获取患者列表出错，请稍后重试');
            }).finally(() => {
                this.loading = false;
            });
        },
        // 搜索患者
        handleSearch() {
            this.currentPage = 1;
            this.getPatientList();
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                diseaseType: '',
                name: '',
                idCard: '',
                phone: '',
                groupId: this.searchForm.groupId // 保留分组ID
            };
            this.currentPage = 1;
            this.getPatientList();
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val;
            this.getPatientList();
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getPatientList();
        },
        // 显示患者详情
        handleDetail(row) {
            if (!row || (!row.patientId && !row.id)) {
                this.$message.error('无效的患者信息');
                return;
            }
            this.loading = true;
            this.currentPatient = row;
            this.detailDialogVisible = true;
            this.activeHealthTab = 'medicalRecords';
            // 加载患者详细信息
            this.getPatientDetail(row.patientId || row.id);
        },
        // 获取患者详情
        getPatientDetail(patientId) {
            if (!patientId) {
                this.loading = false;
                this.$message.error('无效的患者ID');
                return;
            }
            axios({
                method: 'get',
                url: `/api/emr/patient/${patientId}`,
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code === 200) {
                    // 合并患者详情数据
                    this.currentPatient = {...this.currentPatient, ...res.data.data};
                } else {
                    this.$message.error(res.data.message || '获取患者详情失败');
                }
            }).catch(err => {
                console.error('获取患者详情出错', err);
                this.$message.error('获取患者详情出错，请稍后重试');
            }).finally(() => {
                this.loading = false;
            });
        },
        // 审核患者
        handleApprove(row) {
            this.$confirm('确认审核该患者?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true;
                axios({
                    method: 'put',
                    url: `/api/emr/patient/${row.patientId}/status`,
                    data: { status: "1" }, // 1-已入组，使用字符串
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(res => {
                    if (res.data.code === 200) {
                        this.$message.success('审核成功');
                        this.getPatientList();
                    } else {
                        this.$message.error(res.data.message || '审核失败');
                    }
                }).catch(err => {
                    console.error('审核出错', err);
                    this.$message.error('审核出错，请稍后重试');
                }).finally(() => {
                    this.loading = false;
                });
            }).catch(() => {
                // 取消操作
            });
        },
        // 不同意
        handleReject(row) {
            this.$confirm('确认拒绝该患者入组申请?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true;
                axios({
                    method: 'put',
                    url: `/api/emr/patient/${row.patientId}/status`,
                    data: { status: "2" }, // 2-已拒绝，使用字符串
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(res => {
                    if (res.data.code === 200) {
                        this.$message.success('已拒绝入组申请');
                        this.getPatientList();
                    } else {
                        this.$message.error(res.data.message || '操作失败');
                    }
                }).catch(err => {
                    console.error('拒绝入组失败', err);
                    this.$message.error('拒绝入组失败，请稍后重试');
                }).finally(() => {
                    this.loading = false;
                });
            }).catch(() => {
                // 取消操作
            });
        },
        // 关闭详情弹窗
        handleDetailClose() {
            this.detailDialogVisible = false;
            this.currentPatient = {};
        },
        goToGroupList() {
            this.$router.push('/followUp/groupList');
        },
        formatDate(val) {
            if (!val) return '-';
            const d = new Date(val);
            if (isNaN(d.getTime())) return '-';
            return d.getFullYear() + '-' +
                (d.getMonth() + 1).toString().padStart(2, '0') + '-' +
                d.getDate().toString().padStart(2, '0') + ' ' +
                d.getHours().toString().padStart(2, '0') + ':' +
                d.getMinutes().toString().padStart(2, '0');
        },
        statusText(val) {
            // 处理数字状态
            if (val === 1 || val === '1') return '已入组';
            if (val === 0 || val === '0') return '待审核';
            if (val === 2 || val === '2') return '已拒绝';
            // 处理字符串状态
            if (val === 'pending') return '待审核';
            if (val === 'approved') return '已通过';
            if (val === 'rejected') return '已拒绝';
            return val;
        }
    },
    filters: {
        dateFormat(val) {
            if (!val) return '';
            const d = new Date(val);
            return d.getFullYear() + '-' + (d.getMonth()+1).toString().padStart(2,'0') + '-' + d.getDate().toString().padStart(2,'0') +
                ' ' + d.getHours().toString().padStart(2,'0') + ':' + d.getMinutes().toString().padStart(2,'0');
        }
    }
}
</script>

<style lang="scss" scoped>
.patient-group {
    padding: 20px;
}

.page-title {
    margin-bottom: 20px;
    
    h2 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 500;
    }
}

.search-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-form {
    flex: 1;
}

.tool-buttons {
    margin-left: 20px;
}

.status-reviewed {
    color: #67C23A;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

/deep/ .el-table {
    .el-button {
        padding: 5px 12px;
    }
    .el-button + .el-button {
        margin-left: 8px;
    }
}

.patient-detail {
    .basic-info {
        background: #f5f7fa;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 4px;

        .info-row {
            margin-bottom: 15px;
            line-height: 24px;
            &:last-child {
                margin-bottom: 0;
            }

            .label {
                color: #606266;
                margin-right: 10px;
                display: inline-block;
                min-width: 80px;
            }

            .value {
                color: #303133;
                margin-right: 30px;
            }
        }
    }

    .health-records {
        .record-list {
            .record-item {
                display: flex;
                padding: 20px 0;
                border-bottom: 1px solid #EBEEF5;

                .record-type {
                    width: 120px;
                    color: #606266;
                    font-weight: bold;
                }

                .record-content {
                    flex: 1;

                    .record-title {
                        color: #303133;
                        font-weight: 500;
                        margin-bottom: 10px;
                    }

                    .record-info {
                        color: #909399;
                        font-size: 14px;
                        margin-bottom: 5px;

                        span {
                            margin-right: 20px;
                        }
                    }

                    .record-status-group {
                        color: #909399;
                        font-size: 14px;

                        span {
                            margin-right: 20px;
                        }
                    }
                }
            }
        }
    }
}
</style>
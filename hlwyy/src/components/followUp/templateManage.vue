<template>
    <div class="template-manage">
        <div class="header">
            <h2>模板管理</h2>
        </div>
        
        <!-- 标签页导航 -->
        <el-tabs v-model="activeTab" class="template-tabs">
            <!-- 智能随访模块暂时屏蔽 -->
            <!-- <el-tab-pane label="智能随访" name="smartFollowUp">
                <smart-follow-up ref="smartFollowUp"></smart-follow-up>
            </el-tab-pane> -->
            <el-tab-pane label="智能表单" name="smartForm">
                <smart-form ref="smartForm"></smart-form>
            </el-tab-pane>
            <el-tab-pane label="问卷模板" name="questionnaire">
                <questionnaire-template ref="questionnaire"></questionnaire-template>
            </el-tab-pane>
            <el-tab-pane label="内容模板" name="content">
                <content-template ref="content"></content-template>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
// 智能随访模块暂时屏蔽
// import SmartFollowUp from './children/SmartFollowUp.vue'
import SmartForm from './children/SmartForm.vue'
import QuestionnaireTemplate from './children/QuestionnaireTemplate.vue'
import ContentTemplate from './children/ContentTemplate.vue'

export default {
    name: 'TemplateManage',
    components: {
        // SmartFollowUp, // 智能随访模块暂时屏蔽
        SmartForm,
        QuestionnaireTemplate,
        ContentTemplate
    },
    data() {
        return {
            activeTab: 'smartForm' // 默认标签页改为智能表单
        }
    },
    methods: {
        // 刷新当前活跃的标签页
        refreshCurrentTab() {
            const currentComponent = this.$refs[this.activeTab];
            if (currentComponent && currentComponent.refresh) {
                currentComponent.refresh();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.template-manage {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.header {
    margin-bottom: 20px;
    
    h2 {
        margin: 0;
        color: #303133;
        font-size: 20px;
        font-weight: 500;
    }
}

.template-tabs {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    /deep/ .el-tabs__header {
        margin: 0;
        border-bottom: 1px solid #e4e7ed;
        
        .el-tabs__nav-wrap {
            padding: 0 20px;
        }
        
        .el-tabs__item {
            height: 50px;
            line-height: 50px;
            padding: 0 20px;
            font-size: 14px;
        }
    }
    
    /deep/ .el-tab-pane {
        padding: 0;
    }
}
</style> 
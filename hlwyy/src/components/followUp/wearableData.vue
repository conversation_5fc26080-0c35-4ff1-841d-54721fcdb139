<template>
    <div class="wearable-data">
        <!-- 搜索区域 -->
        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="患者姓名">
                    <el-input v-model="searchForm.name" placeholder="请输入患者姓名"></el-input>
                </el-form-item>
                <el-form-item label="监测类型">
                    <el-select v-model="searchForm.monitorType" placeholder="请选择监测类型">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="血压" value="血压"></el-option>
                        <el-option label="血糖" value="血糖"></el-option>
                        <el-option label="血氧" value="血氧"></el-option>
                        <el-option label="心率" value="心率"></el-option>
                        <el-option label="体温" value="体温"></el-option>
                        <el-option label="体重" value="体重"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="监测时间">
                    <el-date-picker
                        v-model="searchForm.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="异常状态">
                    <el-select v-model="searchForm.status" placeholder="请选择状态">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="正常" value="normal"></el-option>
                        <el-option label="异常" value="abnormal"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 数据统计卡片 -->
        <div class="statistics-cards">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-title">今日监测人数</div>
                            <div class="stat-value">128</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-title">异常数据数量</div>
                            <div class="stat-value warning">23</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-title">设备在线数量</div>
                            <div class="stat-value">156</div>
                        </div>
                    </el-card>
                </el-col>
                <el-col :span="6">
                    <el-card shadow="hover">
                        <div class="stat-item">
                            <div class="stat-title">已处理异常</div>
                            <div class="stat-value success">18</div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="monitorTime" label="监测时间" width="180"></el-table-column>
                <el-table-column prop="name" label="患者姓名" width="120"></el-table-column>
                <el-table-column prop="age" label="年龄" width="80"></el-table-column>
                <el-table-column prop="monitorType" label="监测类型" width="100"></el-table-column>
                <el-table-column prop="value" label="监测值" width="150">
                    <template slot-scope="scope">
                        <span :class="{'abnormal-value': scope.row.status === 'abnormal'}">
                            {{scope.row.value}}
                            {{getUnit(scope.row.monitorType)}}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.status === 'normal' ? 'success' : 'danger'">
                            {{scope.row.status === 'normal' ? '正常' : '异常'}}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="device" label="设备信息" width="150"></el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                        <el-button type="text" @click="handleDetail(scope.row)">查看详情</el-button>
                        <el-button type="text" v-if="scope.row.status === 'abnormal'" 
                            @click="handleProcess(scope.row)">处理</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>

        <!-- 详情弹窗 -->
        <el-dialog title="监测详情" :visible.sync="detailDialogVisible" width="60%">
            <div class="monitor-detail">
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-row">
                        <span class="label">患者姓名：</span>
                        <span class="value">{{currentDetail.name}}</span>
                        <span class="label">年龄：</span>
                        <span class="value">{{currentDetail.age}}岁</span>
                        <span class="label">监测时间：</span>
                        <span class="value">{{currentDetail.monitorTime}}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">设备信息：</span>
                        <span class="value">{{currentDetail.device}}</span>
                        <span class="label">监测类型：</span>
                        <span class="value">{{currentDetail.monitorType}}</span>
                    </div>
                </div>
                <div class="detail-section">
                    <h4>监测数据</h4>
                    <div class="detail-row" v-if="currentDetail.monitorType === '血压'">
                        <span class="label">收缩压：</span>
                        <span class="value">{{currentDetail.systolic}} mmHg</span>
                        <span class="label">舒张压：</span>
                        <span class="value">{{currentDetail.diastolic}} mmHg</span>
                    </div>
                    <div class="detail-row" v-else>
                        <span class="label">监测值：</span>
                        <span class="value">{{currentDetail.value}} {{getUnit(currentDetail.monitorType)}}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">参考范围：</span>
                        <span class="value">{{getReferenceRange(currentDetail.monitorType)}}</span>
                    </div>
                </div>
                <div class="detail-section" v-if="currentDetail.status === 'abnormal'">
                    <h4>异常说明</h4>
                    <div class="detail-row">
                        <span class="label">异常类型：</span>
                        <span class="value warning">{{getAbnormalType(currentDetail)}}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">建议措施：</span>
                        <span class="value">{{getSuggestions(currentDetail)}}</span>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'WearableData',
    data() {
        return {
            // 搜索表单
            searchForm: {
                name: '',
                monitorType: '',
                dateRange: [],
                status: ''
            },
            // 表格数据
            tableData: [
                {
                    monitorTime: '2024-12-14 08:30:00',
                    name: '张建华',
                    age: 78,
                    monitorType: '血压',
                    value: '145/95',
                    systolic: 145,
                    diastolic: 95,
                    status: 'abnormal',
                    device: 'BP-102智能血压计'
                },
                {
                    monitorTime: '2024-12-14 09:00:00',
                    name: '李秀英',
                    age: 82,
                    monitorType: '血糖',
                    value: '6.2',
                    status: 'normal',
                    device: 'BG-201血糖仪'
                },
                {
                    monitorTime: '2024-12-14 09:30:00',
                    name: '王志强',
                    age: 75,
                    monitorType: '心率',
                    value: '88',
                    status: 'normal',
                    device: 'HR-401心率监测仪'
                },
                {
                    monitorTime: '2024-12-14 10:00:00',
                    name: '赵丽华',
                    age: 71,
                    monitorType: '血氧',
                    value: '94',
                    status: 'abnormal',
                    device: 'OX-305血氧仪'
                },
                {
                    monitorTime: '2024-12-14 10:30:00',
                    name: '陈志明',
                    age: 77,
                    monitorType: '体温',
                    value: '37.8',
                    status: 'abnormal',
                    device: 'TM-501体温计'
                },
                {
                    monitorTime: '2024-12-14 11:00:00',
                    name: '刘桂花',
                    age: 69,
                    monitorType: '体重',
                    value: '58.5',
                    status: 'normal',
                    device: 'WT-301智能体重秤'
                },
                {
                    monitorTime: '2024-12-14 11:30:00',
                    name: '马传福',
                    age: 84,
                    monitorType: '血压',
                    value: '120/80',
                    systolic: 120,
                    diastolic: 80,
                    status: 'normal',
                    device: 'BP-102智能血压计'
                },
                {
                    monitorTime: '2024-12-14 12:00:00',
                    name: '孙玉梅',
                    age: 73,
                    monitorType: '血糖',
                    value: '8.2',
                    status: 'abnormal',
                    device: 'BG-201血糖仪'
                },
                {
                    monitorTime: '2024-12-14 12:30:00',
                    name: '胡建国',
                    age: 76,
                    monitorType: '心率',
                    value: '72',
                    status: 'normal',
                    device: 'HR-401心率监测仪'
                },
                {
                    monitorTime: '2024-12-14 13:00:00',
                    name: '周淑兰',
                    age: 80,
                    monitorType: '体温',
                    value: '36.5',
                    status: 'normal',
                    device: 'TM-501体温计'
                },
                {
                    monitorTime: '2024-12-14 13:30:00',
                    name: '吴明华',
                    age: 68,
                    monitorType: '血氧',
                    value: '98',
                    status: 'normal',
                    device: 'OX-305血氧仪'
                },
                {
                    monitorTime: '2024-12-14 14:00:00',
                    name: '郭春莲',
                    age: 74,
                    monitorType: '体重',
                    value: '62.8',
                    status: 'normal',
                    device: 'WT-301智能体重秤'
                }
            ],
            // 分页
            currentPage: 1,
            pageSize: 10,
            total: 12,
            // 详情弹窗
            detailDialogVisible: false,
            currentDetail: {}
        }
    },
    methods: {
        // 获取单位
        getUnit(type) {
            const units = {
                '血压': 'mmHg',
                '血糖': 'mmol/L',
                '血氧': '%',
                '心率': 'bpm',
                '体温': '℃',
                '体重': 'kg'
            }
            return units[type] || ''
        },
        // 获取参考范围
        getReferenceRange(type) {
            const ranges = {
                '血压': '收缩压：90-140mmHg，舒张压：60-90mmHg',
                '血糖': '空腹：3.9-6.1mmol/L',
                '血氧': '95-100%',
                '心率': '60-100次/分',
                '体温': '36.3-37.2℃',
                '体重': 'BMI：18.5-24.9（正常范围）'
            }
            return ranges[type] || ''
        },
        // 获取异常类型
        getAbnormalType(record) {
            if (record.monitorType === '血压' && record.systolic > 140) {
                return '高血压'
            }
            if (record.monitorType === '血糖' && record.value > 7.0) {
                return '血糖偏高'
            }
            if (record.monitorType === '血氧' && record.value < 95) {
                return '血氧饱和度偏低'
            }
            if (record.monitorType === '体温' && record.value > 37.2) {
                return '体温偏高'
            }
            if (record.monitorType === '体重') {
                const weight = parseFloat(record.value)
                if (weight < 45) {
                    return '体重偏低'
                } else if (weight > 80) {
                    return '体重偏高'
                }
            }
            return '数值异常'
        },
        // 获取建议措施
        getSuggestions(record) {
            const suggestions = {
                '血压': '请注意休息，避免剧烈运动，及时服用降压药物，必要时就医',
                '血糖': '建议控制饮食，规律运动，遵医嘱服药，定期复查',
                '血氧': '建议深呼吸，保持空气流通，必要时吸氧，及时就医',
                '体温': '多休息，多饮水，必要时服用退烧药，持续发热及时就医',
                '心率': '保持情绪稳定，避免剧烈运动，必要时就医',
                '体重': '建议调整饮食结构，适量运动，必要时咨询营养师指导'
            }
            return suggestions[record.monitorType] || '建议及时就医检查'
        },
        // 搜索
        handleSearch() {
            // 模拟搜索过滤
            let filteredData = [...this.tableData]
            if (this.searchForm.name) {
                filteredData = filteredData.filter(item => 
                    item.name.includes(this.searchForm.name))
            }
            if (this.searchForm.monitorType) {
                filteredData = filteredData.filter(item => 
                    item.monitorType === this.searchForm.monitorType)
            }
            if (this.searchForm.status) {
                filteredData = filteredData.filter(item => 
                    item.status === this.searchForm.status)
            }
            if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                const startDate = this.searchForm.dateRange[0]
                const endDate = this.searchForm.dateRange[1]
                filteredData = filteredData.filter(item => {
                    const itemDate = item.monitorTime.split(' ')[0]
                    return itemDate >= startDate && itemDate <= endDate
                })
            }
            this.tableData = filteredData
            this.total = filteredData.length
        },
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                name: '',
                monitorType: '',
                dateRange: [],
                status: ''
            }
            this.handleSearch()
        },
        // 查看详情
        handleDetail(row) {
            this.currentDetail = { ...row }
            this.detailDialogVisible = true
        },
        // 处理异常
        handleProcess(row) {
            this.$confirm('确认处理该异常数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 模拟处理异常
                const index = this.tableData.findIndex(item => 
                    item.monitorTime === row.monitorTime && 
                    item.name === row.name)
                if (index !== -1) {
                    this.tableData[index].status = 'normal'
                    this.$message.success('异常处理成功')
                }
            }).catch(() => {})
        },
        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val
            this.handleSearch()
        },
        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val
            this.handleSearch()
        }
    }
}
</script>

<style lang="scss" scoped>
.wearable-data {
    padding: 20px;

    .search-area {
        background: #fff;
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .statistics-cards {
        margin-bottom: 20px;

        .stat-item {
            text-align: center;

            .stat-title {
                font-size: 14px;
                color: #606266;
                margin-bottom: 10px;
            }

            .stat-value {
                font-size: 24px;
                font-weight: bold;
                color: #303133;

                &.warning {
                    color: #E6A23C;
                }

                &.success {
                    color: #67C23A;
                }
            }
        }
    }

    .data-table {
        background: #fff;
        padding: 20px;
        border-radius: 4px;

        .abnormal-value {
            color: #F56C6C;
        }
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }

    .monitor-detail {
        .detail-section {
            margin-bottom: 20px;

            h4 {
                margin: 0 0 10px 0;
                color: #303133;
                font-size: 16px;
            }

            .detail-row {
                margin-bottom: 10px;
                line-height: 24px;

                .label {
                    color: #606266;
                    margin-right: 10px;
                    display: inline-block;
                    min-width: 80px;
                }

                .value {
                    color: #303133;
                    margin-right: 30px;

                    &.warning {
                        color: #E6A23C;
                    }
                }
            }
        }
    }
}
</style> 
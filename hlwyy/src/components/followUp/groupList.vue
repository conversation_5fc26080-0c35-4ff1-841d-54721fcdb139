<template>
  <div class="group-list-container">
    <div class="header">
      <h2>分组列表<span>（共{{total}}条）</span></h2>
      <el-button type="primary" @click="addNewGroup">新增分组</el-button>
    </div>

    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="序号" width="80"></el-table-column>
      <el-table-column prop="name" label="组名" min-width="250"></el-table-column>
      <el-table-column prop="description" label="备注" min-width="250"></el-table-column>
      <el-table-column prop="patientCount" label="患者数量" width="120"></el-table-column>
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="text" @click="editGroup(scope.row)">
            <i class="el-icon-edit"></i> 编辑
          </el-button>
          <el-button type="text" @click="viewGroup(scope.row)">
            <i class="el-icon-view"></i> 查看
          </el-button>
          <el-button type="text" @click="deleteGroup(scope.row)">
            <i class="el-icon-delete"></i> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 新增/编辑分组弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="groupForm" :rules="rules" ref="groupForm" label-width="80px">
        <el-form-item label="组名" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input type="textarea" v-model="groupForm.description" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog
      title="删除确认"
      :visible.sync="deleteDialogVisible"
      width="400px">
      <p>确定要删除分组"{{ currentGroup.name }}"吗？</p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDelete">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'GroupList',
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogTitle: '新增分组',
      groupForm: {
        id: null,
        name: '',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ]
      },
      deleteDialogVisible: false,
      currentGroup: {}
    };
  },
  mounted() {
    this.getGroupList();
  },
  methods: {
    // 获取用户信息并编码处理
    getUserHeaders() {
      const userId = sessionStorage.getItem('docId') || '1';
      const userName = sessionStorage.getItem('doctorName') || 'System Admin';
      
      // 如果userId是UUID格式，使用默认数字ID
      let finalUserId = userId;
      if (userId && userId.includes('-')) {
        finalUserId = '1'; // 使用默认用户ID
      }
      
      return {
        'Content-Type': 'application/json',
        'X-User-Id': finalUserId,
        'X-User-Name': encodeURIComponent(userName)
      };
    },
    getGroupList() {
      // 调用API获取分组列表数据
      axios({
        method: 'get',
        url: '/api/emr/patient/group/page',
        params: {
          page: this.currentPage - 1,
          size: this.pageSize
        },
        timeout: 10000,
        headers: this.getUserHeaders()
      }).then(res => {
        if (res.data.code === 200) {
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        } else {
          this.$message.error(res.data.message || '获取分组列表失败');
        }
      }).catch(err => {
        this.$message.error('获取分组列表失败');
        console.error(err);
      });
    },
    addNewGroup() {
      this.dialogTitle = '新增分组';
      this.groupForm = {
        id: null,
        name: '',
        description: ''
      };
      this.dialogVisible = true;
    },
    editGroup(row) {
      this.dialogTitle = '编辑分组';
      this.groupForm = Object.assign({}, row);
      this.dialogVisible = true;
    },
    viewGroup(row) {
      // 跳转到患者入组管理页面，并传递分组ID和分组名称
      this.$router.push({
        path: '/followUp/patientGroup',
        query: { 
          groupId: row.id,
          groupName: row.name
        }
      });
    },
    deleteGroup(row) {
      this.currentGroup = row;
      this.deleteDialogVisible = true;
    },
    submitForm() {
      this.$refs.groupForm.validate(valid => {
        if (valid) {
          if (this.groupForm.id) {
            // 编辑现有分组
            axios({
              method: 'put',
              url: `/api/emr/patient/group/${this.groupForm.id}`,
              data: this.groupForm,
              timeout: 10000,
              headers: this.getUserHeaders()
            }).then(res => {
              if (res.data.code === 200) {
                this.$message.success('编辑分组成功');
                this.dialogVisible = false;
                this.getGroupList();
              } else {
                this.$message.error(res.data.message || '编辑分组失败');
              }
            }).catch(err => {
              this.$message.error('编辑分组失败');
              console.error(err);
            });
          } else {
            // 新增分组
            axios({
              method: 'post',
              url: '/api/emr/patient/group',
              data: this.groupForm,
              timeout: 10000,
              headers: this.getUserHeaders()
            }).then(res => {
              if (res.data.code === 200) {
                this.$message.success('新增分组成功');
                this.dialogVisible = false;
                this.getGroupList();
              } else {
                this.$message.error(res.data.message || '新增分组失败');
              }
            }).catch(err => {
              this.$message.error('新增分组失败');
              console.error(err);
            });
          }
        }
      });
    },
    confirmDelete() {
      axios({
        method: 'delete',
        url: `/api/emr/patient/group/${this.currentGroup.id}`,
        timeout: 10000,
        headers: this.getUserHeaders()
      }).then(res => {
        if (res.data.code === 200) {
          this.$message.success('删除分组成功');
          this.deleteDialogVisible = false;
          this.getGroupList();
        } else {
          this.$message.error(res.data.message || '删除分组失败');
        }
      }).catch(err => {
        this.$message.error('删除分组失败');
        console.error(err);
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getGroupList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getGroupList();
    }
  }
};
</script>

<style scoped>
.group-list-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  font-size: 18px;
  font-weight: normal;
  margin: 0;
}

.header h2 span {
  font-size: 14px;
  color: #999;
  margin-left: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 
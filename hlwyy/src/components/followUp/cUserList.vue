<template>
  <div class="cuser-list">
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="姓名">
        <el-input v-model="searchForm.name" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="searchForm.phone" placeholder="请输入手机号"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" border style="width: 100%;margin-top:20px;">
      <el-table-column prop="name" label="姓名" width="100" />
      <el-table-column prop="gender" label="性别" width="80" />
      <el-table-column prop="age" label="年龄" width="80" />
      <el-table-column prop="phone" label="手机号" width="140" />
      <el-table-column prop="idCard" label="证件号码" width="180" />
      <el-table-column label="分组" width="220">
        <template slot-scope="scope">
          <el-tag v-for="group in scope.row.groups" :key="group.id" style="margin-right:4px;cursor:pointer;" @click="viewGroup(group)">
            {{ group.name }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog :visible.sync="groupDialogVisible" title="分组详情" width="400px">
      <div v-if="currentGroup">
        <p>分组名称：{{ currentGroup.name }}</p>
        <p>分组描述：{{ currentGroup.description || '-' }}</p>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import axios from 'axios';
export default {
  name: 'FollowupUserList',
  data() {
    return {
      searchForm: { name: '', phone: '' },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      groupDialogVisible: false,
      currentGroup: null
    }
  },
  mounted() { this.getList(); },
  methods: {
    getList() {
      const params = {
        page: this.currentPage - 1,
        size: this.pageSize,
        name: this.searchForm.name,
        phone: this.searchForm.phone
      };
      axios.get('/api/emr/followup/cuser/list', { params }).then(res => {
        if (res.data.code === 200) {
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        } else {
          this.$message.error(res.data.message || '获取数据失败');
        }
      });
    },
    handleSearch() { this.currentPage = 1; this.getList(); },
    handleSizeChange(val) { this.pageSize = val; this.getList(); },
    handleCurrentChange(val) { this.currentPage = val; this.getList(); },
    viewGroup(group) { this.currentGroup = group; this.groupDialogVisible = true; }
  }
}
</script>
<style scoped>
.cuser-list { padding: 20px; }
.pagination-container { margin-top: 20px; text-align: right; }
</style> 
<template>
    <div class="smart-form">
        <!-- 表单列表 -->
        <div class="form-list">
            <div class="list-header">
                <h3>智能表单管理</h3>
                <el-button type="primary" @click="createFormTemplate">新增智能表单</el-button>
            </div>
            
            <el-table v-loading="loading" :data="formTemplates" border style="width: 100%">
                <el-table-column prop="name" label="表单名称" min-width="200"></el-table-column>
                <el-table-column prop="formType" label="表单类型" width="120">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="scope.row.formType === 'repeatable' ? 'primary' : 'success'">
                            {{ getFormTypeName(scope.row.formType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="category" label="分类" width="120">
                    <template slot-scope="scope">
                        <el-tag size="small" type="info">
                            {{ getCategoryName(scope.row.category) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="permission" label="权限范围" width="120">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="scope.row.permission === 'all' ? 'warning' : 'primary'">
                            {{ getPermissionName(scope.row.permission) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="scope.row.status === 'active' ? 'success' : 'info'">
                            {{ scope.row.status === 'active' ? '启用' : '停用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="180">
                    <template slot-scope="scope">
                        {{ formatDateTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="editFormTemplate(scope.row)">编辑</el-button>
                        <el-button type="text" size="small" @click="previewFormTemplate(scope.row)">预览</el-button>
                        <el-button type="text" size="small" @click="copyFormTemplate(scope.row)">复制</el-button>
                        <el-button type="text" size="small" class="danger-text" @click="deleteFormTemplate(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.pageNum"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total">
                </el-pagination>
            </div>
        </div>

        <!-- 表单模板编辑弹窗 -->
        <el-dialog 
            :title="isEditing ? '编辑表单模板' : '新增表单模板'" 
            :visible.sync="templateDialogVisible" 
            width="800px" 
            :close-on-click-modal="false"
            class="template-dialog">
            <form-template-edit 
                ref="templateEdit"
                :template-data="currentTemplate"
                :is-editing="isEditing"
                :questionnaires="currentQuestionnaires"
                @save="handleTemplateSave"
                @save-and-edit="handleTemplateSaveAndEdit"
                @cancel="handleTemplateCancel">
            </form-template-edit>
        </el-dialog>

        <!-- 表单管理弹窗 -->
        <el-dialog 
            :title="currentTemplate ? `${currentTemplate.name} - 表单管理` : '表单管理'" 
            :visible.sync="managementDialogVisible" 
            width="95%" 
            :close-on-click-modal="false"
            class="management-dialog">
            <form-management 
                ref="formManagement"
                :template-data="currentTemplate"
                @questionnaire-updated="handleQuestionnaireUpdated"
                @form-updated="handleFormUpdated"
                @close-dialog="handleCloseManagementDialog"
                v-if="managementDialogVisible">
            </form-management>
        </el-dialog>
    </div>
</template>

<script>
import { smartFormApi } from '@/api/smartForm.js'
import FormTemplateEdit from './FormTemplateEdit.vue'
import FormManagement from './FormManagement.vue'

export default {
    name: 'SmartForm',
    components: {
        FormTemplateEdit,
        FormManagement
    },
    data() {
        return {
            loading: false,
            formTemplates: [],
            
            // 搜索表单
            searchForm: {
                name: '',
                formType: '',
                status: ''
            },
            
            // 分页
            pagination: {
                pageNum: 1,
                pageSize: 10,
                total: 0
            },
            
            // 模板编辑弹窗
            templateDialogVisible: false,
            isEditing: false,
            currentTemplate: null,
            
            // 表单管理弹窗
            managementDialogVisible: false,
            
            // 问卷数据（用于新增表单时传递）
            currentQuestionnaires: [],
            
            // 表格选择
            multipleSelection: []
        }
    },
    mounted() {
        this.loadFormTemplates();
    },
    methods: {
        // 加载表单模板列表
        async loadFormTemplates() {
            this.loading = true;
            try {
                const params = {
                    pageNum: this.pagination.pageNum,
                    pageSize: this.pagination.pageSize,
                    name: this.searchForm.name || null,
                    formType: this.searchForm.formType || null,
                    status: this.searchForm.status || null
                };
                
                const response = await smartFormApi.getFormTemplateList(params);
                if (response && response.success) {
                    this.formTemplates = response.data.records || [];
                    this.pagination.total = response.data.total || 0;
                } else {
                    // 即使API失败，也保持表格结构显示
                    this.formTemplates = [];
                    this.pagination.total = 0;
                    console.warn('获取表单模板列表失败:', response.message);
                }
            } catch (error) {
                console.error('加载表单模板列表失败:', error);
                // 即使出错，也保持表格结构显示
                this.formTemplates = [];
                this.pagination.total = 0;
                // 不显示错误消息，避免干扰用户体验
            } finally {
                this.loading = false;
            }
        },
        
        // 刷新方法，供父组件调用
        refresh() {
            this.loadFormTemplates();
        },
        
        // 搜索
        handleSearch() {
            this.pagination.pageNum = 1;
            this.loadFormTemplates();
        },
        
        // 创建新表单模板
        createFormTemplate() {
            this.isEditing = false;
            this.currentTemplate = null;
            this.templateDialogVisible = true;
        },
        
        // 编辑表单模板
        editFormTemplate(template) {
            // 直接打开表单管理界面进行完整编辑
            console.log('编辑表单模板，原始数据:', template);
            this.currentTemplate = { ...template };
            console.log('传递给FormManagement的数据:', this.currentTemplate);
            this.managementDialogVisible = true;
        },
        
        // 预览表单模板
        previewFormTemplate(template) {
            this.$message.info('预览功能开发中...');
        },
        
        // 复制表单模板
        async copyFormTemplate(template) {
            try {
                const newName = template.name + '_副本';
                const response = await smartFormApi.copyFormTemplate(template.id, newName);
                if (response && response.success) {
                    this.$message.success('表单模板复制成功');
                    this.loadFormTemplates();
                } else {
                    this.$message.error(response.message || '复制表单模板失败');
                }
            } catch (error) {
                console.error('复制表单模板失败:', error);
                // 显示具体的错误信息
                const errorMessage = error.message || '复制表单模板失败';
                this.$message.error(errorMessage);
            }
        },
        
        // 打开表单管理界面
        openFormManagement(template) {
            this.currentTemplate = template;
            this.managementDialogVisible = true;
        },
        
        // 处理下拉菜单命令
        handleMenuCommand(command) {
            const { action, row } = command;
            switch (action) {
                case 'share':
                    this.shareFormTemplate(row);
                    break;
                case 'export':
                    this.exportFormTemplate(row);
                    break;
                case 'status':
                    this.toggleFormTemplateStatus(row);
                    break;
                case 'delete':
                    this.deleteFormTemplate(row);
                    break;
            }
        },
        
        // 分享表单模板
        shareFormTemplate(template) {
            this.$message.info('分享功能开发中...');
        },
        
        // 导出表单模板
        exportFormTemplate(template) {
            this.$message.info('导出功能开发中...');
        },
        
        // 切换表单模板状态
        async toggleFormTemplateStatus(template) {
            const newStatus = template.status === 'active' ? 'inactive' : 'active';
            const statusText = newStatus === 'active' ? '启用' : '停用';
            
            try {
                await this.$confirm(`确定要${statusText}这个表单模板吗？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                
                const response = await smartFormApi.updateFormTemplateStatus(template.id, newStatus);
                if (response && response.success) {
                    this.$message.success(`表单模板${statusText}成功`);
                    this.loadFormTemplates();
                } else {
                    this.$message.error(response.message || `${statusText}表单模板失败`);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('切换表单模板状态失败:', error);
                    // 显示具体的错误信息
                    const errorMessage = error.message || '操作失败';
                    this.$message.error(errorMessage);
                }
            }
        },
        
        // 删除表单模板
        deleteFormTemplate(template) {
            this.$confirm('确定要删除这个表单模板吗？删除后不可恢复。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await smartFormApi.deleteFormTemplate(template.id);
                    if (response && response.success) {
                        this.$message.success('表单模板删除成功');
                        this.loadFormTemplates();
                    } else {
                        this.$message.error(response.message || '删除表单模板失败');
                    }
                } catch (error) {
                    console.error('删除表单模板失败:', error);
                    // 显示具体的错误信息
                    const errorMessage = error.message || '删除表单模板失败';
                    this.$message.error(errorMessage);
                }
            });
        },
        
        // 模板保存回调
        handleTemplateSave(savedTemplate) {
            this.templateDialogVisible = false;
            
            // 如果是新增保存，刷新列表（因为新增的表单还没有真实ID）
            if (!this.isEditing) {
                this.loadFormTemplates();
                this.$message.success('表单模板创建成功，请在列表中查看');
            } else {
                // 如果是编辑保存，则刷新列表
                this.loadFormTemplates();
                this.$message.success('表单模板更新成功');
            }
        },
        
        // 模板保存并跳转到编辑页面回调
        handleTemplateSaveAndEdit(savedTemplate) {
            this.templateDialogVisible = false;
            
            // 跳转到表单管理页面进行详细编辑
            this.currentTemplate = savedTemplate;
            this.managementDialogVisible = true;
            
            // 刷新列表
            this.loadFormTemplates();
        },
        
        // 问卷更新回调
        handleQuestionnaireUpdated() {
            // 获取表单管理组件中的问卷数据
            if (this.$refs.formManagement && this.$refs.formManagement.$refs.formManageTab) {
                this.currentQuestionnaires = this.$refs.formManagement.$refs.formManageTab.formQuestionnaires;
            }
        },
        
        // 表单更新回调
        handleFormUpdated() {
            // 刷新表单模板列表
            this.loadFormTemplates();
        },
        
        // 关闭表单管理对话框
        handleCloseManagementDialog() {
            this.managementDialogVisible = false;
            this.currentTemplate = null;
        },
        
        // 模板取消回调
        handleTemplateCancel() {
            this.templateDialogVisible = false;
        },
        
        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.pagination.pageNum = 1;
            this.loadFormTemplates();
        },
        
        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.pageNum = val;
            this.loadFormTemplates();
        },
        
        // 表格选择改变
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        
        // 获取表单类型名称
        getFormTypeName(type) {
            const typeMap = {
                'one_time': '一次性问卷',
                'repeatable': '重复性问卷'
            };
            return typeMap[type] || type;
        },
        
        // 获取分类名称
        getCategoryName(category) {
            const categoryMap = {
                'postoperative_followup': '术后随访',
                'chronic_disease': '慢病管理',
                'health_assessment': '健康评估',
                'satisfaction_survey': '满意度调查',
                'symptom_monitoring': '症状监测',
                'rehabilitation_assessment': '康复评估',
                'other': '其他'
            };
            return categoryMap[category] || category;
        },
        
        // 获取权限名称
        getPermissionName(permission) {
            const permissionMap = {
                'self': '仅本人',
                'department': '同科室',
                'all': '全院'
            };
            return permissionMap[permission] || permission;
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        }
    }
}
</script>

<style lang="scss" scoped>
.smart-form {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
    
    .form-list {
        background-color: #fff;
        border-radius: 4px;
        padding: 20px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        
        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            
            h3 {
                margin: 0;
                color: #303133;
                font-size: 18px;
                font-weight: 500;
            }
        }
        
        .pagination-wrapper {
            margin-top: 20px;
            text-align: right;
        }
    }
}

.danger-text {
    color: #f56c6c;
}

.template-dialog {
    /deep/ .el-dialog__body {
        padding: 20px;
    }
}

.management-dialog {
    /deep/ .el-dialog {
        margin-top: 5vh !important;
        margin-bottom: 5vh !important;
    }
    
    /deep/ .el-dialog__body {
        padding: 10px 20px;
    }
}

/deep/ .el-table {
    font-size: 14px;
    
    .el-table__header {
        color: #303133;
        font-weight: 500;
    }
}

/deep/ .el-tag {
    font-size: 12px;
}

/deep/ .el-dropdown-menu__item.danger-text {
    color: #f56c6c;
}
</style> 
<template>
    <div class="patient-management">
        <!-- 顶部工具栏 -->
        <div class="tool-bar">
            <div class="left-tools">
                <el-button type="primary" icon="el-icon-plus" @click="showImportDialog">导入患者</el-button>
                <el-button type="success" icon="el-icon-download" @click="exportPatients">导出患者</el-button>
            </div>
            <div class="search-box">
                <el-input 
                    v-model="searchParams.keyword" 
                    placeholder="搜索患者姓名或手机号" 
                    class="search-input">
                    <el-button slot="append" icon="el-icon-search" @click="searchPatients"></el-button>
                </el-input>
                <el-select v-model="searchParams.status" placeholder="审核状态" style="width: 120px; margin-left: 10px;">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="待审核" value="pending"></el-option>
                    <el-option label="已通过" value="approved"></el-option>
                    <el-option label="已拒绝" value="rejected"></el-option>
                </el-select>
                <el-button type="default" @click="resetSearch">重置</el-button>
            </div>
        </div>

        <!-- 患者列表 -->
        <div class="patient-list">
            <el-table 
                :data="patients" 
                style="width: 100%"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
                <el-table-column prop="patientPhone" label="手机号" width="130"></el-table-column>
                <el-table-column prop="joinType" label="入组方式" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.joinType === 'qr_code' ? 'success' : 'info'">
                            {{ getJoinTypeText(scope.row.joinType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="审核状态" width="100">
                    <template slot-scope="scope">
                        <el-tag :type="getStatusTagType(scope.row.status)">
                            {{ getStatusText(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="joinTime" label="申请时间" width="160">
                    <template slot-scope="scope">
                        {{ formatDate(scope.row.joinTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="auditTime" label="审核时间" width="160">
                    <template slot-scope="scope">
                        {{ formatDate(scope.row.auditTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="auditorName" label="审核人" width="100"></el-table-column>
                <el-table-column prop="auditRemark" label="审核备注" min-width="150"></el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template slot-scope="scope">
                        <el-button 
                            v-if="scope.row.status === 'pending'"
                            type="success" 
                            size="mini" 
                            @click="auditPatient(scope.row, 'approved')">
                            通过
                        </el-button>
                        <el-button 
                            v-if="scope.row.status === 'pending'"
                            type="danger" 
                            size="mini" 
                            @click="auditPatient(scope.row, 'rejected')">
                            拒绝
                        </el-button>
                        <el-button 
                            type="text" 
                            size="mini" 
                            @click="viewPatientDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button 
                            type="text" 
                            size="mini" 
                            @click="removePatient(scope.row)">
                            移除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[15, 30, 50]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>

        <!-- 批量操作工具栏 -->
        <div v-if="selectedPatients.length > 0" class="batch-toolbar">
            <span>已选择 {{ selectedPatients.length }} 个患者</span>
            <el-button type="success" size="small" @click="batchAudit('approved')">批量通过</el-button>
            <el-button type="danger" size="small" @click="batchAudit('rejected')">批量拒绝</el-button>
            <el-button type="warning" size="small" @click="batchRemove">批量移除</el-button>
        </div>

        <!-- 导入患者对话框 -->
        <el-dialog title="导入患者" :visible.sync="importDialogVisible" width="600px">
            <div class="import-form">
                <el-form :model="importForm" :rules="importRules" ref="importForm" label-width="100px">
                    <el-form-item label="导入方式">
                        <el-radio-group v-model="importForm.importType">
                            <el-radio label="manual">手动输入</el-radio>
                            <el-radio label="file">文件导入</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    
                    <template v-if="importForm.importType === 'manual'">
                        <el-form-item label="患者信息">
                            <div class="patient-input-list">
                                <div 
                                    v-for="(patient, index) in importForm.patients" 
                                    :key="index"
                                    class="patient-input-item">
                                    <el-input 
                                        v-model="patient.name" 
                                        placeholder="患者姓名"
                                        style="width: 150px; margin-right: 10px;">
                                    </el-input>
                                    <el-input 
                                        v-model="patient.phone" 
                                        placeholder="手机号"
                                        style="width: 150px; margin-right: 10px;">
                                    </el-input>
                                    <el-button 
                                        type="text" 
                                        icon="el-icon-delete"
                                        @click="removePatientInput(index)">
                                    </el-button>
                                </div>
                            </div>
                            <el-button type="text" icon="el-icon-plus" @click="addPatientInput">
                                添加患者
                            </el-button>
                        </el-form-item>
                    </template>
                    
                    <template v-if="importForm.importType === 'file'">
                        <el-form-item label="选择文件">
                            <el-upload
                                class="upload-demo"
                                action="#"
                                :auto-upload="false"
                                :on-change="handleFileChange"
                                :file-list="fileList"
                                accept=".xlsx,.xls,.csv">
                                <el-button size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip">只能上传xlsx/xls/csv文件</div>
                            </el-upload>
                        </el-form-item>
                    </template>
                </el-form>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="importDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmImport">确定导入</el-button>
            </div>
        </el-dialog>

        <!-- 患者详情对话框 -->
        <el-dialog title="患者详情" :visible.sync="detailDialogVisible" width="500px">
            <div class="patient-detail" v-if="currentPatient">
                <div class="detail-item">
                    <label>患者姓名：</label>
                    <span>{{ currentPatient.patientName }}</span>
                </div>
                <div class="detail-item">
                    <label>手机号：</label>
                    <span>{{ currentPatient.patientPhone }}</span>
                </div>
                <div class="detail-item">
                    <label>入组方式：</label>
                    <span>{{ getJoinTypeText(currentPatient.joinType) }}</span>
                </div>
                <div class="detail-item">
                    <label>申请时间：</label>
                    <span>{{ formatDate(currentPatient.joinTime) }}</span>
                </div>
                <div class="detail-item">
                    <label>审核状态：</label>
                    <el-tag :type="getStatusTagType(currentPatient.status)">
                        {{ getStatusText(currentPatient.status) }}
                    </el-tag>
                </div>
                <div class="detail-item" v-if="currentPatient.auditTime">
                    <label>审核时间：</label>
                    <span>{{ formatDate(currentPatient.auditTime) }}</span>
                </div>
                <div class="detail-item" v-if="currentPatient.auditorName">
                    <label>审核人：</label>
                    <span>{{ currentPatient.auditorName }}</span>
                </div>
                <div class="detail-item" v-if="currentPatient.auditRemark">
                    <label>审核备注：</label>
                    <span>{{ currentPatient.auditRemark }}</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getGroupPatients, auditPatientJoinRequest, batchAuditPatientJoinRequest } from '@/api/patientGroup'

export default {
    name: 'PatientManagement',
    props: {
        groupId: {
            type: [String, Number],
            required: true
        }
    },
    data() {
        return {
            patients: [],
            currentPage: 1,
            pageSize: 15,
            total: 0,
            searchParams: {
                keyword: '',
                status: ''
            },
            selectedPatients: [],
            
            // 导入对话框
            importDialogVisible: false,
            importForm: {
                importType: 'manual',
                patients: [
                    { name: '', phone: '' }
                ]
            },
            importRules: {
                importType: [
                    { required: true, message: '请选择导入方式', trigger: 'change' }
                ]
            },
            fileList: [],
            
            // 详情对话框
            detailDialogVisible: false,
            currentPatient: null
        }
    },
    
    mounted() {
        this.loadPatients()
    },
    
    methods: {
        // 加载患者列表
        async loadPatients() {
            try {
                const params = {
                    page: this.currentPage,
                    size: this.pageSize,
                    ...this.searchParams
                }
                const response = await getGroupPatients(this.groupId, params)
                this.patients = response.data.records || []
                this.total = response.data.total || 0
            } catch (error) {
                this.$message.error('加载患者列表失败')
                console.error(error)
            }
        },
        
        // 搜索患者
        searchPatients() {
            this.currentPage = 1
            this.loadPatients()
        },
        
        // 重置搜索
        resetSearch() {
            this.searchParams = {
                keyword: '',
                status: ''
            }
            this.searchPatients()
        },
        
        // 显示导入对话框
        showImportDialog() {
            this.importDialogVisible = true
            this.importForm = {
                importType: 'manual',
                patients: [
                    { name: '', phone: '' }
                ]
            }
            this.fileList = []
        },
        
        // 添加患者输入框
        addPatientInput() {
            this.importForm.patients.push({ name: '', phone: '' })
        },
        
        // 移除患者输入框
        removePatientInput(index) {
            if (this.importForm.patients.length > 1) {
                this.importForm.patients.splice(index, 1)
            }
        },
        
        // 文件选择处理
        handleFileChange(file) {
            this.fileList = [file]
        },
        
        // 确认导入
        async confirmImport() {
            try {
                if (this.importForm.importType === 'manual') {
                    // 手动导入逻辑
                    const patients = this.importForm.patients.filter(p => p.name && p.phone)
                    if (patients.length === 0) {
                        this.$message.warning('请至少输入一个患者信息')
                        return
                    }
                    
                    // 这里应该调用后端接口进行导入
                    this.$message.success(`成功导入 ${patients.length} 个患者`)
                } else {
                    // 文件导入逻辑
                    if (this.fileList.length === 0) {
                        this.$message.warning('请选择要导入的文件')
                        return
                    }
                    
                    // 这里应该调用后端接口进行文件导入
                    this.$message.success('文件导入成功')
                }
                
                this.importDialogVisible = false
                this.loadPatients()
            } catch (error) {
                this.$message.error('导入失败')
                console.error(error)
            }
        },
        
        // 导出患者
        exportPatients() {
            // 实现导出功能
            this.$message.info('导出功能开发中')
        },
        
        // 审核患者
        async auditPatient(patient, status) {
            try {
                const remark = status === 'rejected' ? 
                    await this.$prompt('请输入拒绝原因', '审核') : ''
                
                await auditPatientJoinRequest({
                    id: patient.id,
                    status: status,
                    auditRemark: remark
                })
                
                this.$message.success('审核成功')
                this.loadPatients()
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('审核失败')
                    console.error(error)
                }
            }
        },
        
        // 批量审核
        async batchAudit(status) {
            try {
                const remark = status === 'rejected' ? 
                    await this.$prompt('请输入拒绝原因', '批量审核') : ''
                
                const patientIds = this.selectedPatients.map(p => p.id)
                await batchAuditPatientJoinRequest({
                    patientIds: patientIds,
                    status: status,
                    auditRemark: remark
                })
                
                this.$message.success('批量审核成功')
                this.selectedPatients = []
                this.loadPatients()
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('批量审核失败')
                    console.error(error)
                }
            }
        },
        
        // 移除患者
        async removePatient(patient) {
            try {
                await this.$confirm('确定要移除这个患者吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                
                // 这里应该调用后端接口移除患者
                this.$message.success('移除成功')
                this.loadPatients()
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('移除失败')
                    console.error(error)
                }
            }
        },
        
        // 批量移除
        async batchRemove() {
            try {
                await this.$confirm(`确定要移除选中的 ${this.selectedPatients.length} 个患者吗？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                
                // 这里应该调用后端接口批量移除患者
                this.$message.success('批量移除成功')
                this.selectedPatients = []
                this.loadPatients()
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('批量移除失败')
                    console.error(error)
                }
            }
        },
        
        // 查看患者详情
        viewPatientDetail(patient) {
            this.currentPatient = patient
            this.detailDialogVisible = true
        },
        
        // 选择变化处理
        handleSelectionChange(selection) {
            this.selectedPatients = selection
        },
        
        // 分页处理
        handleSizeChange(val) {
            this.pageSize = val
            this.loadPatients()
        },
        
        handleCurrentChange(val) {
            this.currentPage = val
            this.loadPatients()
        },
        
        // 工具方法
        getJoinTypeText(type) {
            const typeMap = {
                'qr_code': '二维码',
                'manual': '手动导入'
            }
            return typeMap[type] || type
        },
        
        getStatusText(status) {
            const statusMap = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝'
            }
            return statusMap[status] || status
        },
        
        getStatusTagType(status) {
            const typeMap = {
                'pending': 'warning',
                'approved': 'success',
                'rejected': 'danger'
            }
            return typeMap[status] || 'info'
        },
        
        formatDate(date) {
            if (!date) return ''
            return new Date(date).toLocaleString()
        }
    }
}
</script>

<style scoped>
.patient-management {
    padding: 20px;
}

.tool-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: white;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-box {
    display: flex;
    align-items: center;
}

.search-input {
    width: 300px;
}

.patient-list {
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.batch-toolbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f0f0f0;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.import-form {
    padding: 20px 0;
}

.patient-input-list {
    margin-bottom: 10px;
}

.patient-input-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.patient-detail {
    padding: 20px 0;
}

.detail-item {
    display: flex;
    margin-bottom: 15px;
}

.detail-item label {
    width: 100px;
    color: #666;
    font-weight: 500;
}

.detail-item span {
    flex: 1;
    color: #333;
}
</style> 
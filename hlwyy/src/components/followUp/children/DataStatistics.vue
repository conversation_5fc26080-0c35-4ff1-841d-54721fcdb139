<template>
    <div class="data-statistics">
        <!-- 统计概览卡片 -->
        <div class="statistics-overview">
            <div class="stat-card">
                <div class="stat-icon patient-icon"></div>
                <div class="stat-content">
                    <div class="stat-value">{{ statistics.totalPatients || 0 }}</div>
                    <div class="stat-label">总患者数</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon task-icon"></div>
                <div class="stat-content">
                    <div class="stat-value">{{ statistics.totalTasks || 0 }}</div>
                    <div class="stat-label">总任务数</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon completed-icon"></div>
                <div class="stat-content">
                    <div class="stat-value">{{ statistics.completedTasks || 0 }}</div>
                    <div class="stat-label">已完成任务</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon rate-icon"></div>
                <div class="stat-content">
                    <div class="stat-value">{{ completionRate }}%</div>
                    <div class="stat-label">完成率</div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
            <!-- 任务完成趋势图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3>任务完成趋势</h3>
                    <el-select v-model="trendTimeRange" placeholder="时间范围" style="width: 120px;">
                        <el-option label="最近7天" value="7"></el-option>
                        <el-option label="最近30天" value="30"></el-option>
                        <el-option label="最近90天" value="90"></el-option>
                    </el-select>
                </div>
                <div class="chart-content">
                    <div ref="trendChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>

            <!-- 子计划完成情况 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3>子计划完成情况</h3>
                </div>
                <div class="chart-content">
                    <div ref="subplanChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="data-tables">
            <!-- 子计划统计表格 -->
            <div class="table-card">
                <div class="table-header">
                    <h3>子计划统计</h3>
                    <el-button type="primary" size="small" @click="exportSubplanData">导出数据</el-button>
                </div>
                <el-table :data="subplanStatistics" style="width: 100%">
                    <el-table-column prop="subplanName" label="子计划名称" width="200"></el-table-column>
                    <el-table-column prop="totalTasks" label="总任务数" width="100"></el-table-column>
                    <el-table-column prop="completedTasks" label="已完成" width="100"></el-table-column>
                    <el-table-column prop="pendingTasks" label="待执行" width="100"></el-table-column>
                    <el-table-column prop="completionRate" label="完成率" width="100">
                        <template slot-scope="scope">
                            <el-progress 
                                :percentage="scope.row.completionRate" 
                                :color="getProgressColor(scope.row.completionRate)">
                            </el-progress>
                        </template>
                    </el-table-column>
                    <el-table-column prop="lastUpdateTime" label="最后更新时间" width="160">
                        <template slot-scope="scope">
                            {{ formatDate(scope.row.lastUpdateTime) }}
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 患者参与情况表格 -->
            <div class="table-card">
                <div class="table-header">
                    <h3>患者参与情况</h3>
                    <el-button type="primary" size="small" @click="exportPatientData">导出数据</el-button>
                </div>
                <el-table :data="patientStatistics" style="width: 100%">
                    <el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
                    <el-table-column prop="patientPhone" label="手机号" width="130"></el-table-column>
                    <el-table-column prop="joinTime" label="入组时间" width="160">
                        <template slot-scope="scope">
                            {{ formatDate(scope.row.joinTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="totalTasks" label="总任务数" width="100"></el-table-column>
                    <el-table-column prop="completedTasks" label="已完成" width="100"></el-table-column>
                    <el-table-column prop="completionRate" label="完成率" width="100">
                        <template slot-scope="scope">
                            <el-progress 
                                :percentage="scope.row.completionRate" 
                                :color="getProgressColor(scope.row.completionRate)">
                            </el-progress>
                        </template>
                    </el-table-column>
                    <el-table-column prop="lastActivityTime" label="最后活动时间" width="160">
                        <template slot-scope="scope">
                            {{ formatDate(scope.row.lastActivityTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                        <template slot-scope="scope">
                            <el-button type="text" size="mini" @click="viewPatientDetail(scope.row)">
                                详情
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 患者详情对话框 -->
        <el-dialog title="患者详情" :visible.sync="patientDetailVisible" width="800px">
            <div class="patient-detail" v-if="currentPatient">
                <div class="detail-section">
                    <h4>基本信息</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>患者姓名：</label>
                            <span>{{ currentPatient.patientName }}</span>
                        </div>
                        <div class="detail-item">
                            <label>手机号：</label>
                            <span>{{ currentPatient.patientPhone }}</span>
                        </div>
                        <div class="detail-item">
                            <label>入组时间：</label>
                            <span>{{ formatDate(currentPatient.joinTime) }}</span>
                        </div>
                        <div class="detail-item">
                            <label>最后活动：</label>
                            <span>{{ formatDate(currentPatient.lastActivityTime) }}</span>
                        </div>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>任务完成情况</h4>
                    <el-table :data="currentPatient.taskDetails" style="width: 100%">
                        <el-table-column prop="subplanName" label="子计划" width="150"></el-table-column>
                        <el-table-column prop="taskName" label="任务名称" width="200"></el-table-column>
                        <el-table-column prop="scheduledTime" label="计划时间" width="160">
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.scheduledTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getTaskStatusType(scope.row.status)">
                                    {{ getTaskStatusText(scope.row.status) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="completedTime" label="完成时间" width="160">
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.completedTime) }}
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getPlanStatistics } from '@/api/followupPlan'
import * as echarts from 'echarts'

export default {
    name: 'DataStatistics',
    props: {
        planId: {
            type: [String, Number],
            required: true
        }
    },
    data() {
        return {
            statistics: {},
            trendTimeRange: '30',
            subplanStatistics: [],
            patientStatistics: [],
            
            // 图表实例
            trendChart: null,
            subplanChart: null,
            
            // 患者详情
            patientDetailVisible: false,
            currentPatient: null
        }
    },
    
    computed: {
        completionRate() {
            if (!this.statistics.totalTasks) return 0
            return Math.round((this.statistics.completedTasks / this.statistics.totalTasks) * 100)
        }
    },
    
    mounted() {
        this.loadStatistics()
        this.initCharts()
    },
    
    beforeDestroy() {
        if (this.trendChart) {
            this.trendChart.dispose()
        }
        if (this.subplanChart) {
            this.subplanChart.dispose()
        }
    },
    
    methods: {
        // 加载统计数据
        async loadStatistics() {
            try {
                const response = await getPlanStatistics(this.planId)
                this.statistics = response.data.overview || {}
                this.subplanStatistics = response.data.subplanStatistics || []
                this.patientStatistics = response.data.patientStatistics || []
                
                this.updateCharts()
            } catch (error) {
                this.$message.error('加载统计数据失败')
                console.error(error)
            }
        },
        
        // 初始化图表
        initCharts() {
            this.$nextTick(() => {
                this.initTrendChart()
                this.initSubplanChart()
            })
        },
        
        // 初始化趋势图
        initTrendChart() {
            if (this.$refs.trendChart) {
                this.trendChart = echarts.init(this.$refs.trendChart)
                this.updateTrendChart()
            }
        },
        
        // 初始化子计划图
        initSubplanChart() {
            if (this.$refs.subplanChart) {
                this.subplanChart = echarts.init(this.$refs.subplanChart)
                this.updateSubplanChart()
            }
        },
        
        // 更新趋势图
        updateTrendChart() {
            if (!this.trendChart) return
            
            // 模拟数据，实际应该从后端获取
            const option = {
                title: {
                    text: '任务完成趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['总任务数', '已完成', '待执行'],
                    bottom: 10
                },
                xAxis: {
                    type: 'category',
                    data: this.getLastDays(parseInt(this.trendTimeRange))
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '总任务数',
                        type: 'line',
                        data: this.generateRandomData(parseInt(this.trendTimeRange), 50, 100),
                        smooth: true
                    },
                    {
                        name: '已完成',
                        type: 'line',
                        data: this.generateRandomData(parseInt(this.trendTimeRange), 30, 80),
                        smooth: true
                    },
                    {
                        name: '待执行',
                        type: 'line',
                        data: this.generateRandomData(parseInt(this.trendTimeRange), 10, 30),
                        smooth: true
                    }
                ]
            }
            
            this.trendChart.setOption(option)
        },
        
        // 更新子计划图
        updateSubplanChart() {
            if (!this.subplanChart) return
            
            const option = {
                title: {
                    text: '子计划完成情况',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: '完成情况',
                        type: 'pie',
                        radius: '50%',
                        data: this.subplanStatistics.map(item => ({
                            name: item.subplanName,
                            value: item.completedTasks
                        }))
                    }
                ]
            }
            
            this.subplanChart.setOption(option)
        },
        
        // 更新所有图表
        updateCharts() {
            this.updateTrendChart()
            this.updateSubplanChart()
        },
        
        // 获取最近N天的日期
        getLastDays(days) {
            const dates = []
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date()
                date.setDate(date.getDate() - i)
                dates.push(date.toLocaleDateString())
            }
            return dates
        },
        
        // 生成随机数据
        generateRandomData(days, min, max) {
            const data = []
            for (let i = 0; i < days; i++) {
                data.push(Math.floor(Math.random() * (max - min + 1)) + min)
            }
            return data
        },
        
        // 查看患者详情
        viewPatientDetail(patient) {
            this.currentPatient = patient
            this.patientDetailVisible = true
        },
        
        // 导出子计划数据
        exportSubplanData() {
            this.$message.info('导出功能开发中')
        },
        
        // 导出患者数据
        exportPatientData() {
            this.$message.info('导出功能开发中')
        },
        
        // 工具方法
        getProgressColor(percentage) {
            if (percentage >= 80) return '#67C23A'
            if (percentage >= 60) return '#E6A23C'
            if (percentage >= 40) return '#F56C6C'
            return '#909399'
        },
        
        getTaskStatusType(status) {
            const typeMap = {
                'pending': 'info',
                'executing': 'warning',
                'completed': 'success',
                'failed': 'danger'
            }
            return typeMap[status] || 'info'
        },
        
        getTaskStatusText(status) {
            const textMap = {
                'pending': '待执行',
                'executing': '执行中',
                'completed': '已完成',
                'failed': '执行失败'
            }
            return textMap[status] || status
        },
        
        formatDate(date) {
            if (!date) return ''
            return new Date(date).toLocaleString()
        }
    },
    
    watch: {
        trendTimeRange() {
            this.updateTrendChart()
        }
    }
}
</script>

<style scoped>
.data-statistics {
    padding: 20px;
}

.statistics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 48px;
    height: 48px;
    margin-right: 15px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    margin: 0;
    color: #333;
}

.data-tables {
    display: grid;
    gap: 20px;
}

.table-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3 {
    margin: 0;
    color: #333;
}

.patient-detail {
    padding: 20px 0;
}

.detail-section {
    margin-bottom: 30px;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 5px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.detail-item {
    display: flex;
    align-items: center;
}

.detail-item label {
    width: 100px;
    color: #666;
    font-weight: 500;
}

.detail-item span {
    flex: 1;
    color: #333;
}

/* 图标样式 */
.patient-icon {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890ff"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
}

.task-icon {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2352c41a"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
}

.completed-icon {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2367c23a"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>');
}

.rate-icon {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e6a23c"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>');
}
</style> 
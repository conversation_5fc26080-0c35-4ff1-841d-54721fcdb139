<template>
    <div class="data-view-tab">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <h4>提交数据查看</h4>
                <p>查看和管理表单的所有提交数据</p>
            </div>
            <div class="toolbar-right">
                <el-button icon="el-icon-download" @click="exportData">导出数据</el-button>
                <el-button icon="el-icon-refresh" @click="refreshData">刷新</el-button>
            </div>
        </div>

        <!-- 搜索筛选区域 -->
        <div class="search-section">
            <el-form :model="searchForm" inline class="search-form">
                <el-form-item label="患者姓名">
                    <el-input 
                        v-model="searchForm.patientName" 
                        placeholder="请输入患者姓名"
                        style="width: 200px;"
                        clearable>
                    </el-input>
                </el-form-item>
                
                <el-form-item label="提交者类型">
                    <el-select v-model="searchForm.submitterType" placeholder="全部类型" style="width: 150px;" clearable>
                        <el-option label="全部类型" value=""></el-option>
                        <el-option label="患者" value="patient"></el-option>
                        <el-option label="医生" value="doctor"></el-option>
                        <el-option label="护士" value="nurse"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="提交状态">
                    <el-select v-model="searchForm.status" placeholder="全部状态" style="width: 150px;" clearable>
                        <el-option label="全部状态" value=""></el-option>
                        <el-option label="已提交" value="submitted"></el-option>
                        <el-option label="已审核" value="reviewed"></el-option>
                        <el-option label="已完成" value="completed"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="分数范围">
                    <el-input-number v-model="searchForm.minScore" placeholder="最低分" style="width: 100px;" :min="0" :max="100"></el-input-number>
                    <span style="margin: 0 10px;">-</span>
                    <el-input-number v-model="searchForm.maxScore" placeholder="最高分" style="width: 100px;" :min="0" :max="100"></el-input-number>
                </el-form-item>
                
                <el-form-item label="提交时间">
                    <el-date-picker
                        v-model="searchForm.timeRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        style="width: 240px;">
                    </el-date-picker>
                </el-form-item>
                
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-icon submit">
                    <i class="el-icon-document"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value">{{ dataStats.totalSubmissions }}</div>
                    <div class="stat-label">累计提交次数</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon patient">
                    <i class="el-icon-user"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value">{{ dataStats.totalPatients }}</div>
                    <div class="stat-label">参与患者数</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon score">
                    <i class="el-icon-star-on"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value">{{ dataStats.avgScore }}</div>
                    <div class="stat-label">平均得分</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon completion">
                    <i class="el-icon-circle-check"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value">{{ dataStats.completionRate }}%</div>
                    <div class="stat-label">完成率</div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
            <el-table 
                v-loading="loading" 
                :data="submissionList" 
                border 
                style="width: 100%"
                @selection-change="handleSelectionChange">
                
                <el-table-column type="selection" width="55"></el-table-column>
                
                <el-table-column prop="submissionNo" label="提交编号" width="150" fixed="left">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="viewSubmissionDetail(scope.row)">
                            {{ scope.row.submissionNo }}
                        </el-link>
                    </template>
                </el-table-column>
                
                <el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
                
                <el-table-column prop="patientPhone" label="联系电话" width="130"></el-table-column>
                
                <el-table-column prop="submitterName" label="提交者" width="120"></el-table-column>
                
                <el-table-column prop="submitterType" label="提交者类型" width="100">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="getSubmitterTypeTag(scope.row.submitterType)">
                            {{ getSubmitterTypeName(scope.row.submitterType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                
                <el-table-column prop="status" label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="getStatusTag(scope.row.status)">
                            {{ getStatusName(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                
                <el-table-column prop="progress" label="完成进度" width="120">
                    <template slot-scope="scope">
                        <el-progress :percentage="scope.row.progress" :stroke-width="8" :show-text="true" :format="format => `${format}%`"></el-progress>
                    </template>
                </el-table-column>
                
                <el-table-column prop="totalScore" label="总分" width="100">
                    <template slot-scope="scope">
                        <span :class="getScoreClass(scope.row.totalScore)">
                            {{ scope.row.totalScore || '-' }}
                        </span>
                    </template>
                </el-table-column>
                
                <el-table-column prop="answeredQuestionnaires" label="已答问卷" width="100"></el-table-column>
                
                <el-table-column prop="answeredQuestions" label="已答题目" width="100"></el-table-column>
                
                <el-table-column prop="submitTime" label="提交时间" width="160">
                    <template slot-scope="scope">
                        {{ formatDateTime(scope.row.submitTime) }}
                    </template>
                </el-table-column>
                
                <el-table-column label="操作" width="200" fixed="right">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="viewSubmissionDetail(scope.row)">查看详情</el-button>
                        <el-button type="text" size="small" @click="downloadSubmission(scope.row)">下载</el-button>
                        <el-dropdown @command="handleRowCommand" style="margin-left: 10px;">
                            <el-button type="text" size="small">
                                更多<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="{action: 'edit', row: scope.row}">编辑状态</el-dropdown-item>
                                <el-dropdown-item :command="{action: 'duplicate', row: scope.row}">复制链接</el-dropdown-item>
                                <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided class="danger-text">删除</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.pageNum"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total">
                </el-pagination>
            </div>
        </div>

        <!-- 提交详情对话框 -->
        <el-dialog 
            :title="`提交详情 - ${currentSubmission ? currentSubmission.submissionNo : ''}`" 
            :visible.sync="detailDialogVisible" 
            width="90%"
            class="detail-dialog">
            <submission-detail 
                v-if="detailDialogVisible && currentSubmission"
                :submission-data="currentSubmission">
            </submission-detail>
        </el-dialog>
    </div>
</template>

<script>
import { smartFormApi } from '@/api/smartForm.js'
import SubmissionDetail from './SubmissionDetail.vue'

export default {
    name: 'DataViewTab',
    components: {
        SubmissionDetail
    },
    props: {
        templateData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            submissionList: [],
            
            // 搜索表单
            searchForm: {
                patientName: '',
                submitterType: '',
                status: '',
                minScore: null,
                maxScore: null,
                timeRange: []
            },
            
            // 分页
            pagination: {
                pageNum: 1,
                pageSize: 20,
                total: 0
            },
            
            // 统计数据
            dataStats: {
                totalSubmissions: 0,
                totalPatients: 0,
                avgScore: 0,
                completionRate: 0
            },
            
            // 表格选择
            multipleSelection: [],
            
            // 提交详情对话框
            detailDialogVisible: false,
            currentSubmission: null
        }
    },
    mounted() {
        // 移除自动加载，改为按需加载
        // 避免与其他组件重复调用相同API
    },
    methods: {
        // 判断是否为新增状态
        isNewForm() {
            return !this.templateData || 
                   !this.templateData.id || 
                   this.templateData.id <= 0 || 
                   this.templateData.id === 'undefined' || 
                   this.templateData.id === 'null';
        },
        
        // 加载提交数据列表
        async loadSubmissionList() {
            // 如果是新增状态，显示空数据
            if (this.isNewForm()) {
                console.log('新增状态，显示空提交数据，templateData:', this.templateData);
                this.submissionList = [];
                this.pagination.total = 0;
                return;
            }
            
            this.loading = true;
            try {
                const params = {
                    formTemplateId: this.templateData.id,
                    pageNum: this.pagination.pageNum,
                    pageSize: this.pagination.pageSize,
                    patientName: this.searchForm.patientName || null,
                    submitterType: this.searchForm.submitterType || null,
                    status: this.searchForm.status || null,
                    startTime: this.searchForm.timeRange && this.searchForm.timeRange[0] ? this.searchForm.timeRange[0] : null,
                    endTime: this.searchForm.timeRange && this.searchForm.timeRange[1] ? this.searchForm.timeRange[1] : null,
                    scoreRange: {
                        min: this.searchForm.minScore,
                        max: this.searchForm.maxScore
                    }
                };
                
                const response = await smartFormApi.getFormSubmissionList(params);
                if (response && response.success) {
                    this.submissionList = response.data.records || [];
                    this.pagination.total = response.data.total || 0;
                } else {
                    this.$message.error(response.message || '获取提交数据失败');
                    this.submissionList = [];
                }
            } catch (error) {
                console.error('加载提交数据列表失败:', error);
                this.$message.error('加载提交数据失败');
                this.submissionList = [];
            } finally {
                this.loading = false;
            }
        },
        
        // 加载统计数据
        async loadDataStats() {
            // 如果是新增状态，显示空数据
            if (this.isNewForm()) {
                console.log('新增状态，显示空统计数据，templateData:', this.templateData);
                this.dataStats = {
                    totalSubmissions: 0,
                    totalPatients: 0,
                    avgScore: 0,
                    completionRate: 0
                };
                return;
            }
            
            try {
                console.log('开始加载统计数据，ID:', this.templateData.id);
                const params = {
                    formTemplateId: this.templateData.id,
                    analysisType: 'overview'
                };
                
                const response = await smartFormApi.getFormDataAnalysis(params);
                if (response && response.success) {
                    const overview = response.data.overview || {};
                    this.dataStats = {
                        totalSubmissions: overview.totalSubmissions || 0,
                        totalPatients: overview.totalPatients || 0,
                        avgScore: overview.avgScore ? parseFloat(overview.avgScore).toFixed(1) : 0,
                        completionRate: overview.completionRate ? parseFloat(overview.completionRate).toFixed(1) : 0
                    };
                    console.log('统计数据加载成功:', this.dataStats);
                } else {
                    // 如果API调用失败，显示空数据
                    this.dataStats = {
                        totalSubmissions: 0,
                        totalPatients: 0,
                        avgScore: 0,
                        completionRate: 0
                    };
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                // 出错时显示空数据
                this.dataStats = {
                    totalSubmissions: 0,
                    totalPatients: 0,
                    avgScore: 0,
                    completionRate: 0
                };
            }
        },
        

        
        // 搜索
        handleSearch() {
            this.pagination.pageNum = 1;
            this.loadSubmissionList();
        },
        
        // 重置搜索
        resetSearch() {
            this.searchForm = {
                patientName: '',
                submitterType: '',
                status: '',
                minScore: null,
                maxScore: null,
                timeRange: []
            };
            this.pagination.pageNum = 1;
            this.loadSubmissionList();
        },
        
        // 刷新数据
        refreshData() {
            this.loadSubmissionList();
            this.loadDataStats();
        },
        
        // 导出数据
        exportData() {
            this.$message.info('数据导出功能开发中...');
        },
        
        // 查看提交详情
        async viewSubmissionDetail(submission) {
            try {
                const response = await smartFormApi.getFormSubmissionDetail(submission.id);
                if (response && response.success) {
                    this.currentSubmission = response.data;
                    this.detailDialogVisible = true;
                } else {
                    this.$message.error('获取提交详情失败');
                }
            } catch (error) {
                console.error('获取提交详情失败:', error);
                this.$message.error('获取提交详情失败');
            }
        },
        
        // 下载提交数据
        downloadSubmission(submission) {
            this.$message.info('下载功能开发中...');
        },
        
        // 处理行操作命令
        handleRowCommand(command) {
            const { action, row } = command;
            switch (action) {
                case 'edit':
                    this.editSubmissionStatus(row);
                    break;
                case 'duplicate':
                    this.copySubmissionLink(row);
                    break;
                case 'delete':
                    this.deleteSubmission(row);
                    break;
            }
        },
        
        // 编辑提交状态
        editSubmissionStatus(submission) {
            this.$message.info('编辑状态功能开发中...');
        },
        
        // 复制提交链接
        copySubmissionLink(submission) {
            this.$message.info('复制链接功能开发中...');
        },
        
        // 删除提交记录
        deleteSubmission(submission) {
            this.$confirm('确定要删除这个提交记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message.info('删除功能开发中...');
            });
        },
        
        // 表格选择改变
        handleSelectionChange(selection) {
            this.multipleSelection = selection;
        },
        
        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.pagination.pageNum = 1;
            this.loadSubmissionList();
        },
        
        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.pageNum = val;
            this.loadSubmissionList();
        },
        
        // 获取提交者类型标签样式
        getSubmitterTypeTag(type) {
            const tagMap = {
                'patient': 'primary',
                'doctor': 'success',
                'nurse': 'warning'
            };
            return tagMap[type] || 'info';
        },
        
        // 获取提交者类型名称
        getSubmitterTypeName(type) {
            const typeMap = {
                'patient': '患者',
                'doctor': '医生',
                'nurse': '护士'
            };
            return typeMap[type] || type;
        },
        
        // 获取状态标签样式
        getStatusTag(status) {
            const tagMap = {
                'submitted': 'info',
                'reviewed': 'warning',
                'completed': 'success'
            };
            return tagMap[status] || 'info';
        },
        
        // 获取状态名称
        getStatusName(status) {
            const statusMap = {
                'submitted': '已提交',
                'reviewed': '已审核',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        },
        
        // 获取分数样式类
        getScoreClass(score) {
            if (!score) return '';
            if (score >= 80) return 'score-high';
            if (score >= 60) return 'score-medium';
            return 'score-low';
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        },
        
        // 刷新方法
        refresh() {
            this.loadSubmissionList();
            this.loadDataStats();
        }
    }
}
</script>

<style lang="scss" scoped>
.data-view-tab {
    padding: 20px;
    
    .toolbar {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
        
        .toolbar-left {
            h4 {
                margin: 0 0 5px 0;
                color: #303133;
                font-size: 18px;
            }
            
            p {
                margin: 0;
                color: #909399;
                font-size: 14px;
            }
        }
        
        .toolbar-right {
            .el-button {
                margin-left: 10px;
            }
        }
    }
    
    .search-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 4px;
        margin-bottom: 20px;
        
        .search-form {
            .el-form-item {
                margin-bottom: 15px;
                margin-right: 20px;
            }
        }
    }
    
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
        
        .stat-card {
            display: flex;
            align-items: center;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            
            .stat-icon {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 15px;
                
                i {
                    font-size: 28px;
                    color: #fff;
                }
                
                &.submit {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }
                
                &.patient {
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                }
                
                &.score {
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                }
                
                &.completion {
                    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                }
            }
            
            .stat-content {
                .stat-value {
                    font-size: 24px;
                    font-weight: 600;
                    color: #303133;
                    line-height: 1;
                    margin-bottom: 5px;
                }
                
                .stat-label {
                    font-size: 14px;
                    color: #909399;
                }
            }
        }
    }
    
    .data-table {
        background-color: #fff;
        border-radius: 4px;
        padding: 20px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        
        .pagination-wrapper {
            margin-top: 20px;
            text-align: right;
        }
    }
}

.detail-dialog {
    /deep/ .el-dialog {
        margin-top: 5vh !important;
        margin-bottom: 5vh !important;
        max-height: 90vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    /deep/ .el-dialog__body {
        flex: 1;
        overflow: auto;
        padding: 10px 20px;
    }
}

.danger-text {
    color: #f56c6c;
}

.score-high {
    color: #67c23a;
    font-weight: 600;
}

.score-medium {
    color: #e6a23c;
    font-weight: 600;
}

.score-low {
    color: #f56c6c;
    font-weight: 600;
}

/deep/ .el-table {
    font-size: 14px;
    
    .el-table__header {
        color: #303133;
        font-weight: 500;
    }
    
    .cell {
        .el-link {
            font-weight: 500;
        }
    }
}

/deep/ .el-tag {
    font-size: 12px;
}

/deep/ .el-progress {
    .el-progress__text {
        font-size: 12px !important;
    }
}

/deep/ .el-dropdown-menu__item.danger-text {
    color: #f56c6c;
}
</style> 
<template>
    <div class="content-template">
        <!-- 空状态 -->
        <div v-if="templates.length === 0 && !loading" class="empty-state">
            <div class="empty-icon">
                <i class="el-icon-collection"></i>
            </div>
            <h3>内容模板管理</h3>
            <p>暂无内容模板，点击下方按钮创建第一个内容模板。</p>
            <el-button type="primary" @click="createTemplate">新增内容模板</el-button>
        </div>
        
        <!-- 模板列表 -->
        <div v-else class="template-list">
            <div class="list-header">
                <h3>内容模板管理</h3>
                <el-button type="primary" @click="createTemplate">新增内容模板</el-button>
            </div>
            
            <el-table v-loading="loading" :data="templates" border style="width: 100%">
                <el-table-column prop="name" label="模板名称" min-width="200"></el-table-column>
                <el-table-column prop="category" label="分类" width="120">
                    <template slot-scope="scope">
                        <el-tag size="small" type="info">
                            {{ getCategoryName(scope.row.category) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="messageType" label="消息类型" width="120">
                    <template slot-scope="scope">
                        <el-tag size="small" type="success">
                            {{ getMessageTypeName(scope.row.messageType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="permission" label="权限范围" width="120">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="scope.row.permission === 'all' ? 'warning' : 'primary'">
                            {{ getPermissionName(scope.row.permission) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template slot-scope="scope">
                        <el-tag size="small" :type="scope.row.status === 'active' ? 'success' : 'info'">
                            {{ scope.row.status === 'active' ? '启用' : '停用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="180">
                    <template slot-scope="scope">
                        {{ formatDateTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="editTemplate(scope.row)">编辑</el-button>
                        <el-button type="text" size="small" @click="previewTemplate(scope.row)">预览</el-button>
                        <el-button type="text" size="small" @click="copyTemplate(scope.row)">复制</el-button>
                        <el-button type="text" size="small" class="danger-text" @click="deleteTemplate(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </div>
        </div>

        <!-- 创建/编辑模板基本信息弹窗（图1） -->
        <el-dialog :title="isEditing ? '编辑模板' : '创建模板'" :visible.sync="templateDialogVisible" width="500px" class="template-basic-dialog">
            <el-form :model="templateForm" :rules="templateRules" ref="templateForm" label-width="80px">
                <el-form-item label="模板名称" prop="name" required>
                    <el-input v-model="templateForm.name" placeholder="请输入模板名称" maxlength="50" show-word-limit></el-input>
                    <div class="char-count">{{ templateForm.name ? templateForm.name.length : 0 }}/50</div>
                </el-form-item>
                
                <el-form-item label="说明" prop="description">
                    <el-input
                        type="textarea"
                        v-model="templateForm.description"
                        placeholder="请输入说明"
                        maxlength="1000"
                        show-word-limit
                        :rows="4">
                    </el-input>
                    <div class="char-count">{{ templateForm.description ? templateForm.description.length : 0 }}/1000</div>
                </el-form-item>
                
                <el-form-item label="分类" prop="category" required>
                    <el-select v-model="templateForm.category" placeholder="请选择分类" style="width: 100%">
                        <el-option
                            v-for="item in categoryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="模板权限" prop="permission" required>
                    <el-select v-model="templateForm.permission" placeholder="请选择权限范围" style="width: 100%">
                        <el-option label="全部用户" value="all"></el-option>
                        <el-option label="仅本人" value="self"></el-option>
                        <el-option label="同科室" value="department"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            
            <div slot="footer">
                <el-button @click="templateDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="nextStep" :loading="saving">{{ isEditing ? '编辑' : '新增' }}</el-button>
            </div>
        </el-dialog>

        <!-- 创建/编辑消息内容弹窗（图2） -->
        <el-dialog :title="isEditing ? '编辑消息模板' : '创建消息模板'" :visible.sync="messageDialogVisible" width="1200px" class="message-content-dialog">
            <div class="message-header">
                <div class="message-type-section">
                    <span class="required-star">*</span>
                    <span class="label">消息类型</span>
                    <el-radio-group v-model="messageForm.messageType" class="message-type-group">
                        <el-radio label="patientEducation">患教内容</el-radio>
                    </el-radio-group>
                    <span class="preview-link">消息预览</span>
                </div>
                
                <div class="content-editor-layout">
                    <!-- 左侧编辑区域 -->
                    <div class="editor-section">
                        <el-form :model="messageForm" :rules="messageRules" ref="messageForm" label-width="80px">
                            <el-form-item label="内容标题" prop="title" required>
                                <el-input v-model="messageForm.title" placeholder="请输入内容标题" @input="updatePreview"></el-input>
                            </el-form-item>
                            
                            <el-form-item label="自定义编辑" class="editor-form-item">
                                <div class="editor-container">
                                    <UEditor
                                        v-model="messageForm.content"
                                        :ueditorConfig="ueditorConfig"
                                        @ready="onEditorReady"
                                        @input="updatePreview">
                                    </UEditor>
                                </div>
                            </el-form-item>
                        </el-form>
                    </div>
                    
                    <!-- 右侧预览区域 -->
                    <div class="preview-section">
                        <div class="preview-header">
                            <h4>消息预览</h4>
                        </div>
                        <div class="preview-content-wrapper">
                            <div class="preview-message">
                                <div class="preview-title" v-if="messageForm.title">
                                    {{ messageForm.title }}
                                </div>
                                <div class="preview-body" v-html="previewContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div slot="footer">
                <el-button @click="closeMessageDialog">取消</el-button>
                <el-button type="primary" @click="saveTemplate" :loading="saving">{{ isEditing ? '更新' : '保存' }}</el-button>
            </div>
        </el-dialog>

        <!-- 预览弹窗 -->
        <el-dialog title="模板预览" :visible.sync="previewDialogVisible" width="700px">
                            <div class="preview-content">
                    <h4>{{ previewTemplateData.title }}</h4>
                    <div class="preview-html" v-html="previewTemplateData.content"></div>
                </div>
        </el-dialog>
    </div>
</template>

<script>
import UEditor from '@/components/common/UEditor.vue'
import { contentTemplateApi } from '@/api/contentTemplate'

export default {
    name: 'ContentTemplate',
    components: {
        UEditor
    },
    data() {
        return {
            loading: false,
            saving: false,
            templates: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            
            // 编辑状态标识
            isEditing: false,
            editingTemplateId: null,
            
            // 模板基本信息弹窗
            templateDialogVisible: false,
            templateForm: {
                name: '',
                description: '',
                category: '',
                permission: 'all'
            },
            templateRules: {
                name: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' },
                    { max: 50, message: '模板名称不能超过50个字符', trigger: 'blur' }
                ],
                category: [
                    { required: true, message: '请选择分类', trigger: 'change' }
                ],
                permission: [
                    { required: true, message: '请选择权限范围', trigger: 'change' }
                ]
            },
            
            // 消息内容弹窗
            messageDialogVisible: false,
            messageForm: {
                messageType: 'patientEducation',
                title: '',
                content: ''
            },
            messageRules: {
                title: [
                    { required: true, message: '请输入内容标题', trigger: 'blur' }
                ]
            },
            
            // 预览弹窗
            previewDialogVisible: false,
            previewTemplateData: {},
            
            // 实时预览内容
            previewContent: '',
            editorInstance: null,
            
            // 下拉选项
            categoryOptions: [],
            
            // 富文本编辑器配置
            ueditorConfig: {
                initialFrameHeight: 400,
                initialFrameWidth: '100%',
                serverUrl: '/cas-admin/ueditor/jsp/',
                toolbars: [[
                    'fullscreen', 'source', '|', 'undo', 'redo', '|',
                    'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                    'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                    'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                    'directionalityltr', 'directionalityrtl', 'indent', '|',
                    'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
                    'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                    'simpleupload', 'insertimage', 'emotion', 'scrawl', 'insertvideo', 'music', 'attachment', 'map', 'gmap', 'insertframe', 'insertcode', 'webapp', 'pagebreak', 'template', 'background', '|',
                    'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|',
                    'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
                    'print', 'preview', 'searchreplace', 'drafts', 'help'
                ]]
            }
        }
    },
    mounted() {
        this.loadTemplates();
        this.loadCategories();
    },
    methods: {
        // 加载模板列表
        async loadTemplates() {
            try {
                this.loading = true;
                const params = {
                    page: this.currentPage,
                    pageSize: this.pageSize
                };
                
                const response = await contentTemplateApi.getTemplateList(params);
                if (response.success) {
                    this.templates = response.data.list || [];
                    this.total = response.data.total || 0;
                } else {
                    this.$message.error(response.message || '加载模板列表失败');
                }
            } catch (error) {
                console.error('加载模板列表失败:', error);
                this.$message.error('加载模板列表失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 加载分类列表
        async loadCategories() {
            try {
                const response = await contentTemplateApi.getCategoryList();
                if (response.success) {
                    this.categoryOptions = response.data.map(item => ({
                        label: item.name,
                        value: item.code
                    }));
                }
            } catch (error) {
                console.error('加载分类列表失败:', error);
                // 如果加载失败，使用默认分类
                this.categoryOptions = [
                    { label: '健康教育', value: 'health_education' },
                    { label: '用药指导', value: 'medication_guide' },
                    { label: '护理指导', value: 'nursing_guide' },
                    { label: '康复指导', value: 'rehabilitation_guide' },
                    { label: '饮食指导', value: 'diet_guide' },
                    { label: '其他', value: 'other' }
                ];
            }
        },
        
        // 创建模板
        createTemplate() {
            this.isEditing = false;
            this.editingTemplateId = null;
            this.templateForm = {
                name: '',
                description: '',
                category: '',
                permission: 'all'
            };
            // 重置预览内容
            this.previewContent = '<p style="color: #999; font-style: italic;">请输入内容...</p>';
            this.editorInstance = null;
            this.templateDialogVisible = true;
        },
        
        // 下一步
        nextStep() {
            this.$refs.templateForm.validate((valid) => {
                if (valid) {
                    this.templateDialogVisible = false;
                    this.messageForm = {
                        messageType: 'patientEducation',
                        title: '',
                        content: ''
                    };
                    // 重置预览内容
                    this.previewContent = '<p style="color: #999; font-style: italic;">请输入内容...</p>';
                    this.editorInstance = null;
                    this.messageDialogVisible = true;
                }
            });
        },
        
        // 保存模板
        async saveTemplate() {
            this.$refs.messageForm.validate(async (valid) => {
                if (valid) {
                    try {
                        this.saving = true;
                        const params = {
                            ...this.templateForm,
                            ...this.messageForm
                        };
                        
                        // 如果是编辑模式，添加ID
                        if (this.isEditing && this.editingTemplateId) {
                            params.id = this.editingTemplateId;
                        }
                        
                        let response;
                        if (this.isEditing) {
                            response = await contentTemplateApi.updateTemplate(params);
                            if (response.success) {
                                this.$message.success('更新模板成功');
                            } else {
                                this.$message.error(response.message || '更新模板失败');
                            }
                        } else {
                            response = await contentTemplateApi.createTemplate(params);
                            if (response.success) {
                                this.$message.success('创建模板成功');
                            } else {
                                this.$message.error(response.message || '创建模板失败');
                            }
                        }
                        
                        if (response.success) {
                            this.closeMessageDialog();
                            this.loadTemplates();
                        }
                    } catch (error) {
                        console.error('保存模板失败:', error);
                        this.$message.error('保存模板失败');
                    } finally {
                        this.saving = false;
                    }
                }
            });
        },
        
        // 编辑模板
        async editTemplate(template) {
            try {
                this.loading = true;
                const response = await contentTemplateApi.getTemplateById(template.id);
                if (response.success) {
                    const templateData = response.data;
                    
                    // 设置编辑状态
                    this.isEditing = true;
                    this.editingTemplateId = templateData.id;
                    
                    // 设置模板基本信息
                    this.templateForm = {
                        name: templateData.name,
                        description: templateData.description,
                        category: templateData.category,
                        permission: templateData.permission
                    };
                    // 设置消息信息
                    this.messageForm = {
                        messageType: templateData.messageType,
                        title: templateData.title,
                        content: templateData.content
                    };
                    // 设置预览内容
                    this.previewContent = templateData.content || '<p style="color: #999; font-style: italic;">请输入内容...</p>';
                    this.editorInstance = null;
                    // 直接打开消息编辑弹窗
                    this.messageDialogVisible = true;
                } else {
                    this.$message.error(response.message || '获取模板详情失败');
                }
            } catch (error) {
                console.error('获取模板详情失败:', error);
                this.$message.error('获取模板详情失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 预览模板
        previewTemplate(template) {
            this.previewTemplateData = template;
            this.previewDialogVisible = true;
        },
        
        // 复制模板
        async copyTemplate(template) {
            try {
                const response = await contentTemplateApi.copyTemplate(template.id);
                if (response.success) {
                    this.$message.success('模板复制成功');
                    this.loadTemplates();
                } else {
                    this.$message.error(response.message || '模板复制失败');
                }
            } catch (error) {
                console.error('复制模板失败:', error);
                this.$message.error('复制模板失败');
            }
        },
        
        // 删除模板
        async deleteTemplate(template) {
            this.$confirm('确定要删除该模板吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await contentTemplateApi.deleteTemplate(template.id);
                    if (response.success) {
                        this.$message.success('删除成功');
                        this.loadTemplates();
                    } else {
                        this.$message.error(response.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除模板失败:', error);
                    this.$message.error('删除失败');
                }
            }).catch(() => {
                // 用户取消删除
            });
        },
        
        // 分页
        handleSizeChange(val) {
            this.pageSize = val;
            this.loadTemplates();
        },
        
        handleCurrentChange(val) {
            this.currentPage = val;
            this.loadTemplates();
        },
        
        // 富文本编辑器准备完成
        onEditorReady(instance) {
            console.log('UEditor实例:', instance);
            this.editorInstance = instance;
            
            // 监听编辑器内容变化
            instance.addListener('contentChange', () => {
                this.updatePreview();
            });
            
            // 初始化预览内容
            this.updatePreview();
        },
        
        // 更新预览内容
        updatePreview() {
            this.$nextTick(() => {
                // 获取富文本编辑器的内容
                let content = this.messageForm.content || '';
                
                // 如果编辑器实例存在，从编辑器获取最新内容
                if (this.editorInstance && this.editorInstance.getContent) {
                    content = this.editorInstance.getContent();
                    // 更新表单数据
                    this.messageForm.content = content;
                }
                
                // 更新预览内容
                this.previewContent = content || '<p style="color: #999; font-style: italic;">请输入内容...</p>';
            });
        },
        
        // 关闭消息编辑弹窗
        closeMessageDialog() {
            this.messageDialogVisible = false;
            // 清理数据
            this.previewContent = '';
            this.editorInstance = null;
            // 清理编辑状态
            this.isEditing = false;
            this.editingTemplateId = null;
        },
        
        // 格式化时间
        formatDateTime(dateTime) {
            if (!dateTime) return '';
            // 如果已经是字符串格式（后端已经格式化），直接返回
            if (typeof dateTime === 'string' && dateTime.includes('-')) {
                return dateTime;
            }
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        },
        
        // 获取分类名称
        getCategoryName(category) {
            const item = this.categoryOptions.find(item => item.value === category);
            return item ? item.label : category;
        },
        
        // 获取消息类型名称
        getMessageTypeName(type) {
            const types = {
                'patientEducation': '患教内容'
            };
            return types[type] || type;
        },
        
        // 获取权限名称
        getPermissionName(permission) {
            const permissions = {
                'all': '全部用户',
                'self': '仅本人',
                'department': '同科室'
            };
            return permissions[permission] || permission;
        },
        
        // 刷新方法，供父组件调用
        refresh() {
            this.loadTemplates();
        }
    }
}
</script>

<style lang="scss" scoped>
.content-template {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.empty-state {
    background-color: #fff;
    border-radius: 8px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .empty-icon {
        font-size: 64px;
        color: #dcdfe6;
        margin-bottom: 20px;
    }
    
    h3 {
        color: #606266;
        margin-bottom: 10px;
    }
    
    p {
        color: #909399;
        margin-bottom: 30px;
    }
}

.template-list {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
            margin: 0;
            color: #303133;
            font-size: 18px;
            font-weight: 500;
        }
    }
    
    .pagination-wrapper {
        margin-top: 20px;
        text-align: right;
    }
}

.danger-text {
    color: #f56c6c;
}

.char-count {
    font-size: 12px;
    color: #909399;
    text-align: right;
    margin-top: 5px;
}

.required-star {
    color: #f56c6c;
    margin-right: 4px;
}

.message-header {
    .message-type-section {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
        
        .label {
            margin-right: 15px;
            font-weight: 500;
        }
        
        .message-type-group {
            flex: 1;
            margin-right: 15px;
        }
        
        .preview-link {
            color: #409eff;
            cursor: pointer;
            font-size: 12px;
        }
    }
}

.editor-form-item {
    /deep/ .el-form-item__content {
        .editor-container {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            overflow: hidden;
        }
    }
}

.preview-content {
    h4 {
        margin-bottom: 15px;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 10px;
    }
    
    .preview-html {
        line-height: 1.6;
        color: #606266;
    }
}

/deep/ .template-basic-dialog {
    .el-dialog__body {
        padding: 20px;
    }
}

/deep/ .message-content-dialog {
    .el-dialog__body {
        padding: 20px;
        max-height: 80vh;
        overflow-y: auto;
    }
}

.content-editor-layout {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    
    .editor-section {
        flex: 1;
        min-width: 0; // 防止flex项目溢出
    }
    
    .preview-section {
        width: 350px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        overflow: hidden;
        background-color: #fff;
        
        .preview-header {
            background-color: #f5f7fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e4e7ed;
            
            h4 {
                margin: 0;
                font-size: 14px;
                font-weight: 500;
                color: #409eff;
            }
        }
        
        .preview-content-wrapper {
            padding: 16px;
            max-height: 400px;
            overflow-y: auto;
            
            .preview-message {
                .preview-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #303133;
                    margin-bottom: 12px;
                    padding-bottom: 8px;
                    border-bottom: 1px solid #f0f0f0;
                }
                
                .preview-body {
                    font-size: 14px;
                    line-height: 1.6;
                    color: #606266;
                    
                    // 重置富文本编辑器的样式
                    * {
                        max-width: 100% !important;
                    }
                    
                    p {
                        margin: 0 0 8px 0;
                        
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                    
                    img {
                        max-width: 100%;
                        height: auto;
                        display: block;
                        margin: 8px 0;
                    }
                    
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 8px 0;
                        font-size: 12px;
                        
                        td, th {
                            border: 1px solid #e4e7ed;
                            padding: 4px 8px;
                            text-align: left;
                        }
                        
                        th {
                            background-color: #f5f7fa;
                            font-weight: 500;
                        }
                    }
                    
                    ul, ol {
                        margin: 8px 0;
                        padding-left: 20px;
                        
                        li {
                            margin: 4px 0;
                        }
                    }
                    
                    blockquote {
                        margin: 8px 0;
                        padding: 8px 12px;
                        background-color: #f5f7fa;
                        border-left: 4px solid #409eff;
                    }
                }
            }
        }
    }
}
</style> 
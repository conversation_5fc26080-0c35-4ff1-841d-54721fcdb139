<template>
    <div class="template-preview">
        <div class="preview-header">
            <h3>{{ templateData.name }}</h3>
            <el-tag :type="templateData.status === 1 ? 'success' : 'danger'">
                {{ templateData.status === 1 ? '启用' : '禁用' }}
            </el-tag>
        </div>
        
        <div class="preview-content">
            <div class="info-section">
                <h4>基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">模板分类：</span>
                        <span class="value">{{ getCategoryName(templateData.category) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">适用科室：</span>
                        <span class="value">{{ getDepartmentName(templateData.departmentId) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">创建人：</span>
                        <span class="value">{{ templateData.creatorName }}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">创建时间：</span>
                        <span class="value">{{ formatTime(templateData.createTime) }}</span>
                    </div>
                </div>
                <div class="description">
                    <span class="label">描述：</span>
                    <p>{{ templateData.description || '暂无描述' }}</p>
                </div>
            </div>
            
            <div class="config-section" v-if="templateData.config">
                <h4>随访配置</h4>
                <div class="config-grid">
                    <div class="config-item">
                        <span class="label">随访周期：</span>
                        <span class="value">{{ templateData.config.duration }} {{ getTimeUnitName(templateData.config.timeUnit) }}</span>
                    </div>
                    <div class="config-item">
                        <span class="label">随访方式：</span>
                        <span class="value">{{ getFollowMethodsText(templateData.config.followMethods) }}</span>
                    </div>
                    <div class="config-item">
                        <span class="label">提醒时间：</span>
                        <span class="value">{{ templateData.config.reminderTime || '未设置' }}</span>
                    </div>
                    <div class="config-item">
                        <span class="label">重复提醒：</span>
                        <span class="value">{{ templateData.config.repeatReminder ? '是' : '否' }}</span>
                    </div>
                </div>
            </div>
            
            <div class="content-section" v-if="templateData.config">
                <h4>随访内容</h4>
                
                <!-- 问卷调查 -->
                <div class="content-block" v-if="templateData.config.questionnaires && templateData.config.questionnaires.length > 0">
                    <h5>问卷调查</h5>
                    <el-table :data="templateData.config.questionnaires" border size="small">
                        <el-table-column prop="name" label="问卷名称"></el-table-column>
                        <el-table-column prop="type" label="类型" width="120">
                            <template slot-scope="scope">
                                <el-tag size="mini">{{ getQuestionnaireTypeName(scope.row.type) }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="required" label="必填" width="80">
                            <template slot-scope="scope">
                                <i class="el-icon-check" v-if="scope.row.required" style="color: #67c23a;"></i>
                                <i class="el-icon-close" v-else style="color: #f56c6c;"></i>
                            </template>
                        </el-table-column>
                        <el-table-column prop="order" label="排序" width="80"></el-table-column>
                    </el-table>
                </div>
                
                <!-- 健康指标 -->
                <div class="content-block" v-if="templateData.config.healthIndexes && templateData.config.healthIndexes.length > 0">
                    <h5>健康指标</h5>
                    <el-table :data="templateData.config.healthIndexes" border size="small">
                        <el-table-column prop="name" label="指标名称"></el-table-column>
                        <el-table-column prop="unit" label="单位" width="80"></el-table-column>
                        <el-table-column prop="normalRange" label="正常范围" width="150"></el-table-column>
                        <el-table-column prop="required" label="必填" width="80">
                            <template slot-scope="scope">
                                <i class="el-icon-check" v-if="scope.row.required" style="color: #67c23a;"></i>
                                <i class="el-icon-close" v-else style="color: #f56c6c;"></i>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                
                <!-- 检查项目 -->
                <div class="content-block" v-if="templateData.config.examinations && templateData.config.examinations.length > 0">
                    <h5>检查项目</h5>
                    <el-table :data="templateData.config.examinations" border size="small">
                        <el-table-column prop="name" label="检查项目"></el-table-column>
                        <el-table-column prop="category" label="类型" width="120">
                            <template slot-scope="scope">
                                <el-tag size="mini">{{ getExaminationTypeName(scope.row.category) }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="required" label="必需" width="80">
                            <template slot-scope="scope">
                                <i class="el-icon-check" v-if="scope.row.required" style="color: #67c23a;"></i>
                                <i class="el-icon-close" v-else style="color: #f56c6c;"></i>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'TemplatePreview',
    props: {
        templateData: {
            type: Object,
            required: true
        }
    },
    methods: {
        // 获取分类名称
        getCategoryName(category) {
            const categoryMap = {
                '1': '术后随访',
                '2': '慢病管理',
                '3': '健康体检'
            }
            return categoryMap[category] || category
        },
        
        // 获取科室名称
        getDepartmentName(departmentId) {
            const departmentMap = {
                '1': '疼痛科',
                '2': '骨科',
                '3': '内科'
            }
            return departmentMap[departmentId] || departmentId
        },
        
        // 获取时间单位名称
        getTimeUnitName(timeUnit) {
            const unitMap = {
                'day': '天',
                'week': '周',
                'month': '月',
                'year': '年'
            }
            return unitMap[timeUnit] || timeUnit
        },
        
        // 获取随访方式文本
        getFollowMethodsText(methods) {
            if (!methods || methods.length === 0) return '未设置'
            const methodMap = {
                'phone': '电话随访',
                'wechat': '微信随访',
                'app': 'APP推送',
                'sms': '短信提醒'
            }
            return methods.map(method => methodMap[method] || method).join('、')
        },
        
        // 获取问卷类型名称
        getQuestionnaireTypeName(type) {
            const typeMap = {
                'symptom': '症状评估',
                'quality': '生活质量',
                'satisfaction': '满意度调查',
                'function': '功能评价'
            }
            return typeMap[type] || type
        },
        
        // 获取检查类型名称
        getExaminationTypeName(type) {
            const typeMap = {
                'imaging': '影像检查',
                'laboratory': '实验室检查',
                'function': '功能检查',
                'pathology': '病理检查'
            }
            return typeMap[type] || type
        },
        
        // 格式化时间
        formatTime(time) {
            if (!time) return ''
            return new Date(time).toLocaleString('zh-CN')
        }
    }
}
</script>

<style lang="scss" scoped>
.template-preview {
    padding: 20px;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    
    h3 {
        margin: 0;
        color: #303133;
    }
}

.preview-content {
    .info-section,
    .config-section,
    .content-section {
        margin-bottom: 30px;
        
        h4 {
            color: #409EFF;
            margin-bottom: 15px;
            font-size: 16px;
            border-left: 3px solid #409EFF;
            padding-left: 10px;
        }
        
        h5 {
            color: #606266;
            margin-bottom: 10px;
            margin-top: 20px;
            font-size: 14px;
        }
    }
    
    .info-grid,
    .config-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .info-item,
    .config-item {
        .label {
            color: #606266;
            font-weight: 500;
        }
        
        .value {
            color: #303133;
            margin-left: 5px;
        }
    }
    
    .description {
        .label {
            color: #606266;
            font-weight: 500;
        }
        
        p {
            margin: 5px 0 0 0;
            color: #303133;
            line-height: 1.6;
        }
    }
    
    .content-block {
        margin-bottom: 25px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

/deep/ .el-table {
    .el-table__header th {
        background-color: #f8f9fa;
    }
    
    .el-table__row:hover {
        background-color: #f5f7fa;
    }
}
</style> 
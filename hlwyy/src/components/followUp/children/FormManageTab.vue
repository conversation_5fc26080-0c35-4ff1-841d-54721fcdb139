<template>
    <div class="form-manage-tab">
        <!-- 表单基本信息 -->
        <div class="form-basic-info">
            <div class="info-row">
                <div class="info-item">
                    <label class="info-label">表单名称</label>
                    <el-input v-model="templateData.name" placeholder="请输入表单名称" style="width: 300px;"></el-input>
                </div>
                <div class="info-item">
                    <label class="info-label">表单类型</label>
                    <el-select v-model="templateData.formType" placeholder="请选择表单类型" style="width: 200px;">
                        <el-option label="一次性问卷" value="one_time"></el-option>
                        <el-option label="重复性问卷" value="repeatable"></el-option>
                    </el-select>
                </div>
            </div>
        </div>

        <!-- 表单收集 -->
        <div class="form-collection-section">
            <h4 class="section-title">表单收集 ({{ formQuestionnaires.length }}个问卷)</h4>
            <div class="questionnaire-grid">
                <!-- 已添加的问卷 -->
                <div 
                    v-for="questionnaire in formQuestionnaires" 
                    :key="questionnaire.id || questionnaire.questionnaireId"
                    class="questionnaire-card">
                    <div class="card-header">
                        <span class="card-title">{{ (questionnaire.displayConfig && questionnaire.displayConfig.title) || questionnaire.questionnaireName }}</span>
                        <div class="custom-dropdown" @click.stop>
                            <span class="el-dropdown-link" @click="toggleCustomDropdown(questionnaire.id)">
                                <i class="el-icon-more"></i>
                            </span>
                            <div v-show="activeDropdownId === questionnaire.id" class="custom-dropdown-menu">
                                <div class="custom-dropdown-item" @click="handleQuestionnaireCommand({action: 'edit', questionnaire})">编辑</div>
                                <div class="custom-dropdown-item" @click="handleQuestionnaireCommand({action: 'preview', questionnaire})">预览</div>
                                <div class="custom-dropdown-item" @click="handleQuestionnaireCommand({action: 'remove', questionnaire})">移除</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-content">
                        <p class="card-description">{{ (questionnaire.displayConfig && questionnaire.displayConfig.description) || '暂无描述' }}</p>
                        <div class="card-meta">
                            <span class="meta-item">题目数：{{ questionnaire.questionCount || 0 }}</span>
                            <span class="meta-item">排序：{{ questionnaire.sortOrder }}</span>
                            <span class="meta-item">{{ questionnaire.isRequired ? '必填' : '选填' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 新增表单卡片 -->
                <div class="add-questionnaire-card" @click="showAddQuestionnaireDialog">
                    <div class="add-icon">
                        <i class="el-icon-plus"></i>
                    </div>
                    <div class="add-text">新增表单</div>
                </div>
            </div>
        </div>

        <!-- 其他数据 -->
        <div class="other-data-section">
            <h4 class="section-title">其他数据</h4>
            <el-table :data="otherDataList" border style="width: 100%">
                <el-table-column prop="sequence" label="序号" width="80"></el-table-column>
                <el-table-column prop="type" label="类型" width="120"></el-table-column>
                <el-table-column prop="name" label="登记名称" min-width="200"></el-table-column>
                <el-table-column label="操作" width="120">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="editOtherData(scope.row)">编辑</el-button>
                        <el-button type="text" size="small" @click="deleteOtherData(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="other-data-actions">
                <el-button @click="addOtherData">新增</el-button>
            </div>
        </div>

        <!-- 备注说明 -->
        <div class="remark-section">
            <h4 class="section-title">备注说明</h4>
            <el-input 
                v-model="remarkText" 
                type="textarea" 
                placeholder="请输入备注说明..."
                maxlength="50"
                show-word-limit
                :rows="3">
            </el-input>
        </div>

        <!-- 知情同意书 -->
        <div class="consent-section">
            <h4 class="section-title">知情同意书</h4>
            <div class="consent-config">
                <el-checkbox v-model="consentConfig.enabled">展示</el-checkbox>
                <span class="consent-text">意见需要签约的知同意</span>
                <el-button type="text" @click="editConsent">设置</el-button>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
            <el-button type="primary" @click="saveForm">保存</el-button>
            <el-button @click="previewForm">预览表单</el-button>
            <el-button @click="shareForm">分享表单</el-button>
        </div>

        <!-- 添加问卷对话框 -->
        <el-dialog title="添加问卷" :visible.sync="addQuestionnaireDialogVisible" width="800px">
            <div class="add-questionnaire-content">
                <!-- 搜索过滤 -->
                <div class="search-section">
                    <el-input
                        v-model="questionnaireSearch.keyword"
                        placeholder="搜索问卷名称"
                        style="width: 300px; margin-right: 10px;"
                        clearable
                        @input="searchQuestionnaires">
                        <i slot="prefix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <el-select v-model="questionnaireSearch.type" placeholder="问卷类型" style="width: 150px;" clearable @change="searchQuestionnaires">
                        <el-option label="全部类型" value=""></el-option>
                        <el-option label="症状评估" value="symptom"></el-option>
                        <el-option label="量表评估" value="scale"></el-option>
                        <el-option label="不良反应" value="side_effect"></el-option>
                        <el-option label="自定义" value="custom"></el-option>
                    </el-select>
                </div>

                <!-- 可用问卷列表 -->
                <div class="available-questionnaires">
                    <el-table 
                        :data="availableQuestionnaires" 
                        @selection-change="handleQuestionnaireSelection"
                        style="width: 100%">
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column prop="name" label="问卷名称" min-width="200"></el-table-column>
                        <el-table-column prop="type" label="类型" width="120">
                            <template slot-scope="scope">
                                <el-tag size="small">{{ getQuestionnaireTypeName(scope.row.type) }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="questionCount" label="题目数" width="80"></el-table-column>
                        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>
                    </el-table>
                </div>
            </div>
            
            <div slot="footer">
                <el-button @click="addQuestionnaireDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="addSelectedQuestionnaires" :disabled="selectedQuestionnaires.length === 0">
                    添加选中问卷 ({{ selectedQuestionnaires.length }})
                </el-button>
            </div>
        </el-dialog>

        <!-- 编辑问卷配置对话框 -->
        <el-dialog title="编辑问卷配置" :visible.sync="editQuestionnaireDialogVisible" width="600px">
            <el-form :model="editingQuestionnaire" :rules="questionnaireRules" ref="questionnaireForm" label-width="120px">
                <el-form-item label="显示标题" prop="displayTitle">
                    <el-input v-model="editingQuestionnaire.displayTitle" placeholder="自定义显示标题"></el-input>
                </el-form-item>
                <el-form-item label="描述说明" prop="displayDescription">
                    <el-input v-model="editingQuestionnaire.displayDescription" type="textarea" :rows="3" placeholder="问卷描述说明"></el-input>
                </el-form-item>
                <el-form-item label="排序顺序" prop="sortOrder">
                    <el-input-number v-model="editingQuestionnaire.sortOrder" :min="1" :max="100"></el-input-number>
                </el-form-item>
                <el-form-item label="是否必填" prop="isRequired">
                    <el-switch v-model="editingQuestionnaire.isRequired" active-text="必填" inactive-text="选填"></el-switch>
                </el-form-item>
            </el-form>
            
            <div slot="footer">
                <el-button @click="editQuestionnaireDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveQuestionnaireConfig">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { smartFormApi } from '@/api/smartForm.js'
import questionnaireApi from '@/api/questionnaire.js'

export default {
    name: 'FormManageTab',
    props: {
        templateData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            formQuestionnaires: [],
            
            // 添加问卷对话框
            addQuestionnaireDialogVisible: false,
            availableQuestionnaires: [],
            selectedQuestionnaires: [],
            questionnaireSearch: {
                keyword: '',
                type: ''
            },
            
            // 编辑问卷配置对话框
            editQuestionnaireDialogVisible: false,
            editingQuestionnaire: {
                id: null,
                displayTitle: '',
                displayDescription: '',
                sortOrder: 1,
                isRequired: true
            },
            
            // 其他数据
            otherDataList: [],
            
            // 备注说明
            remarkText: '',
            
            // 知情同意书配置
            consentConfig: {
                enabled: false,
                content: ''
            },
            
            questionnaireRules: {
                displayTitle: [
                    { required: true, message: '请输入显示标题', trigger: 'blur' }
                ],
                sortOrder: [
                    { required: true, message: '请输入排序顺序', trigger: 'change' }
                ]
            },
            
            // 自定义下拉菜单控制
            activeDropdownId: null
        }
    },
    mounted() {
        // 只加载可用问卷列表，其他数据由watch处理
        this.loadAvailableQuestionnaires();
        this.initOtherData();
        
        // 添加点击外部关闭下拉菜单
        document.addEventListener('click', this.closeAllDropdowns);
    },
    
    beforeDestroy() {
        // 移除事件监听
        document.removeEventListener('click', this.closeAllDropdowns);
    },
    watch: {
        templateData: {
            handler(newVal, oldVal) {
                console.log('FormManageTab watch触发:', {
                    newVal: newVal,
                    oldVal: oldVal,
                    newId: newVal && newVal.id,
                    oldId: oldVal && oldVal.id
                });
                
                // 避免重复加载相同的数据
                if (newVal && newVal.id && newVal.id > 0) {
                    // 当templateData有有效ID时，重新加载数据
                    if (!oldVal || oldVal.id !== newVal.id) {
                        console.log('准备加载表单问卷数据，ID:', newVal.id);
                        // 延迟加载，避免与其他组件重复调用
                        this.$nextTick(() => {
                            this.loadFormQuestionnaires();
                        });
                    } else {
                        console.log('相同ID，跳过重复加载');
                    }
                } else {
                    console.log('新增状态或无效ID，加载默认配置');
                    // 新增状态，只加载配置，不清空问卷数据
                    this.loadFormConfig();
                }
            },
            deep: true,
            immediate: true // 恢复为true，确保编辑状态下能加载数据
        }
    },
    methods: {
        // 刷新方法
        refresh() {
            if (!this.isNewForm()) {
                this.loadFormQuestionnaires();
            }
        },
        
        // 判断是否为新增状态
        isNewForm() {
            // 检查是否为新增状态：没有模板数据，或者ID无效
            const isNew = !this.templateData || 
                   !this.templateData.id || 
                   this.templateData.id <= 0 || 
                   this.templateData.id === 'undefined' || 
                   this.templateData.id === 'null';
            
            console.log('isNewForm检查:', {
                templateData: this.templateData,
                id: this.templateData && this.templateData.id,
                isNew: isNew
            });
            
            return isNew;
        },
        
        // 加载表单问卷列表
        async loadFormQuestionnaires() {
            console.log('loadFormQuestionnaires被调用，templateData:', this.templateData);
            
            // 如果是新增状态，保留现有数据，不重新加载
            if (this.isNewForm()) {
                console.log('新增状态，跳过问卷加载');
                return;
            }
            
            // 防重复调用：如果正在加载中，则跳过
            if (this._loadingQuestionnaires) {
                console.log('正在加载中，跳过重复调用');
                return;
            }
            
            this._loadingQuestionnaires = true;
            
            try {
                console.log('开始调用getFormTemplateById，ID:', this.templateData.id);
                const response = await smartFormApi.getFormTemplateById(this.templateData.id);
                console.log('getFormTemplateById响应:', response);
                
                if (response && response.success && response.data) {
                    const formData = response.data;
                    
                    // 处理问卷数据
                    console.log('加载到的表单数据:', formData);
                    if (formData.questionnaires && Array.isArray(formData.questionnaires)) {
                        this.formQuestionnaires = formData.questionnaires.map(q => ({
                            ...q,
                            questionnaireName: q.questionnaireName || '未知问卷',
                            questionCount: q.questionCount || 0,
                            displayConfig: q.displayConfig || {}
                        }));
                        console.log('处理后的问卷数据:', this.formQuestionnaires);
                    } else {
                        console.log('没有找到问卷数据或格式不正确:', formData.questionnaires);
                        // 兼容处理：如果后端返回null，尝试从questionnaireCount判断
                        if (formData.questionnaireCount && formData.questionnaireCount > 0) {
                            console.warn('后端返回了问卷数量但未返回问卷明细，请检查后端实现');
                        }
                        this.formQuestionnaires = [];
                    }
                    
                    // 同时处理配置数据，避免重复调用API
                    this.processFormConfig(formData);
                } else {
                    console.log('API响应失败或数据为空:', response);
                    this.formQuestionnaires = [];
                    this.processFormConfig(null);
                }
            } catch (error) {
                console.error('加载表单数据失败:', error);
                this.formQuestionnaires = [];
                this.processFormConfig(null);
            } finally {
                this._loadingQuestionnaires = false;
            }
        },
        
        // 加载可用问卷列表 - 修复为使用真实API，参考QuestionnaireTemplate.vue
        async loadAvailableQuestionnaires() {
            try {
                // 直接使用questionnaireApi，与QuestionnaireTemplate.vue保持一致
                const response = await questionnaireApi.getAllQuestionnaires();
                this.availableQuestionnaires = response || [];
            } catch (error) {
                console.error('加载可用问卷列表失败:', error);
                this.$message.error('加载问卷列表失败');
                this.availableQuestionnaires = [];
            }
        },
        
        // 处理表单配置数据
        processFormConfig(formData) {
            // 如果是新增状态，使用默认配置
            if (this.isNewForm()) {
                this.remarkText = '';
                this.consentConfig = {
                    enabled: false,
                    content: ''
                };
                this.otherDataList = [];
                return;
            }
            
            if (formData) {
                // 加载备注说明
                this.remarkText = formData.remarkText || '';
                
                // 加载知情同意书配置
                this.consentConfig = formData.consentConfig || {
                    enabled: false,
                    content: ''
                };
                
                // 加载其他数据
                this.otherDataList = formData.otherData || [];
            } else {
                console.warn('表单模板不存在或已删除，ID:', this.templateData.id);
                // 使用默认配置
                this.remarkText = '';
                this.consentConfig = { enabled: false, content: '' };
                this.otherDataList = [];
            }
        },
        
        // 加载表单配置数据（保留方法名以兼容现有调用）
        async loadFormConfig() {
            // 如果是新增状态，使用默认配置
            if (this.isNewForm()) {
                this.processFormConfig(null);
                return;
            }
            
            // 对于编辑状态，配置数据已经在loadFormQuestionnaires中处理了
            // 这里不需要重复调用API，直接返回
        },
        
        // 初始化其他数据
        initOtherData() {
            this.otherDataList = [];
        },
        
        // 搜索问卷
        searchQuestionnaires() {
            this.loadAvailableQuestionnaires();
        },
        
        // 显示添加问卷对话框
        showAddQuestionnaireDialog() {
            this.addQuestionnaireDialogVisible = true;
            this.selectedQuestionnaires = [];
        },
        
        // 问卷选择改变
        handleQuestionnaireSelection(selection) {
            this.selectedQuestionnaires = selection;
        },
        
        // 添加选中的问卷
        async addSelectedQuestionnaires() {
            try {
                console.log('添加问卷 - 当前状态:', {
                    isNewForm: this.isNewForm(),
                    templateData: this.templateData,
                    selectedQuestionnaires: this.selectedQuestionnaires
                });
                
                // 新增状态下，直接添加到本地问卷列表，不调用API
                if (this.isNewForm()) {
                    console.log('新增状态：直接添加到本地列表');
                    for (const questionnaire of this.selectedQuestionnaires) {
                        // 检查是否已存在相同的问卷
                        const existingQuestionnaire = this.formQuestionnaires.find(q => 
                            q.questionnaireId === questionnaire.id
                        );
                        
                        if (existingQuestionnaire) {
                            this.$message.warning(`问卷"${questionnaire.name}"已存在于表单中，跳过添加`);
                            continue;
                        }
                        
                        const newQuestionnaire = {
                            id: `temp_${Date.now()}_${Math.random()}`, // 添加临时ID用于Vue的key
                            questionnaireId: questionnaire.id, // 直接使用问卷的真实ID
                            questionnaireName: questionnaire.name,
                            questionnaireType: questionnaire.type,
                            questionCount: questionnaire.questionCount,
                            sortOrder: this.formQuestionnaires.length + 1,
                            isRequired: true,
                            displayConfig: {
                                title: questionnaire.name,
                                description: questionnaire.description || ''
                            }
                        };
                        
                        // 使用Vue.set确保响应性
                        this.$set(this.formQuestionnaires, this.formQuestionnaires.length, newQuestionnaire);
                    }
                    
                    // 强制更新视图
                    this.$forceUpdate();
                    
                    this.$message.success('问卷已添加到表单中，保存表单时将一并保存问卷关联');
                    this.addQuestionnaireDialogVisible = false;
                    this.$emit('questionnaire-updated');
                    return;
                }
                
                // 编辑状态下，先检查重复，再添加到本地列表，最后调用API
                console.log('编辑状态：调用API保存关联');
                let addedCount = 0;
                let skippedCount = 0;
                
                for (const questionnaire of this.selectedQuestionnaires) {
                    // 检查是否已存在相同的问卷
                    const existingQuestionnaire = this.formQuestionnaires.find(q => 
                        q.questionnaireId === questionnaire.id
                    );
                    
                    if (existingQuestionnaire) {
                        this.$message.warning(`问卷"${questionnaire.name}"已存在于表单中，跳过添加`);
                        skippedCount++;
                        continue;
                    }
                    
                    const newQuestionnaire = {
                        id: `temp_${Date.now()}_${Math.random()}`, // 临时ID
                        questionnaireId: questionnaire.id,
                        questionnaireName: questionnaire.name,
                        questionnaireType: questionnaire.type,
                        questionCount: questionnaire.questionCount,
                        sortOrder: this.formQuestionnaires.length + 1,
                        isRequired: true,
                        displayConfig: {
                            title: questionnaire.name,
                            description: questionnaire.description || ''
                        }
                    };
                    
                    // 先添加到本地列表
                    this.$set(this.formQuestionnaires, this.formQuestionnaires.length, newQuestionnaire);
                    
                    // 调用API保存到后端
                    const data = {
                        formTemplateId: this.templateData.id,
                        questionnaireId: questionnaire.id,
                        sortOrder: newQuestionnaire.sortOrder,
                        isRequired: newQuestionnaire.isRequired,
                        displayConfig: newQuestionnaire.displayConfig
                    };
                    
                    try {
                        await smartFormApi.addFormQuestionnaire(data);
                        addedCount++;
                    } catch (apiError) {
                        console.error('API调用失败，但本地数据已更新:', apiError);
                        // 如果API失败，从本地列表中移除
                        const index = this.formQuestionnaires.findIndex(q => q.id === newQuestionnaire.id);
                        if (index > -1) {
                            this.formQuestionnaires.splice(index, 1);
                        }
                        
                        // 显示具体的错误信息
                        let errorMessage = '添加问卷失败';
                        if (apiError.response && apiError.response.data && apiError.response.data.message) {
                            errorMessage = apiError.response.data.message;
                        } else if (apiError.message) {
                            errorMessage = apiError.message;
                        }
                        this.$message.error(`问卷"${questionnaire.name}"添加失败: ${errorMessage}`);
                    }
                }
                
                // 强制更新视图
                this.$forceUpdate();
                
                if (addedCount > 0) {
                    this.$message.success(`成功添加 ${addedCount} 个问卷`);
                }
                if (skippedCount > 0) {
                    this.$message.info(`跳过 ${skippedCount} 个重复问卷`);
                }
                
                this.addQuestionnaireDialogVisible = false;
                this.$emit('questionnaire-updated');
            } catch (error) {
                console.error('添加问卷失败:', error);
                // 提供更详细的错误信息
                let errorMessage = '添加问卷失败';
                if (error.response && error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                } else if (error.message) {
                    errorMessage = error.message;
                }
                this.$message.error(errorMessage);
            }
        },
        
        // 处理问卷操作命令
        handleQuestionnaireCommand(command) {
            const { action, questionnaire } = command;
            switch (action) {
                case 'edit':
                    this.editQuestionnaire(questionnaire);
                    break;
                case 'preview':
                    this.previewQuestionnaire(questionnaire);
                    break;
                case 'remove':
                    this.removeQuestionnaire(questionnaire);
                    break;
            }
        },
        
        // 编辑问卷配置
        editQuestionnaire(questionnaire) {
            this.editingQuestionnaire = {
                id: questionnaire.id,
                displayTitle: (questionnaire.displayConfig && questionnaire.displayConfig.title) || questionnaire.questionnaireName,
                displayDescription: (questionnaire.displayConfig && questionnaire.displayConfig.description) || '',
                sortOrder: questionnaire.sortOrder,
                isRequired: questionnaire.isRequired
            };
            this.editQuestionnaireDialogVisible = true;
            // 关闭下拉菜单
            this.activeDropdownId = null;
        },
        
        // 保存问卷配置
        async saveQuestionnaireConfig() {
            try {
                await this.$refs.questionnaireForm.validate();
                
                const data = {
                    id: this.editingQuestionnaire.id,
                    sortOrder: this.editingQuestionnaire.sortOrder,
                    isRequired: this.editingQuestionnaire.isRequired,
                    displayConfig: {
                        title: this.editingQuestionnaire.displayTitle,
                        description: this.editingQuestionnaire.displayDescription
                    }
                };
                
                const response = await smartFormApi.updateFormQuestionnaire(data);
                if (response && response.success) {
                    this.$message.success('问卷配置保存成功');
                    this.editQuestionnaireDialogVisible = false;
                    this.loadFormQuestionnaires();
                } else {
                    this.$message.error(response.message || '保存失败');
                }
            } catch (error) {
                console.error('保存问卷配置失败:', error);
                this.$message.error('保存失败');
            }
        },
        
        // 移除问卷
        removeQuestionnaire(questionnaire) {
            this.$confirm('确定要移除这个问卷吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    const response = await smartFormApi.removeFormQuestionnaire(questionnaire.id);
                    if (response && response.success) {
                        this.$message.success('问卷移除成功');
                        this.loadFormQuestionnaires();
                        this.$emit('questionnaire-updated');
                        // 关闭下拉菜单
                        this.activeDropdownId = null;
                    } else {
                        this.$message.error(response.message || '移除失败');
                    }
                } catch (error) {
                    console.error('移除问卷失败:', error);
                    this.$message.error('移除失败');
                }
            }).catch(() => {
                // 用户取消操作
                this.activeDropdownId = null;
            });
        },
        
        // 切换自定义下拉菜单
        toggleCustomDropdown(id) {
            if (this.activeDropdownId === id) {
                this.activeDropdownId = null;
            } else {
                this.activeDropdownId = id;
            }
        },
        
        // 关闭所有下拉菜单
        closeAllDropdowns() {
            this.activeDropdownId = null;
        },
        
        // 预览问卷
        previewQuestionnaire(questionnaire) {
            this.$message.info('问卷预览功能开发中...');
            // 关闭下拉菜单
            this.activeDropdownId = null;
        },
        
        // 其他数据操作
        addOtherData() {
            this.$message.info('其他数据新增功能开发中...');
        },
        
        editOtherData(row) {
            this.$message.info('其他数据编辑功能开发中...');
        },
        
        deleteOtherData(row) {
            this.$message.info('其他数据删除功能开发中...');
        },
        
        // 编辑知情同意书
        editConsent() {
            this.$message.info('知情同意书设置功能开发中...');
        },
        
        // 保存表单（新增状态下使用）
        async saveForm() {
            if (this.isNewForm()) {
                console.log('保存新增表单，包含问卷关联:', this.formQuestionnaires);
                
                // 准备完整的表单数据
                const formData = {
                    name: this.templateData.name,
                    description: this.templateData.description,
                    category: this.templateData.category,
                    permission: this.templateData.permission,
                    remarkText: this.remarkText,
                    consentConfig: this.consentConfig,
                    otherData: this.otherDataList,
                    questionnaires: this.formQuestionnaires.map(q => ({
                        questionnaireId: q.questionnaireId,
                        questionnaireName: q.questionnaireName || '未知问卷',
                        sortOrder: q.sortOrder,
                        isRequired: q.isRequired,
                        displayConfig: q.displayConfig
                    }))
                };
                
                try {
                    const response = await smartFormApi.createFormTemplate(formData);
                    if (response && response.success) {
                        this.$message.success('表单创建成功！');
                        this.$emit('form-saved', response.data);
                        // 保存成功后关闭对话框
                        this.$emit('close-dialog');
                        return response.data;
                    } else {
                        throw new Error(response.message || '保存失败');
                    }
                } catch (error) {
                    console.error('保存表单失败:', error);
                    this.$message.error(error.message || '保存表单失败');
                    throw error;
                }
            } else {
                console.log('编辑状态：更新现有表单');
                
                // 准备更新数据
                const updateData = {
                    id: this.templateData.id,
                    name: this.templateData.name,
                    description: this.templateData.description,
                    category: this.templateData.category,
                    permission: this.templateData.permission,
                    remarkText: this.remarkText,
                    consentConfig: this.consentConfig,
                    otherData: this.otherDataList,
                    questionnaires: this.formQuestionnaires.map(q => ({
                        questionnaireId: q.questionnaireId,
                        questionnaireName: q.questionnaireName || '未知问卷',
                        sortOrder: q.sortOrder,
                        isRequired: q.isRequired,
                        displayConfig: q.displayConfig
                    }))
                };
                
                try {
                    const response = await smartFormApi.updateFormTemplate(updateData);
                    if (response && response.success) {
                        this.$message.success('表单更新成功！');
                        this.$emit('form-updated', response.data);
                        // 保存成功后关闭对话框
                        this.$emit('close-dialog');
                        return response.data;
                    } else {
                        throw new Error(response.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新表单失败:', error);
                    this.$message.error(error.message || '更新表单失败');
                    throw error;
                }
            }
        },
        
        // 预览表单
        previewForm() {
            this.$message.info('表单预览功能开发中...');
        },
        
        // 分享表单
        shareForm() {
            this.$message.info('表单分享功能开发中...');
        },
        
        // 获取问卷类型名称
        getQuestionnaireTypeName(type) {
            const typeMap = {
                'symptom': '症状评估',
                'scale': '量表评估', 
                'side_effect': '不良反应',
                'custom': '自定义'
            };
            return typeMap[type] || type;
        },
        
        // 保存问卷关联关系
        async saveQuestionnaireRelations() {
            try {
                const validQuestionnaires = this.formQuestionnaires
                    .filter(q => q.questionnaireId && q.questionnaireId > 0);
                
                if (validQuestionnaires.length === 0) {
                    console.log('没有有效的问卷数据需要保存');
                    return;
                }
                
                // 逐个保存问卷关联
                let successCount = 0;
                let skipCount = 0;
                
                for (const questionnaire of validQuestionnaires) {
                    const data = {
                        formTemplateId: this.templateData.id,
                        questionnaireId: questionnaire.questionnaireId,
                        sortOrder: questionnaire.sortOrder || 1,
                        isRequired: questionnaire.isRequired !== undefined ? questionnaire.isRequired : true,
                        displayConfig: questionnaire.displayConfig || {
                            title: questionnaire.questionnaireName || '未知问卷',
                            description: ''
                        }
                    };
                    
                    try {
                        await smartFormApi.addFormQuestionnaire(data);
                        console.log('问卷关联保存成功:', questionnaire.questionnaireName);
                        successCount++;
                    } catch (error) {
                        console.log('问卷关联添加失败:', questionnaire.questionnaireName, error.message);
                        
                        // 如果添加失败且是因为已存在，则静默跳过（不显示错误）
                        if (error.message && error.message.includes('已关联')) {
                            console.log('问卷已存在关联，静默跳过:', questionnaire.questionnaireName);
                            skipCount++;
                        } else {
                            console.error('问卷关联保存失败:', questionnaire.questionnaireName, error);
                        }
                    }
                }
                
                // 根据结果显示不同的消息
                if (successCount > 0 && skipCount === 0) {
                    this.$message.success(`问卷关联保存完成 (${successCount}个)`);
                } else if (successCount > 0 && skipCount > 0) {
                    this.$message.success(`问卷关联保存完成 (新增${successCount}个，跳过${skipCount}个已存在的)`);
                } else if (successCount === 0 && skipCount > 0) {
                    this.$message.success(`问卷关联已存在，无需重复添加 (${skipCount}个)`);
                } else {
                    this.$message.warning('问卷关联保存失败');
                }
            } catch (error) {
                console.error('保存问卷关联失败:', error);
                this.$message.warning('问卷关联保存失败，但基本信息已保存');
            }
        },
        
        // 刷新方法
        refresh() {
            if (!this.isNewForm()) {
                this.loadFormQuestionnaires();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.form-manage-tab {
    padding: 20px;
    
    .form-basic-info {
        margin-bottom: 30px;
        
        .info-row {
            display: flex;
            gap: 20px;
            align-items: center;
            
            .info-item {
                display: flex;
                align-items: center;
                gap: 10px;
                
                .info-label {
                    font-weight: 500;
                    color: #606266;
                    white-space: nowrap;
                }
            }
        }
    }
    
    .section-title {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 500;
    }
    
    .form-collection-section {
        margin-bottom: 30px;
        
        .questionnaire-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            
            .questionnaire-card {
                border: 1px solid #e4e7ed;
                border-radius: 8px;
                padding: 16px;
                background-color: #fff;
                transition: box-shadow 0.3s;
                
                &:hover {
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                }
                
                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                    position: relative;
                    
                    .card-title {
                        font-weight: 500;
                        color: #303133;
                        flex: 1;
                        margin-right: 10px;
                    }
                    
                    .el-dropdown-link {
                        cursor: pointer;
                        color: #909399;
                        padding: 4px 8px;
                        border-radius: 4px;
                        transition: all 0.3s;
                        position: relative;
                        z-index: 10;
                        
                        &:hover {
                            color: #409eff;
                            background-color: #f5f7fa;
                        }
                        
                        i {
                            font-size: 16px;
                        }
                    }
                }
                
                .card-content {
                    .card-description {
                        color: #909399;
                        font-size: 14px;
                        margin: 0 0 10px 0;
                        line-height: 1.4;
                    }
                    
                    .card-meta {
                        display: flex;
                        gap: 15px;
                        
                        .meta-item {
                            font-size: 12px;
                            color: #909399;
                        }
                    }
                }
            }
            
            .add-questionnaire-card {
                border: 2px dashed #d9d9d9;
                border-radius: 8px;
                padding: 40px 16px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s;
                min-height: 120px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                
                &:hover {
                    border-color: #409eff;
                    color: #409eff;
                }
                
                .add-icon {
                    font-size: 36px;
                    color: #c0c4cc;
                    margin-bottom: 10px;
                }
                
                .add-text {
                    color: #909399;
                    font-size: 14px;
                }
                
                &:hover {
                    .add-icon,
                    .add-text {
                        color: #409eff;
                    }
                }
            }
        }
    }
    
    .other-data-section,
    .remark-section,
    .consent-section {
        margin-bottom: 30px;
        
        .other-data-actions {
            margin-top: 10px;
        }
        
        .consent-config {
            display: flex;
            align-items: center;
            gap: 15px;
            
            .consent-text {
                color: #606266;
                flex: 1;
            }
        }
    }
    
    .form-actions {
        text-align: right;
        padding-top: 20px;
        border-top: 1px solid #e4e7ed;
        
        .el-button {
            margin-left: 10px;
        }
    }
    
    .search-section {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    
    .available-questionnaires {
        margin-top: 10px;
    }
}

/deep/ .el-table {
    font-size: 14px;
    
    .el-table__header {
        color: #303133;
        font-weight: 500;
    }
}

/deep/ .el-tag {
    font-size: 12px;
}

::v-deep .el-dropdown-menu {
    z-index: 9999 !important;
}

::v-deep .el-dropdown {
    position: relative;
}

::v-deep .questionnaire-card .el-dropdown {
    position: relative !important;
}

::v-deep .questionnaire-card .el-dropdown .el-dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    z-index: 9999 !important;
    margin-top: 5px !important;
    min-width: 120px !important;
    transform: none !important;
}

::v-deep .el-popper {
    position: absolute !important;
    transform: none !important;
}

/* 强制覆盖所有下拉菜单定位 */
::v-deep .el-dropdown-menu.el-popper {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    transform: none !important;
    margin-top: 5px !important;
}

::v-deep .el-dropdown-menu.el-popper[x-placement^="bottom"] {
    top: 100% !important;
    transform: none !important;
}

::v-deep .el-dropdown-menu.el-popper[x-placement^="bottom-end"] {
    top: 100% !important;
    right: 0 !important;
    transform: none !important;
}

/* 自定义下拉菜单样式 */
.custom-dropdown {
    position: relative;
    display: inline-block;
}

.custom-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 9999;
    min-width: 120px;
    margin-top: 5px;
}

.custom-dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    cursor: pointer;
    color: #606266;
    transition: all 0.3s;
}

.custom-dropdown-item:hover {
    background-color: #f5f7fa;
    color: #409eff;
}

.custom-dropdown-item:first-child {
    border-radius: 4px 4px 0 0;
}

.custom-dropdown-item:last-child {
    border-radius: 0 0 4px 4px;
}

::v-deep .questionnaire-card .el-dropdown .el-dropdown-menu .el-dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
}

::v-deep .questionnaire-card .el-dropdown .el-dropdown-menu .el-dropdown-item:hover {
    background-color: #f5f7fa;
    color: #409eff;
}
</style> 
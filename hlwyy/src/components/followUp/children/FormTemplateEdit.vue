<template>
    <div class="form-template-edit">
        <el-form :model="formData" :rules="formRules" ref="templateForm" label-width="100px">
            <!-- 模板名称 -->
            <el-form-item label="模板名称" prop="name">
                <el-input 
                    v-model="formData.name" 
                    placeholder="请输入模板名称"
                    maxlength="50"
                    show-word-limit>
                </el-input>
            </el-form-item>
            
            <!-- 说明 -->
            <el-form-item label="说明" prop="description">
                <el-input 
                    v-model="formData.description" 
                    type="textarea" 
                    :rows="4"
                    placeholder="请输入说明"
                    maxlength="1000"
                    show-word-limit>
                </el-input>
            </el-form-item>
            
            <!-- 分类 -->
            <el-form-item label="分类" prop="category">
                <el-select v-model="formData.category" placeholder="请选择分类" style="width: 100%">
                    <el-option v-for="category in categoryOptions" :key="category.value" :label="category.label" :value="category.value"></el-option>
                </el-select>
            </el-form-item>
            
            <!-- 模板权限 -->
            <el-form-item label="模板权限" prop="permission">
                <el-select v-model="formData.permission" placeholder="请选择权限" style="width: 100%">
                    <el-option label="仅本人查看" value="self"></el-option>
                    <el-option label="本科室查看" value="department"></el-option>
                    <el-option label="全院查看" value="all"></el-option>
                </el-select>
            </el-form-item>
            
            <!-- 表单类型（编辑时显示） -->
            <el-form-item label="表单类型" prop="formType" v-if="isEditing">
                <el-radio-group v-model="formData.formType" @change="handleFormTypeChange">
                    <el-radio label="one_time">一次性问卷</el-radio>
                    <el-radio label="repeatable">重复性问卷</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 重复配置（仅编辑时且为重复性问卷） -->
            <div v-if="isEditing && formData.formType === 'repeatable'" class="repeat-config-section">
                <el-form-item label="重复间隔" prop="repeatConfig.interval">
                    <el-select v-model="formData.repeatConfig.interval" placeholder="请选择重复间隔" style="width: 200px">
                        <el-option label="每日" value="daily"></el-option>
                        <el-option label="每周" value="weekly"></el-option>
                        <el-option label="每月" value="monthly"></el-option>
                        <el-option label="自定义" value="custom"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="持续时长">
                    <el-input-number 
                        v-model="formData.repeatConfig.duration" 
                        :min="1" 
                        :max="365"
                        style="width: 150px">
                    </el-input-number>
                    <el-select v-model="formData.repeatConfig.unit" style="width: 100px; margin-left: 10px;">
                        <el-option label="天" value="day"></el-option>
                        <el-option label="周" value="week"></el-option>
                        <el-option label="月" value="month"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="提醒设置">
                    <el-checkbox v-model="formData.repeatConfig.reminderEnabled">启用填写提醒</el-checkbox>
                    <el-time-picker
                        v-if="formData.repeatConfig.reminderEnabled"
                        v-model="formData.repeatConfig.reminderTime"
                        placeholder="选择提醒时间"
                        format="HH:mm"
                        value-format="HH:mm"
                        style="margin-left: 20px;">
                    </el-time-picker>
                </el-form-item>
            </div>

            <!-- 表单配置（仅编辑时显示） -->
            <div v-if="isEditing" class="form-config-section">
                <el-form-item label="显示设置">
                    <el-checkbox v-model="formData.config.showProgress">显示进度条</el-checkbox>
                    <el-checkbox v-model="formData.config.allowEdit" style="margin-left: 20px;">允许修改答案</el-checkbox>
                    <el-checkbox v-model="formData.config.autoSave" style="margin-left: 20px;">自动保存草稿</el-checkbox>
                </el-form-item>
                
                <el-form-item label="界面主题">
                    <el-select v-model="formData.config.theme" placeholder="请选择界面主题" style="width: 200px">
                        <el-option label="默认主题" value="default"></el-option>
                        <el-option label="蓝色主题" value="blue"></el-option>
                        <el-option label="绿色主题" value="green"></el-option>
                        <el-option label="紫色主题" value="purple"></el-option>
                    </el-select>
                </el-form-item>
                
                <el-form-item label="提交按钮文本">
                    <el-input 
                        v-model="formData.config.submitBtnText" 
                        placeholder="自定义提交按钮文本"
                        style="width: 200px">
                    </el-input>
                </el-form-item>
            </div>
        </el-form>
        
        <!-- 操作按钮 -->
        <div class="form-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSave" :loading="saving">{{ isEditing ? '保存' : '新增' }}</el-button>
        </div>
    </div>
</template>

<script>
import { smartFormApi } from '@/api/smartForm.js'

export default {
    name: 'FormTemplateEdit',
    props: {
        templateData: {
            type: Object,
            default: null
        },
        isEditing: {
            type: Boolean,
            default: false
        },
        questionnaires: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            saving: false,
            formData: {
                name: '',
                description: '',
                formType: 'one_time',
                category: '',
                permission: 'self',
                config: {
                    showProgress: true,
                    allowEdit: true,
                    autoSave: true,
                    theme: 'default',
                    submitBtnText: '提交表单'
                },
                repeatConfig: {
                    interval: 'weekly',
                    duration: 12,
                    unit: 'week',
                    reminderEnabled: false,
                    reminderTime: '09:00'
                }
            },
            formRules: {
                name: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
                ],
                description: [
                    { required: true, message: '请输入表单描述', trigger: 'blur' },
                    { max: 1000, message: '表单描述不能超过1000个字符', trigger: 'blur' }
                ],
                category: [
                    { required: true, message: '请选择分类', trigger: 'change' }
                ],
                permission: [
                    { required: true, message: '请选择权限', trigger: 'change' }
                ]
            },
            categoryOptions: [
                { label: '术后随访', value: 'postoperative_followup' },
                { label: '慢病管理', value: 'chronic_disease' },
                { label: '健康评估', value: 'health_assessment' },
                { label: '满意度调查', value: 'satisfaction_survey' },
                { label: '症状监测', value: 'symptom_monitoring' },
                { label: '康复评估', value: 'rehabilitation_assessment' },
                { label: '其他', value: 'other' }
            ]
        }
    },
    watch: {
        templateData: {
            handler(newVal) {
                if (newVal) {
                    this.initFormData(newVal);
                } else {
                    this.resetFormData();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 初始化表单数据
        initFormData(templateData) {
            this.formData = {
                id: templateData.id,
                name: templateData.name || '',
                description: templateData.description || '',
                formType: templateData.formType || 'one_time',
                category: templateData.category || '',
                permission: templateData.permission || 'self',
                config: {
                    showProgress: (templateData.config && templateData.config.showProgress) !== false,
                    allowEdit: (templateData.config && templateData.config.allowEdit) !== false,
                    autoSave: (templateData.config && templateData.config.autoSave) !== false,
                    theme: (templateData.config && templateData.config.theme) || 'default',
                    submitBtnText: (templateData.config && templateData.config.submitBtnText) || '提交表单'
                },
                repeatConfig: {
                    interval: (templateData.repeatConfig && templateData.repeatConfig.interval) || 'weekly',
                    duration: (templateData.repeatConfig && templateData.repeatConfig.duration) || 12,
                    unit: (templateData.repeatConfig && templateData.repeatConfig.unit) || 'week',
                    reminderEnabled: (templateData.repeatConfig && templateData.repeatConfig.reminderEnabled) || false,
                    reminderTime: (templateData.repeatConfig && templateData.repeatConfig.reminderTime) || '09:00'
                }
            };
        },
        
        // 重置表单数据
        resetFormData() {
            this.formData = {
                name: '',
                description: '',
                formType: 'one_time',
                category: '',
                permission: 'self',
                config: {
                    showProgress: true,
                    allowEdit: true,
                    autoSave: true,
                    theme: 'default',
                    submitBtnText: '提交表单'
                },
                repeatConfig: {
                    interval: 'weekly',
                    duration: 12,
                    unit: 'week',
                    reminderEnabled: false,
                    reminderTime: '09:00'
                }
            };
        },
        
        // 表单类型改变
        handleFormTypeChange(value) {
            if (value === 'one_time') {
                // 一次性问卷时清空重复配置
                this.formData.repeatConfig = {
                    interval: 'weekly',
                    duration: 12,
                    unit: 'week',
                    reminderEnabled: false,
                    reminderTime: '09:00'
                };
            }
        },
        
        // 保存表单
        async handleSave() {
            try {
                await this.$refs.templateForm.validate();
            } catch (error) {
                return;
            }
            
            this.saving = true;
            try {
                // 准备提交数据
                let submitData;
                if (this.isEditing) {
                    // 编辑时提交完整数据
                    submitData = {
                        ...this.formData,
                        config: this.formData.config,
                        repeatConfig: this.formData.formType === 'repeatable' ? this.formData.repeatConfig : null,
                        questionnaires: this.questionnaires // 包含问卷关联数据
                    };
                } else {
                    // 新增时只提交基本信息
                    submitData = {
                        name: this.formData.name,
                        description: this.formData.description,
                        category: this.formData.category,
                        permission: this.formData.permission
                    };
                }
                
                // 调用后端接口保存表单模板
                let response;
                if (this.isEditing) {
                    response = await smartFormApi.updateFormTemplate(submitData);
                } else {
                    response = await smartFormApi.createFormTemplate(submitData);
                }
                
                if (!response || !response.success) {
                    throw new Error((response && response.message) || '保存失败');
                }
                
                // 如果是新增操作，保存基本信息后跳转到表单管理页面进行详细编辑
                if (!this.isEditing) {
                    this.$message.success('基本信息保存成功，即将跳转到表单编辑页面');
                    this.$emit('save', response.data);
                    // 延迟关闭弹窗，让用户看到成功消息
                    setTimeout(() => {
                        this.$emit('save-and-edit', response.data);
                    }, 1000);
                } else {
                    // 编辑操作直接保存
                    this.$message.success('表单模板更新成功');
                    this.$emit('save', response.data);
                }
                
            } catch (error) {
                console.error('保存表单模板失败:', error);
                this.$message.error(error.message || '保存失败');
            } finally {
                this.saving = false;
            }
        },
        
        // 取消
        handleCancel() {
            this.$emit('cancel');
        }
    }
}
</script>

<style lang="scss" scoped>
.form-template-edit {
    .repeat-config-section,
    .form-config-section {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e4e7ed;
    }
    
    .form-actions {
        text-align: right;
        padding-top: 20px;
        border-top: 1px solid #e4e7ed;
        margin-top: 20px;
        
        .el-button {
            margin-left: 10px;
        }
    }
}

/deep/ .el-form-item {
    margin-bottom: 18px;
}

/deep/ .el-form-item__label {
    color: #606266;
    font-weight: 500;
}

/deep/ .el-checkbox {
    margin-right: 0;
}

/deep/ .el-select {
    .el-input__inner {
        height: 36px;
        line-height: 36px;
    }
}

/deep/ .el-input-number {
    .el-input__inner {
        text-align: left;
    }
}
</style> 
<template>
    <div class="smart-follow-up-edit">
        <!-- 顶部标题和操作按钮 -->
        <div class="header-section">
            <div class="header-left">
                <el-button type="text" @click="goBack">
                    <i class="el-icon-arrow-left"></i> 疼痛互联网随访-tom测试
                </el-button>
            </div>
            <div class="header-right">
                <el-button>存为新模板</el-button>
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 测试按钮区域 -->
            <div style="background: #f0f9ff; padding: 15px; margin-bottom: 20px; border-radius: 4px; border: 1px solid #409eff;">
                <h4 style="margin: 0 0 10px 0; color: #409eff;">调试工具</h4>
                <el-button type="warning" size="small" @click="testAddQuestionnaire" style="margin-right: 10px;">测试添加问卷</el-button>
                <el-button type="info" size="small" @click="testLogData" style="margin-right: 10px;">测试日志数据</el-button>
                <el-button type="success" size="small" @click="testOpenDialog">测试打开对话框</el-button>
            </div>
            
            <!-- 基本信息表单 -->
            <div class="form-section">
                <el-form :model="form" :rules="rules" ref="templateForm" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="计划名称" prop="planName">
                                <el-input v-model="form.planName" placeholder="疼痛互联网随访-tom测试"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="治疗时间" prop="treatmentTime">
                                <el-input v-model="form.treatmentTime" placeholder="14">
                                    <template slot="append">
                                        <el-select v-model="form.timeUnit" style="width: 60px">
                                            <el-option label="天" value="day"></el-option>
                                            <el-option label="月" value="month"></el-option>
                                            <el-option label="年" value="year"></el-option>
                                        </el-select>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="长期">
                                <!-- 空列用于布局 -->
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="计划状态" prop="planStatus">
                                <el-tag type="warning">已停用</el-tag>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <!-- 添加子计划按钮 -->
            <div class="add-subplan-section">
                <!-- 子计划标签页 -->
                <el-tabs v-model="activeSubPlan" type="border-card" @tab-click="handleTabClick">
                    <el-tab-pane v-for="(subplan, index) in form.subPlans" :key="index" :label="subplan.name" :name="index.toString()">
                        <!-- 设置区域 -->
                        <div class="subplan-settings">
                            <h3>设置</h3>
                            <el-form :model="subplan.settings" label-width="100px">
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="类型">
                                            <el-radio-group v-model="subplan.settings.type">
                                                <el-radio label="patient">患者填写</el-radio>
                                                <el-radio label="medical">仅医护填写</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="执行时间">
                                            <el-radio-group v-model="subplan.settings.executionTime">
                                                <el-radio label="allTime">全部时段</el-radio>
                                                <el-radio label="custom">自定义</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="16">
                                        <el-form-item label="加入计划后第">
                                            <div class="time-range-input">
                                                <el-input v-model="subplan.settings.startDay" style="width: 80px"></el-input>
                                                <span style="margin: 0 8px;">至</span>
                                                <el-input v-model="subplan.settings.endDay" style="width: 80px"></el-input>
                                                <span style="margin-left: 8px;">天</span>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="提醒时间">
                                            <el-time-picker 
                                                v-model="subplan.settings.reminderTime" 
                                                format="HH:mm"
                                                placeholder="选择时间"
                                                style="width: 120px">
                                            </el-time-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="计划类型">
                                            <el-radio-group v-model="subplan.settings.planType">
                                                <el-radio label="cycle">周期循环</el-radio>
                                                <el-radio label="custom">自定义</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>

                            <!-- 子计划详情表格 -->
                            <div class="subplan-details">
                                <!-- 周期循环模式 -->
                                <el-table v-if="subplan.settings.planType === 'cycle'" :data="subplan.details" border style="width: 100%">
                                    <el-table-column prop="sequence" label="序号" width="80" align="center"></el-table-column>
                                    <el-table-column prop="name" label="名称" min-width="200"></el-table-column>
                                    <el-table-column prop="cycleDays" label="周期天数" width="120"></el-table-column>
                                    <el-table-column prop="event" label="事件" width="120"></el-table-column>
                                    <el-table-column label="操作" width="100">
                                        <template slot-scope="scope">
                                            <el-button type="text" @click="addEvent(scope.row, scope.$index)">添加事件</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                
                                <!-- 自定义模式 -->
                                <div v-if="subplan.settings.planType === 'custom'" class="custom-plan-table">
                                    <el-table :data="subplan.customDetails" border style="width: 100%">
                                        <el-table-column prop="sequence" label="序号" width="80" align="center">
                                            <template slot-scope="scope">
                                                {{ scope.$index + 1 }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="名称" min-width="200">
                                            <template slot-scope="scope">
                                                <el-input 
                                                    v-model="scope.row.name" 
                                                    placeholder="请输入内容"
                                                    :class="{ 'error-input': !scope.row.name }"
                                                    @blur="validateCustomItem(scope.row)">
                                                </el-input>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="加入计划第几天" width="150">
                                            <template slot-scope="scope">
                                                <div class="day-input-wrapper">
                                                    <el-input 
                                                        v-model="scope.row.day" 
                                                        placeholder=""
                                                        type="number"
                                                        :class="{ 'error-input': !scope.row.day }"
                                                        @blur="validateCustomItem(scope.row)"
                                                        style="width: 80px;">
                                                    </el-input>
                                                    <span style="margin-left: 8px;">天</span>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="事件" width="120">
                                            <template slot-scope="scope">
                                                <el-button type="text" @click="addCustomEvent(scope.row, scope.$index)">添加事件</el-button>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作" width="100">
                                            <template slot-scope="scope">
                                                <el-button type="text" class="danger-text" @click="removeCustomItem(index, scope.$index)">删除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    
                                    <!-- 添加计划按钮 -->
                                    <div class="add-custom-plan">
                                        <el-button type="text" @click="addCustomPlanItem(index)" class="add-plan-btn">
                                            <i class="el-icon-plus"></i> 添加计划
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-tab-pane>
                    
                    <!-- 新增子计划标签 -->
                    <el-tab-pane label="+ 新增子计划" name="add">
                        <div class="add-new-subplan">
                            <el-button type="primary" @click="createNewSubPlan">
                                <i class="el-icon-plus"></i> 创建新的子计划
                            </el-button>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <!-- 表单收集区域 -->
            <div class="data-collection-section">
                <h3>表单收集 ({{ currentSubplanQuestionnaires.length }}个问卷)</h3>
                <div class="collection-cards">
                    <!-- 显示当前子计划的问卷 -->
                    <div 
                        v-for="(questionnaire, index) in currentSubplanQuestionnaires" 
                        :key="`questionnaire-${questionnaire.id || questionnaire.questionnaireId}-${index}`"
                        class="collection-card">
                        <div class="card-header">
                            <h4>{{ questionnaire.questionnaireName || questionnaire.name }}</h4>
                            <span class="card-subtitle">已关联到子计划</span>
                        </div>
                        <div class="card-content">
                            <div class="questionnaire-meta">
                                <span>题目数：{{ questionnaire.questionCount || 0 }}</span>
                                <span>排序：{{ questionnaire.sortOrder }}</span>
                                <span>{{ questionnaire.isRequired ? '必填' : '选填' }}</span>
                            </div>
                            <el-button type="text" @click="removeQuestionnaire(index)" class="danger-text">移除</el-button>
                        </div>
                    </div>
                    
                    <!-- 添加问卷卡片 -->
                    <div class="collection-card add-card" @click="addNewTemplate">
                        <div class="add-icon">
                            <i class="el-icon-plus"></i>
                        </div>
                        <div class="add-text">添加问卷</div>
                    </div>
                </div>
                
                
            </div>

            <!-- 检验检查区域 -->
            <div class="examination-section">
                <h3>检验检查</h3>
                <el-table :data="form.examinations" border style="width: 100%">
                    <el-table-column prop="sequence" label="序号" width="80" align="center"></el-table-column>
                    <el-table-column prop="type" label="类型" width="120"></el-table-column>
                    <el-table-column prop="project" label="项目" min-width="200"></el-table-column>
                    <el-table-column prop="customName" label="自定义名称" min-width="150"></el-table-column>
                    <el-table-column prop="executionTime" label="执行时间" width="120"></el-table-column>
                    <el-table-column label="操作" width="100">
                        <template>
                            <el-button type="text">操作</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add-examination">
                    <el-button type="text" @click="addExamination">
                        <i class="el-icon-plus"></i> 新增
                    </el-button>
                </div>
            </div>

            <!-- 重点指标区域 -->
            <div class="key-indicators-section">
                <h3>重点指标</h3>
                <div class="add-indicator">
                    <el-button type="text" @click="addKeyIndicator">
                        <i class="el-icon-plus"></i> 添加新血压监测指标
                    </el-button>
                </div>
            </div>

            <!-- 其他数据区域 -->
            <div class="other-data-section">
                <h3>其他数据</h3>
                <el-table :data="form.otherData" border style="width: 100%">
                    <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                    <el-table-column prop="type" label="类型" width="120">
                        <template slot-scope="scope">
                            <span>{{ getOtherDataTypeText(scope.row.type) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isRequired" label="是否必填" width="120" align="center">
                        <template slot-scope="scope">
                            <el-tag :type="scope.row.isRequired ? 'success' : 'info'">
                                {{ scope.row.isRequired ? '必填' : '选填' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="editOtherData(scope.row, scope.$index)">编辑</el-button>
                            <el-button type="text" size="small" style="color: #f56c6c;" @click="deleteOtherData(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="add-other-data">
                    <el-button type="text" @click="addOtherData">
                        <i class="el-icon-plus"></i> 新增
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 其他数据编辑弹窗 -->
        <el-dialog 
            :title="otherDataDialogTitle" 
            :visible.sync="otherDataDialogVisible" 
            width="500px"
            :close-on-click-modal="false">
            <el-form :model="currentOtherData" :rules="otherDataRules" ref="otherDataForm" label-width="100px">
                <el-form-item label="类型" prop="type">
                    <el-select v-model="currentOtherData.type" placeholder="请选择类型" style="width: 100%;">
                        <el-option label="血压" value="bloodPressure"></el-option>
                        <el-option label="身高体重" value="heightWeight"></el-option>
                        <el-option label="血糖" value="bloodSugar"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否必填" prop="isRequired">
                    <el-switch v-model="currentOtherData.isRequired" active-text="必填" inactive-text="选填"></el-switch>
                </el-form-item>
            </el-form>
            
            <div slot="footer">
                <el-button @click="otherDataDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="saveOtherData">保存</el-button>
            </div>
        </el-dialog>

        <!-- 问卷选择对话框 -->
        <el-dialog 
            title="选择问卷" 
            :visible.sync="questionnaireDialogVisible" 
            width="800px"
            @close="handleQuestionnaireDialogClose"
            :close-on-click-modal="false"
            :close-on-press-escape="true">
            <div class="questionnaire-search">
                <el-input
                    v-model="questionnaireSearchKeyword"
                    placeholder="搜索问卷名称"
                    style="width: 300px;"
                    clearable
                    @input="handleQuestionnaireSearch">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
            </div>
            
            <!-- 问卷列表 -->
            <div class="questionnaire-list" v-loading="questionnaireLoading">
                <el-table 
                    :data="filteredQuestionnaires" 
                    border 
                    style="width: 100%; margin-top: 20px;"
                    max-height="400px"
                    @selection-change="handleQuestionnaireSelectionChange"
                    ref="questionnaireTable">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="name" label="问卷名称" min-width="200"></el-table-column>
                    <el-table-column prop="description" label="问卷描述" min-width="250"></el-table-column>
                    <el-table-column prop="questionsCount" label="题目数量" width="100" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.questionsCount || 0 }}题</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createdTime" label="创建时间" width="120">
                        <template slot-scope="scope">
                            <span>{{ formatDate(scope.row.createdTime) }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            
            <div slot="footer">
                <el-button @click="handleQuestionnaireDialogClose">取消</el-button>
                <el-button type="primary" @click="() => { console.log('按钮被点击了！'); addSelectedQuestionnaires(); }" :disabled="selectedQuestionnaires.length === 0">
                    添加问卷 ({{ selectedQuestionnaires.length }})
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import questionnaireApi from '@/api/questionnaire.js'

export default {
    name: 'SmartFollowUpEdit',
    props: {
        templateData: {
            type: Object,
            default: () => ({})
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            form: {
                planName: '疼痛互联网随访-tom测试',
                treatmentTime: '14',
                timeUnit: 'day',
                planStatus: 'disabled',
                planDetails: [
                    {
                        sequence: 1,
                        name: '第1个月 (每天随访)',
                        executionTime: '3',
                        executionDay: '天',
                        eventType: '添加事件'
                    }
                ],
                examinations: [],
                otherData: [],
                subPlans: [
                    {
                        name: '子计划1',
                        settings: {
                            type: 'patient',
                            executionTime: 'allTime',
                            startDay: '3',
                            endDay: '29',
                            reminderTime: '18:55',
                            planType: 'custom'
                        },
                        details: [
                            { sequence: 1, name: '第1个月 (每天随访)', cycleDays: '3天', event: '添加事件' }
                        ],
                        customDetails: [
                            { name: '', day: '', events: [] },
                            { name: '', day: '', events: [] }
                        ],
                        questionnaires: []
                    },
                    {
                        name: '子计划2',
                        settings: {
                            type: 'patient',
                            executionTime: 'allTime',
                            startDay: '30',
                            endDay: '60',
                            reminderTime: '18:55',
                            planType: 'cycle'
                        },
                        details: [
                            { sequence: 1, name: '第2个月 (每周随访)', cycleDays: '7天', event: '添加事件' }
                        ],
                        customDetails: [],
                        questionnaires: []
                    }
                ]
            },
            rules: {
                planName: [
                    { required: true, message: '请输入计划名称', trigger: 'blur' }
                ]
            },
            activeSubPlan: '0',
            // 其他数据相关
            otherDataDialogVisible: false,
            isEditingOtherData: false,
            editingOtherDataIndex: -1,
            currentOtherData: {
                type: '',
                isRequired: false
            },
            otherDataRules: {
                type: [
                    { required: true, message: '请选择类型', trigger: 'change' }
                ]
            },
            // 问卷选择相关
            questionnaireDialogVisible: false,
            questionnaireLoading: false,
            questionnaireSearchKeyword: '',
            allQuestionnaires: [],
            selectedQuestionnaires: []
        }
    },
    mounted() {
        // 初始化时加载问卷列表
        this.loadQuestionnaires();
    },
    computed: {
        otherDataDialogTitle() {
            return this.isEditingOtherData ? '编辑其他数据' : '新增其他数据';
        },
        filteredQuestionnaires() {
            console.log('=== filteredQuestionnaires 计算属性执行 ===');
            console.log('搜索关键词:', this.questionnaireSearchKeyword);
            console.log('所有问卷数量:', this.allQuestionnaires.length);
            console.log('所有问卷:', this.allQuestionnaires);
            
            if (!this.questionnaireSearchKeyword) {
                console.log('无搜索关键词，返回所有问卷');
                return this.allQuestionnaires;
            }
            const filtered = this.allQuestionnaires.filter(questionnaire => 
                questionnaire.name.toLowerCase().includes(this.questionnaireSearchKeyword.toLowerCase())
            );
            console.log('过滤后的问卷数量:', filtered.length);
            console.log('过滤后的问卷:', filtered);
            return filtered;
        },
        currentSubplanQuestionnaires() {
            const currentSubplanIndex = parseInt(this.activeSubPlan);
            const currentSubplan = this.form.subPlans[currentSubplanIndex];
            console.log('=== 计算属性执行 ===');
            console.log('当前子计划索引:', currentSubplanIndex);
            console.log('当前子计划:', currentSubplan);
            if (currentSubplan && currentSubplan.questionnaires) {
                console.log('问卷数组存在，数量:', currentSubplan.questionnaires.length);
                console.log('问卷数组内容:', currentSubplan.questionnaires);
                // 返回数组的副本以确保响应性
                const result = [...currentSubplan.questionnaires];
                console.log('返回的数组副本:', result);
                return result;
            }
            console.log('问卷数组不存在或为空，返回空数组');
            return [];
        }
    },
    watch: {
        templateData: {
            handler(newVal) {
                if (newVal && Object.keys(newVal).length > 0) {
                    // 如果是编辑模式，加载模板数据
                    this.loadTemplateData(newVal);
                }
            },
            immediate: true
        },
        'form.subPlans': {
            handler(newVal, oldVal) {
                console.log('子计划数据变化:', newVal);
                console.log('当前活动子计划索引:', this.activeSubPlan);
                if (newVal && newVal.length > 0) {
                    const currentIndex = parseInt(this.activeSubPlan);
                    if (newVal[currentIndex]) {
                        console.log('当前子计划问卷数量:', newVal[currentIndex].questionnaires ? newVal[currentIndex].questionnaires.length : 0);
                    }
                }
            },
            deep: true
        },
        // 添加对当前子计划问卷数组的监听
        'form.subPlans': {
            handler(newVal) {
                const currentIndex = parseInt(this.activeSubPlan);
                if (newVal && newVal[currentIndex] && newVal[currentIndex].questionnaires) {
                    console.log('当前子计划问卷数组变化:', newVal[currentIndex].questionnaires);
                    // 强制更新视图
                    this.$nextTick(() => {
                        this.$forceUpdate();
                    });
                }
            },
            deep: true
        }
    },
    methods: {
        goBack() {
            this.$router.go(-1);
        },
        handleTabClick(tab) {
            console.log('子计划标签页点击', tab.name);
        },
        createNewSubPlan() {
            const newSubPlan = {
                name: `子计划${this.form.subPlans.length + 1}`,
                settings: {
                    type: 'patient',
                    executionTime: 'allTime',
                    startDay: '1',
                    endDay: '30',
                    reminderTime: '18:55',
                    planType: 'cycle'
                },
                details: [
                    { sequence: 1, name: '新子计划事件', cycleDays: '3天', event: '添加事件' }
                ],
                customDetails: [],
                questionnaires: []
            };
            this.form.subPlans.push(newSubPlan);
            this.activeSubPlan = (this.form.subPlans.length - 1).toString();
        },
        addEvent(row, index) {
            console.log('添加事件', row, index);
        },
        addCustomEvent(row, index) {
            console.log('添加自定义事件', row, index);
        },
        addCustomPlanItem(subplanIndex) {
            this.form.subPlans[subplanIndex].customDetails.push({
                name: '',
                day: '',
                events: []
            });
        },
        removeCustomItem(subplanIndex, itemIndex) {
            this.form.subPlans[subplanIndex].customDetails.splice(itemIndex, 1);
        },
        validateCustomItem(item) {
            // 验证自定义计划项
            if (!item.name || !item.day) {
                console.log('验证失败', item);
            }
        },
        async addNewTemplate() {
            console.log('=== 打开问卷选择对话框 ===');
            // 打开问卷选择弹窗
            this.questionnaireDialogVisible = true;
            console.log('对话框状态:', this.questionnaireDialogVisible);
            // 加载问卷列表
            await this.loadQuestionnaires();
            console.log('问卷列表加载完成，总数:', this.allQuestionnaires.length);
        },
        // 加载问卷列表 - 完全按照QuestionnaireTemplate.vue的方式实现
        async loadQuestionnaires() {
            console.log('=== 开始加载问卷列表 ===');
            this.questionnaireLoading = true;
            try {
                const response = await questionnaireApi.getAllQuestionnaires();
                console.log('API响应:', response);
                this.allQuestionnaires = response || [];
                console.log('设置问卷列表，数量:', this.allQuestionnaires.length);
            } catch (error) {
                console.error('加载问卷列表失败:', error);
                this.$message.error('加载问卷列表失败');
                this.allQuestionnaires = [];
            } finally {
                this.questionnaireLoading = false;
                console.log('问卷加载完成，loading状态:', this.questionnaireLoading);
            }
        },
        handleQuestionnaireSearch() {
            // 搜索功能通过computed属性filteredQuestionnaires实现
        },
        handleQuestionnaireSelectionChange(selection) {
            console.log('=== 问卷选择变化 ===');
            console.log('选中的问卷数量:', selection.length);
            console.log('选中的问卷:', selection);
            this.selectedQuestionnaires = selection;
            console.log('更新后的selectedQuestionnaires:', this.selectedQuestionnaires);
        },
        addSelectedQuestionnaires() {
            console.log('=== addSelectedQuestionnaires 方法被调用 ===');
            console.log('当前时间:', new Date().toLocaleTimeString());
            
            if (this.selectedQuestionnaires.length === 0) {
                console.log('没有选中的问卷，显示警告');
                this.$message.warning('请先选择问卷');
                return;
            }
            
            console.log('=== 开始添加问卷 ===');
            console.log('选中的问卷:', this.selectedQuestionnaires);
            
            // 获取当前活动的子计划
            const currentSubplanIndex = parseInt(this.activeSubPlan);
            console.log('当前活动子计划索引:', currentSubplanIndex);
            console.log('子计划总数:', this.form.subPlans.length);
            
            const currentSubplan = this.form.subPlans[currentSubplanIndex];
            
            if (!currentSubplan) {
                this.$message.error('未找到当前子计划');
                return;
            }
            
            console.log('当前子计划:', currentSubplan);
            console.log('当前子计划问卷数组:', currentSubplan.questionnaires);
            
            // 初始化questionnaires数组（如果不存在）
            if (!currentSubplan.questionnaires) {
                console.log('初始化questionnaires数组');
                this.$set(currentSubplan, 'questionnaires', []);
            }
            
            let addedCount = 0;
            let skippedCount = 0;
            
            // 添加选中的问卷到当前子计划
            this.selectedQuestionnaires.forEach((questionnaire, index) => {
                console.log(`处理第${index + 1}个问卷:`, questionnaire);
                
                // 检查是否已存在相同的问卷
                const existingQuestionnaire = currentSubplan.questionnaires.find(q => 
                    q.questionnaireId === questionnaire.id
                );
                
                if (existingQuestionnaire) {
                    console.log(`问卷"${questionnaire.name}"已存在，跳过`);
                    this.$message.warning(`问卷"${questionnaire.name}"已存在于子计划中，跳过添加`);
                    skippedCount++;
                    return;
                }
                
                const newQuestionnaire = {
                    id: `temp_${Date.now()}_${Math.random()}`, // 临时ID
                    questionnaireId: questionnaire.id,
                    questionnaireName: questionnaire.name,
                    questionnaireType: questionnaire.type,
                    questionCount: questionnaire.questionCount || 0,
                    questionnaireDescription: questionnaire.description || '',
                    sortOrder: currentSubplan.questionnaires.length + 1,
                    isRequired: true
                };
                
                console.log('准备添加的问卷对象:', newQuestionnaire);
                
                // 使用Vue.set确保响应性，参考智能表单的实现
                this.$set(currentSubplan.questionnaires, currentSubplan.questionnaires.length, newQuestionnaire);
                addedCount++;
                
                console.log(`问卷"${questionnaire.name}"添加成功，当前数组长度:`, currentSubplan.questionnaires.length);
            });
            
            console.log('=== 问卷添加完成 ===');
            console.log('添加数量:', addedCount);
            console.log('跳过数量:', skippedCount);
            console.log('最终问卷数组:', currentSubplan.questionnaires);
            
            // 强制更新视图
            this.$forceUpdate();
            
            // 使用nextTick确保DOM更新
            this.$nextTick(() => {
                console.log('DOM更新后，计算属性返回的问卷数量:', this.currentSubplanQuestionnaires.length);
                // 强制重新计算计算属性
                this.$forceUpdate();
                
                // 再次强制更新，确保视图同步
                this.$nextTick(() => {
                    console.log('二次更新后，计算属性返回的问卷数量:', this.currentSubplanQuestionnaires.length);
                    // 触发子组件更新
                    this.$emit('questionnaire-updated');
                });
            });
            
            this.$message.success(`成功添加 ${addedCount} 个问卷到子计划"${currentSubplan.name}"`);
            if (skippedCount > 0) {
                this.$message.info(`跳过 ${skippedCount} 个重复问卷`);
            }
            
            // 关闭对话框并清空选择
            this.questionnaireDialogVisible = false;
            this.selectedQuestionnaires = [];
            
            // 触发数据更新事件
            this.$emit('questionnaire-updated');
        },
        
        // 移除问卷
        removeQuestionnaire(index) {
            const currentSubplanIndex = parseInt(this.activeSubPlan);
            const currentSubplan = this.form.subPlans[currentSubplanIndex];
            
            if (!currentSubplan || !currentSubplan.questionnaires) {
                this.$message.error('未找到问卷数据');
                return;
            }
            
            const questionnaire = currentSubplan.questionnaires[index];
            const questionnaireName = questionnaire.questionnaireName || questionnaire.name;
            
            this.$confirm(`确定要移除问卷"${questionnaireName}"吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                currentSubplan.questionnaires.splice(index, 1);
                this.$message.success('问卷移除成功');
            }).catch(() => {
                // 用户取消操作
            });
        },
        
        // 加载模板数据
        loadTemplateData(templateData) {
            if (!templateData) return;
            
            console.log('加载模板数据:', templateData);
            
            // 更新表单数据
            this.form = {
                planName: templateData.name || templateData.planName || '疼痛互联网随访-tom测试',
                treatmentTime: templateData.treatmentTime || '14',
                timeUnit: templateData.timeUnit || 'day',
                planStatus: templateData.planStatus || 'disabled',
                planDetails: templateData.planDetails || [
                    {
                        sequence: 1,
                        name: '第1个月 (每天随访)',
                        executionTime: '3',
                        executionDay: '天',
                        eventType: '添加事件'
                    }
                ],
                examinations: templateData.examinations || [],
                otherData: templateData.otherData || [],
                subPlans: templateData.subPlans || [
                    {
                        name: '子计划1',
                        settings: {
                            type: 'patient',
                            executionTime: 'allTime',
                            startDay: '3',
                            endDay: '29',
                            reminderTime: '18:55',
                            planType: 'custom'
                        },
                        details: [
                            { sequence: 1, name: '第1个月 (每天随访)', cycleDays: '3天', event: '添加事件' }
                        ],
                        customDetails: [
                            { name: '', day: '', events: [] },
                            { name: '', day: '', events: [] }
                        ],
                        questionnaires: templateData.questionnaires || []
                    }
                ]
            };
            
            // 设置活动子计划
            this.activeSubPlan = '0';
            
            console.log('加载后的表单数据:', this.form);
        },
        
        // 处理问卷对话框关闭
        handleQuestionnaireDialogClose() {
            // 关闭对话框时清除焦点并重置选择状态
            this.questionnaireDialogVisible = false;
            this.$nextTick(() => {
                // 清除表格选择
                if (this.$refs.questionnaireTable) {
                    this.$refs.questionnaireTable.clearSelection();
                }
                // 清空选择数组
                this.selectedQuestionnaires = [];
                // 清空搜索关键词
                this.questionnaireSearchKeyword = '';
                // 移除所有可能的焦点
                document.activeElement && document.activeElement.blur();
            });
        },
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString();
        },
        addExamination() {
            console.log('新增检验检查');
        },
        addKeyIndicator() {
            console.log('添加重点指标');
        },
        addOtherData() {
            this.isEditingOtherData = false;
            this.editingOtherDataIndex = -1;
            this.currentOtherData = {
                type: '',
                isRequired: false
            };
            this.otherDataDialogVisible = true;
        },
        editOtherData(row, index) {
            this.isEditingOtherData = true;
            this.editingOtherDataIndex = index;
            this.currentOtherData = {
                type: row.type,
                isRequired: row.isRequired
            };
            this.otherDataDialogVisible = true;
        },
        deleteOtherData(index) {
            this.$confirm('确定要删除这条其他数据吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.form.otherData.splice(index, 1);
                this.$message.success('删除成功');
            }).catch(() => {
                // 用户取消删除
            });
        },
        saveOtherData() {
            this.$refs.otherDataForm.validate((valid) => {
                if (valid) {
                    if (this.isEditingOtherData) {
                        // 编辑模式
                        this.$set(this.form.otherData, this.editingOtherDataIndex, {
                            type: this.currentOtherData.type,
                            isRequired: this.currentOtherData.isRequired
                        });
                        this.$message.success('修改成功');
                    } else {
                        // 新增模式
                        this.form.otherData.push({
                            type: this.currentOtherData.type,
                            isRequired: this.currentOtherData.isRequired
                        });
                        this.$message.success('添加成功');
                    }
                    this.otherDataDialogVisible = false;
                } else {
                    return false;
                }
            });
        },
        getOtherDataTypeText(type) {
            const typeMap = {
                'bloodPressure': '血压',
                'heightWeight': '身高体重',
                'bloodSugar': '血糖'
            };
            return typeMap[type] || type;
        },
        handleCancel() {
            this.$confirm('确定要取消编辑吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$router.go(-1);
            }).catch(() => {
                // 用户取消取消
            });
        },
        handleSave() {
            this.$refs.templateForm.validate((valid) => {
                if (valid) {
                    // 准备保存的数据
                    const saveData = {
                        ...this.form,
                        // 确保所有子计划的问卷数据都被包含
                        subPlans: this.form.subPlans.map(subplan => ({
                            ...subplan,
                            questionnaires: subplan.questionnaires || []
                        }))
                    };
                    
                    // 触发父组件的保存事件
                    this.$emit('save', saveData);
                } else {
                    this.$message.error('请检查表单填写！');
                    return false;
                }
            });
        },
        
        // 测试方法
        testAddQuestionnaire() {
            console.log('=== 测试添加问卷 ===');
            // 模拟添加一个问卷
            const currentSubplanIndex = parseInt(this.activeSubPlan);
            const currentSubplan = this.form.subPlans[currentSubplanIndex];
            
            if (!currentSubplan.questionnaires) {
                this.$set(currentSubplan, 'questionnaires', []);
            }
            
            const testQuestionnaire = {
                id: `test_${Date.now()}`,
                questionnaireId: `test_${Date.now()}`,
                questionnaireName: '测试问卷',
                questionnaireType: 'custom',
                questionCount: 5,
                questionnaireDescription: '这是一个测试问卷',
                sortOrder: currentSubplan.questionnaires.length + 1,
                isRequired: true
            };
            
            console.log('准备添加测试问卷:', testQuestionnaire);
            this.$set(currentSubplan.questionnaires, currentSubplan.questionnaires.length, testQuestionnaire);
            console.log('测试问卷添加完成，当前数组:', currentSubplan.questionnaires);
            
            this.$message.success('测试问卷添加成功！');
        },
        
        testLogData() {
            console.log('=== 测试日志数据 ===');
            console.log('当前表单数据:', this.form);
            console.log('当前活动子计划索引:', this.activeSubPlan);
            console.log('当前活动子计划:', this.form.subPlans[parseInt(this.activeSubPlan)]);
            console.log('当前子计划问卷数组:', this.currentSubplanQuestionnaires);
            console.log('计算属性返回的问卷数量:', this.currentSubplanQuestionnaires.length);
        },
        
        testOpenDialog() {
            console.log('=== 测试打开问卷选择对话框 ===');
            this.questionnaireDialogVisible = true;
            this.loadQuestionnaires(); // 确保问卷列表已加载
            console.log('对话框状态:', this.questionnaireDialogVisible);
            console.log('问卷列表加载完成，总数:', this.allQuestionnaires.length);
        }
    }
}
</script>

<style lang="scss" scoped>
.smart-follow-up-edit {
    background-color: #f5f7fa;
    
    .header-section {
        background-color: #fff;
        padding: 12px 20px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-left {
            .el-button {
                font-size: 16px;
                color: #606266;
                padding: 0;
                
                i {
                    margin-right: 8px;
                }
            }
        }
        
        .header-right {
            .el-button {
                margin-left: 8px;
            }
        }
    }
    
    .main-content {
        padding: 20px;
        
        .form-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .add-subplan-section {
            margin-bottom: 20px;
            
            .el-tabs {
                background-color: #fff;
                border-radius: 4px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }
            
            .subplan-settings {
                padding: 20px;
                
                h3 {
                    margin: 0 0 20px 0;
                    font-size: 16px;
                    color: #303133;
                }
                
                .time-range-input {
                    display: flex;
                    align-items: center;
                }
                
                .subplan-details {
                    margin-top: 20px;
                }
                
                .custom-plan-table {
                    .day-input-wrapper {
                        display: flex;
                        align-items: center;
                    }
                    
                    .add-custom-plan {
                        margin-top: 10px;
                        padding: 20px;
                        border: 2px dashed #d9d9d9;
                        border-radius: 4px;
                        text-align: center;
                        background-color: #fafafa;
                        
                        .add-plan-btn {
                            color: #1890ff;
                            font-size: 14px;
                            
                            &:hover {
                                color: #40a9ff;
                            }
                        }
                    }
                }
            }
            
            .add-new-subplan {
                padding: 40px;
                text-align: center;
                background-color: #f8f9fa;
                border: 2px dashed #ddd;
                border-radius: 4px;
                
                .el-button {
                    background-color: #409eff;
                    border-color: #409eff;
                }
            }
        }
        
        .data-collection-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            
            h3 {
                margin: 0 0 20px 0;
                font-size: 16px;
                color: #303133;
            }
            
            .collection-cards {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
                
                .collection-card {
                    border: 1px solid #e4e7ed;
                    border-radius: 4px;
                    padding: 15px;
                    min-width: 200px;
                    flex: 1;
                    
                    .card-header {
                        margin-bottom: 10px;
                        
                        h4 {
                            margin: 0 0 5px 0;
                            font-size: 14px;
                            color: #303133;
                        }
                        
                        .card-subtitle {
                            font-size: 12px;
                            color: #909399;
                        }
                    }
                }
                
                .add-card {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    min-height: 100px;
                    border-style: dashed;
                    cursor: pointer;
                    
                    &:hover {
                        border-color: #409eff;
                        color: #409eff;
                    }
                    
                    .add-icon {
                        font-size: 24px;
                        margin-bottom: 8px;
                    }
                    
                    .add-text {
                        font-size: 14px;
                    }
                }
                
                .questionnaire-meta {
                    display: flex;
                    gap: 15px;
                    margin-bottom: 10px;
                    font-size: 12px;
                    color: #909399;
                    
                    span {
                        background-color: #f5f7fa;
                        padding: 2px 8px;
                        border-radius: 3px;
                    }
                }
                
                .danger-text {
                    color: #f56c6c;
                }
            }
        }
        
        .examination-section,
        .key-indicators-section,
        .other-data-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            
            h3 {
                margin: 0 0 20px 0;
                font-size: 16px;
                color: #303133;
            }
            
            .add-examination,
            .add-indicator,
            .add-other-data {
                margin-top: 10px;
                
                .el-button {
                    color: #409eff;
                    
                    i {
                        margin-right: 5px;
                    }
                }
            }
        }
    }
}

/deep/ .el-form-item {
    margin-bottom: 15px;
}

/deep/ .el-form-item__label {
    font-size: 14px;
    color: #606266;
}

/deep/ .el-input__inner {
    border: 1px solid #dcdfe6;
}

/deep/ .el-table {
    font-size: 14px;
    
    .el-table__header {
        color: #303133;
        font-weight: 500;
    }
}

/deep/ .el-tag {
    font-size: 12px;
}

/deep/ .error-input .el-input__inner {
    border-color: #f5222d;
    box-shadow: 0 0 0 2px rgba(245, 34, 45, 0.2);
}

/deep/ .danger-text {
    color: #f5222d;
}
</style> 
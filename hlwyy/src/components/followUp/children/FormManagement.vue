<template>
    <div class="form-management">
        <!-- 表单信息展示 -->
        <div class="form-header">
            <div class="form-info">
                <h3>{{ templateData.name }}</h3>
                <div class="form-meta">
                    <el-tag size="small" :type="templateData.formType === 'repeatable' ? 'primary' : 'success'">
                        {{ getFormTypeName(templateData.formType) }}
                    </el-tag>
                    <span class="meta-item">分类：{{ templateData.category }}</span>
                    <span class="meta-item">创建人：{{ templateData.creatorName }}</span>
                    <span class="meta-item">创建时间：{{ formatDateTime(templateData.createTime) }}</span>
                </div>
                <p class="form-description">{{ templateData.description }}</p>
            </div>
            <div class="form-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ formStats.submissionCount }}</div>
                    <div class="stat-label">提交次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ formStats.patientCount }}</div>
                    <div class="stat-label">参与人数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ formStats.avgScore }}</div>
                    <div class="stat-label">平均得分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ formStats.completionRate }}%</div>
                    <div class="stat-label">完成率</div>
                </div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <el-tabs v-model="activeTab" class="management-tabs" @tab-click="handleTabClick">
            <!-- 表单管理 -->
            <el-tab-pane label="表单管理" name="formManage">
                <form-manage-tab 
                    ref="formManageTab"
                    :template-data="templateData"
                    @questionnaire-updated="handleQuestionnaireUpdated"
                    @form-updated="handleFormUpdated"
                    @close-dialog="handleCloseDialog">
                </form-manage-tab>
            </el-tab-pane>
            
            <!-- 查看数据 -->
            <el-tab-pane label="查看数据" name="dataView">
                <data-view-tab 
                    ref="dataViewTab"
                    :template-data="templateData">
                </data-view-tab>
            </el-tab-pane>
            
            <!-- 数据分析 -->
            <el-tab-pane label="数据分析" name="dataAnalysis">
                <data-analysis-tab 
                    ref="dataAnalysisTab"
                    :template-data="templateData">
                </data-analysis-tab>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import { smartFormApi } from '@/api/smartForm.js'
import FormManageTab from './FormManageTab.vue'
import DataViewTab from './DataViewTab.vue'
import DataAnalysisTab from './DataAnalysisTab.vue'

export default {
    name: 'FormManagement',
    components: {
        FormManageTab,
        DataViewTab,
        DataAnalysisTab
    },
    props: {
        templateData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            activeTab: 'formManage',
            formStats: {
                submissionCount: 0,
                patientCount: 0,
                avgScore: 0,
                completionRate: 0
            }
        }
    },
    mounted() {
        // 移除自动加载，改为按需加载
        // 避免与其他子组件重复调用相同API
    },
    watch: {
        templateData: {
            handler(newVal) {
                // 当templateData变化时，如果是编辑状态则加载表单统计数据
                if (newVal && newVal.id && newVal.id > 0 && newVal.id !== 'undefined' && newVal.id !== 'null') {
                    this.$nextTick(() => {
                        // 加载表单统计数据（默认标签页）
                        this.loadFormStats();
                        
                        // 根据当前激活的标签页加载对应数据
                        this.loadActiveTabData();
                    });
                }
            },
            immediate: true
        }
    },
    methods: {
        // 处理问卷更新事件
        handleQuestionnaireUpdated() {
            // 将事件传递给父组件
            this.$emit('questionnaire-updated');
        },
        
        // 处理表单更新事件
        handleFormUpdated() {
            // 将事件传递给父组件
            this.$emit('form-updated');
        },
        
        // 处理关闭对话框事件
        handleCloseDialog() {
            // 将关闭事件传递给父组件
            this.$emit('close-dialog');
        },
        
        // 加载表单统计数据 - 改为手动调用
        async loadFormStats() {
            // 如果是新增状态，使用模拟数据
            if (!this.templateData || !this.templateData.id || this.templateData.id <= 0 || this.templateData.id === 'undefined' || this.templateData.id === 'null') {
                console.log('新增状态，使用默认表单统计，templateData:', this.templateData);
                this.formStats = {
                    submissionCount: 0,
                    patientCount: 0,
                    avgScore: 0,
                    completionRate: 0
                };
                return;
            }
            
            // 避免重复加载，如果已经有数据则跳过
            if (this.formStats.submissionCount > 0) {
                console.log('表单统计数据已存在，跳过重复加载');
                return;
            }
            
            try {
                console.log('开始加载表单统计数据，ID:', this.templateData.id);
                const params = {
                    formTemplateId: this.templateData.id,
                    statisticsType: 'overview'
                };
                
                const response = await smartFormApi.getFormStatistics(params);
                if (response && response.success) {
                    const summary = response.data.summary || {};
                    this.formStats = {
                        submissionCount: summary.totalSubmissions || 0,
                        patientCount: summary.totalPatients || 0,
                        avgScore: summary.avgScore ? parseFloat(summary.avgScore).toFixed(1) : 0,
                        completionRate: summary.completionRate ? parseFloat(summary.completionRate).toFixed(1) : 0
                    };
                    console.log('表单统计数据加载成功:', this.formStats);
                }
            } catch (error) {
                console.error('加载表单统计数据失败:', error);
                // 使用模拟数据
                this.formStats = {
                    submissionCount: this.templateData.submissionCount || 0,
                    patientCount: Math.floor((this.templateData.submissionCount || 0) * 0.7),
                    avgScore: 78.5,
                    completionRate: 94.2
                };
            }
        },
        
        // 根据当前激活的标签页加载对应数据
        loadActiveTabData() {
            const tabName = this.activeTab;
            console.log('加载当前激活标签页数据:', tabName);
            
            this.$nextTick(() => {
                const tabComponent = this.$refs[`${tabName}Tab`];
                
                if (tabName === 'formManage') {
                    // 表单管理标签页 - FormManageTab的watch已经处理了数据加载
                    // loadFormStats已经在watch中被调用，不需要重复调用
                    console.log('表单管理标签页，数据已由watch处理');
                } else if (tabName === 'dataView') {
                    // 查看数据标签页 - 加载提交列表和统计数据
                    if (tabComponent && typeof tabComponent.loadSubmissionList === 'function') {
                        tabComponent.loadSubmissionList();
                    }
                    if (tabComponent && typeof tabComponent.loadDataStats === 'function') {
                        tabComponent.loadDataStats();
                    }
                } else if (tabName === 'dataAnalysis') {
                    // 数据分析标签页 - 加载分析数据
                    if (tabComponent && typeof tabComponent.loadAnalysisData === 'function') {
                        tabComponent.loadAnalysisData();
                    }
                    if (tabComponent && typeof tabComponent.loadQuestionnaireList === 'function') {
                        tabComponent.loadQuestionnaireList();
                    }
                }
            });
        },
        
        // 标签页点击事件
        handleTabClick(tab) {
            const tabName = tab.name;
            
            // 延迟执行，确保组件已挂载
            this.$nextTick(() => {
                const tabComponent = this.$refs[`${tabName}Tab`];
                if (tabComponent && typeof tabComponent.refresh === 'function') {
                    tabComponent.refresh();
                }
                
                // 为特定标签页加载数据
                if (tabName === 'dataView') {
                    // 查看数据标签页 - 加载提交列表和统计数据
                    if (tabComponent && typeof tabComponent.loadSubmissionList === 'function') {
                        tabComponent.loadSubmissionList();
                    }
                    if (tabComponent && typeof tabComponent.loadDataStats === 'function') {
                        tabComponent.loadDataStats();
                    }
                } else if (tabName === 'dataAnalysis') {
                    // 数据分析标签页 - 加载分析数据
                    if (tabComponent && typeof tabComponent.loadAnalysisData === 'function') {
                        tabComponent.loadAnalysisData();
                    }
                    if (tabComponent && typeof tabComponent.loadQuestionnaireList === 'function') {
                        tabComponent.loadQuestionnaireList();
                    }
                } else if (tabName === 'formManage') {
                    // 表单管理标签页 - 加载表单统计数据
                    this.loadFormStats();
                }
            });
        },
        
        // 获取表单类型名称
        getFormTypeName(type) {
            const typeMap = {
                'one_time': '一次性问卷',
                'repeatable': '重复性问卷'
            };
            return typeMap[type] || type;
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        }
    }
}
</script>

<style lang="scss" scoped>
.form-management {
    
    .form-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px;
        background-color: #fff;
        border-bottom: 1px solid #e4e7ed;
        margin-bottom: 20px;
        
        .form-info {
            flex: 1;
            
            h3 {
                margin: 0 0 10px 0;
                color: #303133;
                font-size: 20px;
                font-weight: 600;
            }
            
            .form-meta {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                
                .el-tag {
                    margin-right: 15px;
                }
                
                .meta-item {
                    margin-right: 20px;
                    color: #606266;
                    font-size: 14px;
                }
            }
            
            .form-description {
                margin: 0;
                color: #909399;
                font-size: 14px;
                line-height: 1.5;
            }
        }
        
        .form-stats {
            display: flex;
            gap: 30px;
            
            .stat-item {
                text-align: center;
                
                .stat-value {
                    font-size: 24px;
                    font-weight: 600;
                    color: #409eff;
                    line-height: 1;
                    margin-bottom: 5px;
                }
                
                .stat-label {
                    font-size: 12px;
                    color: #909399;
                }
            }
        }
    }
    
    .management-tabs {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        /deep/ .el-tabs__content {
            flex: 1;
            overflow: hidden;
            
            .el-tab-pane {
                height: 100%;
                overflow: auto;
            }
        }
        
        /deep/ .el-tabs__header {
            margin: 0;
            border-bottom: 2px solid #e4e7ed;
            
            .el-tabs__nav-wrap {
                padding: 0 20px;
            }
            
            .el-tabs__item {
                height: 50px;
                line-height: 50px;
                font-size: 16px;
                font-weight: 500;
                
                &.is-active {
                    color: #409eff;
                    font-weight: 600;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .form-management {
        .form-header {
            flex-direction: column;
            align-items: stretch;
            
            .form-stats {
                margin-top: 20px;
                justify-content: space-around;
            }
        }
    }
}
</style> 
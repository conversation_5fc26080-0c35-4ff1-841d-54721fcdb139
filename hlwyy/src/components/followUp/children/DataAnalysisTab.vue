<template>
    <div class="data-analysis-tab">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <h4>数据分析</h4>
                <p>对表单提交数据进行深入分析，洞察用户行为和数据趋势</p>
            </div>
            <div class="toolbar-right">
                <el-date-picker
                    v-model="analysisDateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    @change="loadAnalysisData"
                    style="margin-right: 10px;">
                </el-date-picker>
                <el-button icon="el-icon-refresh" @click="refreshAnalysis">刷新分析</el-button>
                <el-button icon="el-icon-download" @click="exportAnalysis">导出报告</el-button>
            </div>
        </div>

        <!-- 分析维度选择 -->
        <div class="analysis-filters">
            <el-radio-group v-model="analysisType" @change="loadAnalysisData">
                <el-radio-button label="overview">总体概览</el-radio-button>
                <el-radio-button label="questions">题目分析</el-radio-button>
                <el-radio-button label="trends">趋势分析</el-radio-button>
                <el-radio-button label="scores">分数分析</el-radio-button>
            </el-radio-group>
        </div>

        <div v-loading="loading" class="analysis-content">
            <!-- 总体概览 -->
            <div v-if="analysisType === 'overview'" class="overview-analysis">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-header">
                            <i class="el-icon-document"></i>
                            <span>总提交次数</span>
                        </div>
                        <div class="metric-value">{{ (analysisData.overview && analysisData.overview.totalSubmissions) || 0 }}</div>
                        <div class="metric-change positive">
                            <i class="el-icon-arrow-up"></i>
                            较上周 +12%
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <i class="el-icon-user"></i>
                            <span>参与患者数</span>
                        </div>
                        <div class="metric-value">{{ (analysisData.overview && analysisData.overview.totalPatients) || 0 }}</div>
                        <div class="metric-change positive">
                            <i class="el-icon-arrow-up"></i>
                            较上周 +8%
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <i class="el-icon-star-on"></i>
                            <span>平均得分</span>
                        </div>
                        <div class="metric-value">{{ (analysisData.overview && analysisData.overview.avgScore) || 0 }}</div>
                        <div class="metric-change negative">
                            <i class="el-icon-arrow-down"></i>
                            较上周 -2.1%
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <i class="el-icon-circle-check"></i>
                            <span>完成率</span>
                        </div>
                        <div class="metric-value">{{ (analysisData.overview && analysisData.overview.completionRate) || 0 }}%</div>
                        <div class="metric-change positive">
                            <i class="el-icon-arrow-up"></i>
                            较上周 +5.2%
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <i class="el-icon-time"></i>
                            <span>平均耗时</span>
                        </div>
                        <div class="metric-value">{{ (analysisData.overview && analysisData.overview.avgTimeSpent) || 0 }}分</div>
                        <div class="metric-change neutral">
                            <i class="el-icon-minus"></i>
                            较上周 持平
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <i class="el-icon-medal"></i>
                            <span>优秀率</span>
                        </div>
                        <div class="metric-value">{{ calculateExcellentRate() }}%</div>
                        <div class="metric-change positive">
                            <i class="el-icon-arrow-up"></i>
                            较上周 ****%
                        </div>
                    </div>
                </div>

                <!-- 分数分布图表 -->
                <div class="chart-section">
                    <h5>分数分布</h5>
                    <div class="score-distribution">
                        <div v-for="range in scoreDistribution" :key="range.range" class="distribution-item">
                            <div class="range-label">{{ range.range }}分</div>
                            <div class="range-bar">
                                <div class="bar-fill" :style="{ width: range.percentage + '%' }"></div>
                            </div>
                            <div class="range-stats">
                                <span class="count">{{ range.count }}人</span>
                                <span class="percentage">{{ range.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 题目分析 -->
            <div v-if="analysisType === 'questions'" class="questions-analysis">
                <div class="question-filter">
                    <el-select v-model="selectedQuestionnaireId" placeholder="选择问卷" @change="loadQuestionAnalysis" style="width: 200px;">
                        <el-option 
                            v-for="questionnaire in questionnaireList" 
                            :key="questionnaire.id" 
                            :label="questionnaire.name" 
                            :value="questionnaire.id">
                        </el-option>
                    </el-select>
                </div>

                <div v-if="questionAnalysis.length > 0" class="questions-list">
                    <div v-for="question in questionAnalysis" :key="question.questionId" class="question-analysis-item">
                        <div class="question-header">
                            <h6>{{ question.questionTitle }}</h6>
                            <div class="question-stats">
                                <span>回答数：{{ question.answerCount }}</span>
                                <span>回答率：{{ calculateAnswerRate(question) }}%</span>
                            </div>
                        </div>
                        
                        <div class="question-answers">
                            <div v-if="question.options && question.options.length > 0" class="options-analysis">
                                <div v-for="option in question.options" :key="option.optionText" class="option-item">
                                    <div class="option-label">{{ option.optionText }}</div>
                                    <div class="option-bar">
                                        <div class="bar-fill" :style="{ width: option.percentage + '%' }"></div>
                                    </div>
                                    <div class="option-stats">
                                        <span class="count">{{ option.count }}</span>
                                        <span class="percentage">{{ option.percentage }}%</span>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="text-analysis">
                                <p>文本类题目，共收到 {{ question.answerCount }} 个回答</p>
                                <!-- 这里可以添加文本分析功能，如词云等 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势分析 -->
            <div v-if="analysisType === 'trends'" class="trends-analysis">
                <div class="trend-charts">
                    <div class="chart-item">
                        <h5>提交趋势</h5>
                        <div class="trend-chart">
                            <div class="chart-placeholder">
                                <!-- 这里可以集成图表库如ECharts -->
                                <div class="trend-line">
                                    <div v-for="(trend, index) in analysisData.trends" :key="index" class="trend-point">
                                        <div class="point-date">{{ formatDate(trend.date) }}</div>
                                        <div class="point-value">{{ trend.submissions }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chart-item">
                        <h5>分数趋势</h5>
                        <div class="trend-chart">
                            <div class="chart-placeholder">
                                <div class="trend-line">
                                    <div v-for="(trend, index) in analysisData.trends" :key="index" class="trend-point">
                                        <div class="point-date">{{ formatDate(trend.date) }}</div>
                                        <div class="point-value">{{ trend.avgScore }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分数分析 -->
            <div v-if="analysisType === 'scores'" class="scores-analysis">
                <div class="score-insights">
                    <div class="insight-card">
                        <h6>分数洞察</h6>
                        <ul>
                            <li>最高分：{{ getMaxScore() }}分</li>
                            <li>最低分：{{ getMinScore() }}分</li>
                            <li>分数方差：{{ getScoreVariance() }}</li>
                            <li>优秀率（≥80分）：{{ calculateExcellentRate() }}%</li>
                            <li>及格率（≥60分）：{{ calculatePassRate() }}%</li>
                        </ul>
                    </div>
                    
                    <div class="score-recommendations">
                        <h6>改进建议</h6>
                        <div class="recommendations-list">
                            <div class="recommendation-item">
                                <i class="el-icon-warning"></i>
                                <span>{{ getScoreRecommendation() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { smartFormApi } from '@/api/smartForm.js'

export default {
    name: 'DataAnalysisTab',
    props: {
        templateData: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            loading: false,
            analysisType: 'overview',
            analysisDateRange: [],
            analysisData: {
                overview: {},
                trends: [],
                scoreDistribution: []
            },
            questionAnalysis: [],
            questionnaireList: [],
            selectedQuestionnaireId: null
        }
    },
    computed: {
        scoreDistribution() {
            return this.analysisData.scoreDistribution || [];
        }
    },
    mounted() {
        this.initDateRange();
        // 移除自动加载，改为按需加载
        // 避免与其他组件重复调用相同API
    },
    methods: {
        // 判断是否为新增状态
        isNewForm() {
            return !this.templateData || 
                   !this.templateData.id || 
                   this.templateData.id <= 0 || 
                   this.templateData.id === 'undefined' || 
                   this.templateData.id === 'null';
        },
        
        // 初始化日期范围
        initDateRange() {
            const end = new Date();
            const start = new Date();
            start.setDate(start.getDate() - 30);
            this.analysisDateRange = [
                start.toISOString().split('T')[0],
                end.toISOString().split('T')[0]
            ];
        },
        
        // 加载分析数据
        async loadAnalysisData() {
            // 如果是新增状态，不加载数据
            if (this.isNewForm()) {
                console.log('新增状态，跳过数据加载，templateData:', this.templateData);
                this.analysisData = {
                    overview: {},
                    trends: [],
                    scoreDistribution: []
                };
                return;
            }
            
            this.loading = true;
            try {
                console.log('开始加载分析数据，ID:', this.templateData.id);
                const params = {
                    formTemplateId: this.templateData.id,
                    analysisType: this.analysisType,
                    startTime: this.analysisDateRange && this.analysisDateRange[0],
                    endTime: this.analysisDateRange && this.analysisDateRange[1],
                    groupBy: 'day'
                };
                
                const response = await smartFormApi.getFormDataAnalysis(params);
                if (response && response.success) {
                    this.analysisData = response.data;
                    console.log('分析数据加载成功');
                } else {
                    // 如果API调用失败，显示空数据
                    this.analysisData = {
                        overview: {},
                        trends: [],
                        scoreDistribution: []
                    };
                }
            } catch (error) {
                console.error('加载分析数据失败:', error);
                // 出错时显示空数据
                this.analysisData = {
                    overview: {},
                    trends: [],
                    scoreDistribution: []
                };
            } finally {
                this.loading = false;
            }
        },
        

        
        // 加载问卷列表
        async loadQuestionnaireList() {
            // 如果是新增状态，直接返回空数组
            if (this.isNewForm()) {
                console.log('新增状态，跳过问卷列表加载，templateData:', this.templateData);
                this.questionnaireList = [];
                return;
            }
            
            try {
                const response = await smartFormApi.getFormTemplateById(this.templateData.id);
                if (response && response.success && response.data && response.data.questionnaires) {
                    this.questionnaireList = response.data.questionnaires.map(q => ({
                        id: q.questionnaireId,
                        name: q.questionnaireName
                    }));
                    if (this.questionnaireList.length > 0) {
                        this.selectedQuestionnaireId = this.questionnaireList[0].id;
                        this.loadQuestionAnalysis();
                    }
                } else {
                    console.warn('表单模板不存在或已删除，ID:', this.templateData.id);
                    this.questionnaireList = [];
                }
            } catch (error) {
                console.error('加载问卷列表失败:', error);
                this.questionnaireList = [];
            }
        },
        
        // 加载题目分析
        async loadQuestionAnalysis() {
            // 如果是新增状态，显示空数据
            if (this.isNewForm()) {
                console.log('新增状态，显示空题目分析数据');
                this.questionAnalysis = [];
                return;
            }
            
            try {
                const params = {
                    formTemplateId: this.templateData.id,
                    questionnaireId: this.selectedQuestionnaireId,
                    analysisType: 'questions'
                };
                
                const response = await smartFormApi.getFormDataAnalysis(params);
                if (response && response.success) {
                    this.questionAnalysis = response.data.questions || [];
                } else {
                    // 如果API调用失败，显示空数据
                    this.questionAnalysis = [];
                }
            } catch (error) {
                console.error('加载题目分析失败:', error);
                // 出错时显示空数据
                this.questionAnalysis = [];
            }
        },
        
        // 刷新分析
        refreshAnalysis() {
            this.loadAnalysisData();
        },
        
        // 导出分析报告
        exportAnalysis() {
            this.$message.info('导出功能开发中...');
        },
        
        // 计算优秀率
        calculateExcellentRate() {
            const excellentRange = this.scoreDistribution.find(range => range.range === '81-100');
            return excellentRange ? excellentRange.percentage.toFixed(1) : 0;
        },
        
        // 计算及格率
        calculatePassRate() {
            const passRanges = this.scoreDistribution.filter(range => 
                range.range === '61-80' || range.range === '81-100'
            );
            const passRate = passRanges.reduce((sum, range) => sum + range.percentage, 0);
            return passRate.toFixed(1);
        },
        
        // 计算回答率
        calculateAnswerRate(question) {
            const totalSubmissions = (this.analysisData.overview && this.analysisData.overview.totalSubmissions) || 0;
            if (totalSubmissions === 0) return 0;
            return ((question.answerCount / totalSubmissions) * 100).toFixed(1);
        },
        
        // 获取最高分
        getMaxScore() {
            const scoreDistribution = this.analysisData.scoreDistribution || [];
            if (scoreDistribution.length === 0) return 0;
            // 从分数分布中计算最高分
            const maxRange = scoreDistribution[scoreDistribution.length - 1];
            return maxRange ? maxRange.range.split('-')[1] : 0;
        },
        
        // 获取最低分
        getMinScore() {
            const scoreDistribution = this.analysisData.scoreDistribution || [];
            if (scoreDistribution.length === 0) return 0;
            // 从分数分布中计算最低分
            const minRange = scoreDistribution[0];
            return minRange ? minRange.range.split('-')[0] : 0;
        },
        
        // 获取分数方差
        getScoreVariance() {
            // 这里可以根据实际数据计算方差，暂时返回0
            return 0;
        },
        
        // 获取分数建议
        getScoreRecommendation() {
            const avgScore = (this.analysisData.overview && this.analysisData.overview.avgScore) || 78.6;
            if (avgScore >= 85) {
                return '整体表现优秀，继续保持';
            } else if (avgScore >= 70) {
                return '表现良好，可针对低分题目进行优化';
            } else {
                return '需要重点关注，建议分析低分原因并改进';
            }
        },
        
        // 格式化日期
        formatDate(date) {
            return new Date(date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
        },
        
        // 刷新方法
        refresh() {
            this.loadAnalysisData();
        }
    }
}
</script>

<style lang="scss" scoped>
.data-analysis-tab {
    padding: 20px;
    
    .toolbar {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
        
        .toolbar-left {
            h4 {
                margin: 0 0 5px 0;
                color: #303133;
                font-size: 18px;
            }
            
            p {
                margin: 0;
                color: #909399;
                font-size: 14px;
            }
        }
        
        .toolbar-right {
            display: flex;
            align-items: center;
            
            .el-button {
                margin-left: 10px;
            }
        }
    }
    
    .analysis-filters {
        margin-bottom: 20px;
    }
    
    .analysis-content {
        min-height: 400px;
        
        .overview-analysis {
            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
                
                .metric-card {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                    
                    .metric-header {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;
                        
                        i {
                            font-size: 20px;
                            color: #409eff;
                            margin-right: 8px;
                        }
                        
                        span {
                            color: #606266;
                            font-size: 14px;
                        }
                    }
                    
                    .metric-value {
                        font-size: 28px;
                        font-weight: 600;
                        color: #303133;
                        margin-bottom: 10px;
                    }
                    
                    .metric-change {
                        display: flex;
                        align-items: center;
                        font-size: 12px;
                        
                        i {
                            margin-right: 4px;
                        }
                        
                        &.positive {
                            color: #67c23a;
                        }
                        
                        &.negative {
                            color: #f56c6c;
                        }
                        
                        &.neutral {
                            color: #909399;
                        }
                    }
                }
            }
            
            .chart-section {
                background: #fff;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                
                h5 {
                    margin: 0 0 20px 0;
                    color: #303133;
                }
                
                .score-distribution {
                    .distribution-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;
                        
                        .range-label {
                            width: 80px;
                            color: #606266;
                            font-size: 14px;
                        }
                        
                        .range-bar {
                            flex: 1;
                            height: 20px;
                            background-color: #f5f7fa;
                            border-radius: 10px;
                            margin: 0 15px;
                            overflow: hidden;
                            
                            .bar-fill {
                                height: 100%;
                                background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
                                transition: width 0.3s ease;
                            }
                        }
                        
                        .range-stats {
                            width: 100px;
                            text-align: right;
                            
                            .count {
                                color: #303133;
                                font-weight: 500;
                                margin-right: 5px;
                            }
                            
                            .percentage {
                                color: #909399;
                                font-size: 12px;
                            }
                        }
                    }
                }
            }
        }
        
        .questions-analysis {
            .question-filter {
                margin-bottom: 20px;
            }
            
            .questions-list {
                .question-analysis-item {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                    margin-bottom: 20px;
                    
                    .question-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                        
                        h6 {
                            margin: 0;
                            color: #303133;
                            font-size: 16px;
                        }
                        
                        .question-stats {
                            span {
                                margin-left: 20px;
                                color: #606266;
                                font-size: 14px;
                            }
                        }
                    }
                    
                    .options-analysis {
                        .option-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 10px;
                            
                            .option-label {
                                width: 120px;
                                color: #606266;
                                font-size: 14px;
                            }
                            
                            .option-bar {
                                flex: 1;
                                height: 16px;
                                background-color: #f5f7fa;
                                border-radius: 8px;
                                margin: 0 15px;
                                overflow: hidden;
                                
                                .bar-fill {
                                    height: 100%;
                                    background-color: #409eff;
                                    transition: width 0.3s ease;
                                }
                            }
                            
                            .option-stats {
                                width: 80px;
                                text-align: right;
                                
                                .count {
                                    color: #303133;
                                    font-weight: 500;
                                    margin-right: 5px;
                                }
                                
                                .percentage {
                                    color: #909399;
                                    font-size: 12px;
                                }
                            }
                        }
                    }
                    
                    .text-analysis {
                        padding: 20px;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                        text-align: center;
                        color: #606266;
                    }
                }
            }
        }
        
        .trends-analysis {
            .trend-charts {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                
                .chart-item {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                    
                    h5 {
                        margin: 0 0 20px 0;
                        color: #303133;
                    }
                    
                    .trend-chart {
                        .chart-placeholder {
                            height: 200px;
                            background-color: #f8f9fa;
                            border-radius: 4px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            
                            .trend-line {
                                display: flex;
                                justify-content: space-around;
                                width: 100%;
                                
                                .trend-point {
                                    text-align: center;
                                    
                                    .point-date {
                                        font-size: 12px;
                                        color: #909399;
                                        margin-bottom: 5px;
                                    }
                                    
                                    .point-value {
                                        font-size: 16px;
                                        font-weight: 600;
                                        color: #409eff;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        .scores-analysis {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            
            .insight-card, .score-recommendations {
                background: #fff;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                
                h6 {
                    margin: 0 0 15px 0;
                    color: #303133;
                    font-size: 16px;
                }
                
                ul {
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    
                    li {
                        margin-bottom: 10px;
                        color: #606266;
                        
                        &:before {
                            content: "•";
                            color: #409eff;
                            margin-right: 8px;
                        }
                    }
                }
                
                .recommendations-list {
                    .recommendation-item {
                        display: flex;
                        align-items: center;
                        padding: 10px;
                        background-color: #fff3cd;
                        border-radius: 4px;
                        border-left: 4px solid #ffc107;
                        
                        i {
                            color: #856404;
                            margin-right: 10px;
                        }
                        
                        span {
                            color: #856404;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .data-analysis-tab {
        .metrics-grid {
            grid-template-columns: 1fr !important;
        }
        
        .trend-charts {
            grid-template-columns: 1fr !important;
        }
        
        .scores-analysis {
            grid-template-columns: 1fr !important;
        }
    }
}
</style> 
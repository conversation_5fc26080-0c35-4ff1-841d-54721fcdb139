<template>
    <div class="smart-follow-up">
        <!-- 搜索和操作区域 -->
        <div class="toolbar">
            <div class="search-area">
                <el-input 
                    v-model="searchForm.name" 
                    placeholder="请输入模板名称" 
                    class="search-input"
                    clearable>
                    <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                </el-input>
                <el-select v-model="searchForm.category" placeholder="模板分类" class="category-select" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option label="术后随访" value="1"></el-option>
                    <el-option label="慢病管理" value="2"></el-option>
                    <el-option label="健康体检" value="3"></el-option>
                </el-select>
                <el-select v-model="searchForm.status" placeholder="状态" class="status-select" clearable>
                    <el-option label="全部" value=""></el-option>
                    <el-option label="启用" value="1"></el-option>
                    <el-option label="禁用" value="0"></el-option>
                </el-select>
                <el-button type="default" @click="resetSearch">重置</el-button>
            </div>
            <div class="action-area">
                <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增模板</el-button>
                <el-button type="default" icon="el-icon-download" @click="handleImport">导入模板</el-button>
                <el-button type="default" icon="el-icon-upload2" @click="handleExport">导出模板</el-button>
            </div>
        </div>

        <!-- 模板列表 -->
        <div class="template-list">
            <el-table :data="tableData" border style="width: 100%" v-loading="loading">
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="name" label="模板名称" min-width="200" show-overflow-tooltip></el-table-column>
                <el-table-column prop="categoryName" label="分类" width="120"></el-table-column>
                <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip></el-table-column>
                <el-table-column prop="departmentName" label="科室" width="120"></el-table-column>
                <el-table-column prop="creatorName" label="创建人" width="100"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160">
                    <template slot-scope="scope">
                        {{ formatTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                    <template slot-scope="scope">
                        <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                            {{ scope.row.status === 1 ? '启用' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="280" fixed="right">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
                        <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
                        <el-button type="text" size="small" @click="handleCopy(scope.row)">复制</el-button>
                        <el-button 
                            type="text" 
                            size="small" 
                            @click="handleToggleStatus(scope.row)"
                            :class="scope.row.status === 1 ? 'danger-text' : 'success-text'">
                            {{ scope.row.status === 1 ? '禁用' : '启用' }}
                        </el-button>
                        <el-button type="text" size="small" class="danger-text" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>

        <!-- 新增/编辑模板对话框 -->
        <el-dialog 
            :title="dialogTitle" 
            :visible.sync="dialogVisible" 
            width="1200px"
            :close-on-click-modal="false"
            class="template-dialog">
            <smart-follow-up-edit 
                ref="templateEdit"
                :template-data="currentTemplate"
                :is-edit="isEdit"
                @save="handleSave"
                @cancel="handleCancel">
            </smart-follow-up-edit>
        </el-dialog>

        <!-- 查看模板对话框 -->
        <el-dialog 
            title="查看模板" 
            :visible.sync="viewDialogVisible" 
            width="1000px"
            class="view-dialog">
            <template-preview 
                :template-data="currentTemplate"
                v-if="viewDialogVisible">
            </template-preview>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios'
import SmartFollowUpEdit from './SmartFollowUpEdit.vue'
import TemplatePreview from './TemplatePreview.vue'

export default {
    name: 'SmartFollowUp',
    components: {
        SmartFollowUpEdit,
        TemplatePreview
    },
    data() {
        return {
            loading: false,
            searchForm: {
                name: '',
                category: '',
                status: ''
            },
            tableData: [],
            currentPage: 1,
            pageSize: 20,
            total: 0,
            dialogVisible: false,
            viewDialogVisible: false,
            isEdit: false,
            currentTemplate: null,
            multipleSelection: []
        }
    },
    computed: {
        dialogTitle() {
            return this.isEdit ? '编辑智能随访模板' : '新增智能随访模板'
        }
    },
    mounted() {
        this.getTemplateList()
    },
    methods: {
        // 获取模板列表
        getTemplateList() {
            this.loading = true
            const params = {
                page: this.currentPage - 1,
                size: this.pageSize,
                name: this.searchForm.name || null,
                category: this.searchForm.category || null,
                status: this.searchForm.status || null
            }
            
            axios({
                method: 'get',
                url: '/api/emr/template/smart-followup/page',
                params: params,
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code === 200) {
                    this.tableData = res.data.data.records
                    this.total = res.data.data.total
                } else {
                    this.$message.error(res.data.message || '获取模板列表失败')
                }
            }).catch(err => {
                console.error('获取模板列表出错', err)
                this.$message.error('获取模板列表出错，请稍后重试')
            }).finally(() => {
                this.loading = false
            })
        },

        // 搜索
        handleSearch() {
            this.currentPage = 1
            this.getTemplateList()
        },

        // 重置搜索
        resetSearch() {
            this.searchForm = {
                name: '',
                category: '',
                status: ''
            }
            this.currentPage = 1
            this.getTemplateList()
        },

        // 新增模板
        handleAdd() {
            this.isEdit = false
            this.currentTemplate = null
            this.dialogVisible = true
        },

        // 编辑模板
        handleEdit(row) {
            this.isEdit = true
            this.currentTemplate = { ...row }
            this.dialogVisible = true
        },

        // 查看模板
        handleView(row) {
            this.currentTemplate = { ...row }
            this.viewDialogVisible = true
        },

        // 复制模板
        handleCopy(row) {
            this.$confirm('确认复制该模板？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                const copyTemplate = {
                    ...row,
                    name: row.name + '(副本)',
                    id: null
                }
                this.isEdit = false
                this.currentTemplate = copyTemplate
                this.dialogVisible = true
            })
        },

        // 切换状态
        handleToggleStatus(row) {
            const action = row.status === 1 ? '禁用' : '启用'
            this.$confirm(`确认${action}该模板？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true
                axios({
                    method: 'put',
                    url: `/api/emr/template/smart-followup/${row.id}/status`,
                    data: { status: row.status === 1 ? 0 : 1 },
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(res => {
                    if (res.data.code === 200) {
                        this.$message.success(`${action}成功`)
                        this.getTemplateList()
                    } else {
                        this.$message.error(res.data.message || `${action}失败`)
                    }
                }).catch(err => {
                    console.error(`${action}模板出错`, err)
                    this.$message.error(`${action}模板出错，请稍后重试`)
                }).finally(() => {
                    this.loading = false
                })
            })
        },

        // 删除模板
        handleDelete(row) {
            this.$confirm('确认删除该模板？删除后将无法恢复。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.loading = true
                axios({
                    method: 'delete',
                    url: `/api/emr/template/smart-followup/${row.id}`,
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(res => {
                    if (res.data.code === 200) {
                        this.$message.success('删除成功')
                        this.getTemplateList()
                    } else {
                        this.$message.error(res.data.message || '删除失败')
                    }
                }).catch(err => {
                    console.error('删除模板出错', err)
                    this.$message.error('删除模板出错，请稍后重试')
                }).finally(() => {
                    this.loading = false
                })
            })
        },

        // 导入模板
        handleImport() {
            this.$message.info('导入功能开发中...')
        },

        // 导出模板
        handleExport() {
            this.$message.info('导出功能开发中...')
        },

        // 保存模板
        handleSave(templateData) {
            this.loading = true
            const url = this.isEdit 
                ? `/api/emr/template/smart-followup/${templateData.id}` 
                : '/api/emr/template/smart-followup'
            const method = this.isEdit ? 'put' : 'post'
            
            axios({
                method: method,
                url: url,
                data: templateData,
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(res => {
                if (res.data.code === 200) {
                    this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
                    this.dialogVisible = false
                    this.getTemplateList()
                } else {
                    this.$message.error(res.data.message || (this.isEdit ? '编辑失败' : '新增失败'))
                }
            }).catch(err => {
                console.error('保存模板出错', err)
                this.$message.error('保存模板出错，请稍后重试')
            }).finally(() => {
                this.loading = false
            })
        },

        // 取消编辑
        handleCancel() {
            this.dialogVisible = false
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pageSize = val
            this.getTemplateList()
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.currentPage = val
            this.getTemplateList()
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return ''
            return new Date(time).toLocaleString('zh-CN')
        },

        // 刷新数据
        refresh() {
            this.getTemplateList()
        }
    }
}
</script>

<style lang="scss" scoped>
.smart-follow-up {
    padding: 20px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .search-area {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .search-input {
            width: 250px;
        }
        
        .category-select,
        .status-select {
            width: 120px;
        }
    }
    
    .action-area {
        display: flex;
        gap: 10px;
    }
}

.template-list {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.danger-text {
    color: #f56c6c;
}

.success-text {
    color: #67c23a;
}

/deep/ .template-dialog {
    .el-dialog__body {
        padding: 0;
    }
}

/deep/ .view-dialog {
    .el-dialog__body {
        max-height: 600px;
        overflow-y: auto;
    }
}
</style> 
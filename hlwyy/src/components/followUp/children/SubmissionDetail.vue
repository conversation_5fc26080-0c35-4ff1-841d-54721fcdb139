<template>
    <div class="submission-detail">
        <!-- 提交信息概览 -->
        <div class="submission-overview">
            <h4>提交信息概览</h4>
            <div class="overview-grid">
                <div class="overview-item">
                    <span class="item-label">提交编号：</span>
                    <span class="item-value">{{ submissionData.submission.submissionNo }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">表单名称：</span>
                    <span class="item-value">{{ submissionData.submission.formInfo.name }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">患者姓名：</span>
                    <span class="item-value">{{ submissionData.submission.patientInfo.name }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">联系电话：</span>
                    <span class="item-value">{{ submissionData.submission.patientInfo.phone }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">提交者：</span>
                    <span class="item-value">{{ submissionData.submission.submitterInfo.name }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">提交时间：</span>
                    <span class="item-value">{{ formatDateTime(submissionData.submission.submitTime) }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">总分数：</span>
                    <span class="item-value score">{{ submissionData.submission.totalScore }}</span>
                </div>
                <div class="overview-item">
                    <span class="item-label">完成进度：</span>
                    <span class="item-value">{{ submissionData.submission.progress }}%</span>
                </div>
            </div>
        </div>

        <!-- 答案详情 -->
        <div class="answers-section">
            <h4>答案详情</h4>
            <div class="questionnaire-answers">
                <div 
                    v-for="(questionnaire, index) in groupedAnswers" 
                    :key="questionnaire.id" 
                    class="questionnaire-group">
                    
                    <div class="questionnaire-header">
                        <h5>{{ index + 1 }}. {{ questionnaire.name }}</h5>
                        <div class="questionnaire-meta">
                            <span>题目数量：{{ questionnaire.answers.length }}</span>
                            <span>得分：{{ questionnaire.totalScore }}</span>
                        </div>
                    </div>
                    
                    <div class="answers-list">
                        <div 
                            v-for="answer in questionnaire.answers" 
                            :key="answer.question.id"
                            class="answer-item">
                            
                            <div class="question-info">
                                <div class="question-title">
                                    {{ answer.question.title }}
                                    <el-tag v-if="answer.question.required" size="mini" type="danger">必答</el-tag>
                                </div>
                                <div class="question-type">
                                    类型：{{ getQuestionTypeName(answer.question.type) }}
                                </div>
                            </div>
                            
                            <div class="answer-content">
                                <div class="answer-value">
                                    <span class="answer-label">答案：</span>
                                    <span class="answer-text">{{ formatAnswer(answer.answer) }}</span>
                                </div>
                                <div v-if="answer.answer.answerScore !== null" class="answer-score">
                                    <span class="score-label">得分：</span>
                                    <span class="score-value">{{ answer.answer.answerScore }}</span>
                                </div>
                                <div class="answer-meta">
                                    <span>答题时间：{{ formatDateTime(answer.answer.answerTime) }}</span>
                                    <span v-if="answer.answer.timeSpent">耗时：{{ answer.answer.timeSpent }}秒</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SubmissionDetail',
    props: {
        submissionData: {
            type: Object,
            required: true
        }
    },
    computed: {
        // 按问卷分组的答案
        groupedAnswers() {
            const groups = {};
            
            this.submissionData.answers.forEach(answer => {
                const questionnaireId = answer.questionnaire.id;
                if (!groups[questionnaireId]) {
                    groups[questionnaireId] = {
                        id: questionnaireId,
                        name: answer.questionnaire.name,
                        answers: [],
                        totalScore: 0
                    };
                }
                groups[questionnaireId].answers.push(answer);
                if (answer.answer.answerScore) {
                    groups[questionnaireId].totalScore += answer.answer.answerScore;
                }
            });
            
            return Object.values(groups);
        }
    },
    methods: {
        // 格式化答案显示
        formatAnswer(answer) {
            if (answer.answerType === 'multiple') {
                // 多选答案
                return answer.answerText || answer.answerValue;
            } else if (answer.answerType === 'text') {
                // 文本答案
                return answer.answerText || '未填写';
            } else if (answer.answerType === 'number') {
                // 数字答案
                return answer.answerValue || '未填写';
            } else {
                // 单选等其他类型
                return answer.answerText || answer.answerValue || '未填写';
            }
        },
        
        // 获取题目类型名称
        getQuestionTypeName(type) {
            const typeMap = {
                'single': '单选题',
                'multiple': '多选题',
                'text': '文本题',
                'number': '数字题',
                'date': '日期题',
                'slider': '滑动题',
                'attachment': '附件题'
            };
            return typeMap[type] || type;
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        }
    }
}
</script>

<style lang="scss" scoped>
.submission-detail {
    .submission-overview {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 4px;
        margin-bottom: 20px;
        
        h4 {
            margin: 0 0 15px 0;
            color: #303133;
            font-size: 16px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            
            .overview-item {
                display: flex;
                
                .item-label {
                    color: #606266;
                    margin-right: 10px;
                    min-width: 80px;
                }
                
                .item-value {
                    color: #303133;
                    font-weight: 500;
                    
                    &.score {
                        color: #409eff;
                        font-size: 16px;
                    }
                }
            }
        }
    }
    
    .answers-section {
        h4 {
            margin: 0 0 20px 0;
            color: #303133;
            font-size: 16px;
        }
        
        .questionnaire-group {
            background-color: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            margin-bottom: 20px;
            
            .questionnaire-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                background-color: #f5f7fa;
                border-bottom: 1px solid #e4e7ed;
                
                h5 {
                    margin: 0;
                    color: #303133;
                    font-size: 16px;
                    font-weight: 600;
                }
                
                .questionnaire-meta {
                    span {
                        margin-left: 20px;
                        color: #606266;
                        font-size: 14px;
                    }
                }
            }
            
            .answers-list {
                padding: 20px;
                
                .answer-item {
                    display: flex;
                    padding: 15px 0;
                    border-bottom: 1px solid #f0f0f0;
                    
                    &:last-child {
                        border-bottom: none;
                    }
                    
                    .question-info {
                        flex: 0 0 300px;
                        margin-right: 30px;
                        
                        .question-title {
                            color: #303133;
                            font-weight: 500;
                            margin-bottom: 5px;
                            line-height: 1.5;
                            
                            .el-tag {
                                margin-left: 10px;
                            }
                        }
                        
                        .question-type {
                            color: #909399;
                            font-size: 12px;
                        }
                    }
                    
                    .answer-content {
                        flex: 1;
                        
                        .answer-value {
                            margin-bottom: 8px;
                            
                            .answer-label {
                                color: #606266;
                                margin-right: 10px;
                            }
                            
                            .answer-text {
                                color: #303133;
                                font-weight: 500;
                            }
                        }
                        
                        .answer-score {
                            margin-bottom: 8px;
                            
                            .score-label {
                                color: #606266;
                                margin-right: 10px;
                            }
                            
                            .score-value {
                                color: #409eff;
                                font-weight: 600;
                            }
                        }
                        
                        .answer-meta {
                            span {
                                margin-right: 20px;
                                color: #909399;
                                font-size: 12px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .submission-detail {
        .overview-grid {
            grid-template-columns: 1fr;
        }
        
        .answer-item {
            flex-direction: column !important;
            
            .question-info {
                flex: none !important;
                margin-right: 0 !important;
                margin-bottom: 15px;
            }
        }
    }
}
</style> 
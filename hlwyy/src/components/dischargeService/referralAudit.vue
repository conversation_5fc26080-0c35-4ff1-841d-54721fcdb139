<template>
    <div class="referral-audit-list">
        <div class="list-main-head">
            <div class="fieldName">
                <el-input size="small" v-model="params.name" clearable class="shopValue" placeholder="患者姓名"></el-input>
            </div>
            <div class="fieldName">
                <el-input size="small" v-model="params.phone" clearable class="shopValue" placeholder="患者手机号"></el-input>
            </div>
            <div class="fieldName">
                <el-input size="small" v-model="params.idCard" clearable class="shopValue" placeholder="身份证号"></el-input>
            </div>
            <div class="fieldName">
                <el-select size="small" v-model="params.auditStatus" clearable placeholder="审核状态">
                    <el-option label="未审核" value="0"></el-option>
                    <el-option label="已审核" value="1"></el-option>
                    <el-option label="已拒绝" value="2"></el-option>
                </el-select>
            </div>
            <div class="fieldName">
                <el-select size="small" v-model="params.referralInstitutionCode" clearable placeholder="转介机构">
                    <el-option 
                        v-for="institution in activeInstitutions" 
                        :key="institution.code" 
                        :label="institution.name" 
                        :value="institution.code">
                    </el-option>
                </el-select>
            </div>
            <div class="fieldName handleWrap">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
            </div>
        </div>
        
        <el-table :data="patList" stripe style="width: 100%">
            <el-table-column label="患者姓名" prop="patientName" width="100">
            </el-table-column>
            <el-table-column label="性别" prop="gender" width="60">
                <template slot-scope="scope">
                    <span v-if="scope.row.gender == '男'">男</span>
                    <span v-if="scope.row.gender == '女'">女</span>
                </template>
            </el-table-column>
            <el-table-column label="年龄" prop="age" width="60">
            </el-table-column>
            <el-table-column label="手机号" prop="phone" width="120">
            </el-table-column>
            <el-table-column label="身份证号" prop="idCard" width="180">
                <template slot-scope="scope">
                    <span>{{ scope.row.idCard ? scope.row.idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2') : '' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="出院日期" prop="dischargeDate" width="100">
            </el-table-column>
            <el-table-column label="出院科室" prop="dischargeDepartment" width="120">
            </el-table-column>
            <el-table-column label="转介机构" prop="institutionName" width="150">
                <template slot-scope="scope">
                    <span>{{ scope.row.institutionName || scope.row.referralInstitutionName || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="审核状态" prop="auditStatus" width="100">
                <template slot-scope="scope">
                    <el-tag :type="getAuditStatusType(scope.row.auditStatus)" size="mini">
                        {{ getAuditStatusText(scope.row.auditStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="紧急联系人" prop="emergencyContact" width="100">
            </el-table-column>
            <el-table-column label="联系电话" prop="emergencyPhone" min-width="120">
            </el-table-column>
            <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                    <div class="operation-buttons">
                        <el-button type="primary" size="mini" @click="auditReferralLetter(scope.row)">
                            {{ scope.row.auditStatus === '1' ? '查看转介信' : '审核转介信' }}
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        
        <!--分页-->
        <div class="pagination">
            <el-pagination 
                layout="total, sizes, prev, pager, next, jumper" 
                :total="total" 
                :current-page="params.pageNo" 
                :page-sizes="[10, 20, 50]" 
                @size-change="handleSizeChange" 
                @current-change="handleCurrentChange" 
                :page-size="params.pageSize">
            </el-pagination>
        </div>

        <!-- 转介信审核弹窗 -->
        <el-dialog
            :title="dialogTitle"
            :visible.sync="referralDialogVisible"
            width="90%"
            :before-close="handleReferralDialogClose">
            <div class="referral-letter-content" v-if="currentReferralData">
                <!-- 患者基本信息 -->
                <div class="section">
                    <h3 class="section-title">患者基本信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>姓名：</label>
                            <el-input v-if="isEditing" v-model="currentReferralData.patientName" size="small" style="width: 200px;"></el-input>
                            <span v-else>{{ currentReferralData.patientName }}</span>
                        </div>
                        <div class="info-item">
                            <label>性别：</label>
                            <el-select v-if="isEditing" v-model="currentReferralData.gender" size="small" style="width: 100px;">
                                <el-option label="男" value="男"></el-option>
                                <el-option label="女" value="女"></el-option>
                            </el-select>
                            <span v-else>{{ currentReferralData.gender }}</span>
                        </div>
                        <div class="info-item">
                            <label>出生日期：</label>
                            <el-date-picker v-if="isEditing" v-model="currentReferralData.birthDate" type="date" size="small" style="width: 150px;"></el-date-picker>
                            <span v-else>{{ currentReferralData.birthDate }}</span>
                        </div>
                        <div class="info-item">
                            <label>转介机构：</label>
                            <el-select v-if="isEditing" v-model="currentReferralData.referralInstitutionCode" size="small" style="width: 200px;" @change="onInstitutionChange">
                                <el-option 
                                    v-for="institution in activeInstitutions" 
                                    :key="institution.code" 
                                    :label="institution.name" 
                                    :value="institution.code">
                                </el-option>
                            </el-select>
                            <span v-else>{{ currentReferralData.institutionName || currentReferralData.referralInstitutionName }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- 院内评估结果 -->
                <div class="section">
                    <h3 class="section-title">院内评估结果</h3>
                    <div class="assessment-items">
                        <div class="info-item">
                            <label>ADL（Barthel指数）评分：</label>
                            <el-input-number v-if="isEditing" v-model="currentReferralData.adlScore" :min="0" :max="100" size="small" style="width: 100px;"></el-input-number>
                            <span v-else>{{ currentReferralData.adlScore }}分（详见附表）</span>
                        </div>
                        <div class="info-item">
                            <label>吞咽功能评估：</label>
                            <el-select v-if="isEditing" v-model="currentReferralData.swallowingAssessment" size="small" style="width: 120px;">
                                <el-option label="正常" value="正常"></el-option>
                                <el-option label="轻度异常" value="轻度异常"></el-option>
                                <el-option label="中度异常" value="中度异常"></el-option>
                                <el-option label="重度异常" value="重度异常"></el-option>
                            </el-select>
                            <span v-else>{{ currentReferralData.swallowingAssessment }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- 附件和签章 -->
                <div class="section">
                    <div class="appendix-section">
                        <div class="appendix-item">
                            <strong>附：</strong>
                            <div>自理能力评估（Barthel指数）：详见附表。</div>
                        </div>
                        <div class="hospital-signature">
                            <div class="hospital-name">{{ currentReferralData.hospitalName }}</div>
                            <div class="create-date">日期：{{ currentReferralData.createDate }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleReferralDialogClose">关 闭</el-button>
                <el-button v-if="!isEditing && currentPatient && currentPatient.auditStatus === '0'" type="primary" @click="startEdit">编 辑</el-button>
                <el-button v-if="isEditing" type="success" @click="saveAuditResult">保存审核</el-button>
                <el-button v-if="isEditing" @click="cancelEdit">取 消</el-button>
                <el-button v-if="currentPatient && currentPatient.auditStatus === '1'" type="info" @click="printReferralLetter">打 印</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios'

// 配置axios默认值
axios.defaults.headers.post['Content-Type'] = 'application/json'

export default {
    name: 'ReferralAudit',
    data() {
        return {
            patList: [],
            activeInstitutions: [], // 有效的养老院机构列表
            params: {
                name: "",
                phone: "",
                idCard: "",
                auditStatus: "",
                referralInstitutionCode: "", // 新增转介机构筛选条件
                pageNo: 1,
                pageSize: 10,
            },
            total: 0,
            referralDialogVisible: false,
            currentReferralData: null,
            currentPatient: null,
            isEditing: false,
            originalReferralData: null,
        }
    },
    computed: {
        dialogTitle() {
            if (!this.currentPatient) return '转介信详情';
            if (this.currentPatient.auditStatus === '1') {
                return '转介信详情（已审核）';
            } else {
                return this.isEditing ? '审核转介信' : '转介信审核';
            }
        }
    },
    methods: {
        // 获取转介信列表
        async getPatList() {
            try {
                const requestParams = {
                    patientName: this.params.name || null,
                    phone: this.params.phone || null,
                    idCard: this.params.idCard || null,
                    auditStatus: this.params.auditStatus || null,
                    referralInstitutionCode: this.params.referralInstitutionCode || null,
                }

                console.log('转介审核查询参数：', requestParams)

                const response = await axios.post('/api/emr/referral-audit/list', requestParams)
                
                if (response.data.success && response.data.code === 200) {
                    this.patList = response.data.data || []
                    this.total = this.patList.length
                    console.log('转介审核列表获取成功：', this.patList)
                } else {
                    this.patList = []
                    this.total = 0
                    this.$message.error(response.data.message || '获取转介信列表失败')
                }
            } catch (error) {
                console.error('获取转介信列表失败:', error)
                this.patList = []
                this.total = 0
                this.$message.error('获取转介信列表失败，请检查网络连接')
            }
        },



        // 加载有效的养老院机构列表
        async loadActiveInstitutions() {
            try {
                const response = await axios.post('/api/emr/nursing-home/getAllActiveInstitutions')
                if (response.data.success) {
                    this.activeInstitutions = response.data.data || []
                    console.log('养老院机构列表加载成功：', this.activeInstitutions)
                } else {
                    this.activeInstitutions = []
                    this.$message.error(response.data.message || '获取养老院机构列表失败')
                }
            } catch (error) {
                console.error('加载养老院机构列表失败:', error)
                this.activeInstitutions = []
                this.$message.error('获取养老院机构列表失败，请检查网络连接')
            }
        },

        // 获取审核状态显示文本
        getAuditStatusText(status) {
            const statusMap = {
                '0': '未审核',
                '1': '已审核',
                '2': '已拒绝'
            }
            return statusMap[status] || '未知'
        },

        // 获取审核状态标签类型
        getAuditStatusType(status) {
            const typeMap = {
                '0': 'warning',
                '1': 'success',
                '2': 'danger'
            }
            return typeMap[status] || 'info'
        },

        // 机构选择变化处理
        onInstitutionChange(institutionCode) {
            const selectedInstitution = this.activeInstitutions.find(inst => inst.code === institutionCode)
            if (selectedInstitution && this.currentReferralData) {
                this.currentReferralData.referralInstitutionName = selectedInstitution.name
                this.currentReferralData.institutionName = selectedInstitution.name
            }
        },

        // 计算年龄
        calculateAge(birthDate) {
            if (!birthDate) return ''
            const birth = new Date(birthDate)
            const today = new Date()
            let age = today.getFullYear() - birth.getFullYear()
            const monthDiff = today.getMonth() - birth.getMonth()
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                age--
            }
            return age
        },

        handleSizeChange(val) {
            this.params.pageSize = val;
            this.getPatList();
        },
        
        handleCurrentChange(val) {
            this.params.pageNo = val;
            this.getPatList();
        },
        
        searchList() {
            this.params.pageNo = 1;
            this.getPatList();
        },
        
        clearParams() {
            this.params = {
                name: "",
                phone: "",
                idCard: "",
                auditStatus: "",
                referralInstitutionCode: "",
                pageNo: 1,
                pageSize: 10,
            }
            this.getPatList();
        },

        // 审核转介信
        auditReferralLetter(row) {
            this.currentPatient = row;
            this.currentReferralData = this.generateReferralData(row);
            this.originalReferralData = JSON.parse(JSON.stringify(this.currentReferralData));
            this.isEditing = row.auditStatus === '0';
            this.referralDialogVisible = true;
        },

        // 开始编辑
        startEdit() {
            this.isEditing = true;
            this.originalReferralData = JSON.parse(JSON.stringify(this.currentReferralData));
        },

        // 取消编辑
        cancelEdit() {
            this.isEditing = false;
            this.currentReferralData = JSON.parse(JSON.stringify(this.originalReferralData));
        },

        // 保存审核结果
        async saveAuditResult() {
            try {
                const currentUser = this.$store.getters.currentUser || {};
                
                // 构建审核数据，包含患者完整信息
                const auditData = {
                    auditStatus: '1',
                    auditorId: currentUser.id || null,
                    auditorName: currentUser.name || '',
                    auditRemarks: '审核通过',
                    // 添加患者信息，用于临时记录创建
                    patientName: this.currentPatient.patientName,
                    gender: this.currentPatient.gender,
                    phone: this.currentPatient.phone,
                    idCard: this.currentPatient.idCard,
                    referralInstitutionCode: this.currentPatient.referralInstitutionCode,
                    referralInstitutionName: this.currentPatient.referralInstitutionName,
                    dischargeDepartment: this.currentPatient.dischargeDepartment,
                    primaryDiagnosis: this.currentPatient.primaryDiagnosis,
                    emergencyContact: this.currentPatient.emergencyContact,
                    emergencyPhone: this.currentPatient.emergencyPhone,
                    contactRelation: this.currentPatient.contactRelation
                }

                // 处理临时ID的情况
                let requestId = this.currentPatient.id;
                if (this.currentPatient.isTempRecord || !requestId || requestId === 0) {
                    // 使用患者ID作为临时ID
                    requestId = `TEMP_${this.currentPatient.patientId}`;
                }

                const response = await axios.put(`/api/emr/referral-audit/${requestId}/audit`, auditData)
                
                if (response.data.success && response.data.code === 200) {
                    // 更新当前患者的审核状态
                    this.currentPatient.auditStatus = '1';
                    
                    // 如果是临时记录，更新为真实记录
                    if (this.currentPatient.isTempRecord) {
                        this.currentPatient.isTempRecord = false;
                        // 如果后端返回了新的ID，更新它
                        if (response.data.data && response.data.data.id) {
                            this.currentPatient.id = response.data.data.id;
                        }
                    }
                    
                    // 更新列表中的数据
                    const index = this.patList.findIndex(item => 
                        item.patientId === this.currentPatient.patientId
                    );
                    if (index !== -1) {
                        this.patList[index].auditStatus = '1';
                        this.patList[index].isTempRecord = false;
                        if (response.data.data && response.data.data.id) {
                            this.patList[index].id = response.data.data.id;
                        }
                    }
                    
                    this.isEditing = false;
                    this.$message.success('转介信审核完成！');
                } else {
                    this.$message.error(response.data.message || '审核失败')
                }
                
            } catch (error) {
                console.error('保存审核结果失败:', error);
                this.$message.error('保存审核结果失败');
            }
        },

        // 生成转介信数据（与养老院门户患者列表保持一致）
        generateReferralData(patient) {
            const genderText = patient.gender === '1' ? '男' : '女';
            const birthYear = new Date().getFullYear() - patient.age;
            const idCardLast4 = patient.idCard ? patient.idCard.slice(-4) : '****';
            
            // 根据患者姓名和信息生成模拟数据（与养老院门户保持一致）
            const referralData = {
                // 患者基本信息
                patientName: patient.patientName,
                gender: genderText,
                birthDate: `${birthYear}年06月15日`,
                idCardLast4: idCardLast4,
                admissionDate: patient.admissionDate || '2024年01月15日',
                dischargeDate: '2024年06月01日',
                admissionDepartment: this.getDepartmentByPatient(patient),
                dischargeDepartment: this.getDepartmentByPatient(patient),
                admissionChiefComplaint: this.getChiefComplaint(patient),
                primaryDiagnosis: this.getPrimaryDiagnosis(patient),
                otherDiagnosis: '详见出院小结',
                
                // 患者去向
                destinations: {
                    rehabilitation: false,
                    palliative: false,
                    daycare: false,
                    nursing: true, // 养老机构床位
                    homecare: false,
                    longcare: false,
                    homebed: false
                },
                
                // 机构信息
                institutionName: patient.institutionName || '上海XX养老机构',
                institutionContact: patient.institutionContact || '张护士长',
                institutionPhone: patient.institutionPhone || '021-12345678',
                
                // 居家照护情况
                homeCare: {
                    spouse: true,
                    housekeeper: false,
                    hourly: false,
                    caregiver: false,
                    other: false,
                    otherDesc: ''
                },
                
                // 联系方式
                patientPhone: patient.phone,
                emergencyContact: patient.emergencyContact,
                emergencyPhone: patient.emergencyPhone,
                
                // 院内评估结果
                adlScore: this.getADLScore(patient),
                pressureRisk: this.getPressureRisk(patient),
                dvtRisk: this.getDvtRisk(patient),
                cognition: this.getCognition(patient),
                cognitionDesc: this.getCognitionDesc(patient),
                
                // 服务资源
                serviceResources: {
                    hospitalService: {
                        name: '四院转介服务',
                        contact: '慢病管理中心全科医生：陈医生',
                        phone: '021-12345678'
                    },
                    communityService: {
                        name: '社区服务',
                        contact: '凉城新村街道社工：郑女士',
                        phone: '021-65284030'
                    },
                    healthCenter: {
                        name: '社区卫生服务中心',
                        contact: '家庭医生：李医生',
                        phone: '021-12333371111'
                    },
                    specialistFollowUp: {
                        name: '四院专科随访',
                        contact: '健康管理师：魏护士',
                        phone: '021-55603198'
                    }
                },
                
                // 责任签收
                responsibility: {
                    hospitalContact: '陈医生',
                    hospitalPhone: '021-12345678',
                    platformId: 'HD001'
                },
                
                // 其他信息
                createDate: '2024年06月01日',
                hospitalName: '上海市第四人民医院慢病管理中心'
            };
            
            return referralData;
        },
        
        // 根据患者信息获取科室
        getDepartmentByPatient(patient) {
            const departments = ['神经内科', '心血管科', '内分泌科', '康复科', '老年医学科'];
            return departments[Math.floor(Math.random() * departments.length)];
        },
        
        // 根据患者信息获取主要诊断
        getPrimaryDiagnosis(patient) {
            const diagnoses = ['冠心病', '高血压', '糖尿病', '脑梗塞', '帕金森病', '阿尔茨海默病'];
            return diagnoses[Math.floor(Math.random() * diagnoses.length)];
        },
        
        // 根据患者信息获取主诉
        getChiefComplaint(patient) {
            const complaints = ['头晕', '胸闷', '乏力', '行走不稳', '记忆力减退', '肢体麻木'];
            return complaints[Math.floor(Math.random() * complaints.length)];
        },
        
        // 获取ADL评分
        getADLScore(patient) {
            const scores = [60, 70, 80, 90, 100];
            return scores[Math.floor(Math.random() * scores.length)];
        },
        
        // 获取压疮风险
        getPressureRisk(patient) {
            const risks = ['低风险', '中风险', '高风险'];
            const index = Math.min(parseInt(patient.healthStatus || '2') - 1, 2);
            return risks[index] || '低风险';
        },
        
        // 获取DVT风险
        getDvtRisk(patient) {
            const risks = ['低风险', '中风险', '高风险'];
            const index = Math.min(parseInt(patient.healthStatus || '2') - 1, 2);
            return risks[index] || '低风险';
        },
        
        // 获取认知状态
        getCognition(patient) {
            const cognitions = ['无', '轻度', '中度', '重度'];
            const index = Math.min(parseInt(patient.healthStatus || '2') - 1, 3);
            return cognitions[index] || '无';
        },
        
        // 获取认知描述
        getCognitionDesc(patient) {
            const descs = ['', '轻度认知障碍', '中度认知障碍', '重度认知障碍'];
            const index = Math.min(parseInt(patient.healthStatus || '2') - 1, 3);
            return descs[index] || '';
        },



        // 关闭弹窗
        handleReferralDialogClose() {
            this.referralDialogVisible = false;
            this.currentReferralData = null;
            this.currentPatient = null;
            this.isEditing = false;
            this.originalReferralData = null;
        },

        // 打印转介信
        printReferralLetter() {
            this.$message.success('转介信打印功能开发中...');
        }
    },
    
    mounted() {
        // 页面加载时初始化数据
        this.loadActiveInstitutions();
        this.getPatList();
    }
}
</script>

<style lang="scss" scoped>
@import '~css/public.scss';

.referral-audit-list {
    @include titleAndTable;
    
    .list-main-head {
        .fieldName {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .handleWrap {
            .el-button {
                margin-right: 10px;
            }
        }
    }

    .operation-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        
        /deep/ .el-button {
            margin: 0 !important;
            padding: 5px 10px !important;
            font-size: 13px !important;
            min-width: 80px;
            text-align: center;
        }
        
        /deep/ .el-button + .el-button {
            margin-left: 0 !important;
        }
    }

    .referral-letter-content {
        .section {
            margin-bottom: 20px;
            
            .section-title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
                border-bottom: 2px solid #409EFF;
                padding-bottom: 5px;
            }
            
            .info-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                
                .info-item {
                    display: flex;
                    align-items: center;
                    
                    label {
                        font-weight: bold;
                        margin-right: 10px;
                        min-width: 100px;
                        color: #606266;
                    }
                }
            }
            
            .assessment-items {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                gap: 15px;
                
                .info-item {
                    display: flex;
                    align-items: center;
                    
                    label {
                        font-weight: bold;
                        margin-right: 10px;
                        min-width: 150px;
                        color: #606266;
                    }
                }
            }
        }
        
        .appendix-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            
            .appendix-item {
                flex: 1;
            }
            
            .hospital-signature {
                text-align: right;
                
                .hospital-name {
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                
                .create-date {
                    color: #666;
                }
            }
        }
    }
}
</style> 
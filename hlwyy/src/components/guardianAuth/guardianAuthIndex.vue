<template>
    <div class="guardian-auth">
        <!-- 左侧申请表单 -->
        <el-card class="auth-form">
            <div slot="header">
                <span>监护人授权申请</span>
            </div>
            <el-form :model="authForm" ref="authForm" label-width="100px" :rules="rules">
                <el-form-item label="患者ID" prop="patientId" style="display: none;">
                    <el-input v-model="authForm.patientId" disabled></el-input>
                </el-form-item>
                <el-form-item label="患者姓名" prop="patientName">
                    <el-input v-model="authForm.patientName" placeholder="请选择患者" disabled>
                        <el-button slot="append" icon="el-icon-search" @click="showPatientDialog"></el-button>
                    </el-input>
                </el-form-item>
                <el-form-item label="患者身份证号" prop="patientIdCard">
                    <el-input v-model="authForm.patientIdCard" disabled></el-input>
                </el-form-item>
                <el-form-item label="门诊号码" prop="outpatientNo">
                    <el-input v-model="authForm.outpatientNo" disabled></el-input>
                </el-form-item>
                <el-form-item label="监护人姓名" prop="name">
                    <el-input v-model="authForm.name" placeholder="请输入监护人姓名"></el-input>
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-radio-group v-model="authForm.gender">
                        <el-radio label="male">男</el-radio>
                        <el-radio label="female">女</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="与患者关系" prop="relationship">
                    <el-select v-model="authForm.relationship" placeholder="请选择与患者关系" style="width: 100%">
                        <el-option label="父亲" value="father"></el-option>
                        <el-option label="母亲" value="mother"></el-option>
                        <el-option label="儿子" value="son"></el-option>
                        <el-option label="女儿" value="daughter"></el-option>
                        <el-option label="配偶" value="spouse"></el-option>
                        <el-option label="兄弟姐妹" value="sibling"></el-option>
                        <el-option label="其他亲属" value="other_relative"></el-option>
                        <el-option label="法定监护人" value="legal_guardian"></el-option>
                        <el-option label="其他" value="other"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="身份证号" prop="idCard">
                    <el-input v-model="authForm.idCard" placeholder="请输入身份证号"></el-input>
                </el-form-item>
                <el-form-item label="联系电话" prop="phone">
                    <el-input v-model="authForm.phone" placeholder="请输入联系电话"></el-input>
                </el-form-item>
                <el-form-item label="地址" prop="address">
                    <el-input v-model="authForm.address" placeholder="请输入地址"></el-input>
                </el-form-item>
                <!--<el-form-item label="紧急联系人" prop="emergencyContact">
                    <el-switch v-model="authForm.emergencyContact"></el-switch>
                </el-form-item>
                <el-form-item label="授权级别" prop="accessLevel">
                    <el-select v-model="authForm.accessLevel" placeholder="请选择授权级别" style="width: 100%">
                        <el-option label="完全授权" value="full"></el-option>
                        <el-option label="部分授权" value="partial"></el-option>
                        <el-option label="限制授权" value="limited"></el-option>
                    </el-select>
                </el-form-item>-->
                <el-form-item label="授权文档" prop="authorizationDocuments">
                    <el-upload
                        action="/api/upload"
                        :on-success="handleUploadSuccess"
                        :on-error="handleUploadError"
                        :before-upload="beforeUpload"
                        multiple>
                        <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="过期时间" prop="expirationDate">
                    <el-date-picker
                        v-model="authForm.expirationDate"
                        type="datetime"
                        placeholder="选择过期时间"
                        value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="备注" prop="remarks">
                    <el-input type="textarea" v-model="authForm.remarks" placeholder="请输入备注"></el-input>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm">提交</el-button>
                    <el-button @click="resetForm">取消</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 右侧授权列表 -->
        <el-card class="auth-list">
            <div slot="header" class="list-header">
                <div class="status-filter">
                    <el-radio-group v-model="listStatus" @change="handleStatusChange">
                        <el-radio-button label="pending">待审核</el-radio-button>
                        <el-radio-button label="approved">已审核通过</el-radio-button>
                        <el-radio-button label="rejected">已审核未通过</el-radio-button>
                    </el-radio-group>
                </div>
            </div>

            <el-table
                :data="authList"
                border
                style="width: 100%">
                <el-table-column
                    prop="sequence"
                    label="序号"
                    width="80">
                </el-table-column>
                <el-table-column
                    prop="name"
                    label="监护人姓名"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="relationship"
                    label="与患者关系"
                    width="120">
                    <template slot-scope="scope">
                        {{ getRelationshipText(scope.row.relationship) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="idCard"
                    label="身份证号"
                    width="180">
                </el-table-column>
                <el-table-column
                    prop="phone"
                    label="联系电话"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="address"
                    label="地址"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="accessLevel"
                    label="授权类型"
                    width="120">
                    <template slot-scope="scope">
                        {{ getAuthReason(scope.row.accessLevel) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="authorizationDate"
                    label="授权日期"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="expirationDate"
                    label="到期日期"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="authorizedBy"
                    label="授权人"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="状态"
                    width="100">
                    <template slot-scope="scope">
                        <el-tag :type="getStatusType(scope.row.status)">
                            {{ getStatusText(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 患者选择对话框 -->
        <el-dialog
            title="选择患者"
            :visible.sync="patientDialogVisible"
            width="70%"
            :before-close="handlePatientDialogClose">
            <div class="patient-search">
                <el-form :inline="true" :model="patientSearchForm" class="demo-form-inline">
                    <el-form-item label="身份证号">
                        <el-input v-model="patientSearchForm.idCard" placeholder="请输入身份证号"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名">
                        <el-input v-model="patientSearchForm.name" placeholder="请输入姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="门诊号码">
                        <el-input v-model="patientSearchForm.outpatientNo" placeholder="请输入门诊号码"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchPatients">查询</el-button>
                        <el-button @click="resetPatientSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-table
                :data="patientList"
                border
                style="width: 100%"
                @row-click="selectPatient">
                <el-table-column
                    prop="name"
                    label="姓名"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="gender"
                    label="性别"
                    width="80">
                    <template slot-scope="scope">
                        {{ scope.row.gender === 'male' ? '男' : '女' }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="age"
                    label="年龄"
                    width="80">
                </el-table-column>
                <el-table-column
                    prop="birthDate"
                    label="出生年月"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="idCard"
                    label="身份证号"
                    width="180">
                </el-table-column>
                <el-table-column
                    prop="outpatientNo"
                    label="门诊号码"
                    width="120">
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination
                    @size-change="handlePatientSizeChange"
                    @current-change="handlePatientCurrentChange"
                    :current-page="patientPagination.currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="patientPagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="patientPagination.total">
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { commonAjax } from '@/api/api'
import axios from 'axios'

export default {
    name: 'GuardianAuth',
    data() {
        // 计算6个月后的日期
        const getDefaultExpirationDate = () => {
            const date = new Date()
            date.setMonth(date.getMonth() + 6)
            return date.toISOString().slice(0, 19).replace('T', ' ')
        }

        // 身份证号验证规则
        const validateIdCard = (rule, value, callback) => {
            const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
            if (!value) {
                callback(new Error('请输入身份证号'))
            } else if (!reg.test(value)) {
                callback(new Error('请输入正确的身份证号'))
            } else {
                callback()
            }
        }
        // 手机号验证规则
        const validatePhone = (rule, value, callback) => {
            const reg = /^1[3-9]\d{9}$/
            if (!value) {
                callback(new Error('请输入手机号'))
            } else if (!reg.test(value)) {
                callback(new Error('请输入正确的手机号'))
            } else {
                callback()
            }
        }
        return {
            // 申请表单数据
            authForm: {
                patientId: '',
                patientName: '',
                patientIdCard: '',
                outpatientNo: '',
                name: '',
                gender: 'male',
                relationship: '',
                idCard: '',
                phone: '',
                address: '',
                emergencyContact: true,
                accessLevel: 'full',
                authorizationDocuments: [],
                expirationDate: getDefaultExpirationDate(),
                remarks: ''
            },
            // 表单验证规则
            rules: {
                name: [
                    { required: true, message: '请输入监护人姓名', trigger: 'blur' }
                ],
                idCard: [
                    { required: true, validator: validateIdCard, trigger: 'blur' }
                ],
                phone: [
                    { required: true, validator: validatePhone, trigger: 'blur' }
                ],
                relationship: [
                    { required: true, message: '请选择与患者关系', trigger: 'change' }
                ],
                accessLevel: [
                    { required: true, message: '请选择授权级别', trigger: 'change' }
                ]
            },
            // 列表状态
            listStatus: 'pending',
            // 授权列表数据
            authList: [],
            // 分页数据
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
            // 患者选择对话框
            patientDialogVisible: false,
            // 患者搜索表单
            patientSearchForm: {
                idCard: '',
                name: '',
                outpatientNo: ''
            },
            // 患者列表数据
            patientList: [],
            // 患者分页数据
            patientPagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            }
        }
    },
    methods: {
        // 提交表单
        async submitForm() {
            this.$refs.authForm.validate(async (valid) => {
                if (valid) {
                    try {
                        console.log('开始提交表单，数据：', this.authForm)
                        
                        // 测试服务器连接
                        try {
                            const testResponse = await axios.get('/')
                            console.log('服务器连接测试成功：', testResponse)
                        } catch (testError) {
                            console.error('服务器连接测试失败：', testError)
                        }

                        const response = await axios({
                            method: 'post',
                            url: '/api/guardian/add',
                            data: {
                                patientId: 'P67890',
                                name: this.authForm.name,
                                gender: this.authForm.gender,
                                relationship: this.authForm.relationship,
                                idCard: this.authForm.idCard,
                                phone: this.authForm.phone,
                                address: this.authForm.address,
                                emergencyContact: this.authForm.emergencyContact,
                                accessLevel: this.authForm.accessLevel,
                                authorizationDocuments: this.authForm.authorizationDocuments,
                                expirationDate: this.authForm.expirationDate,
                                remarks: this.authForm.remarks
                            },
                            timeout: 10000,
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            },
                            withCredentials: false
                        })

                        console.log('服务器响应：', response)

                        if (response.data.code === "200") {
                            this.$message.success('提交成功')
                            this.resetForm()
                            // 刷新列表
                            this.loadAuthList()
                        } else {
                            this.$message.error(response.data.message || '提交失败')
                        }
                    } catch (error) {
                        console.error('提交失败，详细错误：', error)
                        console.error('错误类型：', error.constructor.name)
                        console.error('错误堆栈：', error.stack)
                        
                        if (error.response) {
                            console.error('错误响应：', error.response)
                            console.error('错误响应状态：', error.response.status)
                            console.error('错误响应数据：', error.response.data)
                            this.$message.error(`服务器错误：${error.response.status}`)
                        } else if (error.request) {
                            console.error('请求错误：', error.request)
                            console.error('请求配置：', error.config)
                            console.error('请求URL：', error.config && error.config.url)
                            console.error('请求方法：', error.config && error.config.method)
                            console.error('请求数据：', error.config && error.config.data)
                            
                            // 检查网络状态
                            if (!navigator.onLine) {
                                this.$message.error('网络连接已断开，请检查网络设置')
                            } else {
                                this.$message.error('无法连接到服务器，请检查：\n1. 服务器是否运行\n2. 地址是否正确\n3. 网络连接是否正常')
                            }
                        } else {
                            console.error('错误信息：', error.message)
                            this.$message.error('请求失败：' + error.message)
                        }
                    }
                }
            })
        },
        // 重置表单
        resetForm() {
            this.$refs.authForm.resetFields()
        },
        // 状态切换
        handleStatusChange(value) {
            this.pagination.currentPage = 1
            this.loadAuthList()
        },
        // 加载授权列表
        async loadAuthList() {
            try {
                const status = this.getStatusValue(this.listStatus)
                console.log('请求状态：', this.listStatus, '转换后：', status)
                
                const response = await axios({
                    method: 'post',
                    url: '/api/guardian/list',
                    data: {
                        patientId: 'P67890',
                        status: status,
                        pageNum: this.pagination.currentPage,
                        pageSize: this.pagination.pageSize
                    },
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    withCredentials: false
                })
                
                console.log('完整响应：', response)
                console.log('响应数据：', response.data)
                
                if (response.data.code === "200") {
                    const listData = response.data.data.list
                    console.log('原始数据：', listData)
                    
                    if (listData && listData.length > 0) {
                        this.authList = listData.map((item, index) => {
                            console.log('处理数据项：', item)
                            return {
                                sequence: index + 1,
                                name: item.name,
                                relationship: item.relationship,
                                idCard: item.idCard,
                                phone: item.phone,
                                address: item.address,
                                accessLevel: item.accessLevel,
                                authorizationDate: item.authorizationDate,
                                expirationDate: item.expirationDate,
                                authorizedBy: item.authorizedBy || '-',
                                status: item.status
                            }
                        })
                    } else {
                        this.authList = []
                        console.log('没有数据')
                    }
                    
                    this.pagination.total = response.data.data.total
                    this.pagination.currentPage = response.data.data.pageNum
                    this.pagination.pageSize = response.data.data.pageSize
                } else {
                    this.$message.error(response.data.message || '获取数据失败')
                }
            } catch (error) {
                console.error('请求失败，详细错误：', error)
                console.error('错误类型：', error.constructor.name)
                console.error('错误堆栈：', error.stack)
                
                if (error.response) {
                    console.error('错误响应：', error.response)
                    this.$message.error(`服务器错误：${error.response.status}`)
                } else if (error.request) {
                    console.error('请求错误：', error.request)
                    this.$message.error('无法连接到服务器，请检查网络连接')
                } else {
                    console.error('错误信息：', error.message)
                    this.$message.error('请求失败：' + error.message)
                }
            }
        },
        // 获取状态值
        getStatusValue(status) {
            const statusMap = {
                'pending': 'pending',
                'approved': 'active',
                'rejected': 'rejected'
            }
            console.log('转换前的状态：', status)
            console.log('转换后的状态：', statusMap[status] || 'pending')
            return statusMap[status] || 'pending'
        },
        // 获取授权原因
        getAuthReason(accessLevel) {
            const reasonMap = {
                'full': '完全授权',
                'partial': '部分授权',
                'limited': '限制授权'
            }
            return reasonMap[accessLevel] || accessLevel
        },
        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'pending': 'warning',
                'rejected': 'danger'
            }
            console.log('状态标签类型：', status, typeMap[status] || 'info')
            return typeMap[status] || 'info'
        },
        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                'active': '已通过',
                'pending': '待审核',
                'rejected': '已拒绝'
            }
            console.log('状态文本：', status, textMap[status] || status)
            return textMap[status] || status
        },
        // 处理上传成功
        handleUploadSuccess(response, file) {
            console.log('上传成功，响应：', response)
            this.$message.success('上传成功')
            this.authForm.authorizationDocuments.push(response.data)
        },
        // 处理上传错误
        handleUploadError(error) {
            console.error('上传错误：', error)
            this.$message.error('上传失败')
        },
        // 处理上传前
        beforeUpload(file) {
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
            const isLt500K = file.size / 1024 / 1024 < 0.5

            if (!isJPG) {
                this.$message.error('上传文件必须是jpg/png格式')
            }
            if (!isLt500K) {
                this.$message.error('上传文件大小不能超过500kb')
            }
            return isJPG && isLt500K
        },
        // 获取关系显示文本
        getRelationshipText(relationship) {
            const relationshipMap = {
                'father': '父亲',
                'mother': '母亲',
                'son': '儿子',
                'daughter': '女儿',
                'spouse': '配偶',
                'sibling': '兄弟姐妹',
                'other_relative': '其他亲属',
                'legal_guardian': '法定监护人',
                'other': '其他'
            }
            return relationshipMap[relationship] || relationship
        },
        // 显示患者选择对话框
        showPatientDialog() {
            this.patientDialogVisible = true
            this.loadPatients()
        },
        // 关闭患者选择对话框
        handlePatientDialogClose() {
            this.patientDialogVisible = false
            this.resetPatientSearch()
        },
        // 加载患者列表
        async loadPatients() {
            try {
                // 使用本地模拟数据
                this.patientList = [
                    {
                        patientId: 'P67890',
                        name: '陈晓',
                        gender: 'female',
                        age: 35,
                        birthDate: '1988-05-15',
                        idCard: '330101198805151234',
                        outpatientNo: 'M123456'
                    },
                    {
                        patientId: 'P67891',
                        name: '王雄',
                        gender: 'female',
                        age: 28,
                        birthDate: '1995-08-20',
                        idCard: '330101199508201234',
                        outpatientNo: 'M123457'
                    },
                    {
                        patientId: 'P67892',
                        name: '张雨欣',
                        gender: 'female',
                        age: 42,
                        birthDate: '1981-03-10',
                        idCard: '330101198103101234',
                        outpatientNo: 'M123458'
                    }
                ]
                this.patientPagination.total = this.patientList.length
            } catch (error) {
                console.error('获取患者列表失败：', error)
                // 不再显示错误提示
            }
        },
        // 搜索患者
        searchPatients() {
            this.patientPagination.currentPage = 1
            this.loadPatients()
        },
        // 重置患者搜索
        resetPatientSearch() {
            this.patientSearchForm = {
                idCard: '',
                name: '',
                outpatientNo: ''
            }
            this.patientPagination.currentPage = 1
            this.loadPatients()
        },
        // 选择患者
        selectPatient(row) {
            this.authForm.patientId = row.patientId
            this.authForm.patientName = row.name
            this.authForm.patientIdCard = row.idCard
            this.authForm.outpatientNo = row.outpatientNo
            this.patientDialogVisible = false
        },
        // 处理患者分页大小变化
        handlePatientSizeChange(val) {
            this.patientPagination.pageSize = val
            this.loadPatients()
        },
        // 处理患者页码变化
        handlePatientCurrentChange(val) {
            this.patientPagination.currentPage = val
            this.loadPatients()
        }
    },
    mounted() {
        this.loadAuthList()
    }
}
</script>

<style lang="scss" scoped>
.guardian-auth {
    display: flex;
    padding: 20px;
    gap: 20px;

    .auth-form {
        flex: 0 0 400px;
        .el-checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
    }

    .auth-list {
        flex: 1;
        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
    }
}
</style> 
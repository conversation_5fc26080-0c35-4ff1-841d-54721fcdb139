<template>
    <div class="guardian-auth-list">
        <el-card>
            <div slot="header" class="list-header">
                <div class="status-filter">
                    <el-radio-group v-model="listStatus" @change="handleStatusChange">
                        <el-radio-button label="pending">待审核</el-radio-button>
                        <el-radio-button label="approved">已审核通过</el-radio-button>
                        <el-radio-button label="rejected">已审核未通过</el-radio-button>
                    </el-radio-group>
                </div>
                <div class="operations" v-if="listStatus === 'pending'">
                    <el-button 
                        type="primary" 
                        size="small" 
                        @click="handleApprove"
                        :disabled="!hasSelectedItems">
                        通过
                    </el-button>
                    <el-button 
                        type="danger" 
                        size="small" 
                        @click="handleReject"
                        :disabled="!hasSelectedItems">
                        不通过
                    </el-button>
                </div>
            </div>

            <el-table
                :data="authList"
                border
                style="width: 100%"
                @selection-change="handleSelectionChange"
                ref="table">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="sequence"
                    label="序号"
                    width="80">
                </el-table-column>
                <el-table-column
                    prop="name"
                    label="监护人姓名"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="relationship"
                    label="与患者关系"
                    width="120">
                    <template slot-scope="scope">
                        {{ getRelationshipText(scope.row.relationship) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="idCard"
                    label="身份证号"
                    width="180">
                </el-table-column>
                <el-table-column
                    prop="phone"
                    label="联系电话"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="address"
                    label="地址"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="accessLevel"
                    label="授权类型"
                    width="120">
                    <template slot-scope="scope">
                        {{ getAuthReason(scope.row.accessLevel) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="authorizationDate"
                    label="授权日期"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="expirationDate"
                    label="到期日期"
                    width="160">
                </el-table-column>
                <el-table-column
                    prop="authorizedBy"
                    label="授权人"
                    width="120">
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="状态"
                    width="100">
                    <template slot-scope="scope">
                        <el-tag :type="getStatusType(scope.row.status)">
                            {{ getStatusText(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="reason"
                    label="拒绝原因"
                    width="200"
                    v-if="listStatus === 'rejected'">
                </el-table-column>
            </el-table>

            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="pagination.currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="pagination.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total">
                </el-pagination>
            </div>
        </el-card>

        <!-- 添加审核不通过对话框 -->
        <el-dialog
            title="审核不通过"
            :visible.sync="rejectDialogVisible"
            width="30%"
            :before-close="handleRejectDialogClose">
            <el-form :model="rejectForm" label-width="100px">
                <el-form-item label="不通过原因">
                    <el-input
                        type="textarea"
                        v-model="rejectForm.reason"
                        :rows="4"
                        placeholder="请输入审核不通过的原因">
                    </el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleRejectDialogClose">取 消</el-button>
                <el-button type="primary" @click="confirmReject">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios'

export default {
    name: 'GuardianAuthList',
    data() {
        return {
            // 列表状态
            listStatus: 'pending',
            // 授权列表数据
            authList: [],
            // 分页数据
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
            // Added for the new condition
            hasSelectedItems: false,
            // 添加对话框相关数据
            rejectDialogVisible: false,
            rejectForm: {
                reason: ''
            },
            selectedItemForReject: null
        }
    },
    methods: {
        // 状态切换
        handleStatusChange(value) {
            this.pagination.currentPage = 1
            this.loadAuthList()
        },
        // 加载授权列表
        async loadAuthList() {
            try {
                const status = this.getStatusValue(this.listStatus)
                console.log('请求状态：', this.listStatus, '转换后：', status)
                
                const response = await axios({
                    method: 'post',
                    url: '/api/guardian/list',
                    data: {
                        patientId: 'P67890',
                        status: status,
                        pageNum: this.pagination.currentPage,
                        pageSize: this.pagination.pageSize
                    },
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                
                console.log('完整响应：', response)
                console.log('响应数据：', response.data)
                
                if (response.data.code === "200") {
                    const listData = response.data.data.list
                    console.log('原始数据：', listData)
                    
                    if (listData && listData.length > 0) {
                        this.authList = listData.map((item, index) => {
                            console.log('处理数据项：', item)
                            return {
                                sequence: index + 1,
                                guardianId: item.guardianId,
                                name: item.name,
                                relationship: item.relationship,
                                idCard: item.idCard,
                                phone: item.phone,
                                address: item.address,
                                accessLevel: item.accessLevel,
                                authorizationDate: item.authorizationDate,
                                expirationDate: item.expirationDate,
                                authorizedBy: item.authorizedBy || '-',
                                status: item.status,
                                remarks: item.remarks,
                                operator: item.operator,
                                reason: item.reason || '-'
                            }
                        })
                    } else {
                        this.authList = []
                        console.log('没有数据')
                    }
                    
                    this.pagination.total = response.data.data.total
                    this.pagination.currentPage = response.data.data.pageNum
                    this.pagination.pageSize = response.data.data.pageSize
                } else {
                    this.$message.error(response.data.message || '获取数据失败')
                }
            } catch (error) {
                console.error('请求失败，详细错误：', error)
                console.error('错误类型：', error.constructor.name)
                console.error('错误堆栈：', error.stack)
                
                if (error.response) {
                    console.error('错误响应：', error.response)
                    this.$message.error(`服务器错误：${error.response.status}`)
                } else if (error.request) {
                    console.error('请求错误：', error.request)
                    this.$message.error('无法连接到服务器，请检查网络连接')
                } else {
                    console.error('错误信息：', error.message)
                    this.$message.error('请求失败：' + error.message)
                }
            }
        },
        // 获取状态值
        getStatusValue(status) {
            const statusMap = {
                'pending': 'pending',
                'approved': 'active',
                'rejected': 'rejected'
            }
            console.log('转换前的状态：', status)
            console.log('转换后的状态：', statusMap[status] || 'pending')
            return statusMap[status] || 'pending'
        },
        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                'active': 'success',
                'pending': 'warning',
                'rejected': 'danger'
            }
            console.log('状态标签类型：', status, typeMap[status] || 'info')
            return typeMap[status] || 'info'
        },
        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                'active': '已通过',
                'pending': '待审核',
                'rejected': '已拒绝'
            }
            console.log('状态文本：', status, textMap[status] || status)
            return textMap[status] || status
        },
        // 获取授权原因
        getAuthReason(accessLevel) {
            const reasonMap = {
                'full': '完全授权',
                'partial': '部分授权',
                'limited': '限制授权'
            }
            return reasonMap[accessLevel] || accessLevel
        },
        // 获取关系文本
        getRelationshipText(relationship) {
            const relationshipMap = {
                'father': '父亲',
                'mother': '母亲',
                'son': '儿子',
                'daughter': '女儿',
                'spouse': '配偶',
                'sibling': '兄弟姐妹',
                'other_relative': '其他亲属',
                'legal_guardian': '法定监护人',
                'other': '其他'
            }
            return relationshipMap[relationship] || relationship
        },
        // 处理分页大小变化
        handleSizeChange(val) {
            this.pagination.pageSize = val
            this.loadAuthList()
        },
        // 处理页码变化
        handleCurrentChange(val) {
            this.pagination.currentPage = val
            this.loadAuthList()
        },
        // 处理选择变化
        handleSelectionChange(selected) {
            // Implementation needed
            this.hasSelectedItems = selected.length > 0
        },
        // 处理通过
        async handleApprove() {
            try {
                const selectedItems = this.$refs.table.selection
                if (!selectedItems || selectedItems.length === 0) {
                    this.$message.warning('请选择要审核的记录')
                    return
                }

                const selectedItem = selectedItems[0]
                console.log('选中的记录：', selectedItem)
                console.log('guardianId：', selectedItem.guardianId)

                // 构建审核数据
                const reviewData = {
                    guardianId: selectedItem.guardianId,
                    approved: true,
                    remarks: selectedItem.remarks,
                    expirationDate: selectedItem.expirationDate,
                    operator: selectedItem.operator
                }

                console.log('审核通过请求数据：', reviewData)

                const response = await axios({
                    method: 'post',
                    url: '/api/guardian/review',
                    data: reviewData,
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })

                if (response.data.code === "200") {
                    this.$message.success('审核通过成功')
                    this.loadAuthList() // 刷新列表
                } else {
                    this.$message.error(response.data.message || '审核通过失败')
                }
            } catch (error) {
                console.error('审核通过失败：', error)
                this.$message.error('审核通过失败，请重试')
            }
        },
        // 处理拒绝
        handleReject() {
            const selectedItems = this.$refs.table.selection
            if (!selectedItems || selectedItems.length === 0) {
                this.$message.warning('请选择要审核的记录')
                return
            }

            this.selectedItemForReject = selectedItems[0]
            this.rejectDialogVisible = true
        },
        // 处理拒绝对话框关闭
        handleRejectDialogClose() {
            this.rejectDialogVisible = false
            this.rejectForm.reason = ''
            this.selectedItemForReject = null
        },
        // 确认拒绝
        async confirmReject() {
            if (!this.rejectForm.reason) {
                this.$message.warning('请输入审核不通过的原因')
                return
            }

            try {
                const reviewData = {
                    guardianId: this.selectedItemForReject.guardianId,
                    approved: false,
                    reason: this.rejectForm.reason,
                    remarks: this.selectedItemForReject.remarks,
                    expirationDate: this.selectedItemForReject.expirationDate,
                    operator: this.selectedItemForReject.operator || '九医生'
                }

                console.log('审核拒绝请求数据：', reviewData)

                const response = await axios({
                    method: 'post',
                    url: '/api/guardian/revoke',
                    data: reviewData,
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })

                if (response.data.code === "200") {
                    this.$message.success('审核拒绝成功')
                    this.handleRejectDialogClose()
                    this.loadAuthList() // 刷新列表
                } else {
                    this.$message.error(response.data.message || '审核拒绝失败')
                }
            } catch (error) {
                console.error('审核拒绝失败：', error)
                this.$message.error('审核拒绝失败，请重试')
            }
        }
    },
    mounted() {
        this.loadAuthList()
    }
}
</script>

<style lang="scss" scoped>
.guardian-auth-list {
    padding: 20px;

    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style> 
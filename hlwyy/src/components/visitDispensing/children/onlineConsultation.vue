<template>
    <div :class="{
            onlineConsultation:true,
            active:show,
            imgText:type=='imgText',
            video:type=='video',
            visit:type=='visit',
        }">
        <section class="content">
            <div class="chatWrap">
                <div class="chatMessage">
                    <div class="patient">
                        <div class="patientWrap">
                            <div class="patientInfo active">
                                <div class="portrait" :style="{
                                        backgroundImage:patientInfo.avatar?`url(${patientInfo.avatar})`:'',
                                    }"></div>
                                <div class="rightContent">
                                    <h3>
                                        <span>{{patientInfo.patientName}}</span>
                                        <em :class="{
                                                female:patientInfo.sex!='1',
                                            }">{{patientInfo.age}}岁</em>
                                        <a @click="toMedicationGuides" style="width:120px;">
                                            查看就诊记录
                                        </a>
                                        <a v-if="showRevisiting" @click="toclearRevisitingTime" style="width:120px;">
                                            重新接诊
                                        </a>
                                    </h3>
                                    <h4 v-if="type=='visit'&&patientInfo.docScheduleId">
                                        <em>预约时间：</em>
                                        <em>{{patientInfo.scheduleStartTime|date('yyyy-MM-dd')}}</em>
                                        <em>{{patientInfo.scheduleStartTime|date('hh:mm')}}-{{patientInfo.scheduleEndTime|date('hh:mm')}}</em>
                                    </h4>
                                    <h4 v-if="type!='visit'&&patientInfo.docScheduleId">
                                        <em>预约时间：</em>
                                        <em>{{patientInfo.startTime|date('yyyy-MM-dd')}}</em>
                                        <em>{{patientInfo.startTime|date('hh:mm')}}-{{patientInfo.endTime|date('hh:mm')}}</em>
                                    </h4>
                                </div>
                            </div>
                            <div class="rightContent">
                                <a class="finish" @click="finish">
                                    结束{{type=='visit'?'复诊':'咨询'}}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div v-if="type!='visit'" class="basicInfo">
                        <div :class="{
                                infoWrap:true,
                                active:showInfo,
                            }">
                            <div class="title">
                                基本健康信息
                            </div>
                            <ul class="main">
                                <li>
                                    <span>药物过敏：</span>
                                    <em>{{healthinfo.medicalAllergy || '暂无'}}</em>
                                </li>
                                <li>
                                    <span>食物/接触物过敏：</span>
                                    <em>{{healthinfo.foodAllergy || '暂无'}}</em>
                                </li>
                                <li>
                                    <span>家族病史：</span>
                                    <em>{{healthinfo.familyDiseaseHistory || '暂无'}}</em>
                                </li>
                                <li>
                                    <span>手术或外伤：</span>
                                    <em>{{healthinfo.operationOrTrauma || '暂无'}}</em>
                                </li>
                            </ul>
                        </div>
                        <div :class="{
                                controlInfo:true,
                                active:showInfo,
                            }">
                            <a @click="showInfo=!showInfo">
                                <span>{{!showInfo?'展开':'收起'}}个人信息</span>
                                <i></i>
                            </a>
                        </div>
                    </div>
                    <div v-else class="visitInfo">
                        <div :class="{
                                infoWrap:true,
                                active:showInfo,
                            }">
                            <ul class="itemList">
                                <!-- <li>
                                    <div class="title">
                                        病情信息
                                    </div>
                                    <ul class="main">
                                        <li>
                                            <span>是否去过院外就诊：</span>
                                            <em>{{diseaseSituation.encounterOutFlag=='1'?'是':'否'}}</em>
                                        </li>
                                        <li v-if="diseaseSituation.encounterOutFlag=='1'">
                                            <span>就诊医院：</span>
                                            <em>{{diseaseSituation.encounterOrgName||'暂无'}}</em>
                                        </li>
                                        <li v-if="diseaseSituation.encounterOutFlag=='1'">
                                            <span>服用药物：</span>
                                            <em>{{diseaseSituation.drugName||'暂无'}}</em>
                                        </li>
                                        <li>
                                            <span>是否咳嗽：</span>
                                            <em>{{diseaseSituation.counghFlag=='1'?'是':'否'}}</em>
                                        </li>
                                        <li>
                                            <span>病情描述：</span>
                                            <em>{{diseaseSituation.chiefComplaint}}</em>
                                        </li>
                                    </ul>
                                </li> -->
                                <li>
                                    <div class="title">
                                        基本健康信息
                                    </div>
                                    <ul class="main">
                                        <!-- <li>
                                            <span>既往是否患过肺结核病：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfoSpecial.jhbHaved=='1'?'是':'否'}}</em>
                                        </li>
                                        <li>
                                            <span>是否按规定服药：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfoSpecial.drugAccordingRule=='1'?'是':'否'}}</em>
                                        </li>
                                        <li>
                                            <span>是否与肺结核病患者有密切接触：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfoSpecial.jhbPatientContracted=='1'?'是':'否'}}</em>
                                        </li>
                                        <li>
                                            <span>是否患有乙肝：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfoSpecial.ygHaved=='1'?'是':'否'}}</em>
                                        </li> -->
                                        <li>
                                            <span>药物过敏：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfo.medicalAllergy || '暂无'}}</em>
                                        </li>
                                        <li>
                                            <span>食物/接触物过敏：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfo.foodAllergy || '暂无'}}</em>
                                        </li>
                                        <li>
                                            <span>家族病史：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfo.familyDiseaseHistory || '暂无'}}</em>
                                        </li>
                                        <li>
                                            <span>手术或外伤：</span>
                                            <em>{{healthinfoAndSpecialVo.healthinfo.operationOrTrauma || '暂无'}}</em>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                        <div :class="{
                                controlInfo:true,
                                active:showInfo,
                            }">
                            <a @click="showInfo=!showInfo">
                                <span>{{!showInfo?'展开':'收起'}}患者信息</span>
                                <i></i>
                            </a>
                        </div>
                    </div>
                    <div class="messageList" ref="messageList" @scroll="scrollLoad($event)">
                        <div :class="{
                                loadingWrap:true,
                                loading,
                                finished,
                            }">
                            <h3>
                                <span>正在加载中</span>
                            </h3>
                            <h4>
                                <span>加载完毕</span>
                            </h4>
                        </div>
                        <ul>
                            <li v-if="msgFilter(item)" v-for="(item, index) in currentMsgList" :key="index" :class="{
                                    myself:lStore.get('nimAccount')==item.from,
                                }">
                                <div v-if="index==0||index>0&&item.time-currentMsgList[index-1].time>5*1000*60" class="time">
                                    {{item.time|date('yyyy-MM-dd hh:mm:ss')}}
                                </div>
                                <div class="messageWrap">
                                    <div v-if="outerMsgFilter(item)" class="outerCustom">
                                        <div v-if="item.content.type=='7'" class="consultFinish hint">
                                            <span>{{item.content.data.value}}</span>
                                        </div>
                                        <div v-if="item.content.type=='12'&&item.content.data.show==1" class="warmPrompt hint">
                                            <span>{{item.content.data.value}}</span>
                                        </div>
                                        <div v-if="item.content.type=='13'&&item.content.data.show==1" class="returnDiagnosis hint">
                                            <span>{{item.content.data.value}}</span>
                                        </div>
                                    </div>
                                    <div v-else class="personage">
                                        <div class="portrait" :style="{
                                                backgroundImage:getPersonageAvatar(item),
                                            }"></div>
                                        <div class="message">
                                            <div v-if="item.type=='text'" class="msg">
                                                <span>{{item.text}}</span>
                                            </div>
                                            <div v-if="item.type=='custom'" class="innerCustom">
                                                <div v-if="item.content.type=='8'" class="patientBrief">
                                                    <h3>病情信息：</h3>
                                                    <div>{{type=='visit'?'病情描述':'咨询目的'}}：{{item.content.data&&item.content.data.chiefComplaint}}</div>
                                                    <template v-if="item.content.data.pics&&item.content.data.pics.length">
                                                        <h3>病情照片：</h3>
                                                        <ul class="imgList">
                                                            <li v-for="(item1, index) in item.content.data.pics" :key="index" @click="previewBigImage(item1)">
                                                                <img :src="item1" alt="图片" @load="scrollBottom" />
                                                            </li>
                                                        </ul>
                                                    </template>
                                                </div>
                                                <div v-if="item.content.type=='9'" class="record" @click="toMedicationGuides1">
                                                    <div class="title">
                                                        <div class="wrap">
                                                            <span>{{item.content.data.visitDateTime|date('yyyy-MM-dd')}}</span>
                                                            <span>就诊记录</span>
                                                        </div>
                                                    </div>
                                                    <div class="main">
                                                        <div class="rightContent">
                                                            <h3>{{item.content.data.orgFullName}}</h3>
                                                            <h3>{{item.content.data.deptName}}</h3>
                                                            <h4>确诊诊断：{{item.content.data.diagnoseName || '无'}}</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="item.content.type=='10'" class="prescription" @click="prescribing(item.content.data)">
                                                    <div class="title">
                                                        <div class="wrap">
                                                            <span>电子处方</span>
                                                            <em>{{item.content.data.invalidDate|date('yyyy-MM-dd')}}到期</em>
                                                        </div>
                                                    </div>
                                                    <div class="main">
                                                        <div class="img"></div>
                                                        <div class="rightContent">
                                                            <h3>{{item.content.data.personName}}</h3>
                                                            <h4>{{item.content.data.prescriptionCheckDt|date('yyyy年MM月dd日')}}</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="item.content.type=='11'" class="videoDuration">
                                                    <h3 v-if="item.content.data.state==0">未接听</h3>
                                                    <h4 v-else>{{getDuration(item.content.data.duration)}}</h4>
                                                </div>
                                                <div v-if="item.content.type=='14'" class="reception">
                                                    <span>{{item.content.data.msg}}</span>
                                                </div>
                                                <div v-if="item.content.type=='15'" class="prescriptionHint">
                                                    <span>{{item.content.data.msg}}</span>
                                                </div>
                                                <div v-if="item.content.type=='20'" class="prescription" @click="previewInsApp(item.content.data)">
                                                    <div class="title">
                                                        <div class="wrap">
                                                            <span>医技申请单</span>
                                                            <em>{{item.content.data.expireTime|date('yyyy-MM-dd')}}到期</em>
                                                        </div>
                                                    </div>
                                                    <div class="main">
                                                        <div class="img"></div>
                                                        <div class="rightContent">
                                                            <h3>{{item.content.data.personName}}</h3>
                                                            <h4>{{item.content.data.applyTime|date('yyyy年MM月dd日')}}</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="file">
                                                <img v-if="item.type=='image'" :src="item.file.url" alt="图片" @load="scrollBottom" @click="previewBigImage(item.file.url)" />
                                                <audio v-if="item.type=='audio'" :src="item.file.url" controls></audio>
                                                <video v-if="item.type=='video'" :src="item.file.url" controls></video>
                                                <a v-if="item.type=='file'" :href="item.file.url" download="true" class="download">
                                                    <h3 class="multiLine-2">{{item.file.name}}</h3>
                                                </a>
                                            </div>
                                            <div v-if="item.attach&&(item.attach.type=='netcallRejected'||item.attach.type=='cancelNetcallBeforeAccept')" class="attach">
                                                <span>{{item.attach.type=='netcallRejected'?'未接听':'已取消'}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="end">
                        <div v-if="showQuickRes" class="quickRes" :style="{
                            top: `-${quickResList.length * 34 + 1}px`,
                        }">
                            <ul>
                                <li v-for="(item,index) in quickResList" :key="index" :class="{
                                        active:index==mouseoverIndex,
                                    }" @click="selectQuickRes(item)" @mouseover="mouseoverIndex=index">
                                    {{item.content}}
                                </li>
                            </ul>
                        </div>
                        <div class="toolbar">
                            <a>
                                <label class="labelFor" for="onlineConsultationFile">
                                    <input id="onlineConsultationFile" class="file" type="file" @change="sendFile($event.currentTarget)" />
                                </label>
                            </a>
                        </div>
                        <textarea v-model="msg" class="inputWrap" @focus="delayChange(true)" @blur="delayChange(false)" @keydown.prevent.enter="sendText" @keydown.prevent.up="changeSelectIndex('up')" @keydown.prevent.down="changeSelectIndex('down')" @input="searchQuickRes"></textarea>
                        <div class="buttonWrap">
                            <a @click="sendText">发送</a>
                        </div>
                    </div>
                </div>
            </div>
            <div :class="{
                    videoWrap:true,
                    active:showNimVideoCall,
                }">
                <div class="wrap">
                    <div :class="{
                            containerWrap:true,
                            active:showNimVideoCall,
                        }">
                        <div :class="{
                            remoteContainerPA: netcallType == 0,
                            remoteContainerWR: netcallType == 1
                        }" ref="remoteContainer"></div>
                        <div :class="{
                                containerPA: netcallType == 0,
                                containerWR: netcallType == 1
                            }" ref="container" @click="changeContainer"></div>
                    </div>
                    <a class="call" @click="getDocDetail">
                        点击发起视频
                    </a>
                    <div class="maskWrap">
                        <div v-show="!callAccepted" class="patientInfo">
                            <div class="portrait" :style="{
                                    backgroundImage:patientInfo.avatar?`url(${patientInfo.avatar})`:'',
                                }"></div>
                            <div class="rightContent">
                                <h3>
                                    <span>{{patientInfo.patientName}}</span>
                                </h3>
                                <h4 class="multiLine">
                                    {{calleeOffline?'对方不在线':'等待对方加入...'}}
                                </h4>
                            </div>
                        </div>
                        <div class="handle">
                            <div class="muteWrap">
                                <a :class="{
                                        active:isMute,
                                    }" @click="muteSwitch">{{!isMute?'静音':'开启'}}</a>
                            </div>
                            <div class="hangupWrap">
                                <a @click="hangup(true)"></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <yydDialog class="previewBigImage" :parent="this" :show="showBigImage" :showName="'showBigImage'" :height="600" :control="true" :downloadSrc="bigImage">
            <img :src="bigImage" alt="图片" />
        </yydDialog>
        <previewPrescription v-if="showPreviewPrescription" :parent="this" :show="true" :showName="'showPreviewPrescription'" :orderNo="orderNo" :recipeId="recipeId" />
        <previewInsApp v-if="showpreviewInsApp" :parent="this" :show="true" :showName="'showpreviewInsApp'" :applyId="applyId" />
    </div>
</template>
<script>
import vm from 'src/main';
import nimInit, { safeParse } from 'components/NIM/nimInit';
import nimNetcallInit from 'components/NIM/nimNetcallInit';
import yydDialog from 'components/common/yydDialog';
import previewPrescription from 'components/common/previewPrescription';
import previewInsApp from 'components/common/previewInsApp';
import { Type, lStore, sStore, dateFormat0, resetFile, copyJson, fileType, normalDate, toTwo, alerts, imgFilesToBase64, isSafari } from 'js/yydjs';
import { imgview, profile, endConsult, finishRevisit, doRecipeSyncPdf, validConsultBeforeEndConsult, validDataBeforeFinishRevisit, isOrderFinish, findCommonExpressionsByTypeAndContent, commonAjax, sendVideoMessage } from 'services';

export default {
    data() {
        let doctorName = sStore.get('doctorName');

        return {
            searchTimer: null,
            showQuickRes: false,
            quickResList: [],
            mouseoverIndex: 0,
            query: this.$router.currentRoute.query,
            //业务相关
            showInfo: false,
            showBigImage: false,
            isRemote: true,
            params: [{
                // orderDetailId:'',
                // itemCode:'',
            }],
            params1: [{
                pdfStatus: '2',
                revisitId: '',
                recipeId: '',
            }],
            showPreviewPrescription: false,
            showpreviewInsApp: false,
            previewPrescriptionUrl: '',
            consultFinish: false,
            bigImage: '',
            doctorName,
            imgview,
            orderNo: -1,
            recipeId: '',
            applyId: -1,
            oldHeight: 0,
            //nim聊天
            nimChat: sStore.get('nimChat') || {},
            limit: 20,
            loaded: false,
            loading: false,
            finished: false,
            firstLoad: false,
            isScrollBottom: true,
            beginTime: 0,
            endTime: 0,
            lastMsgId: '0',
            countMsg: [],
            idClientList: [],
            oldMsgList: [],
            msgList: [],
            msg: '',
            tabIndex: -1,
            dateFormat0,
            lStore,
            fileType,
            netcallMsgJson: {
                netcallBill: '通话已结束',
                netcallRejected: '呼叫被拒绝',
                cancelNetcallBeforeAccept: '通话已取消',
            },
            //nim音视频
            netcallType: 1,
            agentDownloadUrl: 'https://yx-web-nosdn.netease.im/package/WebAgent_Setup_V3.0.4.0.exe?download=WebAgent_Setup_V3.0.4.0.exe​',
            width: 320,
            height: 568,
            showNimVideoCall: false,
            netcallInited: false,
            signalInited: false,
            pushConfig: {
                enable: true,
                needBadge: true,
                needPushNick: true,
                pushContent: '',
                custom: '',
                pushPayload: '',
                sound: '',
                forceKeepCalling: 0,
            },
            sessionConfig: {
                videoQuality: Netcall.CHAT_VIDEO_QUALITY_HIGH,
                videoFrameRate: Netcall.CHAT_VIDEO_FRAME_RATE_15,
                videoBitrate: 0,
                recordVideo: true,
                recordAudio: true,
                recordType: 1,
                highAudio: false,
                bypassRtmp: false,
                rtmpUrl: '',
                rtmpRecord: false,
                splitMode: Netcall.LAYOUT_SPLITLATTICETILE,
            },
            netcallInfo: {
                beCalling: false, //是否被叫中
                type: null, //呼叫类型
                beCalledInfo: null, //被叫信息
                busy: false, //是否正忙
            },
            callTimer: null,
            callDoneTimer: null,
            scrollTimer: null,
            callDone: true,
            isMute: false,
            calleeOffline: false,
            callAccepted: false,
            offlineNum: 0,
            offlineTimer: null,
            sendTimer: null,
            sendDone: false,
        }
    },

    computed: {
        showRevisiting(){
            let endTime = new Date(this.patientInfo.scheduleEndTime);
            console.log(new Date(),this.patientInfo.scheduleEndTime)
            if(endTime - new Date() < 0){
                return true;
            }
        },
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId;
        },
        orderDetailId() {
            let { patientInfo = {} } = this;
            let { orderDetailId } = patientInfo;

            return orderDetailId;
        },
        buzId() {
            let { revisitId, orderDetailId } = this;

            return revisitId || orderDetailId;
        },
        buzType() {
            let { type } = this;
            let buzTypeJson = {
                'imgText': '30',
                'video': '40',
                'visit': '70',
            };

            return buzTypeJson[type];
        },
        typeText() {
            let { type } = this;

            return type == 'visit' ? '复诊' : '咨询';
        },
        healthinfo() {
            let { patientDetailInfo = {} } = this;
            let { healthinfo = {} } = patientDetailInfo;
            let {
                medicalAllergy = '',
                    medicalAllergyInput = '',
                    foodAllergy = '',
                    foodAllergyInput = '',
                    familyDiseaseHistory = '',
                    familyDiseaseHistoryInput = '',
                    operationOrTrauma = '',
                    operationOrTraumaInput = ''
            } = healthinfo
            medicalAllergyInput = medicalAllergyInput && medicalAllergy ? `|${medicalAllergyInput}` : medicalAllergyInput
            foodAllergyInput = foodAllergyInput && foodAllergy ? `|${foodAllergyInput}` : foodAllergyInput
            familyDiseaseHistoryInput = familyDiseaseHistoryInput && familyDiseaseHistory ? `|${familyDiseaseHistoryInput}` : familyDiseaseHistoryInput
            operationOrTraumaInput = operationOrTraumaInput && operationOrTrauma ? `|${operationOrTraumaInput}` : operationOrTraumaInput
            healthinfo.medicalAllergy = `${medicalAllergy + medicalAllergyInput}`
            healthinfo.foodAllergy = `${foodAllergy + foodAllergyInput}`
            healthinfo.familyDiseaseHistory = `${familyDiseaseHistory + familyDiseaseHistoryInput}`
            healthinfo.operationOrTrauma = `${operationOrTrauma + operationOrTraumaInput}`
            return healthinfo;
        },
        diseaseSituation() {
            let { patientDetailInfo = {} } = this;
            let { diseaseSituation = {} } = patientDetailInfo;

            return diseaseSituation;
        },
        healthinfoAndSpecialVo() {
            let { patientDetailInfo = {} } = this;
            let { healthinfoAndSpecialVo = {} } = patientDetailInfo;
            let { healthinfo = {}, healthinfoSpecial = {} } = healthinfoAndSpecialVo;
            let {
                medicalAllergy = '',
                    medicalAllergyInput = '',
                    foodAllergy = '',
                    foodAllergyInput = '',
                    familyDiseaseHistory = '',
                    familyDiseaseHistoryInput = '',
                    operationOrTrauma = '',
                    operationOrTraumaInput = ''
            } = healthinfo
            medicalAllergyInput = medicalAllergyInput && medicalAllergy ? `|${medicalAllergyInput}` : medicalAllergyInput
            foodAllergyInput = foodAllergyInput && foodAllergy ? `|${foodAllergyInput}` : foodAllergyInput
            familyDiseaseHistoryInput = familyDiseaseHistoryInput && familyDiseaseHistory ? `|${familyDiseaseHistoryInput}` : familyDiseaseHistoryInput
            operationOrTraumaInput = operationOrTraumaInput && operationOrTrauma ? `|${operationOrTraumaInput}` : operationOrTraumaInput
            healthinfo.medicalAllergy = `${medicalAllergy + medicalAllergyInput}`
            healthinfo.foodAllergy = `${foodAllergy + foodAllergyInput}`
            healthinfo.familyDiseaseHistory = `${familyDiseaseHistory + familyDiseaseHistoryInput}`
            healthinfo.operationOrTrauma = `${operationOrTrauma + operationOrTraumaInput}`
            healthinfoAndSpecialVo.healthinfo = healthinfo;
            healthinfoAndSpecialVo.healthinfoSpecial = healthinfoSpecial;

            return healthinfoAndSpecialVo;
        },
        currentMsgList() {
            let result = [];
            let { oldMsgList, msgList } = this;
            let { revisitId, orderDetailId } = this;
            let buzId = revisitId || orderDetailId;
            let consultFinish = false;
            let allMsgList = [].concat(oldMsgList, msgList);

            result = allMsgList.filter((item, index) => {
                let { custom = {} } = item;
                let { buzId: currentBuzId = '' } = custom;

                return currentBuzId == buzId;
            });

            for (let item of result) {
                let { content = {} } = item;
                if (content.type == '7') {
                    let value = content.data && content.data.value

                    if (~value.indexOf('取消') || ~value.indexOf('结束')) {
                        consultFinish = true
                        break;
                    }
                }
            }

            this.consultFinish = consultFinish;
            this.scrollBottom();
            console.log(copyJson(result));
            return result;
        },
    },

    /*
        <onlineConsultation
            :show="tabIndex==0"
            :patientInfo="patientInfo"
            :patientDetailInfo="patientDetailInfo"
        />
    */

    props: {
        show: { //是否显示该组件
            type: Boolean,
            default: false,
        },
        type: { //患者列表咨询类型
            type: String,
            default: 'visit', //图文：imgText，视频：video，复诊配药：visit
        },
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
        patientDetailInfo: { //当前患者的详细信息
            type: Object,
            default () {
                return { healthinfo: {} };
            },
        },
    },

    created() {
        vm.$on('patientListPatientChange', this.updatePatientInfo);

        vm.$on('nimOnConnect', this.getHistoryMsgs);
        vm.$on('componentsUpdate', this.componentsUpdate);
        vm.$on('nimOnMsg', this.getMsgs);
        if (isSafari() == false) {
            this.netcallType = 0
        }
    },

    mounted() {
        document.onselectstart = true;
        //初始化nim
        nimInit();

        //聊天滚动到底部
        this.scrollBottom();
    },

    beforeDestroy() {
        vm.$off('patientListPatientChange', this.updatePatientInfo);

        vm.$off('nimOnConnect', this.getHistoryMsgs);
        vm.$off('componentsUpdate', this.componentsUpdate);
        vm.$off('nimOnMsg', this.getMsgs);
        clearTimeout(this.callTimer);
        this.appendContainerTo(true);
    },

    methods: {
        toclearRevisitingTime(){
            console.log('重置复诊时间')
        },
        toMedicationGuides() {
            if(true){
                console.log('跳转360')
                //跳转360档案
                vm.$emit('toChangeTabIndex', 5);
                return;
            }
            console.log(this.patientInfo, 'pat')
            let { patientId, outpatientCode, idCard } = this.patientInfo;
            let mesIdCard = btoa(idCard); // base64加密
            if (!idCard) {
                this.$message({ type: "error", message: '暂无记录，无法查看' })
            } else {
                // let url = `http://www.shpdph.com:8100/winsso/c/00/0/1/${patientId}/0/0/0/${outpatientCode}/-1/42509273X00/0/0/mzysclient`
                let url = `https://yszt-hlw.mh-hospital.sh.cn/mhwsw-healthRecord/index.html#/healthRecord?idCard=${mesIdCard}`;
                window.open(url)
            }
        },
        toMedicationGuides1() {
            if(true){
                console.log('跳转360')
                //跳转360档案
                vm.$emit('toChangeTabIndex', 5);
                return;
            }
            console.log(this.patientInfo, 'patOn')
            let { idCard, outpatientCode } = this.patientInfo;
            let mesIdCard = btoa(idCard); // base64加密
            if (!idCard) {
                this.$message({ type: "error", message: '暂无记录，无法查看' })
            } else {
                // let url = `http://www.shpdph.com:8888/ehrviews1_0/encrypt.action?klx=8&kh=${idCard}`
                let url = `https://yszt-hlw.mh-hospital.sh.cn/mhwsw-healthRecord/index.html#/healthRecord?idCard=${mesIdCard}`;
                window.open(url)
            }
        },
        getDocDetail() {
            commonAjax("cas.doctorServiceAdmin", "doctorInfo", []).then(res => {
                if (res && res.code == 200) {
                    this.call()
                } else {
                    this.$message({
                        type: "error",
                        message: res.msg,
                    })
                }
            })
        },
        //业务相关
        updatePatientInfo(tabIndex, patientInfo) {
            this.tabIndex = tabIndex;
            if (tabIndex == 1 && patientInfo) {
                this.componentsUpdate();
            }
        },
        msgFilter(item) {
            let { type, content = {} } = item;
            let { type: type1, data = {} } = content;
            let { show } = data;
            let excludeArr = ['12', '16', '17', '18', '99'];

            return type != 'custom' || type == 'custom' && !~excludeArr.indexOf(type1) || type == 'custom' && type1 == '12' && show == 1;
        },
        outerMsgFilter(item) {
            let { type, content = {} } = item;
            let { type: type1, data = {} } = content;
            let includeArr = ['7', '12', '13'];

            return type == 'custom' && ~includeArr.indexOf(type1);
        },
        getDuration(duration) {
            let minute = Math.floor(duration / 60);
            let second = duration % 60;

            return `${toTwo(minute)}:${toTwo(second)}`;
        },
        previewBigImage(src) {
            this.bigImage = src;
            this.showBigImage = true;
        },
        getPersonageAvatar(item) {
            let isMyself = lStore.get('nimAccount') == item.from;
            let { imgview, patientInfo = {} } = this;
            let { avatar: patientAvatar } = patientInfo;
            let doctorAvatar = sStore.get('userAvatar');
            let url = '';

            if (isMyself) {
                url = (doctorAvatar && doctorAvatar != '0') ? `url(${imgview}${doctorAvatar})` : '';
            } else {
                url = (patientAvatar && patientAvatar != "0") ? `url(${patientAvatar})` : '';
            }
            return url;
        },
        medicalRecord(data) {
            let { buzId, buzType } = this;

            this.$router.push({
                path: '/orderManage/orderDetail',
                query: {
                    buzId,
                    buzType,
                },
            });
        },
        prescribing(data) {
            let { orderNo, recipeId } = data;

            this.orderNo = orderNo;
            this.recipeId = recipeId;
            this.showPreviewPrescription = true;
        },
        previewInsApp(data) {
            let { applyId } = data;
            this.applyId = applyId;
            this.showpreviewInsApp = true;
        },
        toHistoricalRecords(data) {
            let { visitId } = data;

            sStore.set('imVisitId', visitId);
            vm.$emit('toChangeTabIndex', 3);
        },
        finish() {
            let { type, typeText, revisitId, orderDetailId, showNimVideoCall, startTime, endTime } = this;
            let api = null;
            let iDate = +new Date();
            const finishFn = () => {
                this.$msgbox({
                    title: '',
                    message: `确定结束该患者的${typeText}？`,
                    showCancelButton: true,
                    showConfirmButton: true,
                    cancelButtonText: '取消',
                    confirmButtonText: '结束',
                }).then(() => {
                    api(this.params, null, (res) => {
                        let { code } = res;

                        switch (code) {
                            case 802:
                                this.$msgbox({
                                    title: '提醒',
                                    message: '还未回复患者，暂时不能结束',
                                    showConfirmButton: true,
                                    confirmButtonText: '确认',
                                }).then(() => {

                                });
                                break;
                            case 803:
                                this.$msgbox({
                                    title: '提醒',
                                    message: '还未与患者进行视频，暂时不能结束',
                                    showConfirmButton: true,
                                    confirmButtonText: '确认',
                                }).then(() => {

                                });
                                break;
                            case 200:
                                alerts(`${typeText}已结束`, 'success');
                                vm.$emit('patientListRefreshPatientList');
                                break;

                        }
                    });
                });
            };

            switch (type) {
                case 'imgText':
                    this.judgeStatus(() => {
                        this.params[0].orderDetailId = orderDetailId;
                        this.params[0].itemCode = '01';
                        this.params[0].userType = '10';
                        api = endConsult;

                        validConsultBeforeEndConsult(this.params, null, (res) => {
                            let { code } = res;

                            switch (code) {
                                case 802:
                                    this.$msgbox({
                                        title: '提醒',
                                        message: '还未回复患者，暂时不能结束',
                                        showConfirmButton: true,
                                        confirmButtonText: '确认',
                                    }).then(() => {

                                    });
                                    break;
                                case 200:
                                    finishFn();
                                    break;
                            }
                        });
                    });
                    break;
                case 'video':
                    this.judgeStatus(() => {
                        this.params[0].orderDetailId = orderDetailId;
                        this.params[0].itemCode = '02';
                        this.params[0].userType = '10';
                        api = endConsult;

                        validConsultBeforeEndConsult(this.params, null, (res) => {
                            let { code } = res;

                            switch (code) {
                                case 802:
                                    this.$msgbox({
                                        title: '提醒',
                                        message: '还未回复患者，暂时不能结束',
                                        showConfirmButton: true,
                                        confirmButtonText: '确认',
                                    }).then(() => {

                                    });
                                    break;
                                case 803:
                                    this.$msgbox({
                                        title: '提醒',
                                        message: `还未与患者进行视频，暂时不能结束,如果在${this.patientInfo.endTime}前未接通视频，订单将做退费处理`,
                                        showConfirmButton: true,
                                        confirmButtonText: '确认',
                                    }).then(() => {

                                    });
                                    break;
                                case 200:
                                    finishFn();
                                    break;
                            }
                        });
                    });
                    break;
                case 'visit':
                    this.judgeStatus(() => {
                        this.params = [revisitId];
                        api = finishRevisit;

                        validDataBeforeFinishRevisit([revisitId], null, (res) => {
                            let { code } = res;

                            switch (code) {
                                case 800:
                                    this.$msgbox({
                                        title: '',
                                        message: '存在未审核通过的处方，请您耐心等待审核结果或调整已开处方',
                                        showCancelButton: true,
                                        showConfirmButton: true,
                                        cancelButtonText: '关闭',
                                        confirmButtonText: '查看处方',
                                        distinguishCancelAndClose: true,
                                    }).then(() => {
                                        vm.$emit('toChangeTabIndex', 2);
                                        vm.$emit('toChangeStatusTabIndex', 3);
                                    }).catch((action) => {
                                        if (action == 'cancel') {
                                            // finishFn();
                                        }
                                    });
                                    break;
                                case 801:
                                    this.$msgbox({
                                        title: '',
                                        message: '还未录入有效的病历信息，是否结束咨询？',
                                        showCancelButton: true,
                                        showConfirmButton: true,
                                        cancelButtonText: '结束',
                                        confirmButtonText: '录入病历',
                                        distinguishCancelAndClose: true,
                                    }).then(() => {
                                        vm.$emit('toChangeTabIndex', 1);
                                    }).catch((action) => {
                                        if (action == 'cancel') {
                                            finishFn();
                                        }
                                    });
                                    break;
                                case 802:
                                    this.$msgbox({
                                        title: '提醒',
                                        message: '还未回复患者，暂时不能结束',
                                        showConfirmButton: true,
                                        confirmButtonText: '确认',
                                    }).then(() => {

                                    });
                                    break;
                                case 200:
                                    finishFn();
                                    break;
                            }
                        });
                    });
                    break;
            }
        },
        //nim聊天
        componentsUpdate(controlName) {
            setTimeout(() => {
                let { patientInfo = {} } = this;
                let { createDt } = patientInfo;

                this.loaded = false;
                this.loading = false;
                this.finished = false;
                this.firstLoad = false;
                this.isScrollBottom = true;
                this.beginTime = 0;
                this.endTime = 0;
                this.lastMsgId = '0';
                this.countMsg = [];
                this.oldHeight = 0;
                createDt = +normalDate(createDt);
                this.beginTime = createDt - 1000 * 60 * 5;

                this.nimChat = sStore.get('nimChat') || {};
                this.idClientList = [];
                this.oldMsgList = [];
                this.msgList = [];
                this.msg = '';
                this.hangup();
                this.getHistoryMsgs();
            });
        },
        scrollBottom() {
            clearTimeout(this.scrollTimer);
            this.scrollTimer = setTimeout(() => {
                let oContent = this.$refs.messageList;
                let { scene, to } = this.nimChat;

                if (oContent) {
                    let scrollHeight = oContent.scrollHeight;
                    let diffHeight = scrollHeight - this.oldHeight;

                    if (this.isScrollBottom) {
                        oContent.scrollTop = scrollHeight;
                    } else {
                        oContent.scrollTop = diffHeight;
                    }
                }
                nim.resetSessionUnread(scene + '-' + to);
            }, 100);
        },
        scrollLoad(ev) {
            let { loaded, loading, finished, oldMsgList, countMsg } = this;
            let { currentTarget } = ev;
            let scrollTop = currentTarget.scrollTop;

            if (scrollTop == 0) {
                if (loaded && !finished && !loading) {
                    this.loading = true;
                    this.getHistoryMsgs();
                }
            }
        },
        getHistoryMsgs() {
            let { patientInfo = {} } = this;
            let { createDt } = patientInfo;

            if (!this.nimChat || !this.nimChat.to || !createDt) return;
            let { scene, to } = this.nimChat;
            let { beginTime, endTime, lastMsgId, limit } = this;

            this.loaded = false;
            nim.getHistoryMsgs({
                scene,
                to,
                beginTime,
                endTime,
                lastMsgId,
                limit,
                asc: true,
                done: (error, msg) => {
                    if (!error) {
                        let msgs = copyJson(msg.msgs) || [];
                        let oContent = this.$refs.messageList;
                        let { time } = msgs[0] || {};
                        let { idServer } = msgs[msgs.length - 1] || {};

                        this.endTime = time;
                        this.lastMsgId = idServer;

                        msgs = msgs.map((item, index) => {
                            let { content, custom, pushPayload } = item;

                            item.content = safeParse(content);
                            item.custom = safeParse(custom);
                            item.pushPayload = safeParse(pushPayload);
                            return item;
                        });
                        msgs = msgs.filter((item) => {
                            let { idClient, custom = {} } = item;
                            let { buzId } = custom;

                            if (!~this.idClientList.indexOf(idClient)) {
                                this.idClientList.push(idClient);
                                if (buzId == this.buzId) {
                                    return true;
                                }
                            }
                        });

                        this.oldHeight = oContent.scrollHeight;
                        this.isScrollBottom = false;
                        this.countMsg = [].concat(msgs, this.countMsg);

                        if (!msg.msgs.length) {
                            this.finished = true;
                        }

                        //满足条件则继续拉数据
                        if (!this.finished && this.countMsg.length < this.limit) {
                            this.getHistoryMsgs();
                        } else {
                            this.oldMsgList = [].concat(this.countMsg, this.oldMsgList);
                            this.countMsg = [];

                            setTimeout(() => {
                                this.loading = false;
                                this.loaded = true;
                                if (!this.firstLoad) {
                                    this.firstLoad = true;
                                    this.isScrollBottom = true;
                                    this.scrollBottom();
                                }
                            }, 300);
                        }

                        //console.log(copyJson(msgs));
                    }
                },
            });
        },
        getMsgs(res) {
            setTimeout(() => {
                //console.log(copyJson(res));
                let { scene, to } = this.nimChat;

                if (!scene || !to) return;
                this.isScrollBottom = true;
                for (let attr in res) {
                    let { idClient } = res[attr];

                    if (!~this.idClientList.indexOf(idClient)) {
                        this.idClientList.push(idClient);
                        this.msgList = [].concat(this.msgList, res[attr]);
                    }
                }
            }, 300);
        },
        getCustom() {
            let { type, revisitId, orderDetailId, nimChat = {}, patientInfo = {} } = this;
            let { scene, to } = nimChat;
            let buzTypeJson = {
                imgText: 30,
                video: 40,
                visit: 70,
            };
            let buzId = revisitId || orderDetailId;
            let buzType = buzTypeJson[type];
            let fromUserName = sStore.get('doctorName');
            let { patientName: toUserName, patientAccId: toUser } = patientInfo;
            let fromUserType = 'doctor';
            let result = {
                buzId,
                buzType,
                fromUserName,
                toUserName,
                fromUserType,
                toUser,
                profile,
                doctorReplyFlag: 1,
            };

            return JSON.stringify(result);
        },
        judgeStatus(endFn) {
            if (this.consultFinish) {
                this.$msgbox({
                    title: '',
                    message: '当前咨询状态已结束',
                    showCancelButton: false,
                    showConfirmButton: true,
                    cancelButtonText: '取消',
                    confirmButtonText: '好的',
                }).then(() => {
                    this.msg = '';
                    vm.$emit('patientListRefreshPatientList');
                });
            } else {
                endFn && endFn();
            }
        },
        delayChange(key) {
            if (key) {
                this.searchQuickRes()
            } else {
                clearTimeout(this.searchTimer)
                this.searchTimer = setTimeout(() => {
                    this.showQuickRes = false
                }, 300)
            }
        },
        changeSelectIndex(key) {
            let lenIndex = this.quickResList.length - 1
            let currentIndex = this.mouseoverIndex
            if (key == 'up') {
                if (currentIndex == 0) {
                    currentIndex = lenIndex
                } else {
                    currentIndex = currentIndex - 1
                }
            } else {
                if (currentIndex == lenIndex) {
                    currentIndex = 0
                } else {
                    currentIndex = currentIndex + 1
                }
            }
            this.mouseoverIndex = currentIndex
        },
        selectQuickRes(item) {
            this.sendNimText(item.content)
        },
        searchQuickRes() {
            this.getQuickResList()
        },
        getQuickResList() {
            if (this.msg == '') return
            findCommonExpressionsByTypeAndContent([1, this.msg], (res) => {
                if (res.code == 200) {
                    this.quickResList = res.body
                    this.showQuickRes = this.quickResList.length ? true : false
                    this.mouseoverIndex = 0
                } else {
                    this.quickResList = []
                    this.showQuickRes = false
                    this.mouseoverIndex = 0
                }
            })
        },
        sendText() {
            clearTimeout(this.searchTimer)
            this.searchTimer = setTimeout(() => {
                if (this.showQuickRes) {
                    let item = this.quickResList[this.mouseoverIndex]
                    this.selectQuickRes(item)
                } else {
                    if (!this.msg) return alerts('发送内容不能为空，请重新输入');
                    this.sendNimText(this.msg)
                }
            }, 300)
        },
        sendNimText(sendMsg) {
            this.isScrollBottom = true;
            let { scene, to } = this.nimChat;
            let custom = this.getCustom();
            let pushPayload = this.getCustom();
            const sendTextFn = () => {
                nim.sendText({
                    scene,
                    to,
                    text: sendMsg,
                    custom,
                    pushPayload,
                    done: (error, msg) => {
                        let { content, custom, pushPayload } = msg;

                        this.msg = '';
                        this.showQuickRes = false;
                        msg.content = safeParse(content);
                        msg.custom = safeParse(custom);
                        msg.pushPayload = safeParse(pushPayload);
                        if (!error) {
                            this.msgList = [].concat(this.msgList, [msg]);
                        }
                    },
                });
            };

            this.judgeStatus(sendTextFn);
        },
        sendFile(fileInput) {
            this.isScrollBottom = true;
            let { scene, to } = this.nimChat;
            let custom = this.getCustom();
            let pushPayload = this.getCustom();
            let sendFileFn = () => {
                nim.previewFile({
                    fileInput,
                    uploadprogress: (obj) => {
                        // console.log('文件总大小: ' + obj.total + 'bytes');
                        // console.log('已经上传的大小: ' + obj.loaded + 'bytes');
                        // console.log('上传进度: ' + obj.percentage);
                        // console.log('上传进度文本: ' + obj.percentageText);
                    },
                    done: (error, file) => {
                        let type = fileType(file.ext);

                        if (type == 'file') return alerts('只能上传图片、音频、视频类型的文件');

                        if (!error) {
                            nim.sendFile({
                                scene,
                                to,
                                type,
                                fileInput,
                                custom,
                                pushPayload,
                                done: (error1, msg1) => {
                                    if (!error) {
                                        let { content, custom, pushPayload } = msg1;

                                        msg1.content = safeParse(content);
                                        msg1.custom = safeParse(custom);
                                        msg1.pushPayload = safeParse(pushPayload);
                                        this.msgList = [].concat(this.msgList, [msg1]);

                                        resetFile(fileInput);
                                    }
                                },
                            });
                        } else {
                            alert(error);
                        }
                    }
                });
            };

            this.judgeStatus(sendFileFn);
        },
        //nim音视频
        checkPlatform(endFn) {
            const checkBrowser = () => {
                //判断是否是Chrome, Edge, IE 11
                if (/Chrome/gi.test(platform.name) || platform.name == 'Microsoft Edge' || (platform.name == 'IE' && platform.version == '11.0')) {
                    endFn && endFn();
                } else {
                    alert('当前浏览器不支持音视频功能，请使用 Chrome、IE 11 或者 Edge 浏览器');
                }
            };

            if (this.netcallType == 0) {
                //判断是否是win7或win10
                if (~platform.os.family.indexOf("Windows") && (platform.os.version == '7' || platform.os.version == '10')) {
                    checkBrowser();
                } else {
                    alert('当前系统不支持音视频功能，请使用win7、win10系统');
                }
            } else {
                checkBrowser();
            }
        },
        controlNimVideoCall(show) {
            this.showNimVideoCall = show;
        },
        sendCustomHangup() {
            let { scene, to } = this.nimChat;
            let custom = this.getCustom();
            let pushPayload = this.getCustom();

            nim.sendCustomMsg({
                scene,
                to,
                content: JSON.stringify({
                    data: {
                        duration: '0',
                        state: 0,
                        value: "视频未接通",
                    },
                    type: '11',
                }),
                custom,
                pushPayload,
                done: (error, msg) => {
                    let { content, custom, pushPayload } = msg;

                    msg.content = safeParse(content);
                    msg.custom = safeParse(custom);
                    msg.pushPayload = safeParse(pushPayload);
                    if (!error) {
                        this.msgList = [].concat(this.msgList, [msg]);
                    }
                },
            });
        },
        hangup(sendCustom) {
            if (!window.netcall) return;
            if (this.callAccepted) sendCustom = false;
            this.controlNimVideoCall(false);
            clearInterval(this.offlineTimer)
            this.offlineNum = 0
            netcall.hangup();
            this.resetWhenHangup();
            if (sendCustom) {
                this.sendCustomHangup();
            }
        },
        resetWhenHangup() {
            this.controlNimVideoCall(false);

            //停止本地视频预览
            netcall.stopLocalStream();

            //停止对端视频预览
            netcall.stopRemoteStream();

            //停止设备麦克风
            netcall.stopDevice(Netcall.DEVICE_TYPE_AUDIO_IN);

            //停止设备摄像头
            netcall.stopDevice(Netcall.DEVICE_TYPE_VIDEO);

            //停止播放本地音频
            netcall.stopDevice(Netcall.DEVICE_TYPE_AUDIO_OUT_LOCAL);

            //停止播放对端音频
            netcall.stopDevice(Netcall.DEVICE_TYPE_AUDIO_OUT_CHAT);

            if (this.netcallType == 0) { //PCAgent
                //停止信令通道
                netcall.stopSignal();
            }

            this.netcallInited = false;
            this.signalInited = false;
            this.callDone = true;
            this.isMute = false;
            this.calleeOffline = false;
            this.callAccepted = false;
            this.isRemote = true;
        },
        createContainer() {
            let nimContainer = document.getElementById('nimContainer');
            let nimRemoteContainer = document.getElementById('nimRemoteContainer');

            if (!nimContainer || !nimRemoteContainer) {
                nimContainer = document.createElement('div');
                nimRemoteContainer = document.createElement('div');

                nimContainer.id = 'nimContainer';
                nimRemoteContainer.id = 'nimRemoteContainer';

                document.body.appendChild(nimContainer);
                document.body.appendChild(nimRemoteContainer);
            }

            return { nimContainer, nimRemoteContainer };
        },
        appendContainerTo(body) {
            let nimContainer = document.getElementById('nimContainer');
            let nimRemoteContainer = document.getElementById('nimRemoteContainer');
            let { container, remoteContainer } = this.$refs;

            if (nimContainer && nimRemoteContainer) {
                if (body) {
                    document.body.appendChild(nimContainer);
                    document.body.appendChild(nimRemoteContainer);
                } else {
                    container.appendChild(nimContainer);
                    remoteContainer.appendChild(nimRemoteContainer);
                }
            }
        },
        changeContainer() {
            let nimContainer = document.getElementById('nimContainer');
            let nimRemoteContainer = document.getElementById('nimRemoteContainer');
            let { container, remoteContainer } = this.$refs;

            this.isRemote = !this.isRemote;
            if (!this.isRemote) {
                container.appendChild(nimRemoteContainer);
                remoteContainer.appendChild(nimContainer);
            } else {
                container.appendChild(nimContainer);
                remoteContainer.appendChild(nimRemoteContainer);
            }
        },
        netcallInit(endFn) {
            let { nimContainer, nimRemoteContainer } = this.createContainer();

            nimNetcallInit(this.netcallType, nimContainer, nimRemoteContainer);

            this.appendContainerTo(false);

            this.netcallInited = true;

            //初始化信令
            this.signalInit(endFn);

            //被叫收到呼叫通知
            this.beCalling();

            //主叫收到被叫响应回调
            this.callResponse();
        },
        signalInit(endFn) {
            //先检查系统和浏览器是否支持pcAgent
            this.checkPlatform(() => {
                //信令通道初始化完毕之后, 开发者可以启用音视频通话相关的 UI, 比如说展示呼叫别人的按钮
                //信令通道初始化失败的时候, 请展示错误并禁用所有音视频通话相关的 UI
                if (this.netcallType == 0) {
                    this.$notify({
                        title: '提示',
                        message: '插件加载中，请耐心等待',
                        duration: 3000
                    });
                }
                netcall.initSignal().then(() => {
                    //当信令通道断开时, 会触发 signalClosed 事件
                    netcall.off('signalClosed');
                    netcall.on('signalClosed', () => {
                        this.signalInited = false;
                        this.hangup();
                    });

                    //初始化过程中会通过 devices 事件回传所有的设备列表
                    netcall.off('devices');
                    netcall.on('devices', (obj) => {
                        //console.log('on devices',obj);
                    });

                    this.signalInited = true;

                    if (Type(endFn) == 'function') {
                        endFn();
                    }
                }).catch((err) => {
                    console.log(err);
                    this.signalInited = false;
                    if (err.code == 'noPC') {
                        this.$confirm('请安装PC Agent，方可使用音视频功能?', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                        }).then(() => {
                            window.open(this.agentDownloadUrl);
                            this.$message({
                                type: 'success',
                                message: '下载完成后，需手动安装插件!'
                            });
                            setTimeout(() => {
                                this.$message({
                                    type: 'success',
                                    message: '插件安装成功后，请刷新页面！'
                                })
                            }, 3000)
                        })
                    }
                });
            });
        },
        beCalling() {
            netcall.off('beCalling');
            netcall.on('beCalling', (obj) => {
                const channelId = obj.channelId;

                //被叫回应主叫自己已经收到了通话请求
                netcall.control({
                    channelId,
                    command: WebRTC.NETCALL_CONTROL_COMMAND_START_NOTIFY_RECEIVED,
                });

                //只有在没有通话并且没有被叫的时候才记录被叫信息, 否则通知对方忙并拒绝通话
                if (!netcall.calling && !this.netcallInfo.beCalling) {
                    this.netcallInfo.type = obj.type;
                    this.netcallInfo.beCalling = true;
                    this.netcallInfo.beCalledInfo = obj;
                    this.controlNimVideoCall(true);
                } else {
                    if (netcall.calling) {
                        this.netcallInfo.busy = netcall.notCurrentChannelId(obj);
                    } else if (beCalling) {
                        this.netcallInfo.busy = this.netcallInfo.beCalledInfo.channelId !== channelId;
                    }

                    if (this.netcallInfo.busy) {
                        netcall.control({
                            channelId,
                            command: WebRTC.NETCALL_CONTROL_COMMAND_BUSY,
                        });

                        //拒绝通话
                        netcall.response({
                            accepted: false,
                            beCalledInfo: obj,
                        });
                    }
                }
            });
        },
        callResponse() {
            //被叫拒绝的通知
            netcall.off('callRejected');
            netcall.on('callRejected', (obj) => {
                //取消呼叫倒计时
                clearTimeout(this.callTimer);

                //挂断
                this.hangup(true);
            });

            //被叫接受的通知
            netcall.off('callAccepted');
            netcall.on('callAccepted', (obj) => {
                this.callAccepted = true;

                //缓存呼叫类型，后面开启音视频连接需要用到
                this.netcallInfo.type = obj.type;

                //取消呼叫倒计时
                clearTimeout(this.callTimer);

                //可以开启音视频连接操作。。。
                this.videoLink();
            });

            //被叫正忙的通知
            netcall.off('control');
            netcall.on('control', (obj) => {
                let { type } = obj;

                if (type == 9) {
                    alerts('对方正忙，请稍后再试');

                    //取消呼叫倒计时
                    clearTimeout(this.callTimer);

                    //挂断
                    this.hangup(true);
                }
            });

            //收到挂断通知
            netcall.off('hangup');
            netcall.on('hangup', (obj) => {
                let { beCalledInfo } = this.netcallInfo;

                // 判断需要挂断的通话是否是当前正在进行中的通话
                if (!beCalledInfo || beCalledInfo.channelId === obj.channelId) {
                    // 清理工作
                    this.resetWhenHangup();
                }
            });

            //其他端已处理的通知
            netcall.on('callerAckSync', function (obj) {
                console.log('其他端已经做了处理', obj);
            });

            //sdk内部消息通知
            netcall.on('error', function (obj) {
                console.log('sdk反馈错误信息', obj);
                if (obj.code) {
                    if (obj.code == 509) {
                        console.warn('你被提出房间了');
                    } else if (obj.code == 500) {
                        console.warn('你的web不支持H264视频编码，无法进行呼叫，请检测浏览器的H264插件是否正常');
                    }
                }
            });
        },
        videoLink() {
            if (this.netcallType == 0) { //开启PCAgent连接
                this.videoLinkByPcAgent()
            } else { //开启WebRTC连接
                this.videoLinkByWebRtc()
            }
        },
        videoLinkByPcAgent() {
            let { scene, to } = this.nimChat;

            //开启麦克风
            netcall.startDevice({
                type: Netcall.DEVICE_TYPE_AUDIO_IN,
            }).then(() => {
                //通知对方自己开启了麦克风
                netcall.control({
                    command: Netcall.NETCALL_CONTROL_COMMAND_NOTIFY_AUDIO_ON,
                });
            }).catch(function (err) {
                console.log(err);
            });

            //开启摄像头
            netcall.startDevice({
                type: Netcall.DEVICE_TYPE_VIDEO,
                width: this.width,
                height: this.height,
            }).then(() => {
                //通知对方自己开启了摄像头
                netcall.control({
                    command: Netcall.NETCALL_CONTROL_COMMAND_NOTIFY_VIDEO_ON,
                });
            }).catch((err) => {
                //通知对方自己的摄像头不可用
                netcall.control({
                    command: Netcall.NETCALL_CONTROL_COMMAND_SELF_CAMERA_INVALID,
                });
                console.log(err);
            })

            //开启本地视频预览
            netcall.startLocalStream();

            //开启远程视频预览
            netcall.startRemoteStream();

            //开启本地音频播放
            netcall.startDevice({
                type: Netcall.DEVICE_TYPE_AUDIO_OUT_CHAT,
            }).catch((err) => {
                console.log(err);
            })

            //设置本地音量采集大小, 该API可以在通话过程中动态调用调整自己的音量采集大小
            netcall.setCaptureVolume(255);

            //设置本地音量播放大小, 该API可以在通话过程中动态调用调整自己的音量播放大小(即自己听对端的音量)
            netcall.setPlayVolume(255);

            //设置本地视频画面大小
            netcall.setVideoViewSize({
                width: this.width,
                height: this.height,
                cut: true,
            });

            //设置远程视频画面大小
            netcall.setVideoViewRemoteSize({
                account: to,
                width: this.width,
                height: this.height,
                cut: true,
            });

            this.controlNimVideoCall(true);
        },
        videoLinkByWebRtc() {
            let { to } = this.nimChat
            console.log(this.width, this.height, to)
            netcall.startRtc().then(() => {
                    // 开启麦克风
                    return netcall.startDevice({
                        type: Netcall.DEVICE_TYPE_AUDIO_IN,
                    }).then(() => {
                        //通知对方自己开启了麦克风
                        netcall.control({
                            command: Netcall.NETCALL_CONTROL_COMMAND_NOTIFY_AUDIO_ON,
                        });
                    }).catch(function (err) {
                        console.log(err);
                    });
                }).then(() => {
                    // 设置采集音量
                    netcall.setCaptureVolume(255)
                    // 开启摄像头
                    netcall.startDevice({
                        type: Netcall.DEVICE_TYPE_VIDEO,
                        width: this.width,
                        height: this.height,
                    }).then(() => {
                        //通知对方自己开启了摄像头
                        netcall.control({
                            command: Netcall.NETCALL_CONTROL_COMMAND_NOTIFY_VIDEO_ON,
                        });
                    }).catch((err) => {
                        //通知对方自己的摄像头不可用
                        netcall.control({
                            command: Netcall.NETCALL_CONTROL_COMMAND_SELF_CAMERA_INVALID,
                        });
                        console.log(err);
                    })
                }).then(() => {
                    //预览本地画面
                    netcall.startLocalStream()

                    // 设置本地预览画面大小
                    netcall.setVideoViewSize({
                        width: this.width,
                        height: this.height,
                        cut: true
                    })
                })
                .catch(function (err) {
                    console.log('发生错误')
                    console.log(err)
                    netcall.hangup()
                })

            // 在回调里监听对方加入通话，并显示对方的视频画面
            netcall.on('remoteTrack', (obj) => {
                console.log('user join', obj)
                // 播放对方声音
                netcall.startDevice({
                    type: Netcall.DEVICE_TYPE_AUDIO_OUT_CHAT
                }).catch(function (err) {
                    console.log('播放对方的声音失败')
                    console.error(err)
                })
                // 预览对方视频画面
                netcall.startRemoteStream({
                    account: to
                })
                // 设置对方预览画面大小
                netcall.setVideoViewRemoteSize({
                    account: to,
                    width: this.width,
                    height: this.height,
                    cut: true,
                });
            })
            this.controlNimVideoCall(true);
        },
        sendVideoMessage(channelId = '') {
            channelId = channelId.toString()
            //防止多次发送通知
            if (!this.sendDone) {
                sendVideoMessage([this.buzType, this.buzId, channelId], (res) => {
                    if (res.code == 200) {
                        this.sendDone = true
                        clearTimeout(this.sendTimer)
                        this.sendTimer = setTimeout(() => {
                            this.sendDone = false
                            console.log(this.sendDone, 'setTimeout success')
                        }, 30000)
                        console.log('sendVideoMessage success')
                    }
                })
            }
        },
        call() {
            if (!this.callDone) return alert('请不要频繁发起');
            let { scene, to } = this.nimChat;
            let { pushConfig, sessionConfig, patientInfo = {}, type } = this;
            let { patientName, startTime, endTime } = patientInfo;
            let iDate = +new Date();

            pushConfig.custom = this.getCustom();
            pushConfig.pushPayload = this.getCustom();
            const netcallCall = () => {
                this.controlNimVideoCall(true);
                this.netcallCall(to, pushConfig, sessionConfig)
                //设置超时计时器
                clearTimeout(this.callTimer);
                this.callTimer = setTimeout(() => {
                    if (!netcall.callAccepted) {
                        this.hangup();
                        clearInterval(this.offlineTimer)
                        this.offlineNum = 0
                    }
                }, 1000 * 30);
            };
            this.judgeStatusBox(type, startTime, endTime, iDate, netcallCall)
        },
        netcallCall(to, pushConfig, sessionConfig) {
            netcall.call({
                type: Netcall.NETCALL_TYPE_VIDEO,
                account: to,
                pushConfig,
                sessionConfig,
                webrtcEnable: true,
            }).then((obj) => {
                //成功发起呼叫
                this.sendVideoMessage(obj.channelId)
                clearInterval(this.offlineTimer)
                this.offlineNum = 0
                vm.$emit('nimVideoCallSuccess', obj);
                console.log('call success', obj);
            }).catch((err) => {
                console.log(err);
                //被叫不在线
                vm.$emit('nimVideoCallFail', err);
                if (err.event && err.event.code === 11001 || err.event && err.event.event && err.event.event.code === 11001) {
                    console.log('callee offline');
                    // this.calleeOffline=true;
                    if (this.offlineNum == 0) {
                        this.sendVideoMessage()
                    }
                    clearInterval(this.offlineTimer)
                    this.offlineTimer = setInterval(() => {
                        console.log(this.offlineNum, 'offNum')
                        this.netcallCall(to, pushConfig, sessionConfig)
                        this.offlineNum = this.offlineNum + 1
                    }, 2000)
                    vm.$emit('nimVideoCallcalleeOffline', err);
                }

                //防止频繁发起
                clearTimeout(this.callDoneTimer);
                this.callDoneTimer = setTimeout(() => {
                    this.callDone = true;
                }, 3000);
            });
        },
        judgeStatusBox(type, startTime, endTime, iDate, netcallCall) {
            const callFn = () => {
                this.callDone = false;
                this.netcallInit(netcallCall);
            };

            if (type == 'video') {
                startTime = dateFormat0(startTime, 'yyyy-MM-dd hh:mm');
                endTime = dateFormat0(endTime, 'yyyy-MM-dd hh:mm');
                startTime = +normalDate(startTime);
                endTime = +normalDate(endTime);
                if (iDate < startTime) {
                    this.$msgbox({
                        title: '',
                        message: '还未到预约时间，是否发起视频',
                        showConfirmButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '发起',
                    }).then(() => {
                        this.judgeStatus(callFn);
                    });
                } else {
                    this.judgeStatus(callFn);
                }
            } else {
                this.judgeStatus(callFn);
            }

        },
        response() {
            let { sessionConfig, netcallInfo } = this;
            let { beCalledInfo } = netcallInfo;

            netcall.response({
                accepted: true,
                beCalledInfo,
                sessionConfig,
            }).catch((err) => {
                reject();
                console.log('接听失败', err);
            });

            //拒绝
            //可以先通知对方自己忙, 拒绝的时候需要回传在 `beCalling` 事件里面接收到的对象
            this.netcallInfo.beCalledInfo = false;

            netcall.control({
                channelId: beCalledInfo.channelId,
                command: WebRTC.NETCALL_CONTROL_COMMAND_BUSY,
            });
            netcall.response({
                accepted: false,
                beCalledInfo,
            });
            this.netcallInfo.beCalledInfo = null;
            this.netcallInfo.beCalling = false;
        },
        muteSwitch() {
            this.isMute = !this.isMute;
            netcall.setCaptureVolume(this.isMute ? 0 : 255);
        },
    },

    components: {
        yydDialog,
        previewPrescription,
        previewInsApp
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.onlineConsultation {
    max-width: 1200px;
    display: none;
    &.active {
        display: block;
    }
    &.imgText {
        .patientWrap {
            border-bottom: none !important;
        }
        .basicInfo {
            display: block !important;
        }
    }
    &.video {
        .videoWrap {
            display: block !important;
        }
        .patientWrap {
            border-bottom: none !important;
        }
        .patientInfo {
            .rightContent {
                h4 {
                    display: block !important;
                }
            }
        }
        .basicInfo {
            display: block !important;
        }
    }
    &.visit {
        .videoWrap {
            display: block !important;
        }
        .patientWrap {
            border-bottom: none !important;
        }
        .patientInfo {
            .rightContent {
                h3 {
                    >a {
                        display: block !important;
                    }
                }
            }
        }
        .visitInfo {
            display: block !important;
        }
    }
    .content {
        @include content;
        display: flex;
        overflow: hidden;
        .chatWrap {
            flex: 1;
            .chatMessage {
                .patient {
                    padding-left: 30px;
                    .patientWrap {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: $border1;
                        padding: 15px 0;
                        line-height: 60px;
                        .patientInfo {
                            @include patientInfo('../../../', 64, 27);
                            display: flex;
                            flex: 1;
                            padding: 0;
                            .rightContent {
                                h3 {
                                    >a {
                                        display: none;
                                    }
                                }
                                h4 {
                                    display: block;
                                    color: $main;
                                }
                            }
                        }
                        >.rightContent {
                            width: 150px;
                            padding: 0 20px;
                            .finish {
                                display: inline-block;
                                width: 100%;
                                height: 32px;
                                line-height: 32px;
                                text-align: center;
                                font-size: 12px;
                                background-color: #ff8081;
                                color: #fff;
                                border-radius: 16px;
                            }
                        }
                    }
                }
                .basicInfo, .visitInfo {
                    display: none;
                    .infoWrap {
                        height: 0;
                        padding: 0 30px;
                        overflow: hidden;
                        transition: all .5s ease-out;
                        .title {
                            @include basicTitle;
                        }
                        .main {
                            padding: 10px 0;
                            overflow: hidden;
                            li {
                                display: flex;
                                padding: 5px 0;
                                line-height: 26px;
                                span {
                                    width: 200px;
                                    text-align: right;
                                    color: #666;
                                }
                                em {
                                    flex: 1;
                                    padding-left: 24px;
                                }
                            }
                            a {
                                display: inline-block;
                                padding: 3px 7px;
                                border: 1px solid #52a3fc;
                                color: #52a3fc;
                                border-radius: 20px;
                                text-align: center;
                                line-height: 26px;
                            }
                            p {
                                padding: 5px 0;
                                width: 100%;
                                text-align: center;
                                line-height: 26px;
                            }
                        }
                        &.active {
                            height: auto;
                            padding: 10px 30px;
                        }
                    }
                    .controlInfo {
                        text-align: center;
                        position: relative;
                        &:before {
                            content: "";
                            width: 100%;
                            height: 1px;
                            background-color: #e4e4e4;
                            position: absolute;
                            left: 0;
                            top: 50%;
                        }
                        a {
                            display: flex;
                            justify-content: center;
                            width: 135px;
                            margin: 0 auto;
                            height: 28px;
                            line-height: 26px;
                            background-color: #fff;
                            border: 1px solid $main;
                            border-radius: 14px;
                            color: $main;
                            position: relative;
                            z-index: 10;
                            i {
                                width: 30px;
                                height: 100%;
                                background: url('../../../assets/img/down.png') no-repeat center center;
                                background-size: 10px;
                                transition: transform .3s ease-out;
                            }
                        }
                        &.active {
                            a {
                                i {
                                    transform: rotate3d(0, 0, 1, -180deg);
                                }
                            }
                        }
                    }
                }
                .messageList {
                    padding: 20px 0;
                    height: 320px;
                    overflow-y: auto;
                    .loadingWrap {
                        @include loadingWrap('../../../');
                    }
                    >ul {
                        >li {
                            padding: 0 30px;
                            .time {
                                padding-top: 20px;
                                padding-bottom: 10px;
                                line-height: 20px;
                                text-align: center;
                                font-size: 12px;
                                color: $gray;
                            }
                            .messageWrap {
                                padding: 10px 0;
                                overflow: hidden;
                                .hint {
                                    padding-top: 10px;
                                    padding-bottom: 20px;
                                    text-align: center;
                                    >span {
                                        display: inline-block;
                                        padding: 8px 24px;
                                        line-height: 20px;
                                        text-align: left;
                                        background-color: $bg;
                                        color: #999;
                                        border-radius: 16px;
                                    }
                                }
                                .outerCustom {}
                                .portrait, .message {
                                    float: left;
                                }
                                .portrait {
                                    @include portrait('../../../');
                                }
                                .message {
                                    max-width: 60%;
                                    min-height: 40px;
                                    padding: 7px 15px;
                                    margin: 0 10px;
                                    line-height: 24px;
                                    border: $border1;
                                    border-radius: 0 10px 10px 10px;
                                    overflow: hidden;
                                    .file {
                                        img, video {
                                            max-width: 200px !important;
                                        }
                                        img {
                                            cursor: pointer;
                                        }
                                    }
                                    .attach {
                                        padding-left: 20px;
                                        background: url('../../../assets/img/icon_vieo.png') no-repeat left center;
                                        background-size: 12px;
                                    }
                                    .download {
                                        display: block;
                                        width: 100px;
                                        padding: 0 10px;
                                        padding-top: 80px;
                                        line-height: 20px;
                                        text-align: center;
                                        background: #fff url('../../../assets/img/icon_page.png') no-repeat top center;
                                        background-size: 80px;
                                        color: #000;
                                        border-radius: 5px;
                                        h4 {
                                            padding-top: 10px;
                                        }
                                    }
                                    .innerCustom {
                                        .patientBrief {
                                            .helInfo {
                                                height: auto;
                                                word-break: break-all;
                                            }
                                            .imgList {
                                                overflow: hidden;
                                                li {
                                                    float: left;
                                                    width: 33.3333%;
                                                    padding-right: 10px;
                                                    padding-bottom: 10px;
                                                    cursor: pointer;
                                                    img {
                                                        width: 100%;
                                                        height: 90px;
                                                    }
                                                }
                                            }
                                        }
                                        .record, .prescription {
                                            width: 200px;
                                            background-color: #eee;
                                            color: #333;
                                            border-radius: 10px;
                                            cursor: pointer;
                                            .title {
                                                padding: 0 10px;
                                                .wrap {
                                                    display: block;
                                                    padding: 0 10px;
                                                    line-height: 30px;
                                                    background: url('../../../assets/img/arrow_right.png') no-repeat right center;
                                                    background-size: 8px;
                                                    border-bottom: $border1;
                                                    em {
                                                        color: $red;
                                                    }
                                                }
                                            }
                                            .main {
                                                display: flex;
                                                padding: 10px;
                                                .img {
                                                    width: 40px;
                                                    height: 40px;
                                                    background: url('../../../assets/img/icon_page.png') no-repeat right center;
                                                    background-size: cover;
                                                }
                                                .rightContent {
                                                    flex: 1;
                                                    padding-left: 10px;
                                                    line-height: 20px;
                                                }
                                            }
                                        }
                                        .videoDuration {
                                            padding-left: 20px;
                                            background: url('../../../assets/img/icon_vieo.png') no-repeat left center;
                                            background-size: 12px;
                                        }
                                    }
                                }
                            }
                            &.myself {
                                .messageWrap {
                                    .portrait, .message {
                                        float: right;
                                    }
                                    .portrait {
                                        background-image: url('../../../assets/img/doc_avatar.png');
                                    }
                                    .message {
                                        background-color: $main;
                                        color: #fff;
                                        border-color: transparent;
                                        border-radius: 10px 0 10px 10px;
                                    }
                                }
                            }
                        }
                    }
                }
                .end {
                    padding: 0 30px;
                    border-top: $border1;
                    position: relative;
                    .toolbar {
                        padding: 5px 0;
                        height: 30px;
                        line-height: 50px;
                        overflow: hidden;
                        a {
                            float: left;
                            width: 20px;
                            margin-right: 15px;
                            height: 20px;
                            background: url('../../../assets/img/icon_vol.png') no-repeat center center;
                            background-size: 16px;
                            position: relative;
                            label {
                                cursor: pointer;
                            }
                        }
                    }
                    .quickRes {
                        width: 100%;
                        position: absolute;
                        left: -5px;
                        overflow-y: auto;
                        background: #fff;
                        li {
                            padding: 5px 20px;
                            line-height: 24px;
                            text-align: left;
                            cursor: pointer;
                            &.active {
                                background-color: $blueBg;
                                color: $main;
                            }
                            span {
                                padding-right: 10px;
                                &:last-of-type {
                                    padding-right: 0;
                                }
                            }
                        }
                    }
                    .inputWrap {
                        width: 100%;
                        height: 50px;
                        line-height: 25px;
                        border: none;
                    }
                    .buttonWrap {
                        display: flex;
                        justify-content: flex-end;
                        padding: 5px 0;
                        a {
                            width: 90px;
                            height: 30px;
                            line-height: 30px;
                            text-align: center;
                            font-size: 12px;
                            background-color: $main;
                            color: #fff;
                            border-radius: 15px;
                        }
                    }
                }
            }
        }
        .videoWrap {
            width: 320px;
            background: url('../../../assets/img/bg_fzpy_video.png') no-repeat center center;
            background-size: cover;
            position: relative;
            display: none;
            .wrap {
                width: 100%;
                height: 568px;
                left: 0;
                top: 50%;
                transform: translate3d(0, -50%, 0);
                position: absolute;
                .containerWrap {
                    width: 100%;
                    height: 100%;
                    background-color: #000;
                    position: absolute;
                    left: 0;
                    top: 0;
                    opacity: 0;
                    filter: alpha(opacity=0);
                    /deep/ canvas {
                        width: 100%;
                        height: 100%;
                    }
                    .remoteContainerPA {
                        width: 100%;
                        height: 100%;
                    }
                    .containerPA {
                        width: 80px;
                        height: 142px;
                        position: absolute;
                        right: 20px;
                        top: 20px;
                        z-index: 10;
                        cursor: pointer;
                    }
                    .remoteContainerWR {
                        width: 100%;
                        height: 100%;
                        /deep/ div {
                            width: 100% !important;
                            height: 100% !important;
                            max-width: 100%;
                            max-height: 100%;
                        }
                        /deep/ video {
                            height: 568px !important;
                            width: auto !important;
                        }
                    }
                    .containerWR {
                        width: 80px;
                        height: 142px;
                        position: absolute;
                        right: 20px;
                        top: 20px;
                        z-index: 10;
                        cursor: pointer;
                        /deep/ div {
                            width: 100% !important;
                            height: 100% !important;
                            max-width: 100%;
                            max-height: 100%;
                        }
                        /deep/ video {
                            height: 142px !important;
                            width: auto !important;
                        }
                    }
                    &.active {
                        opacity: 1;
                        filter: alpha(opacity=1);
                    }
                }
                .call {
                    padding-top: 80px;
                    background: url('../../../assets/img/vedioPic.png') no-repeat top center;
                    background-size: 90px;
                    line-height: 20px;
                    text-align: center;
                    color: #999;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    z-index: 10;
                    transform: translate3d(-50%, -50%, 0);
                }
                .maskWrap {
                    width: 100%;
                    height: 200px;
                    background-color: transparent;
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    z-index: 10;
                    display: none;
                    .patientInfo {
                        @include patientInfo('../../../', 48, 19);
                        padding: 20px;
                        width: 200px;
                        position: absolute;
                        left: 0;
                        top: -368px;
                        overflow: hidden;
                        h3, h4 {
                            color: #fff !important;
                        }
                    }
                    .handle {
                        width: 100%;
                        text-align: center;
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        .muteWrap {
                            padding-bottom: 40px;
                            a {
                                display: inline-block;
                                width: 60px;
                                padding-top: 30px;
                                line-height: 20px;
                                font-size: 12px;
                                background: url('../../../assets/img/icon_mute.png') no-repeat top center;
                                background-size: 20px;
                                color: #fff;
                                &.active {
                                    background-image: url('../../../assets/img/icon_voice.png');
                                }
                            }
                        }
                        .hangupWrap {
                            padding-bottom: 56px;
                            a {
                                display: inline-block;
                                width: 50px;
                                height: 50px;
                                background: url('../../../assets/img/icon_refuse.png') no-repeat center center;
                                border-radius: 50%;
                                background-size: cover;
                            }
                        }
                    }
                }
            }
            &.active {
                background: rgba(0, 0, 0, .8);
                .wrap {
                    .call {
                        display: none;
                    }
                    .maskWrap {
                        display: block;
                    }
                }
            }
        }
    }
    .previewBigImage {
        img {
            height: 100%;
        }
    }
    .previewPrescription {
        iframe {
            width: 100%;
            height: 100%;
        }
    }
}

</style>

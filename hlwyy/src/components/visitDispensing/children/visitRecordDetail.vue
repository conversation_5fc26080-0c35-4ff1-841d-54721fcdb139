<template>
    <div :class="{
            visitRecordDetail:true,
            active:show,
        }"
    >
        <section class="content">
            <div class="goBackWrap">
                <a
                    @click="goBack"
                >返回上一层</a>
            </div>
            <div class="main">
                <ol class="tab">
                    <li
                        v-for="(item,index) in ['门诊病历','药品处方','检查','检验']"
                        @click="changeTab(index)"
                        :class="{
                            active:tabIndex==index,
                        }"
                    >
                        {{item}}
                    </li>
                </ol>
                <div class="option">
                    <div v-show="tabIndex==0||tabIndex==1" class="patientWrap">
                        <div class="patientInfo active">
                            <div
                                class="portrait"
                                :style="{
                                    backgroundImage:patientInfo.avatar?`url(${patientInfo.avatar})`:'',
                                }"
                            ></div>
                            <div class="rightContent">
                                <h3>
                                    <span>{{patientInfo.patientName}}</span>
                                    <em
                                        :class="{
                                            female:patientInfo.sex!='1',
                                        }"
                                    >{{patientInfo.age}}岁</em>
                                </h3>
                                <h4>
                                    <span>诊断：{{listItem.diagnoseName}}</span>
                                    <span>医生：{{listItem.doctorName}}</span>
                                    <span>日期：{{listItem.visitDateTime|date('yyyy-MM-dd')}}</span>
                                </h4>
                            </div>
                        </div>
                        <div class="rightContent">
                            <h3>
                                <span>{{listItem.standardDeptName}}</span>
                            </h3>
                            <h4>门诊号：{{listItem.visitId}}</h4>
                        </div>
                    </div>

                    <div v-show="tabIndex==0" class="mzblWrap">
                        <ul class="infoList">
                            <li>
                                <span>主诉：</span>
                                <em>{{recordDetail.mainComplaint||'暂无'}}</em>
                            </li>
                            <li>
                                <span>现病史：</span>
                                <em>{{recordDetail.medicalhostoty||'暂无'}}</em>
                            </li>
                            <li>
                                <span>既往史：</span>
                                <em>{{recordDetail.pastHostory||'暂无'}}</em>
                            </li>
                            <!-- <li>
                                <span>体格检查：</span>
                                <em>{{recordDetail.physicalExamination||'暂无'}}</em>
                            </li> -->
                            <li>
                                <span>辅助检查：</span>
                                <em>{{recordDetail.supplementaryExamination||'暂无'}}</em>
                            </li>
                            <li>
                                <span>诊断：</span>
                                <em>{{listItem.diagnoseName}}</em>
                            </li>
                            <li>
                                <span>处理意见：</span>
                                <em>{{recordDetail.handlingOpinions||'暂无'}}</em>
                            </li>
                        </ul>
                    </div>

                    <div
                        v-show="tabIndex==1"
                        :class="{
                            ypcfWrap:true,
                            active:prescriptionList.length,
                        }"
                    >
                        <ul
                            v-for="item in prescriptionList"
                            v-if="prescriptionList.length"
                            class="drugList"
                        >
                            <li v-for="item1 in item.drugs">
                                <div class="wrap">
                                    <div class="left">
                                        <h3>
                                            <span>{{item1.drugName}}</span>
                                            <span>{{item1.drugSpecifications}}</span>
                                        </h3>
                                        <h4>
                                            <span>用法：每次{{item1.drugUseDose}}{{item1.drugUseDoseUnit}}</span>
                                            <span>{{item1.drugUsingRate}}</span>
                                            <span>{{item1.drugUsePathwaysCode}}</span>
                                        </h4>
                                    </div>
                                    <div class="right">
                                        <h3>
                                            <span>〤{{item1.drugSendNumber}}{{item1.drugSendUnit}}</span>
                                        </h3>
                                        <h4 style="color: #999;">
                                            <span>〤{{item1.drugUseDays}}天</span>
                                        </h4>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div v-show="!prescriptionList.length&&loaded1" class="defaultImageWrap">
                            <defaultImage
                                :show="true"
                                :showIndex="5"
                            />
                        </div>
                    </div>

                    <div v-show="tabIndex==2" class="jcWrap">
                        <div
                            v-for="(tableName, tableIndex) in ['theadWrap','tbodyWrap']"
                            :key="tableIndex"
                            v-if="jcList.length"
                            :class="{
                                tableWrap:true,
                                [tableName]:true,
                            }"
                        >
                            <table>
                                <thead>
                                    <tr>
                                        <th v-for="(item,index) in ['项目','时间','操作']" :key="index">
                                            <div class="item">
                                                {{item}}
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item,index) in jcList"
                                        :key="index"
                                        :class="{
                                            active:index==currentHandleIndex1,
                                        }"
                                    >
                                        <td>
                                            <div class="item">
                                                {{item.itemName}}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="item">
                                                {{item.reportDt|date('yyyy-MM-dd hh:mm:ss')}}
                                            </div>
                                        </td>
                                         <td>
                                            <div class="item handle">
                                                <a
                                                    @click="checkJcDetail(item,index)"
                                                >查看</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-show="!jcList.length&&loaded2" class="defaultImageWrap">
                            <defaultImage
                                :show="true"
                                :showIndex="6"
                            />
                        </div>
                    </div>

                    <div v-show="tabIndex==3" class="jyWrap">
                        <div
                            v-for="(tableName, tableIndex) in ['theadWrap','tbodyWrap']" :key="tableIndex"
                            v-if="jyList.length"
                            :class="{
                                tableWrap:true,
                                [tableName]:true,
                            }"
                        >
                            <table>
                                <thead>
                                    <tr>
                                        <th v-for="(item, index) in ['项目','时间','操作']" :key="index">
                                            <div class="item">
                                                {{item}}
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        v-for="(item,index) in jyList"
                                        :key="index"
                                        :class="{
                                            active:index==currentHandleIndex2,
                                        }"
                                    >
                                        <td>
                                            <div class="item">
                                                {{item.itemName}}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="item">
                                                {{item.reportDt|date('yyyy-MM-dd hh:mm:ss')}}
                                            </div>
                                        </td>
                                         <td>
                                            <div class="item handle">
                                                <a
                                                    @click="checkJyDetail(item,index)"
                                                >查看</a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div v-show="!jyList.length&&loaded3" class="defaultImageWrap">
                            <defaultImage
                                :show="true"
                                :showIndex="7"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="jcDetail">
            <yydDialog
                :parent="this"
                :show="showJcDetail"
                :showName="'showJcDetail'"
                :width="800"
                :height="600"
            >
                <div class="title">
                    <h3>检查报告</h3>
                </div>
                <div class="main">
                    <ul class="reportInfoList">
                        <li>
                            <span>项目：</span>
                            <em class="multiLine">{{jcList[currentHandleIndex1]&&jcList[currentHandleIndex1].itemName}}</em>
                        </li>
                        <li>
                            <span>患者姓名：</span>
                            <em class="multiLine">{{patientInfo.patientName}}</em>
                        </li>
                        <li>
                            <span>科室：</span>
                            <em class="multiLine">{{patientInfo.deptName}}</em>
                        </li>
                        <li>
                            <span>送检医生：</span>
                            <em class="multiLine">未知</em>
                        </li>
                        <li>
                            <span>送检时间：</span>
                            <em class="multiLine">{{jcDetail.createDt|date('yyyy-MM-dd')||'未知'}}</em>
                        </li>
                        <li>
                            <span>报告医生：</span>
                            <em class="multiLine">未知</em>
                        </li>
                        <li>
                            <span>报告时间：</span>
                            <em class="multiLine">{{jcDetail.reportDt|date('yyyy-MM-dd')||'未知'}}</em>
                        </li>
                    </ul>
                    <ul class="reportInfoList oneLine">
                        <li>
                            <span>临床诊断：</span>
                            <em>{{patientInfo.diagnoseName}}</em>
                        </li>
                        <li>
                            <span>影像所见：</span>
                            <em>{{jcDetail.examView}}</em>
                        </li>
                        <li>
                            <span>诊断意见：</span>
                            <em>{{jcDetail.diagnoseOpinion}}</em>
                        </li>
                    </ul>
                </div>
            </yydDialog>
        </div>

        <div class="jyDetail">
            <yydDialog
                :parent="this"
                :show="showJyDetail"
                :showName="'showJyDetail'"
                :width="800"
                :height="600"
            >
                <div class="title">
                    <h3>检验报告</h3>
                </div>
                <div class="main">
                    <ul class="reportInfoList">
                        <li>
                            <span>项目：</span>
                            <em class="multiLine">{{jyList[currentHandleIndex2]&&jyList[currentHandleIndex2].itemName}}</em>
                        </li>
                        <li>
                            <span>患者姓名：</span>
                            <em class="multiLine">{{patientInfo.patientName}}</em>
                        </li>
                        <li>
                            <span>科室：</span>
                            <em class="multiLine">{{patientInfo.deptName}}</em>
                        </li>
                        <li>
                            <span>送检医生：</span>
                            <em class="multiLine">未知</em>
                        </li>
                        <li>
                            <span>送检时间：</span>
                            <em class="multiLine">{{jyDetail.createDt|date('yyyy-MM-dd')||'未知'}}</em>
                        </li>
                        <li>
                            <span>报告医生：</span>
                            <em class="multiLine">未知</em>
                        </li>
                        <li>
                            <span>报告时间：</span>
                            <em class="multiLine">{{jyDetail.reportDt|date('yyyy-MM-dd')||'未知'}}</em>
                        </li>
                    </ul>
                    <div
                        v-for="tableName in ['theadWrap','tbodyWrap']"
                        v-if="jyDetail.details&&jyDetail.details.length"
                        :class="{
                            tableWrap:true,
                            [tableName]:true,
                        }"
                    >
                        <table>
                            <thead>
                                <tr>
                                    <th v-for="item in ['项目','结果','参考范围','单位']">
                                        <div class="item">
                                            {{item}}
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="(item,index) in jyDetail.details"
                                >
                                    <td>
                                        <div class="item">
                                            {{item.lab&&item.lab.localText}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.result}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.referLower}}-{{item.referUpper}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.resultUnit}}
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </yydDialog>
        </div>
    </div>
</template>

<script>
    import yydDialog from 'components/common/yydDialog';
    import defaultImage from 'components/common/defaultImage';
    import {sStore} from 'js/yydjs';
    import {findDiagnosisRecordDetail,findRecipeByVisitId,queryRevisitHistoryReport,getRevisitExamReport,getRevisitLabReport} from 'services';

    export default{
        data(){
            return{
                query:this.$router.currentRoute.query,
                tabIndex:0,
                currentHandleIndex1:-1,
                showJcDetail:false,
                currentHandleIndex2:-1,
                showJyDetail:false,
                params:[
                    '',//orgId
                    '',//sourceVisitId
                    '',//mpiId
                ],
                params1:[
                    '',//orgId
                    '',//visitId
                    '',//patientId
                ],
                params21:[
                    '',//orgId
                    '',//visitId
                    '',//mpiId
                    '1',//reportType
                    '',//clinicId
                ],
                params22:[
                    '',//orgId
                    '',//visitId
                    '',//mpiId
                    '2',//reportType
                    '',//clinicId
                ],
                params3:[
                    '',//orgId
                    '',//mpiId
                    '',//sourceId
                ],
                params4:[
                    '',//orgId
                    '',//mpiId
                    '',//sourceId
                    '',//reportViewType
                ],
                recordDetail:{},
                prescriptionList:[],
                jcList:[],
                jyList:[],
                jcDetail:{},
                jyDetail:{},
                doctorName:sStore.get('doctorName'),
                loaded1:false,
                loaded2:false,
                loaded3:false,
                tabIndexOne: false,
                tabIndexTwo: false,
                tabIndexThree: false,
                tabIndexFour: false,
            }
        },

        computed:{
            orgId(){
                let {listItem={}}=this;
                let {orgId}=listItem;

                return orgId;
            },
            revisitId(){
                let {patientInfo={}}=this;
                let {revisitId}=patientInfo;

                return revisitId;
            },
            visitId(){
                let {listItem={}}=this;
                let {visitId}=listItem;

                return visitId;
            },
            mpiId(){
                let {patientDetailInfo={}}=this;
                let {mpiId}=patientDetailInfo;

                return mpiId;
            },
            patientId(){
                let {patientDetailInfo={}}=this;
                let {patientId=''}=patientDetailInfo;

                return patientId;
            },
            diseaseSituation(){
                let {patientDetailInfo={}}=this;
                let {diseaseSituation={}}=patientDetailInfo;

                return diseaseSituation;
            },
        },

        /*
            <visitRecordDetail
                v-if="showRecordDetail"
                :parent="this"
                :show="true"
                :showName="'showRecordDetail'"
                :patientInfo="patientInfo"
                :patientDetailInfo="patientDetailInfo"
                :listItem="listItem"
            />
        */

        props:{
            parent:{//父组件的this
                type:Object,
                default:null,
            },
            show:{//是否显示该组件
                type:Boolean,
                default:false,
            },
            showName:{//控制该组件显示的名字
                type:String,
                default:'',
            },
            patientInfo:{//当前患者的信息
                type:Object,
                default(){
                    return {};
                },
            },
            patientDetailInfo:{//当前患者的详细信息
                type:Object,
                default(){
                    return {healthinfo:{}};
                },
            },
            listItem:{//列表项目的数据
                type:Object,
                default(){
                    return {};
                },
            },
        },

        mounted(){
            this.changeTab(0)
            // // 获取门诊病历详情
            // this.getRecordDetail();

            // //获取处方列表
            // this.getPrescriptionList();

            // //获取检查列表
            // this.getJcOrJyList('1');

            // //获取检验列表
            // this.getJcOrJyList('2');
        },

        methods:{
            changeTab (index) {
                this.tabIndex = index
                switch (index) {
                    case 0:
                        if (this.tabIndexOne == false) {
                            //获取门诊病历详情
                            this.getRecordDetail();
                        }
                    break;
                    case 1:
                        if (this.tabIndexTwo == false) {
                            //获取处方列表
                            this.getPrescriptionList();
                        }
                    break;
                    case 2:
                        if (this.tabIndexThree == false) {
                            //获取检查列表
                            this.getJcOrJyList('1');
                        }
                    break;
                    case 3:
                        if (this.tabIndexFour == false) {
                            //获取检验列表
                            this.getJcOrJyList('2');
                        }
                    break;
                }
            },
            goBack(){
                let {parent,showName}=this;

                if(parent&&showName){
                    parent[showName]=false;
                }
            },
            checkJcDetail(item,index){
                this.currentHandleIndex1=index;
                this.getJcDetail(item);
                this.showJcDetail=true;
            },
            checkJyDetail(item,index){
                this.currentHandleIndex2=index;
                this.getJyDetail(item);
                this.showJyDetail=true;
            },
            getRecordDetail(){
                let {orgId,visitId,mpiId}=this;

                this.params[0]=orgId;
                this.params[1]=visitId;
                this.params[2]=mpiId;
                findDiagnosisRecordDetail(this.params,(res)=>{
                    if(res.body){
                        this.tabIndexOne = true
                        this.recordDetail=res.body;
                    } else {
                        this.tabIndexOne = false
                    }
                });
            },
            getPrescriptionList(){
                let {orgId,visitId,patientId}=this;

                this.params1[0]=orgId;
                this.params1[1]=visitId;
                this.params1[2]=patientId;
                findRecipeByVisitId(this.params1,(res)=>{
                    this.loaded1=true;
                    if(res.body){
                        this.tabIndexTwo = true
                        this.prescriptionList=res.body;
                    } else {
                        this.tabIndexTwo = false
                    }
                });
            },
            getJcOrJyList(reportType='1'){
                let {orgId,visitId,mpiId,clinicId}=this;
                let paramsName=reportType=='1'?'params21':'params22';

                this[paramsName][0]=orgId;
                this[paramsName][1]=visitId;
                this[paramsName][2]=mpiId;
                this[paramsName][4]=clinicId;
                queryRevisitHistoryReport(this[paramsName],(res)=>{
                    this[reportType=='1'?'loaded2':'loaded3']=true;
                    if(res.body){
                        this[reportType=='1'?'tabIndexThree':'tabIndexFour'] = true
                        this[reportType=='1'?'jcList':'jyList']=res.body;
                    } else {
                        this[reportType=='1'?'tabIndexThree':'tabIndexFour'] = false
                    }
                });
            },
            getJcDetail(item){
                let {orgId,mpiId}=this;
                let {sourceId}=item;

                this.params3[0]=orgId;
                this.params3[1]=mpiId;
                this.params3[2]=sourceId;
                getRevisitExamReport(this.params3,(res)=>{
                    if(res.body){
                        this.jcDetail=res.body;
                        this.showJcDetail=true;
                    }
                });
            },
            getJyDetail(item){
                let {orgId,mpiId}=this;
                let {reportId,reportType}=item;

                this.params4[0]=orgId;
                this.params4[1]=mpiId;
                this.params4[2]=reportId;
                this.params4[3]=reportType;
                getRevisitLabReport(this.params4,(res)=>{
                    if(res.body){
                        this.jyDetail=res.body;
                        this.showJyDetail=true;
                    }
                });
            },
        },

        components:{
            yydDialog,
            defaultImage,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .visitRecordDetail{
        display: none;
        &.active{
            display: block;
        }
        .content{
            @include content;
            .goBackWrap{
                padding: 20px;
                line-height: 20px;
                overflow: hidden;
                a{
                    float: right;
                    color: #666;
                    padding-left: 20px;
                    background: url('../../../assets/img/arrow_back.png') no-repeat left center;
                    background-size: 16px;
                }
            }
            .main{
                padding: 30px;
                padding-top: 0;
                .tab{
                    overflow: hidden;
                    li{
                        float: left;
                        height: 44px;
                        line-height: 42px;
                        margin: 0 20px;
                        height: 100%;
                        color: #666;
                        font-size: 14px;
                        position: relative;
                        cursor: pointer;
                        &:before{
                            content: "";
                            width: 60%;
                            height: 3px;
                            background-color: transparent;
                            position: absolute;
                            left: 50%;
                            bottom: 0;
                            transform: translate3d(-50%,0,0);
                        }
                        &:first-of-type{
                            margin-left: 0;
                        }
                        &.active{
                            color: $main;
                            &:before{
                                background-color: $main;
                            }
                        }
                    }
                }
                .option{
                    min-height: 500px;
                    .defaultImageWrap{
                        height: 400px;
                        overflow: hidden;
                        /deep/ .defaultList{
                            padding: 50px 0;
                        }
                    }
                    .patientWrap{
                        display: flex;
                        .patientInfo{
                            @include patientInfo('../../../',64,27);
                            padding: 15px 0;
                            flex: 1;
                        }
                        >.rightContent{
                            width: 300px;
                            padding: 20px 0;
                            line-height: 35px;
                            text-align: right;
                            h3{
                                span{
                                    padding-left: 25px;
                                    background: url('../../../assets/img/icon_shijian01.png') no-repeat left center;
                                    background-size: 16px;
                                    font-size: 16px;
                                }
                            }
                            h4{
                                color: #666;
                            }
                        }
                    }
                    .mzblWrap{
                        .infoList{
                            padding-top: 30px;
                            li{
                                display: flex;
                                line-height: 24px;
                                padding-bottom: 30px;
                                span{
                                    width: 70px;
                                    text-align: right;
                                    color: #666;
                                }
                                em{
                                    flex: 1;
                                    padding-left: 10px;
                                }
                            }
                        }
                    }
                    .ypcfWrap{
                        border-top: 10px solid transparent;
                        padding-top: 40px;
                        background: no-repeat left top;
                        background-size: 30px;
                        &.active{
                            background-image: url('../../../assets/img/RP.png');
                        }
                        .drugList{
                            @include drugList;
                        }
                    }
                    .jcWrap,.jyWrap{
                        padding-top: 30px;
                        .tableWrap{
                            @include scrollTable;
                        }
                        table{
                            @include table3('../../../');
                        }
                    }
                }
            }
        }

        .jcDetail,.jyDetail{
            .title{
                padding: 0 30px;
                padding-right: 50px;
                height: 51px;
                line-height: 50px;
                border-bottom: $border1;
                h3{
                    font-size: 16px;
                }
            }
            .main{
                padding: 0 30px;
                .reportInfoList{
                    padding: 10px 0;
                    overflow: hidden;
                    li{
                        float: left;
                        width: 33.3333%;
                        line-height: 40px;
                        span{
                            float: left;
                            width: 70px;
                            text-align: right;
                            color: #666;
                        }
                        em{
                            padding-left: 10px;
                        }
                    }
                    &.oneLine{
                        border-top: $border1;
                        li{
                            float: none;
                            width: auto;
                            padding-bottom: 30px;
                        }
                    }
                }
                .tableWrap{
                    @include scrollTable;
                }
                table{
                    @include table3('../../../');
                }
            }
        }
    }
</style>

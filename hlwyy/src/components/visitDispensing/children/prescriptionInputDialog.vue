<template>
    <div class="prescriptionInputDialog">
        <yydDialog
            :parent="parent"
            :show="show"
            :showName="showName"
            :width="1000"
            :height="542"
            :title="'处方模板'"
        >
            <div class="wrap">
                <div class="leftContent">
                    <ol class="tabList">
                        <li
                            v-for="(item,index) in ['个人','处方记录','科室']"
                            :key="index"
                            :class="{
                                active:index==tabIndex,
                            }"
                            @click="tabChange(index)"
                        >
                            <a>{{item}}</a>
                        </li>
                    </ol>
                    <ul class="templateList">
                        <li
                            v-for="(item,index) in templateList"
                            :key="index"
                            :class="{
                                multiLine:true,
                                active:index==templateIndex,
                            }"
                            @click="templateChange(item,index)"
                        >
                            <i>西药</i>
                            <span>{{getTemplateName(item)}}</span>
                        </li>
                    </ul>
                </div>
                <div class="rightContent">
                    <div v-if="tabIndex!=1" class="title multiLine-2">
                        <span>
                            <em>诊断：</em>
                            <b>{{getDiagnoseName(currentTemplate)}}</b>
                        </span>
                        <span>
                            <em>修改日期：</em>
                            <b>{{currentLastModify}}</b>
                        </span>
                    </div>
                    <div v-else class="title multiLine-2">
                        <span>
                            <em>就诊日期：</em>
                            <b>{{currentPrescriptionDt}}</b>
                        </span>
                        <span>
                            <em>就诊科室：</em>
                            <b>{{currentDeptName}}</b>
                        </span>
                        <span>
                            <em>就诊医生：</em>
                            <b>{{currentDoctorName}}</b>
                        </span>
                        <span>
                            <em>诊断：</em>
                            <b>{{getDiagnoseName(currentTemplate)}}</b>
                        </span>
                    </div>
                    <div class="main">
                        <table>
                            <thead>
                                <tr>
                                    <td v-for="(item, index) in ['药品名称','药品代码','规格','剂型','用药方式','剂量（单位）','频次','天数','总量','单位', '备注']" :key="index">
                                        <div class="item">
                                            {{item}}
                                        </div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in (currentTemplate.contents||currentTemplate.drugs)" :key="index">
                                    <td>
                                        <div class="item">
                                            {{item.drugName}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugCodeStandard}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugSpecifications}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.pharmaceuticalDosageFormName}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugUsePathwaysCodeText}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugUseDose}}{{item.drugUseDoseUnitText}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugUsingRateText}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugUseDays}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugSendNumber}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.drugPackSpecificationUnitText}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="item">
                                            {{item.remark}}
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="end">
                        <div class="handleWrap">
                            <a
                                @click="selectFn(currentTemplate,templateIndex)"
                            >使用模板</a>
                        </div>
                    </div>
                </div>
            </div>
        </yydDialog>
    </div>
</template>

<script>
    import yydDialog from 'components/common/yydDialog';
    import {sStore,copyJson,dateFormat0} from 'js/yydjs';
    import {queryTemplateRecipeForPatient,saveOrUpdateTemplateRecipe} from 'services';

    export default{
        data(){
            let standardDeptId=sStore.get('standardDeptId');
            let doctorId=sStore.get('docId');

            return{
                tabIndex:0,
                templateIndex:0,
                params:[],
                templateList:[],
                standardDeptId,
                doctorId,
            }
        },

        computed:{
            currentTemplate(){
                let {templateList,templateIndex}=this;
                let result=templateList[templateIndex]||{};

                return result;
            },
            currentLastModify(){
                let {currentTemplate={}}=this;
                let {lastModify}=currentTemplate;

                return lastModify?dateFormat0(lastModify,'yyyy.MM.dd'):'';
            },

            currentPrescriptionDt(){
                let {currentTemplate={}}=this;
                let {prescriptionDt}=currentTemplate;

                return prescriptionDt?dateFormat0(prescriptionDt,'yyyy.MM.dd'):'';
            },
            currentDeptName(){
                let {currentTemplate={}}=this;
                let {deptName}=currentTemplate;

                return deptName;
            },
            currentDoctorName(){
                let {currentTemplate={}}=this;
                let {doctorName}=currentTemplate;

                return doctorName;
            },
        },

        /*
            <prescriptionInputDialog
                v-if="showPrescriptionInputDialog"
                :parent="this"
                :show="true"
                :showName="'showPrescriptionInputDialog'"
                :revisitId="revisitId"
                :select="selectPrescription"
            />
        */

        props:{
            parent:{//父组件的this
                type:Object,
                default:null,
                required:true,
            },
            show:{//是否显示组件
                type:Boolean,
                default:false,
            },
            showName:{//控制显示组件的名称
                type:String,
                default:'',
            },
            revisitId:{// 复诊id
                type:Number,
                default:-1,
            },
            select:{//选择模板时触发的函数
                type:Function,
                default:(item,index,list)=>{},//选择的模板数据，选择模板数据的索引，模板数据列表
            },
        },

        mounted(){
            //获取模板列表
            this.getTemplateList();
        },

        methods:{
            tabChange(index){
                this.tabIndex=index;
                this.templateList=[];
                this.getTemplateList();
            },
            templateChange(item,index){
                this.templateIndex=index;
            },
            getDiagnoseName(item){
                let {icdName,mzDiagnoses=[]}=item;

                return icdName||mzDiagnoses.map((item)=>item.icdName).join('|');
            },
            getTemplateName(item){
                let {templateName,prescriptionDt,name=''}=item;

                return templateName || `${dateFormat0(prescriptionDt,'yyyy.MM.dd')}处方记录${name}`;
            },
            closeFn(){
                let {parent,showName}=this;

                if(parent&&showName&&parent[showName]){
                    parent[showName]=false;
                }
            },
            getTemplateList(){
                let {revisitId,tabIndex,standardDeptId,doctorId}=this;
                this.params[0]=revisitId;
                this.params[1] = tabIndex == 2 ? 1 : tabIndex == 1 ? 3 : 2;
                this.params[2]=tabIndex==2?standardDeptId:doctorId;
                queryTemplateRecipeForPatient(this.params,(res)=>{
                    if(res.body){
                        let templateList=[];
                        let {templateRecipeVo=[],recipeVos=[]}=res.body;

                        for(let i=0;i<recipeVos.length;i++){
                            recipeVos[i].name=i+1;
                        }

                        switch(tabIndex){
                            case 0:
                                    templateList=templateRecipeVo;
                                break;
                            case 1:
                                    recipeVos = this.getRecipeVosNameList(recipeVos)
                                    templateList=recipeVos;
                                break;
                            case 2:
                                    templateList=templateRecipeVo;
                                break;
                        }

                        this.templateList=templateList;
                    }
                });
            },
            getRecipeVosNameList (data) {
                let map = {}
                let prescriptionDtArr = []
                let recipeVosNameList = []
                let ret = []
                data.forEach((item) => {
                    if (!map[item.prescriptionDt]) {
                        prescriptionDtArr.push(item.prescriptionDt)
                        map[item.prescriptionDt] = {arr: [item], length: 1, prescriptionDt: item.prescriptionDt}
                    } else {
                        map[item.prescriptionDt].arr.push(item)
                        map[item.prescriptionDt].length = map[item.prescriptionDt].length + 1
                    }
                })
                prescriptionDtArr.forEach((item) => {
                    recipeVosNameList.push(map[item])
                })
                recipeVosNameList.forEach((item) => {
                    item.arr.forEach((val, index) => {
                        if (item.length > 1) {
                            val.name = index + 1
                        } else {
                            delete val.name
                        }
                        ret.push(val)
                    })
                })
                return ret
            },
            selectFn(item,index){
                let {select,templateList}=this;
                let template=copyJson(item);
                let {mzDiagnoses,drugs,contents}=template;
                let str=drugs?'drugs':'contents';

                template[str]=template[str].map((item)=>{
                    item.drugType=item.recipeTypeCode||'21';
                    return item;
                });
                if(mzDiagnoses){
                    template.mzDiagnoses=mzDiagnoses.map((item)=>{
                        delete item.diagnoseRecordId;
                        return item;
                    });
                }

                select&&select(template,index,templateList);
                this.closeFn();
            },
        },

        components:{
            yydDialog,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .prescriptionInputDialog{
        position: relative;
        .wrap{
            display: flex;
            width: 100%;
            height: 100%;
            border-bottom: 58px solid transparent;
            position: absolute;
            .leftContent{
                width: 230px;
                border-right: $border1;
                .tabList{
                    display: flex;
                    margin-bottom: 20px;
                    li{
                        display: flex;
                        flex: 1;
                        justify-content: center;
                        a{
                            display: block;
                            width: 60px;
                            padding-left: 20px;
                            height: 40px;
                            line-height: 40px;
                            text-align: center;
                            border-bottom: 2px solid transparent;
                            background: url('../../../assets/img/ic_user_adduser.png') no-repeat left center;
                            background-size: 20px;
                        }
                        &:nth-of-type(2){
                            a{
                                width: 80px;
                                background-image: url('../../../assets/img/ic_bljl_ychz.png');
                            }
                        }
                        &:nth-of-type(3){
                            a{
                                background-image: url('../../../assets/img/ic_syks_ychz.png');
                            }
                        }
                        &.active{
                            a{
                                border-color: $main;
                                color: $main;
                            }
                        }
                    }
                }
                .templateList{
                    max-height: 480px;
                    overflow-y: auto;
                    li{
                        display: flex;
                        padding: 20px;
                        height: 60px;
                        line-height: 20px;
                        border-left: 2px solid transparent;
                        cursor: pointer;
                        &.active{
                            background-color: #f8fbff;
                            border-color: $main;
                        }
                        i{
                            width: 40px;
                            height: 20px;
                            line-height: 18px;
                            text-align: center;
                            border: 1px solid $main;
                            border-radius: 2px;
                            color: $main;
                        }
                        span{
                            padding-left: 10px;
                        }
                    }
                }
            }
            .rightContent{
                flex: 1;
                padding: 20px;
                .title{
                    max-height: 60px;
                    line-height: 30px;
                    margin-bottom: 20px;
                    overflow: hidden;
                    span{
                        padding-right: 56px;
                        &:last-of-type{
                            padding-right: 0;
                        }
                        em{
                            color: #666;
                        }
                    }
                }
                .main{
                    height: 340px;
                    overflow: hidden;
                    overflow-y: auto;
                    table{
                        @include table2('../../../');
                    }
                }
                .end{
                    .handleWrap{
                        @include handleWrap;
                    }
                }
            }
        }
    }
</style>

videoLinkByWebRtc(){
    let {to} = this.nimChat
    // 开启麦克风
    function startMicro(){
        return netcall.startDevice({
            type: Netcall.DEVICE_TYPE_AUDIO_IN,
            device: deviceMicro,
        }).catch(function(err) {
            console.log('启动麦克风失败', err)
        })
    }
    // 开启摄像头
    function startCamera(){
        return netcall.startDevice({
            type: Netcall.DEVICE_TYPE_VIDEO,
            device: deviceCamera,
            width: 640,
            height: 480
        }).catch(function(err) {
            console.log('启动摄像头失败', err)
        })
    }
    // 设置本地预览画面大小
    function setLocalVideoSize(){
        return netcall.setVideoViewSize({
            with: 500,
            height: 500,
            cut:true
        })
    }
    const netcall = this.netcall
    const arrFn = [
        startMicro,
        netcall.setCaptureVolume.bind(netcall),
        startCamera,
        netcall.changeRoleToPlayer.bind(netcall),
        netcall.startRtc.bind(netcall),
        setLocalVideoSize
    ]
    const promise = WebRTC.pipe(arrFn)
  
    promise.then(function () {
      console.log('webrtc连接成功')
    })
    .catch(function (e) {
      console.log('发生错误, 结束会话', e)
      netcall.leaveChannel()
    })
},
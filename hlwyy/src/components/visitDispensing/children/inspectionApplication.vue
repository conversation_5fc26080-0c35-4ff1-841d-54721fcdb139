<template>
    <div :class="{
            inspectionApplication:true,
            active:show,
        }">
        <section class="content">
            <div class="main">
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="检验申请" name="first">
                        <insApp v-if="activeName=='first'" :show="true" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" />
                    </el-tab-pane>
                    <el-tab-pane label="检查申请" name="second">
                        <checkApp v-if="activeName=='second'" :show="true" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" />
                    </el-tab-pane>
                    <el-tab-pane label="已开申请" name="third">
                        <div class="hasTableWrap">
                            <el-table :data="applyTableList" border>
                                <el-table-column v-for="(item,index) in defineHasTable" :key="index" :label="item.label" :prop="item.prop" :width="item.width?item.width:''">
                                    <template slot-scope="scope">
                                        <template v-if="index==4">
                                            {{scope.row[item.prop]}}
                                            <a v-if="scope.row.applyState==0" class="aButton" @click="invalid(scope.row)">作废</a>
                                            <a v-if="scope.row.applyState==5" class="aButton" @click="toSee(scope.row,scope.$index)">查看报告</a>
                                        </template>
                                        <template v-else>
                                            {{scope.row[item.prop]}}
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </section>
    </div>
</template>
<script>
import insApp from './insApp';
import checkApp from './checkApp';
import { findMedicalSkillInfoByRevisitId, deleteMedicalSkillApply } from 'services';
import { sStore, alerts } from 'js/yydjs';

export default {
    data() {
        return {
            activeName: 'first',
            query: this.$router.currentRoute.query,
            defineHasTable: [{
                label: '申请单号',
                prop: 'applyId',
            }, {
                label: '申请时间',
                prop: 'applyTime',
                width: '180'
            }, {
                label: '申请项目',
                prop: 'applyNameStr',
                width: '220'
            }, {
                label: '总额(元)',
                prop: 'totalAmount',
            }, {
                label: '状态',
                prop: 'applyStateText',
                width: '200'
            }],
            applyTableList: [],
        }
    },
    computed: {
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId;
        },
    },
    props: {
        show: { //是否显示该组件
            type: Boolean,
            default: false,
        },
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
        patientDetailInfo: { //当前患者的详细信息
            type: Object,
            default () {
                return { healthinfo: {} };
            },
        },
    },
    methods: {
        findMedicalSkillInfoByRevisitIdFn() {
            findMedicalSkillInfoByRevisitId([this.revisitId], (res) => {
                if (res.code == 200) {
                    this.applyTableList = res.body.map(v => {
                        let applyNameArr = []
                        v.applyNameStr = ''
                        v.details.map(item => {
                            applyNameArr.push(item.itemName)
                        })
                        v.applyNameStr = applyNameArr.join('、')
                        return v
                    })
                }
            })
        },
        toSee(item) {
            console.log(this.patientInfo, 'patOn')
            let { outpatientCode, patientName } = this.patientInfo;
            if (!patientName || !outpatientCode || item.applyState != 5) {
                this.$message({ type: "error", message: '暂无报告，无法查看' })
            } else {
                let url = `http://shpdph.com:8889/qyljpro/bgview?dyz=00&orgCode=4300&unitName=42509273X00&bdyz=${patientName}&kh=${outpatientCode}&klx=0`
                window.open(url)
            }
        },
        handleClick(tab, event) {
            console.log(this.activeName, 'ssss')
            if (this.activeName == 'third') {
                this.findMedicalSkillInfoByRevisitIdFn()
            }
        },
        invalid(item) {
            this.$msgbox({
                title: '',
                message: '确定作废当前申请单？',
                showCancelButton: true,
                showConfirmButton: true,
                cancelButtonText: '取消',
                confirmButtonText: '确定',
            }).then(() => {
                let { applyId } = item;

                deleteMedicalSkillApply([applyId], (res) => {
                    alerts('申请单已作废', 'success');
                    this.findMedicalSkillInfoByRevisitIdFn();
                });
            });
        },
    },

    components: {
        insApp,
        checkApp,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.inspectionApplication {
    max-width: 1200px;
    display: none;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 10px 10px 10px 10px;
    &.active {
        display: block;
    }
    .content {
        padding: 30px;
        .main {
            min-height: 200px;
            width: 100%;
        }
    }
    .aButton {
        display: inline-block;
        padding: 0 5px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        margin-left: 10px;
        background-color: $main;
        color: #fff;
        border-radius: 4px;
        cursor: pointer;
    }
}

</style>

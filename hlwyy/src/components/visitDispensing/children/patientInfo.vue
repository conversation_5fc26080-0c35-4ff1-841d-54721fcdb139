<template>
    <div :class="{
            patientInfo:true,
            imgText:type=='imgText',
            video:type=='video',
            visit:type=='visit',
        }">
        <section class="content">
            <div class="patientInfo active">
                <div class="portrait" :style="{
                        backgroundImage:patientInfo.avatar?`url(${patientInfo.avatar})`:'',
                    }"></div>
                <div class="rightContent">
                    <h3>
                        <span>{{patientInfo.patientName}}</span>
                        <em :class="{
                                female:patientInfo.sex!='1',
                            }">{{patientInfo.age}}岁</em>
                    </h3>
                    <h4>手机号：{{type=='visit'?patientInfo.phoneNo:patientInfo.patientPhone}}</h4>
                    <!-- <h5 >
                        <em>预约时间：</em>
                        <em>{{patientInfo.startTime|date('yyyy-MM-dd')}}</em>
                        <em>{{patientInfo.startTime|date('hh:mm')}}-{{patientInfo.endTime|date('hh:mm')}}</em>
                    </h5> -->
                    <h5 v-if="type=='visit'&&patientInfo.docScheduleId">
                        <em>预约时间：</em>
                        <em>{{patientInfo.scheduleStartTime|date('MM-dd')}}</em>
                        <em>{{patientInfo.scheduleStartTime|date('hh:mm')}}-{{patientInfo.scheduleEndTime|date('hh:mm')}}</em>
                    </h5>
                    <h5 v-if="type!='visit'&&patientInfo.docScheduleId">
                        <em>预约时间：</em>
                        <em>{{patientInfo.startTime|date('MM-dd')}}</em>
                        <em>{{patientInfo.startTime|date('hh:mm')}}-{{patientInfo.endTime|date('hh:mm')}}</em>
                    </h5>
                </div>
            </div>
            <div v-if="type=='visit'" class="visitInfo">
                <ul class="itemList">
                    <li>
                        <div class="title">
                            基本健康信息
                        </div>
                        <ul class="main">
                            <li>
                                <span>药物过敏：</span>
                                <div class="rightContent">{{healthinfoAndSpecialVo.healthinfo.medicalAllergy || '暂无'}}</div>
                            </li>
                            <li>
                                <span>食物/接触物过敏：</span>
                                <div class="rightContent">{{healthinfoAndSpecialVo.healthinfo.foodAllergy || '暂无'}}</div>
                            </li>
                            <li>
                                <span>家族病史：</span>
                                <div class="rightContent">{{healthinfoAndSpecialVo.healthinfo.familyDiseaseHistory || '暂无'}}</div>
                            </li>
                            <li>
                                <span>手术或外伤：</span>
                                <div class="rightContent">{{healthinfoAndSpecialVo.healthinfo.operationOrTrauma || '暂无'}}</div>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <div class="title">
                            诊断
                        </div>
                        <div class="main">
                            {{patientDetailInfo.diagnoseName || '暂无'}}
                        </div>
                    </li>
                    <li class="unaligned">
                        <div class="title">
                            就诊记录
                        </div>
                        <ul class="main">
                            <li>
                                <span>就诊记录：</span>
                                <div class="rightContent jzjl" style="display:flex;">
                                    <h3>
                                        <span>{{patientDetailInfo.visitDateTime|date('yyyy-MM-dd')}}</span>
                                        <span>{{patientDetailInfo.orgFullName}}</span>
                                        <span>{{patientDetailInfo.deptName}}</span>
                                        <span v-if="false" @click="toMedicationGuides" style="cursor: pointer;padding-right:20px;color:#52a3fc;">查看</span>
                                    </h3>
                                    <!-- <h4>
                                        <span class="active">门诊病历</span>
                                        <span>药品处方</span>
                                        <span>检验检查</span>
                                        <span>处置记录</span>
                                    </h4> -->
                                </div>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <div class="title">病情信息</div>
                        <ul class="main">
                            <li>
                                <span>病情描述：</span>
                                <div class="rightContent">{{diseaseSituation.chiefComplaint}}</div>
                            </li>
                            <li v-if="illnessPic.length">
                                <span>病情图片：</span>
                                <div class="rightContent bqtp">
                                    <div v-for="(item, index) in illnessPic" :key="index" class="illnessPic" :style="{
                                            backgroundImage:`url(${imgview}${item})`,
                                        }">
                                        <a class="check" @click="previewBigImage(`${imgview}${item}`)">
                                            点击查看大图
                                        </a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div v-else class="imgTextOrvideoInfo">
                <div class="title">
                    基本健康信息
                </div>
                <ul class="infoList">
                    <li>
                        <span>药物过敏：</span>
                        <em>{{healthinfo.medicalAllergy || '暂无'}}</em>
                    </li>
                    <li>
                        <span>食物/接触物过敏：</span>
                        <em>{{healthinfo.foodAllergy || '暂无'}}</em>
                    </li>
                    <li>
                        <span>家族病史：</span>
                        <em>{{healthinfo.familyDiseaseHistory || '暂无'}}</em>
                    </li>
                    <li>
                        <span>手术或外伤：</span>
                        <em>{{healthinfo.operationOrTrauma || '暂无'}}</em>
                    </li>
                </ul>
                <div class="title">咨询信息</div>
                <ul class="main">
                    <li>
                        <span>咨询目的：</span>
                        <div class="rightContent">{{patientDetailInfo.chiefComplaint}}</div>
                    </li>
                    <li v-if="illnessConPic.length">
                        <span>病情图片：</span>
                        <div class="rightContent bqtp">
                            <div v-for="(item, index) in illnessConPic" :key="index" class="illnessPic" :style="{
                                    backgroundImage:`url(${imgview}${item})`,
                                }">
                                <a class="check" @click="previewBigImage(`${imgview}${item}`)">
                                    点击查看大图
                                </a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="handleWrap">
                <a @click="reception(true)">确认接诊</a>
                <a class="red" @click="reception(false)">退诊</a>
            </div>
        </section>
        <div class="causeDialogWrap">
            <el-dialog title="" :visible.sync="showCauseDialog">
                <div class="wrap">
                    <div class="title">
                        请选择取消原因：
                    </div>
                    <div class="main">
                        <el-select v-model="remarkCode" placeholder="请选择" @change="getRemark">
                            <el-option v-for="item in causeList" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div style="margin-top:20px;" v-if="remarkCode=='2'">
                        <el-input type="textarea" placeholder="请输入" maxlength="30" v-model="params[0].remark"></el-input>
                    </div>
                    <div class="end">
                        <a class="borderGray" @click="confirmCause(false)">取消</a>
                        <a @click="confirmCause(true)">确定</a>
                    </div>
                </div>
            </el-dialog>
        </div>
        <yydDialog class="previewBigImage" :parent="this" :show="showBigImage" :showName="'showBigImage'" :height="600" :control="true" :downloadSrc="bigImage">
            <img :src="bigImage" alt="图片" />
        </yydDialog>
    </div>
</template>
<script>
import vm from 'src/main';
import yydDialog from 'components/common/yydDialog';
import { alerts } from 'js/yydjs';
import { imgview, confirmVisit, confirmConsult } from 'services';

export default {
    data() {
        return {
            remarkCode: '',
            query: this.$router.currentRoute.query,
            showCauseDialog: false,
            showBigImage: false,
            params: [{
                choose: '',
                remark: '',
                revisitPersonNum: '1',
            }],
            causeListMap: {
                '0': '工作繁忙',
                '1': '不对症'
            },
            causeList: [{
                    value: '0',
                    label: '工作繁忙',
                },
                {
                    value: '1',
                    label: '不对症',
                },
                {
                    value: '2',
                    label: '其他',
                }
            ],
            bigImage: '',
            imgview,
        }
    },

    computed: {
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId;
        },
        orderDetailId() {
            let { patientInfo = {} } = this;
            let { orderDetailId } = patientInfo;

            return orderDetailId;
        },
        mpiId() {
            let { patientDetailInfo = {} } = this;
            let { mpiId } = patientDetailInfo;

            return mpiId;
        },
        healthinfo() {
            let { patientDetailInfo = {} } = this;
            let { healthinfo = {} } = patientDetailInfo;
            return healthinfo;
        },
        diseaseSituation() {
            let { patientDetailInfo = {} } = this;
            let { diseaseSituation = {} } = patientDetailInfo;

            return diseaseSituation;
        },
        illnessConPic() {
            let { patientDetailInfo = {} } = this;
            let { illnessPic } = patientDetailInfo;
            let imgList = [];

            if (illnessPic) {
                imgList = illnessPic.split('|');
            }
            return imgList;
        },
        illnessPic() {
            let { diseaseSituation = {} } = this;
            let { illnessPic } = diseaseSituation;
            let imgList = [];

            if (illnessPic) {
                imgList = illnessPic.split('|');
            }

            return imgList;
        },
        healthinfoAndSpecialVo() {
            let { patientDetailInfo = {} } = this;
            let { healthinfoAndSpecialVo = {} } = patientDetailInfo;
            let { healthinfo = {}, healthinfoSpecial = {} } = healthinfoAndSpecialVo;
            healthinfoAndSpecialVo.healthinfo = healthinfo;
            healthinfoAndSpecialVo.healthinfoSpecial = healthinfoSpecial;

            return healthinfoAndSpecialVo;
        },
    },

    /*
        <patientInfo
            v-show="patientListTabIndex==0&&patientInfo.userId"
            :type="'visit'"
            :patientInfo="patientInfo"
            :patientDetailInfo="patientDetailInfo"
        />
    */

    props: {
        type: { //患者列表咨询类型
            type: String,
            default: 'visit', //图文：imgText，视频：video，复诊配药：visit
        },
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
        patientDetailInfo: { //当前患者的详细信息
            type: Object,
            default () {
                return { healthinfo: {} };
            },
        },
    },

    watch: {
        showCauseDialog(newVal, oldVal) {
            if (newVal != oldVal && !newVal) {
                this.params[0].remark = '';
                this.remarkCode = ''
            }
        },
    },

    mounted() {

    },

    methods: {
        toMedicationGuides() {
            if(true){
                // console.log('跳转360')
                //跳转360档案
                // vm.$emit('toChangeTabIndex', 5);
                return;
            }

            console.log(this.patientInfo, 'pat')
            let { patientId, outpatientCode, idCard } = this.patientInfo;
            let mesIdCard = btoa(idCard); // base64加密
            if (!patientId || !outpatientCode) {
                this.$message({ type: "error", message: '暂无记录，无法查看' })
            } else {
                // let url = `http://www.shpdph.com:8100/winsso/c/00/0/1/${patientId}/0/0/0/${outpatientCode}/-1/42509273X00/0/0/mzysclient`
                // let url = `http://180.168.136.83:8100/winsso/c/00/0/1/${patientId}/0/0/0/${outpatientCode}/-1/42509273X00/0/0/mzysclient`
                let url = `https://yszt-hlw.mh-hospital.sh.cn/mhwsw-healthRecord/index.html#/healthRecord?idCard=${mesIdCard}`
                window.open(url)
            }
        },
        getRemark(value) {
            if (value != '2') {
                this.params[0].remark = this.causeListMap[value]
            } else {
                this.params[0].remark = ''
            }
        },
        previewBigImage(src) {
            this.bigImage = src;
            this.showBigImage = true;
        },
        reception(bool) {
            let { type, revisitId, orderDetailId } = this;
            let api = null;

            this.params[0].choose = bool ? '1' : '2';
            if (!bool) this.params[0].revisitPersonNum = '0';
            switch (type) {
                case 'imgText':
                case 'video':
                    this.params[0].orderDetailId = orderDetailId;
                    api = confirmConsult;
                    break;
                case 'visit':
                    this.params[0].revisitId = revisitId;
                    api = confirmVisit;
                    break;
            }

            if (bool || this.params[0].remark) {
                let params = JSON.parse(JSON.stringify(this.params))
                this.showCauseDialog = false;
                api(params, null, (res) => {
                    switch (res.code) {
                        case 201:
                            this.$msgbox({
                                title: '',
                                message: '当前复诊中患者已大于2人，是否确认接诊？',
                                showCancelButton: true,
                                showConfirmButton: true,
                                cancelButtonText: '取消',
                                confirmButtonText: '确认',
                                distinguishCancelAndClose: true,
                            }).then(() => {
                                this.params[0].revisitPersonNum = '0';
                                this.reception(bool);
                            });
                            break;
                        case 202:
                            let { type, revisitId, orderDetailId } = this;
                            let buzId = revisitId || orderDetailId;
                            let pathJson = {
                                imgText: '/imgTextConsultation',
                                video: '/videoConsultation',
                                visit: '/visitDispensing',
                            };
                            this.$router.push({
                                path: pathJson[type],
                                query: {
                                    tabIndex: 1,
                                    buzId,
                                },
                            });

                            alerts('当前订单已接诊，请勿重复操作');
                            break;
                        case 203:
                            alerts('当前订单已结束');
                            vm.$emit('patientListRefreshPatientList');
                            break;
                        case 204:
                            alerts('当前订单已退诊，请勿重复操作');
                            vm.$emit('patientListRefreshPatientList');
                            break;
                        case 200:
                            alerts(`${bool?'确认接诊':'退诊'}成功`, 'success');
                            this.params[0].remark = '';
                            this.params[0].revisitPersonNum = '1';
                            if (bool) {
                                vm.$emit('patientListTabChange', 1);
                            } else {
                                vm.$emit('patientListRefreshPatientList');
                            }
                            break;
                    }
                });
            } else {
                this.showCauseDialog = true;
            }
        },
        confirmCause(bool) {
            if (bool) {
                if (this.params[0].remark) {
                    this.$msgbox({
                        title: '',
                        message: '确定执行该操作？',
                        showCancelButton: true,
                        showConfirmButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '确定',
                    }).then(() => {
                        this.reception(false);
                        this.showCauseDialog = false;
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消'
                        });
                    })
                } else {
                    if (this.remarkCode == '2') {
                        alerts('请输入取消原因')
                    } else {
                        alerts('请选择取消原因')
                    }
                }
            } else {
                this.params[0].remark = '';
                this.remarkCode = ''
                this.showCauseDialog = false;
            }
        },
    },

    components: {
        yydDialog,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.patientInfo {
    &.imgText {
        .imgTextOrvideoInfo {
            display: block !important;
        }
    }
    &.video {
        .imgTextOrvideoInfo {
            display: block !important;
        }
        .patientInfo {
            .rightContent {
                h4, h5 {
                    line-height: 20px;
                }
                h5 {
                    display: block !important;
                }
            }
        }
    }
    &.visit {
        .visitInfo {
            display: block !important;
        }
    }
    .content {
        @include content;
        padding: 15px 20px;
        background-color: #fff;
        border-radius: 10px;
        .patientInfo {
            @include patientInfo('../../../', 64, 27);
            padding: 0 30px;
            h5 {
                display: none;
                color: $main;
            }
        }
        .visitInfo {
            display: none;
            .itemList {
                padding-top: 30px;
                border-bottom: $border1;
                >li {
                    .title {
                        padding: 0 16px;
                        height: 40px;
                        line-height: 40px;
                        background-color: #f6f7fb;
                    }
                    .main {
                        padding: 10px 40px;
                        line-height: 30px;
                        >li {
                            display: flex;
                            padding-bottom: 10px;
                            line-height: 20px;
                            &:last-of-type {
                                padding-bottom: 0;
                            }
                            >span {
                                width: 200px;
                                text-align: right;
                                color: #666;
                            }
                            .rightContent {
                                flex: 1;
                                line-height: 20px;
                                padding-left: 40px;
                                overflow: hidden;
                                &.bqtp {
                                    .illnessPic {
                                        @include illnessPic;
                                    }
                                }
                                &.jzjl {
                                    h3 {
                                        overflow: hidden;
                                        span, a {
                                            float: left;
                                        }
                                        span {
                                            padding-left: 60px;
                                            &:first-of-type {
                                                padding-left: 25px;
                                                background: url('../../../assets/img/icon_time.png') no-repeat left center;
                                                background-size: 16px;
                                            }
                                        }
                                        a {
                                            width: 100px;
                                            margin-left: 120px;
                                            height: 20px;
                                            line-height: 20px;
                                            text-align: center;
                                            background-color: $main;
                                            color: #fff;
                                            border-radius: 18px;
                                        }
                                    }
                                    h4 {
                                        display: flex;
                                        span {
                                            padding-left: 20px;
                                            margin-right: 30px;
                                            background: url('../../../assets/img/ico_mzbl.png') no-repeat left center;
                                            background-size: 16px;
                                            &:nth-of-type(2) {
                                                background-image: url('../../../assets/img/ico_ypcf.png');
                                            }
                                            &:nth-of-type(3) {
                                                background-image: url('../../../assets/img/ico_jcjy.png');
                                            }
                                            &:nth-of-type(4) {
                                                background-image: url('../../../assets/img/ico_czjl.png');
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    &.unaligned .main>li>span {
                        width: auto;
                    }
                }
            }
        }
        .imgTextOrvideoInfo {
            display: none;
            .title {
                @include basicTitle;
                margin-top: 10px;
                margin-bottom: 20px;
            }
            .infoList {
                li {
                    display: flex;
                    line-height: 26px;
                    padding: 5px 0;
                    padding-bottom: 30px;
                    span {
                        width: 110px;
                        text-align: right;
                        color: #666;
                    }
                    em {
                        flex: 1;
                        padding-left: 34px;
                        font-size: 14px;
                    }
                }
            }
            .main {
                padding: 10px 40px;
                line-height: 30px;
                >li {
                    display: flex;
                    padding-bottom: 20px;
                    line-height: 30px;
                    &:last-of-type {
                        padding-bottom: 0;
                    }
                    >span {
                        width: 200px;
                        text-align: right;
                        color: #666;
                    }
                    .rightContent {
                        flex: 1;
                        line-height: 30px;
                        padding-left: 40px;
                        overflow: hidden;
                        &.bqtp {
                            .illnessPic {
                                @include illnessPic;
                            }
                        }
                    }
                }
            }
            &.main>li>span {
                width: auto;
            }
        }
        .handleWrap {
            @include handleWrap;
            padding-top: 30px;
            padding-bottom: 40px;
        }
    }
    .causeDialogWrap {
        @include elementDialog;
    }
    .previewBigImage {
        img {
            height: 100%;
        }
    }
}

</style>

<template>
    <div :class="{
            recordCaseHistory:true,
            active:show,
        }">
        <section class="content">
            <ul class="caseHistory">
                <li :class="{
                        hasHint:currentHintIndex==0,
                    }">
                    <div class="left">
                        <span><em>*</em>主诉：</span>
                    </div>
                    <div class="right">
                        <input v-model="detail.chiefComplaint" type="text" />
                        <div class="hint">请录入主诉，否则无法保存！</div>
                    </div>
                </li>
                <li>
                    <div class="left">
                        <span>过敏史：</span>
                    </div>
                    <div class="right">
                        <input v-model="detail.allergicHistory" type="text" />
                    </div>
                </li>
                <li>
                    <div class="left">
                        <span>现病史：</span>
                    </div>
                    <div class="right">
                        <div class="xbsWrap">
                            <textarea v-model="detail.presentHistory"></textarea>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="left">
                        <span>既往史：</span>
                    </div>
                    <div class="right">
                        <div class="jwsWrap">
                            <textarea v-model="detail.pastHistory"></textarea>
                        </div>
                    </div>
                </li>
                <!-- <li>
                    <div class="left">
                        <span>体格检查：</span>
                    </div>
                    <div class="right">
                        <input v-model="detail.physicalExamination" type="text" />
                    </div>
                </li> -->
                <li>
                    <div class="left">
                        <span>辅助检查：</span>
                    </div>
                    <div class="right">
                        <input v-model="detail.assistExamResult" type="text" />
                    </div>
                </li>
                <li>
                    <div class="left"></div>
                    <div class="right">
                        <template v-if="assistExamResultPict.length">
                            <div v-for="(item,index) in assistExamResultPict" :class="{
                                    assistExamResultPict:true,
                                    delete:~deleteIllnessPic.indexOf(item),
                                }" :style="{
                                    backgroundImage:`url(${imgview}${item})`,
                                }" :key="index">
                                <a v-show="!~deleteIllnessPic.indexOf(item)" class="check" @click="previewBigImage(`${imgview}${item}`)">
                                    点击查看大图
                                </a>
                                <a v-if="~deleteIllnessPic.indexOf(item)" class="recoverMask" @click="deleteOrRecover(item)"></a>
                                <a v-else class="close" @click="deleteOrRecover(item)"></a>
                            </div>
                        </template>
                    </div>
                </li>
                <!-- <li class="tz">
                    <div class="left">
                        <span>体征：</span>
                    </div>
                    <div class="right">
                        <ul class="tzList">
                            <li>
                                体温<input v-model="detail.temperature" type="text" />摄氏度
                            </li>
                            <li>
                                脉搏<input v-model="detail.pulseRate" type="text" @input="detail.pulseRate=detail.pulseRate.replace(/[^\d.]+/g,'')" />次/分
                            </li>
                            <li>
                                血压<input v-model="detail.systolicPressure" type="text" @input="detail.systolicPressure=detail.systolicPressure.replace(/[^\d.]+/g,'')" />
                                /<input v-model="detail.diastolicPressure" type="text" @input="detail.diastolicPressure=detail.diastolicPressure.replace(/[^\d.]+/g,'')" />mmHg
                            </li>
                            <li>
                                呼吸<input v-model="detail.beathingRate" type="text" @input="detail.beathingRate=detail.beathingRate.replace(/[^\d.]+/g,'')" />次/分
                            </li>
                            <li>
                                身高<input v-model="detail.height" type="text" />cm
                            </li>
                            <li>
                                体重<input v-model="detail.weight" type="text" />kg
                            </li>
                        </ul>
                    </div>
                </li> -->
                <li :class="{
                        hasHint:currentHintIndex==9,
                    }">
                    <div class="left">
                        <span><em>*</em>诊断：</span>
                    </div>
                    <div class="right">
                        <div class="zdWrap">
                            <queryDiagnoseList2 :revisitId="revisitId" :mpiId="mpiId" :changeFn="changeDiagnoseList" :componentId="'recordCaseHistory1'" />
                            <!-- <queryDiagnoseList :revisitId="revisitId" :mpiId="mpiId" :changeFn="changeDiagnoseList" :componentId="'recordCaseHistory1'" /> -->
                        </div>
                        <div class="hint">请录入搜索结果中的诊断，否则无法保存！</div>
                    </div>
                </li>
                <li>
                    <div class="left">
                        <span>处理措施：</span>
                    </div>
                    <div class="right">
                        <div class="cfcsWrap">
                            <div class="leftContent">
                                <div v-for="item in prescriptionList" class="cfcsList">
                                    <span>处方</span>
                                    <ul>
                                        <li v-for="item1 in item.drugs">
                                            <h3>
                                                <span>{{item1.drugName}}</span>
                                                <span>{{item1.drugSpecifications}}</span>
                                            </h3>
                                            <h4>
                                                <span>用法：{{item1.drugUseDose}}{{item1.drugUseDoseUnitText}}</span>
                                                <span>{{item1.drugUsingRateText}}</span>
                                                <span>{{item1.drugUsePathwaysCodeText}}</span>
                                            </h4>
                                            <h3>
                                                <span>{{item1.remark}}</span>
                                            </h3>
                                        </li>
                                    </ul>
                                </div>
                                <div class="treatMethod">
                                    <textarea v-model="detail.treatMethod"></textarea>
                                </div>
                            </div>
                            <div class="cfcsButtonWrap">
                                <a @click="toRecordPrescription">
                                    处方
                                </a>
                            </div>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="left">
                        <span>处理意见：</span>
                    </div>
                    <div class="right">
                        <div class="cfcsWrap">
                            <div class="clyjWrap">
                                <textarea v-model="detail.treatOpinion" ref="focusTextarea"></textarea>
                            </div>
                            <div class="cfcsButtonWrap">
                                <a @click="commonPhrases">常用语</a>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="handleWrap">
                <a @click="saveAsTemplate">另存为模板</a>
                <a class="border" @click="save">保存</a>
            </div>
        </section>
        <div class="nameDialogWrap">
            <el-dialog title="" :visible.sync="showNameDialog">
                <div class="wrap">
                    <div class="title">
                        请输入模板名称
                    </div>
                    <div :class="{
                            main:true,
                            hasHint,
                        }">
                        <input v-model="params3[0].templateName" maxlength="15" class="templateName" type="text" />
                        <div class="hint">已存在相同名称的模板</div>
                    </div>
                    <div class="end">
                        <a class="borderGray" @click="confirmName(false)">取消</a>
                        <a @click="confirmName(true)">确定</a>
                    </div>
                </div>
            </el-dialog>
        </div>
        <yydDialog class="previewBigImage" :parent="this" :show="showBigImage" :showName="'showBigImage'" :height="600" :control="true" :downloadSrc="bigImage">
            <img :src="bigImage" alt="图片" />
        </yydDialog>
        <caseHistoryInputDialog :revisitId="revisitId" :select="selectCaseHistory" />
        <el-dialog title="请选择常用语：" :visible.sync="showcommonPhrasesDialog" customClass="customPadding">
            <div class="commonPhrasesPopup">
                <ul class="commonPhrasesList">
                    <li v-for="(item,index) in commonPhrasesList" :key="index" :class="{
                            active:index==mouseoverIndex,
                        }" @click="selectCommonPhrases(item)" @mouseover="mouseoverIndex=index">
                        {{item.commonPhrases}}
                    </li>
                </ul>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import vm from 'src/main';
import queryDiagnoseList2 from 'components/common/queryDiagnoseList2';
import queryDiagnoseList from 'components/common/queryDiagnoseList';
import yydDialog from 'components/common/yydDialog';
import caseHistoryInputDialog from './caseHistoryInputDialog';
import commonPhrasesDialog from './commonPhrasesDialog'
import { alerts, alertss, sStore, copyJson } from 'js/yydjs';
import { imgview, getRecordByRevisitId, saveMzMedicalRecord, findRecipeDrugInfoByStatus, checkTemplateName, saveOrUpdateTemplateMzMedicalRecord } from 'services';

export default {
    data() {
        return {
            query: this.$router.currentRoute.query,
            mouseoverIndex: -1,
            commonPhrasesList: [{
                commonPhrases: '密切监测病情变化，不适随诊，按时复查'
            }, {
                commonPhrases: '严禁烟酒 避免劳累熬夜、避免感冒'
            }],
            showNameDialog: false,
            showcommonPhrasesDialog: false,
            showBigImage: false,
            bigImage: '',
            blfyList: [{
                    key: 'blfyWbs',
                    value: '无不适',
                },
                {
                    key: 'blfyKs',
                    value: '咳嗽',
                },
                {
                    key: 'blfyKt',
                    value: '咳痰',
                },
                {
                    key: 'blfyXt',
                    value: '胸痛',
                },
                {
                    key: 'blfyXm',
                    value: '胸闷',
                },
                {
                    key: 'blfyDh',
                    value: '盗汗',
                },
                {
                    key: 'blfyEx',
                    value: '恶心',
                },
                {
                    key: 'blfySybz',
                    value: '食欲不振',
                },
                {
                    key: 'blfyOt',
                    value: '呕吐',
                },
                {
                    key: 'blfyPfsy',
                    value: '皮肤瘙痒',
                },
                {
                    key: 'blfyPz',
                    value: '皮疹',
                },
                {
                    key: 'blfyTy',
                    value: '头晕',
                },
                {
                    key: 'blfyEm',
                    value: '耳鸣',
                },
                {
                    key: 'blfyFx',
                    value: '腹泻',
                },
                {
                    key: 'blfyGjt',
                    value: '关节痛',
                },
                {
                    key: 'blfySlxj',
                    value: '视力下降',
                },
                {
                    key: 'blfyQt',
                    value: '其它',
                },
                {},
            ],
            blfySelected: [],
            params: [],
            params1: [],
            detail: { //整型的字段不能传''，所以注释了
                // medicalRecordId:'',// 主键，有则为修改，无则为新增
                // mpiId:'',
                // revisitId:'',
                // chiefComplaint:'',
                // allergicHistory:'',
                // presentHistory:'',
                // pastHistory:'',
                // physicalExamination:'',
                // assistExamResult:'',
                // temperature:'',
                // pulseRate:'',
                // diastolicPressure:'',
                // systolicPressure:'',
                // beathingRate:'',
                // height:'',
                // weight:'',
                // treatMethod:'',
                // treatOpinion:'',
                // blfyQrsm:'',
                // medicationDelayFlag'',
            },
            hasHint: false,
            params2: [
                1,
            ],
            params3: [{
                templateId: '', //模板id  无则新增,有则更新
                tmpBelongObjType: 2, //模板所属对象类型：1科室、2医生
                tmpBelongObjId: sStore.get('docId'), //医生或者科室id
                templateName: '',
                mnemonic: 'cybl',
                chiefComplaint: '', //主诉
                presentHistory: '', //现病史
                // physicalExamination:'',//体格检查
                assistExamResult: '', //辅助检查
                treatOpinion: '', //处理意见
                treatMethod: '', //处理措施
                icdCode: '',
                icdName: '',
            }],
            diagnoseList: [],
            prescriptionList: [],
            currentHintIndex: -1,
            deleteIllnessPic: [],
            imgview,
        }
    },

    computed: {
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId;
        },
        mpiId() {
            let { patientDetailInfo = {} } = this;
            let { mpiId } = patientDetailInfo;

            return mpiId;
        },
        assistExamResultPict() {
            let { detail = {} } = this;
            let { assistExamResultPict } = detail;
            let imgList = [];

            if (assistExamResultPict) {
                imgList = assistExamResultPict.split('|');
            }

            return imgList;
        },
    },

    /*
        <recordCaseHistory
            :show="tabIndex==1"
            :patientInfo="patientInfo"
            :patientDetailInfo="patientDetailInfo"
        />
    */

    props: {
        show: { //是否显示该组件
            type: Boolean,
            default: false,
        },
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
        patientDetailInfo: { //当前患者的详细信息
            type: Object,
            default () {
                return { healthinfo: {} };
            },
        },
    },

    watch: {
        revisitId(newVal, oldVal) {
            if (newVal && newVal != oldVal) {
                //获取病历
                this.getCaseHistory();

                //获取处方
                this.getPrescriptionList();
            }
        },
        blfySelected(newVal, oldVal) {
            let key = this.blfyList[0].key;
            let newPosIndex = newVal.indexOf(key);
            let oldPosIndex = oldVal.indexOf(key);

            if (newVal.length > 1) {
                if (~oldPosIndex) {
                    this.blfySelected.splice(oldPosIndex, 1);
                } else if (~newPosIndex) {
                    this.blfySelected = [key];
                }
            }
            if (this.blfySelected.indexOf('blfyQt') == -1) {
                this.detail.blfyQrsm = ''
            }
        },
    },

    mounted() {

    },

    methods: {
        focusTextarea(ref) {
            this.$refs[ref].focus();
        },
        commonPhrases() {
            this.showcommonPhrasesDialog = true
        },
        selectCommonPhrases(item) {
            this.showcommonPhrasesDialog = false
            if (item.commonPhrases) {
                if (!this.detail.treatOpinion) {
                    this.$set(this.detail, 'treatOpinion', `${item.commonPhrases}`)
                } else {
                    this.$set(this.detail, 'treatOpinion', `${this.detail.treatOpinion}\n${item.commonPhrases}`)
                }
                // this.focusTextarea('focusTextarea')
            }
        },
        createId(key, index) {
            return `recordCaseHistory${key}${index}`;
        },
        changeDiagnoseList(list, item, index) {
            this.diagnoseList = list;
        },
        previewBigImage(src) {
            this.bigImage = src;
            this.showBigImage = true;
        },
        deleteOrRecover(item) {
            let posIndex = this.deleteIllnessPic.indexOf(item);

            if (!~posIndex) {
                this.deleteIllnessPic.push(item);
            } else {
                this.deleteIllnessPic.splice(posIndex, 1);
            }
        },
        selectCaseHistory(item, index, list) {
            let { mzMedicalRecord = {} } = item;
            item = Object.assign({}, item, mzMedicalRecord);
            let detail = copyJson(this.detail);
            let { chiefComplaint, allergicHistory, presentHistory, pastHistory, physicalExamination, assistExamResult, treatOpinion, treatMethod, icdCode: icdCodeList = '', icdName: icdNameList = '', mzDiagnoses } = item;
            let diagnoseList = [];

            detail.chiefComplaint = chiefComplaint;
            detail.presentHistory = presentHistory;
            detail.physicalExamination = physicalExamination;
            detail.assistExamResult = assistExamResult;
            detail.treatOpinion = treatOpinion;
            detail.treatMethod = treatMethod ? (detail.treatMethod ? detail.treatMethod : '') + treatMethod : detail.treatMethod;
            this.detail = detail;

            if (mzDiagnoses) {
                diagnoseList = mzDiagnoses;
            } else {
                icdCodeList = icdCodeList.split('|');
                icdNameList = icdNameList.split('|');
                diagnoseList = icdCodeList.map((item, index) => {
                    let icdCode = icdCodeList[index];
                    let icdName = icdNameList[index];

                    return {
                        icdCode,
                        icdName,
                    };
                });
            }
            vm.$emit('queryDiagnoseListSave', diagnoseList);
        },
        getCaseHistory() {
            this.detail = {};
            this.params[0] = this.revisitId;
            getRecordByRevisitId(this.params, (res) => {
                this.blfySelected = [];
                if (res.body) {
                    this.detail = res.body;
                    for (let item of this.blfyList) {
                        if (item.key && this.detail[item.key] == '1') {
                            this.blfySelected.push(item.key);
                        }
                    }
                    sStore.set('recordJson', this.detail);
                }
            });
        },
        getPrescriptionList() {
            let { revisitId, statusIndex } = this;

            this.prescriptionList = [];
            this.params1[0] = revisitId;
            this.params1[1] = '3';
            findRecipeDrugInfoByStatus(this.params1, (res) => {
                if (res.body) {
                    this.prescriptionList = res.body;
                }
            });
        },
        toRecordPrescription() {
            if (!this.diagnoseList.length) {
                alerts('未维护诊断');
            } else {
                vm.$emit('toChangeTabIndex', 2);
            }
        },
        confirmName(bool) {
            if (bool) {
                if (!this.params3[0].templateName) {
                    this.currentHintIndex = -1;
                    alerts('请输入模板名称');
                } else {
                    this.params2[1] = this.params3[0].tmpBelongObjId;
                    this.params2[2] = this.params3[0].templateName;
                    checkTemplateName(this.params2, (res) => {
                        this.hasHint = !res.body;
                        if (this.hasHint) {
                            alerts('已存在相同名称的模板');
                        } else {
                            this.showNameDialog = false;
                            saveOrUpdateTemplateMzMedicalRecord(this.params3, (res) => {
                                this.params3[0].templateName = '';
                                vm.$emit('caseHistoryInputDialogUpdate');
                                alerts('病历存为模板成功', 'success');
                            });
                        }
                    });
                }
            } else {
                this.showNameDialog = false;
            }
        },
        saveAsTemplate(bool) {
            let { detail, diagnoseList } = this;
            let { chiefComplaint } = detail;
            let arr = [
                { if: !chiefComplaint, hint: '请录入主诉，否则无法保存' },
                { if: !diagnoseList.length, hint: '请录入搜索结果中的诊断，否则无法保存' },
            ];
            let errorIndex1 = -1;

            this.currentHintIndex = -1;
            alertss(arr, () => {
                for (let attr in this.params3[0]) {
                    if (detail[attr]) {
                        this.params3[0][attr] = detail[attr];
                    }
                }
                this.params3[0].icdCode = diagnoseList.map(item => item.icdCode).join('|');
                this.params3[0].icdName = diagnoseList.map(item => item.icdName).join('|');
            }, (errorIndex) => {
                errorIndex1 = errorIndex;
                switch (errorIndex) {
                    case 1:
                        this.currentHintIndex = 9;
                        break;
                    default:
                        this.currentHintIndex = errorIndex;
                }
            });

            if (!~errorIndex1) {
                this.showNameDialog = true;
            }
        },
        save() {
            let { detail, diagnoseList, blfyList, blfySelected, revisitId, mpiId, assistExamResultPict } = this;
            let { chiefComplaint, temperature, pulseRate, systolicPressure, diastolicPressure, beathingRate, height, weight, blfyQrsm, medicationDelayFlag } = detail;
            let key = blfyList[blfyList.length - 2].key;
            let fourTwo = /^(([1-9]\d{0,3}(\.\d{1,2})?)|((0)\.\d{1,2}))$/
            let four = /^[1-9]\d{0,3}$/
            let arr = [
                { if: !chiefComplaint, hint: '请录入主诉，否则无法保存' },
                { if: temperature, value: temperature, reg: fourTwo, hint: '体温输入格式0.01-9999.99' },
                { if: pulseRate, value: pulseRate, reg: four, hint: '脉搏只能输入格式1-9999的正整数' },
                { if: systolicPressure, value: systolicPressure, reg: four, hint: '收缩压只能输入格式1-9999的正整数' },
                { if: diastolicPressure, value: diastolicPressure, reg: four, hint: '舒张压只能输入格式1-9999的正整数' },
                { if: (!systolicPressure && diastolicPressure) || (systolicPressure && !diastolicPressure) || (+systolicPressure <= +diastolicPressure), hint: '收缩压需大于舒张压' },
                { if: beathingRate, value: beathingRate, reg: four, hint: '呼吸只能输入格式1-9999的正整数' },
                { if: height, value: height, reg: fourTwo, hint: '身高输入格式0.01-9999.99' },
                { if: weight, value: weight, reg: fourTwo, hint: '体重输入格式0.01-9999.99' },
                { if: !diagnoseList.length, hint: '请录入搜索结果中的诊断，否则无法保存' },
            ];

            alertss(arr, () => {
                this.$msgbox({
                    title: '',
                    message: '是否保存本次修改？',
                    showCancelButton: true,
                    showConfirmButton: true,
                    cancelButtonText: '取消',
                    confirmButtonText: '保存',
                }).then(() => {
                    for (let item of blfyList) {
                        if (item.key) {
                            this.detail[item.key] = ~blfySelected.indexOf(item.key) ? '1' : '0';
                        }
                    }
                    this.detail.revisitId = revisitId;
                    this.detail.mpiId = mpiId;
                    this.detail.assistExamResultPict = assistExamResultPict.filter((item) => !~this.deleteIllnessPic.indexOf(item)).join('|');
                    delete this.detail.diagnoseList;
                    delete this.detail.createDt;
                    delete this.detail.createUser;
                    delete this.detail.lastModify;
                    delete this.detail.lastModifyUser;
                    saveMzMedicalRecord([this.detail], (res) => {
                        if (res.body) {
                            this.detail.medicalRecordId = res.body;
                            alerts('病历内容保存成功', 'success');
                            sStore.set('recordJson', this.detail);
                        }
                    });
                });
            }, (errorIndex) => {
                this.currentHintIndex = errorIndex;
            });
        },
    },

    components: {
        queryDiagnoseList2,
        queryDiagnoseList,
        yydDialog,
        caseHistoryInputDialog,
        commonPhrasesDialog,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';
.recordCaseHistory {
    max-width: 1200px;
    display: none;
    &.active {
        display: block;
    }
    .content {
        @include content;
        .caseHistory {
            @include caseHistory('../../../');
            .assistExamResultPict {
                @include illnessPic;
                .recoverMask {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    left: 0;
                    top: 0;
                }
                .close {
                    width: 20px;
                    height: 20px;
                    background: #fff url('../../../assets/img/close.png') no-repeat center center;
                    background-size: 20px;
                    border-radius: 50%;
                    position: absolute;
                    right: -10px;
                    top: -10px;
                    z-index: 10;
                }
            }
        }
        .handleWrap {
            @include handleWrap;
            padding-top: 40px;
            padding-bottom: 30px;
        }
    }
    .nameDialogWrap {
        @include nameDialogWrap('../../../');
    }
    .commonPhrasesWrap {
        @include elementDialog;
    }
    .commonPhrasesPopup {
        width: 100%;
        background-color: #fff;
        margin-top: -20px;
        .commonPhrasesList {
            max-height: 350px;
            overflow-y: auto;
            li {
                padding: 5px 20px;
                line-height: 24px;
                text-align: left;
                cursor: pointer;
                &.active {
                    background-color: $blueBg;
                    color: $main;
                }
                span {
                    padding-right: 10px;
                    &:last-of-type {
                        padding-right: 0;
                    }
                }
            }
        }
    }
}

</style>

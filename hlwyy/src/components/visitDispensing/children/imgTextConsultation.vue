<template>
    <div class="imgTextConsultation">
        <section class="content">
            <div class="leftContent">
                <patientList
                    :type="'imgText'"
                />
            </div>

            <div class="rightContent">
                <!-- <div class="title">
                    图文咨询
                </div> -->

                <patientInfo
                    v-show="patientListTabIndex==0&&patientInfo.userId"
                    :type="'imgText'"
                    :patientInfo="patientInfo"
                    :patientDetailInfo="patientDetailInfo"
                />

                <div v-show="patientListTabIndex==1&&patientInfo.userId">
                    <onlineConsultation
                        :show="true"
                        :type="'imgText'"
                        :patientInfo="patientInfo"
                        :patientDetailInfo="patientDetailInfo"
                    />
                </div>

                <defaultImage
                    :show="!patientInfo.userId"
                    :showIndex="1"
                />
            </div>
        </section>
    </div>
</template>

<script>
    import patientList from './patientList';
    import patientInfo from './patientInfo';
    import onlineConsultation from './onlineConsultation';
    import defaultImage from 'components/common/defaultImage';
    import {getPatientRevisitDetail,getConsultDetail} from 'services';

    export default{
        data(){
            return{
                query:this.$router.currentRoute.query,
                tabIndex:0,
                patientListTabIndex:-1,
                patientInfo:{},
                params:[],
                patientDetailInfo:{healthinfo:{}},
            }
        },

        computed:{
            revisitId(){
                let {patientInfo={}}=this;
                let {revisitId}=patientInfo;

                return revisitId;
            },
            orderDetailId(){
                let {patientInfo={}}=this;
                let {orderDetailId}=patientInfo;

                return orderDetailId;
            },
        },

        created(){
            vm.$on('patientListShowTabIndex',this.showTabIndex);
            vm.$on('patientListPatientChange',this.updatePatientInfo);
        },

        mounted(){

        },

        beforeDestroy(){
            vm.$off('patientListShowTabIndex',this.showTabIndex);
            vm.$off('patientListPatientChange',this.updatePatientInfo);
        },

        methods:{
            showTabIndex(patientListTabIndex){
                this.patientListTabIndex=patientListTabIndex;
            },
            updatePatientInfo(tabIndex,patientInfo){
                this.patientListTabIndex=tabIndex;
                if(patientInfo){
                    this.patientInfo=patientInfo;

                    //获取患者信息详情
                    this.getDetail();
                }
            },
            getDetail(){
                if(!this.revisitId&&!this.orderDetailId)return;
                let {patientInfo={},type='imgText'}=this;
                let {revisitId,orderDetailId}=patientInfo;
                let api=null;

                switch(type){
                    case 'imgText':
                    case 'video':
                            this.params[0]=orderDetailId;
                            api=getConsultDetail;
                        break;
                    case 'visit':
                            this.params[0]=revisitId;
                            api=getPatientRevisitDetail;
                        break;
                }

                api(this.params,(res)=>{
                    if(res.body){
                        this.patientDetailInfo=res.body;
                    }
                });
            },
        },

        components:{
            patientList,
            patientInfo,
            onlineConsultation,
            defaultImage,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .imgTextConsultation{
        height: 100%;
        overflow: hidden;
        .content{
            display: flex;
            .leftContent{
                width: 300px;
            }
            .rightContent{
                flex: 1;
                padding: 30px;
                padding-top: 0;
                height: 100vh;
                border-bottom: 98px solid transparent;
                overflow-y: auto;
                .title{
                    height: 50px;
                    line-height: 50px;
                    font-size: 16px;
                }
            }
        }
    }
</style>
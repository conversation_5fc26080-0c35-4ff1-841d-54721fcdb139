<template>
    <div :class="{
            insApp:true,
            active:show,
        }">
        <section class="content">
            <div class="insApp">
                <div class="insAppTem">
                    <div class="insAppTemTitle">检验单模板</div>
                    <div class="insAppTemCon">
                        <ul>
                            <li v-for="(item,index) in insAppTemList" :key="index" :class="{active: index == tabIndex}" @click="tabChange(item, index)">
                                <span>{{item.tempName}}</span>
                                <span v-if="index == tabIndex" class="arrow">＞</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="tableWrapBox">
                    <div class="tableWrap">
                        <el-table :data="tableList" border row-key="id" :default-expand-all="isExpand" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" height="350">
                            <el-table-column v-for="(item,index) in defineTable" :key="index" :label="item.label" :prop="item.prop" :width="item.width?item.width:''">
                                <template slot-scope="scope">
                                    <template v-if="index==0">
                                        <span v-if="scope.row.hasChild">
                                            <el-checkbox style="padding: 0 2px" v-model="scope.row.selectStatus"  @change="selectionCheckedChange(scope.row)"></el-checkbox>
                                            {{scope.row[item.prop]}}
                                        </span>
                                        <span v-else>
                                            <span v-if="scope.row.type=='1'" style="width:5px;display:inline-block"></span>
                                            <el-checkbox style="padding: 0 2px" v-model="scope.row.selectStatus"  @change="selectionCheckedChange(scope.row)"></el-checkbox>
                                            {{scope.row[item.prop]}}
                                        </span>
                                    </template>
                                    <template v-else>
                                        {{scope.row[item.prop]}}
                                    </template>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
            <div class="hasInsApp">
                <div class="titel">已选项目</div>
                <div class="content">
                    <div class="tableWrapBox">
                        <div class="tableWrap">
                            <el-table :data="hasTableList" border row-key="id" :default-expand-all="isExpand" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" height="350">
                                <el-table-column v-for="(item,index) in defineTable" :key="index" :label="item.label" :prop="item.prop" :width="item.width?item.width:''">
                                    <template slot-scope="scope">
                                        <template v-if="index==0">
                                            <span v-if="scope.row.hasChild">
                                                {{scope.row[item.prop]}}
                                            </span>
                                            <span v-else>
                                                <span style="width:15px;display:inline-block"></span>
                                                {{scope.row[item.prop]}}
                                            </span>
                                        </template>
                                        <template v-else>
                                            {{scope.row[item.prop]}}
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div class="total">
                        <div class="end">
                            <div class="priceTotal">合计: {{totalPrice}}</div>
                            <div class="handleWrap">
                                <a @click="submit()">提交</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <previewInsApp v-if="showpreviewInsApp" :parent="this" :show="true" :showName="'showpreviewInsApp'" :previewDetail="previewDetail" :signature="applyMedicalSkillFn" />
    </div>
</template>
<script>
import previewInsApp from 'components/common/previewInsApp';
import { getMedicalSkillTmpl, findMedicalSkillItem, applyMedicalSkill, findMedicalSkillInfoByRevisitId, applyMedicalSkillValid } from 'services';
import { sStore, alerts, Dec } from 'js/yydjs';

export default {
    data() {
        return {
            orgId: sStore.get('orgId'),
            showpreviewInsApp: false,
            tabIndex: 0,
            activeName: 'first',
            isExpand: false,
            query: this.$router.currentRoute.query,
            defineTable: [{
                    label: '项目代码',
                    prop: 'itemCode',
                    width: '150'
                },
                {
                    label: '项目名称',
                    prop: 'itemName',
                },
                {
                    label: '单价(元)',
                    prop: 'itemPrice',
                    width: '150'
                },
            ],
            tableList: [],
            hasTableList: [],
            applyTableList: [],
            insAppTemList: [],
            currentTab: {}
        }
    },

    computed: {
        mpiId() {
            let { patientDetailInfo = {} } = this;
            let { mpiId } = patientDetailInfo;

            return mpiId;
        },
        previewDetail() {
            let result = {};
            let { patientInfo = {}, patientDetailInfo = {}, prescription = {}, diagnoseList = [] } = this;
            let { drugs = [] } = prescription;

            prescription.drugs = drugs;
            result = Object.assign({}, patientInfo, patientDetailInfo, prescription, { mzDiagnoses: diagnoseList });
            console.log('详情', result);
            return result;
        },
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId;
        },
        totalPrice() {
            let total = this.hasTableList.reduce((total, v) => {
                return new Dec(total).add(new Dec(v.itemPrice)).toNumber()
            }, 0)
            console.log(total)
            return total + '元'
        }
    },

    props: {
        show: { //是否显示该组件
            type: Boolean,
            default: false,
        },
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
        patientDetailInfo: { //当前患者的详细信息
            type: Object,
            default () {
                return { healthinfo: {} };
            },
        },
    },

    watch: {
        mpiId(newVal, oldVal) {
            if (newVal && newVal != oldVal) {
                this.getMedicalSkillTmpl()
            }
        },
    },

    mounted() {
        this.getMedicalSkillTmpl()
    },

    methods: {
        findMedicalSkillItem(tempId) {
            findMedicalSkillItem([tempId], (res) => {
                if (res.code == 200) {
                    console.log(res.body, 'body')
                    this.tableList = res.body.map((v) => {
                        let { medicalSkillItemChildren = [], itemId, itemName, itemPrice, type = '0', itemCode, execDeptId, execDeptName, itemCatalog, itemType, selectStatus = false } = v
                        let itemPId = itemId
                        let obj = {
                            tempId,
                            itemId,
                            itemName,
                            itemPrice,
                            type,
                            itemCode,
                            execDeptId,
                            execDeptName,
                            itemCatalog,
                            itemType,
                            hasChild: false,
                            selectStatus
                        }
                        obj.id = type + itemId
                        let children = JSON.parse(JSON.stringify(medicalSkillItemChildren))
                        console.log(medicalSkillItemChildren)
                        if (children.length) {
                            children = children.map((item) => {
                                let { itemId, itemName, itemPrice, type = '1', itemCode, execDeptId, execDeptName, itemCatalog, itemType, selectStatus = false } = item
                                let itemObj = {
                                    tempId,
                                    itemId,
                                    itemName,
                                    itemPrice,
                                    type,
                                    itemCode,
                                    execDeptId,
                                    execDeptName,
                                    itemCatalog,
                                    itemType,
                                    itemPId,
                                    selectStatus
                                }
                                itemObj.id = type + itemId
                                return itemObj
                            })
                            obj.hasChild = true
                            obj.children = children
                        }
                        return obj
                    })
                    let hasList = JSON.parse(JSON.stringify(this.hasTableList))
                    let currentTempIdArr = hasList.filter((v) => {
                        return v.tempId == tempId
                    })
                    console.log(currentTempIdArr, 'currentTempIdArr')
                    currentTempIdArr.map((v, i) => {
                        console.log(v, i)
                        this.selectOpt(v)
                    })
                    console.log(this.tableList, 'tablist')
                }
            })
        },
        getMedicalSkillTmpl() {
            getMedicalSkillTmpl([this.orgId], (res) => {
                if (res.code == 200) {
                    this.insAppTemList = res.body.filter((v) => {
                        return v.tempCatalog == 1
                    })
                    this.tabChange(this.insAppTemList[0], 0)
                }
            })
        },
        tabChange(item, index) {
            this.tabIndex = index
            this.currentTab = item
            this.findMedicalSkillItem(item.tempId)
        },
        selectOpt(item) {
            let oneIndex = 0
            let retArr = []
            let retList = JSON.parse(JSON.stringify(this.tableList))
            retList.map((v, i) => {
                if (item.hasChild) {
                    if (item.id == v.id) {
                        oneIndex = i
                        console.log(oneIndex, 'three')
                        item.children = item.children.map((child, childIndex) => {
                            child.selectStatus = item.selectStatus
                            return child
                        })
                        this.tableList.splice(oneIndex, 1, item)
                    }
                } else {
                    if (item.type == '1') {
                        if (item.itemPId == v.itemId) {
                            oneIndex = i
                            console.log(oneIndex, 'two')
                            let selectStatusArr = []
                            v.children = v.children.map((child, childIndex) => {
                                if (item.id == child.id) {
                                    child.selectStatus = item.selectStatus
                                }
                                selectStatusArr.push(child.selectStatus)
                                return child
                            })
                            if (!~selectStatusArr.indexOf(false)) {
                                v.selectStatus = true
                            } else {
                                v.selectStatus = false
                            }
                            this.tableList.splice(oneIndex, 1, v)
                        }
                    } else {
                        if (item.id == v.id) {
                            oneIndex = i
                            console.log(oneIndex, 'one')
                            this.tableList.splice(oneIndex, 1, item)
                        }
                    }
                }
            })

        },
        selectionCheckedChange(item) {
            console.log(item, 'item')
            let retArr = []
            this.selectOpt(item)
            let hasList = JSON.parse(JSON.stringify(this.hasTableList))
            let retList = JSON.parse(JSON.stringify(this.tableList))
            retList.map((v, i) => {
                if (v.hasChild) {
                    if (v.selectStatus) {
                        retArr.push(v)
                    } else {
                        v.children = v.children.map((child, childIndex) => {
                            if (child.selectStatus) {
                                retArr.push(child)
                            }
                        })
                    }
                } else {
                    if (v.selectStatus) {
                        retArr.push(v)
                    }
                }
            })
            let otherTempIdArr = hasList.filter((v) => {
                return v.tempId != this.currentTab.tempId
            })
            console.log(otherTempIdArr, 'otherTempIdArr')
            this.hasTableList = otherTempIdArr.concat(retArr)
        },
        submit() {
            if (this.hasTableList.length) {
                this.previewDetail.hasTableList = this.hasTableList
                this.previewDetail.currentTab = this.currentTab
                this.doctorCheck();
            } else {
                alerts('请选择申请项目')
            }
        },
        getParams() {
            let params = {
                revisitId: this.revisitId,
                applyType: this.currentTab.tempCatalog,
                applyName: this.currentTab.tempName
            }
            let skillItems = []
            this.hasTableList.map((v) => {
                let obj = {}
                let { itemId, itemName, itemPrice, itemCount = 1, hasChild, children = [] } = v
                obj = {
                    itemId,
                    itemName,
                    itemPrice,
                    itemCount
                }
                skillItems.push(obj)
                if (hasChild) {
                    let childObj = {}
                    children.map(item => {
                        let { itemId, itemName, itemPrice, itemCount = 1, itemPId } = item
                        childObj = {
                            itemId,
                            itemName,
                            itemPrice,
                            itemCount,
                            itemPId,
                        }
                        skillItems.push(childObj)
                    })
                }
            })
            params.skillItems = skillItems
            return params;
        },
        applyMedicalSkillFn() {
            let params = this.getParams();

            applyMedicalSkill([params], (res) => {
                if (res.code == 200) {
                    setTimeout(() => {
                        alerts('申请单提交成功', 'success');
                        this.showpreviewInsApp = false
                        this.tableList = this.tableList.map(v => {
                            v.selectStatus = false
                            if (v.hasChild) {
                                v.children = v.children.map(item => {
                                    item.selectStatus = false
                                    return item
                                })
                            }
                            return v
                        })
                        this.hasTableList = []
                        // this.activeName = 'second'
                    }, 300);
                }
            })
        },
        doctorCheck() {
            let params = this.getParams();

            applyMedicalSkillValid([params], (res) => {
                let { code, msg } = res;

                if (code == 200) {
                    this.showpreviewInsApp = true;
                } else if (code == 100) {
                    this.$msgbox({
                        title: '',
                        message: msg,
                        showCancelButton: true,
                        showConfirmButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '确定',
                    }).then(() => {
                        this.showpreviewInsApp = true;
                    });
                } else {
                    alerts(msg);
                }
            });
        },
    },

    components: {
        previewInsApp,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.insApp {
    &.active {
        display: block;
    }
    .content {
        min-height: 200px;
        width: 100%;
        .insApp {
            width: 100%;
            display: flex;
            padding: 20px;
            border: 1px solid #ebeef5;
            ;
            border-radius: 10px 10px 10px 10px;
            .insAppTem {
                min-width: 200px;
                max-width: 200px;
                padding-top: 5px;
                border: 1px solid #ebeef5;
                border-radius: 10px 10px 10px 10px;
                font-size: 14px;
                .insAppTemTitle {
                    font-size: 18px;
                    text-align: center;
                    font-weight: bold;
                    padding: 10px 20px;
                }
                .insAppTemCon {
                    min-height: 300px;
                    max-height: 400px;
                    overflow-y: scroll;
                    width: 100%;
                    ul {
                        padding: 0 30px;
                        li {
                            cursor: pointer;
                            display: flex;
                            padding: 5px 0px;
                            align-items: center;
                            span {
                                display: inline-block;
                                line-height: 20px;
                                color: #666;
                            }
                            .arrow {
                                padding-left: 5px;
                            }
                            &.active {
                                span {
                                    color: $main;
                                    border-color: $main;
                                }
                            }
                        }
                    }
                }
            }
            .tableWrapBox {
                width: 100%;
                padding: 0 20px;
                overflow-x: scroll;
                .cell {
                    display: flex;
                }
            }
        }
        .hasInsApp {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ebeef5;
            ;
            border-radius: 10px 10px 10px 10px;
            .title {
                font-size: 18px;
                font-weight: bold;
            }
            .content {
                display: flex;
                .total {
                    min-width: 300px;
                    max-width: 300px;
                    padding-left: 20px;
                    position: relative;
                    .end {
                        display: flex;
                        align-items: center;
                        position: absolute;
                        bottom: 0px;
                        left: 20px;
                        .handleWrap {
                            width: 100%;
                            @include handleWrap;
                        }
                        .priceTotal {
                            min-width: 150px;
                            padding-right: 20px;
                        }
                    }
                }
                .tableWrapBox {
                    width: 100%;
                    padding: 0 20px;
                    overflow-x: scroll;
                    .cell {
                        display: flex;
                    }
                }
            }
        }
    }
}

</style>

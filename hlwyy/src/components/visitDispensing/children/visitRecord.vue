<template>
    <div
        :class="{
            visitRecord:true,
            active:show,
        }"
    >
        <section v-show="!showRecordDetail" class="content">
            <div class="main">
                <div
                    v-for="tableName in ['theadWrap','tbodyWrap']"
                    v-if="recordList.length"
                    :class="{
                        tableWrap:true,
                        [tableName]:true,
                    }"
                >
                    <table>
                        <thead>
                            <tr>
                                <th v-for="item in ['诊断','医院','科室','医生','类型','时间','操作']">
                                    <div class="item">
                                        {{item}}
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(item,index) in recordList"
                                :class="{
                                    active:index==currentHandleIndex,
                                }"
                            >
                                <td>
                                    <div
                                        :class="{
                                            item:true,
                                            active:item.clinicId==item.visitId,
                                        }"
                                    >
                                        <span>{{item.diagnoseName}}</span>
                                        <em>(本次复诊)</em>
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.orgFullName}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.standardDeptName}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.doctorName}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        门诊
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                       {{item.visitDateTime|date('yyyy-MM-dd')}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item handle">
                                        <a
                                            @click="checkDetail(item,index)"
                                        >查看</a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div v-show="!recordList.length&&loaded" class="defaultImageWrap">
                    <defaultImage
                        :show="true"
                        :showIndex="4"
                    />
                </div>
            </div>
            <!-- <div class="end">
                <el-pagination
                    layout="total,sizes,prev,pager,next,jumper"
                    :total="total"
                    :current-page="params[0].page"
                    :page-size="params[0].limit"
                    :page-sizes="[10,20,50]"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                />
            </div> -->
        </section>

        <visitRecordDetail
            v-if="showRecordDetail"
            :parent="this"
            :show="true"
            :showName="'showRecordDetail'"
            :patientInfo="patientInfo"
            :patientDetailInfo="patientDetailInfo"
            :listItem="listItem"
        />
    </div>
</template>

<script>
    import visitRecordDetail from './visitRecordDetail';
    import defaultImage from 'components/common/defaultImage';
    import {findDiagnosisRecords} from 'services';
    import {sStore,alerts} from 'js/yydjs';

    export default{
        data(){
            return{
                query:this.$router.currentRoute.query,
                showRecordDetail:false,
                total:0,
                currentHandleIndex:-1,
                params:[
                    sStore.get('orgId'),
                    '',
                    sStore.get('standardDeptId'),
                ],
                recordList:[],
                listItem:{},
                loaded:false,
            }
        },

        computed:{
            mpiId(){
                let {patientDetailInfo={}}=this;
                let {mpiId}=patientDetailInfo;

                return mpiId;
            },
        },

        /*
            <visitRecord
                v-if="tabIndex==3"
                :show="true"
                :patientInfo="patientInfo"
                :patientDetailInfo="patientDetailInfo"
            />
        */

        props:{
            show:{//是否显示该组件
                type:Boolean,
                default:false,
            },
            patientInfo:{//当前患者的信息
                type:Object,
                default(){
                    return {};
                },
            },
            patientDetailInfo:{//当前患者的详细信息
                type:Object,
                default(){
                    return {healthinfo:{}};
                },
            },
        },

        watch:{
            mpiId(newVal,oldVal){
                if(newVal&&newVal!=oldVal){
                    //获取就诊记录列表
                    this.getRecordList();
                }
            },
        },

        mounted(){
            //获取就诊记录列表
            this.getRecordList();
        },

        methods:{
            sizeChange(value){
                this.params[0].limit=value;
                this.getTemplateList();
            },
            currentChange(value){
                this.params[0].page=value;
                this.getTemplateList();
            },
            checkDetail(item,index){
                this.listItem=item;
                this.currentHandleIndex=index;
                this.showRecordDetail=true;
            },
            getRecordList(){
                this.params[1]=this.mpiId;
                let paramsArr =  [this.params[0], this.params[1], '']
                findDiagnosisRecords(paramsArr,(res)=>{
                    this.loaded=true;
                    if(res.body){
                        let imVisitId=sStore.get('imVisitId');

                        this.recordList=res.body;
                        if(imVisitId){
                            let visitArr=this.recordList.map((item)=>item.visitId);
                            let posIndex=visitArr.indexOf(imVisitId);

                            sStore.set('imVisitId','');
                            if(~posIndex){
                                this.checkDetail(this.recordList[posIndex],posIndex);
                            }
                        }
                    }
                });
            },
        },

        components:{
            visitRecordDetail,
            defaultImage,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .visitRecord{
        max-width: 1200px;
        display: none;
        &.active{
            display: block;
        }
        .content{
            @include content;
            padding: 30px;
            .main{
                min-height: 200px;
                .tableWrap{
                    @include scrollTable;
                }
                table{
                    @include table3('../../../');
                    .item{
                        em{
                            color: $main;
                            display: none;
                        }
                        &.active{
                            em{
                                display: inline;
                            }
                        }
                    }
                }
                .defaultImageWrap{
                    height: 400px;
                    overflow: hidden;
                    /deep/ .defaultList{
                        padding: 50px 0;
                    }
                }
            }
            .end{
                display: flex;
                justify-content: flex-end;
                padding: 20px 0;
                /deep/ .el-input__inner{
                    height: 28px!important;
                    line-height: 28px!important;
                }
            }
        }
    }
</style>

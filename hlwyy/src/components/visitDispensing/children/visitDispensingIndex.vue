<template>
    <div class="visitDispensingIndex">
        <!-- 开发模式测试面板 -->
        <!-- <div v-if="isDevelopment" class="dev-panel">
            <button @click="loadTestData" class="dev-button">加载测试数据</button>
            <span class="dev-label">开发模式</span>
        </div> -->

        <section class="content">
            <div class="leftContent">
                <patientList :type="'visit'" />
            </div>
            <div class="rightContent">
                <!-- <div class="title">
                    复诊配药
                </div> -->
                <div v-show="patientListTabIndex==0&&patientInfo.userId">
                    <ol class="tab clear">
                        <li v-for="(item,index) in ['患者信息','360调阅']" :key="index" :class="{
                                active:index==otherTabIndex,
                            }" @click="otherTabIndex=index">
                            <span>{{item}}</span>
                        </li>
                    </ol>
                    <div v-show="otherTabIndex==0&&patientInfo.userId">
                        <patientInfo :type="'visit'" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" />
                        <!-- 调试信息 -->
                        <div style="background: #f0f9eb; padding: 10px; margin: 10px 0; border-radius: 4px;">
                            <p>调试信息：patientListTabIndex={{patientListTabIndex}}, otherTabIndex={{otherTabIndex}}, userId={{patientInfo.userId}}</p>
                        </div>
                        <!-- 转为住院按钮 -->
                        <div class="hospitalization-action">
                            <el-button 
                                type="primary" 
                                icon="el-icon-plus"
                                @click="showHospitalizationForm">
                                转为住院
                            </el-button>
                        </div>
                    </div>
                    <patient360View v-if="otherTabIndex==1" :show="true" :patientInfo="patientInfo" />
                    <!-- <visitRecord v-if="otherTabIndex==1" :show="true" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" /> -->
                </div>
                <div v-show="patientListTabIndex==1&&patientInfo.userId">
                    <div class="tab-container">
                        <ol class="tab clear">
                            <li v-for="(item,index) in itemList" :key="index" :class="{
                                    active:index==tabIndex,
                                }" @click="tabIndex=index">
                                <span>{{item}}</span>
                            </li>
                        </ol>
                        <!-- 转为住院按钮 - 放在标签页右侧 -->
                        <div class="hospitalization-action">
                            <el-button 
                                type="primary" 
                                size="small"
                                icon="el-icon-plus"
                                @click="showHospitalizationForm">
                                转为住院
                            </el-button>
                        </div>
                    </div>
                    
                    <onlineConsultation :show="tabIndex==0" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" />
                    <recordCaseHistory :show="tabIndex==1" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" />
                    <recordPrescription :show="tabIndex==2" :patientInfo="patientInfo" :patientDetailInfo="patientDetailInfo" />
                    <div v-if="tabIndex==3">
                        <!-- 医技申请子标签 -->
                        <div class="medical-tech-tabs">
                            <div class="sub-tab-container">
                                <div class="sub-tab" 
                                     :class="{ active: medicalTechTabIndex === 0 }"
                                     @click="medicalTechTabIndex = 0">开检查</div>
                                <div class="sub-tab" 
                                     :class="{ active: medicalTechTabIndex === 1 }"
                                     @click="medicalTechTabIndex = 1">开检验</div>
                            </div>
                            <Examine v-if="medicalTechTabIndex === 0" :patient-info="patientInfo" />
                            <LabTest v-if="medicalTechTabIndex === 1" :patient-info="patientInfo" />
                        </div>
                    </div>

                    <div v-if="tabIndex==4" class="external-iframe-container">
                        <iframe :src="medTechAppointmentUrl" frameborder="0" class="medical-tech-iframe"></iframe>
                    </div>
                    <patient360View v-if="tabIndex==5" :show="tabIndex==5" :patientInfo="patientInfo" />
                </div>
                <defaultImage :show="!patientInfo.userId" :showIndex="1" />
            </div>
        </section>

        <!-- 入院登记表组件 -->
        <hospitalization-form 
            :visible.sync="hospitalizationFormVisible"
            :patientInfo="patientInfo"
            @close="handleHospitalizationFormClose"
            @success="handleHospitalizationFormSuccess"
        />
    </div>
</template>
<script>
import vm from 'src/main';
import patientList from './patientList';
import patientInfo from './patientInfo';
import onlineConsultation from './onlineConsultation';
import recordCaseHistory from './recordCaseHistory';
import recordPrescription from './recordPrescription';
import visitRecord from './visitRecord';
import Examine from 'components/internetWard/Examine.vue';
import LabTest from 'components/internetWard/LabTest.vue';
import defaultImage from 'components/common/defaultImage';
import hospitalizationForm from 'components/common/HospitalizationForm.vue';
import { getPatientRevisitDetail, getConsultDetail } from 'services';
import patient360View from './patient360View';

export default {
    data() {
        return {
            query: this.$router.currentRoute.query,
            otherTabIndex: 0,
            tabIndex: 0,
            patientListTabIndex: -1,
            patientInfo: {},
            params: [],
            patientDetailInfo: { healthinfo: {} },
            // 添加开发模式标志
            // isDevelopment: process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local',
            // 医技预约URL
            medTechAppointmentUrl: 'http://************:6887/mrc/appointment?jsid=4&zyh=503521',
            // 切换检查和检验的标签
            medicalTechTabIndex: 0,
            // 入院登记表显示状态
            hospitalizationFormVisible: false
        }
    },

    computed: {
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId ? Number(revisitId) : undefined;
        },
        orderDetailId() {
            let { patientInfo = {} } = this;
            let { orderDetailId } = patientInfo;

            return orderDetailId;
        },
        itemList() {
            let arr = ['复诊配药', '录入病历', '录入处方']
            let { patientInfo = {} } = this;
            let { outpatientCode } = patientInfo;
            // if (outpatientCode) {
            //     arr.push('医技申请')
            // }
            arr.push('医技申请')
            arr.push('医技预约')
            arr.push('360调阅')
            return arr
        }
    },

    created() {
        vm.$on('patientListShowTabIndex', this.showTabIndex);
        vm.$on('patientListPatientChange', this.updatePatientInfo);
        vm.$on('toChangeTabIndex', this.toChangeTabIndex);
    },

    mounted() {
        // 在开发模式下显示提示
        /* if (this.isDevelopment) {
            this.$nextTick(() => {
                this.$message({
                    message: '开发模式：您可以使用右上角的"加载测试数据"按钮进行功能测试',
                    type: 'info',
                    duration: 5000
                });
            });
        } */
    },

    beforeDestroy() {
        vm.$off('patientListShowTabIndex', this.showTabIndex);
        vm.$off('patientListPatientChange', this.updatePatientInfo);
        vm.$off('toChangeTabIndex', this.toChangeTabIndex);
    },

    methods: {
        showTabIndex(patientListTabIndex) {
            this.patientListTabIndex = patientListTabIndex;
        },
        updatePatientInfo(tabIndex, patientInfo) {
            this.tabIndex = 0;
            this.patientListTabIndex = tabIndex;
            console.log('patientInfo==========', patientInfo);
            if (patientInfo) {
                this.patientInfo = patientInfo;
                
                // 添加调试信息，确保复诊ID存在
                console.log('复诊ID:', this.patientInfo.revisitId);
                console.log('患者ID:', this.patientInfo.patientId);
                console.log('机构ID:', this.patientInfo.orgId);

                //获取患者信息详情
                this.getDetail();
            }
        },
        toChangeTabIndex(tabIndex) {
            console.log('tabIndex======', tabIndex);
            this.tabIndex = tabIndex;
        },
        getDetail() {
            if (!this.revisitId && !this.orderDetailId) return;
            let { patientInfo = {}, type = 'visit' } = this;
            let { revisitId, orderDetailId } = patientInfo;
            let api = null;

            switch (type) {
                case 'imgText':
                case 'video':
                    this.params[0] = orderDetailId;
                    api = getConsultDetail;
                    break;
                case 'visit':
                    this.params[0] = revisitId;
                    api = getPatientRevisitDetail;
                    break;
            }

            api(this.params, (res) => {
                if (res.body) {
                    this.patientDetailInfo = res.body;
                }
            });
        },
        
        // 显示入院登记表
        showHospitalizationForm() {
            console.log('点击转为住院按钮')
            console.log('patientInfo:', this.patientInfo)
            console.log('patientInfo.userId:', this.patientInfo.userId)
            
            if (!this.patientInfo.userId) {
                this.$message.warning('请先选择患者');
                return;
            }
            
            console.log('设置hospitalizationFormVisible为true')
            this.hospitalizationFormVisible = true;
        },
        
        // 处理入院登记表关闭
        handleHospitalizationFormClose() {
            this.hospitalizationFormVisible = false;
        },
        
        // 处理入院登记表提交成功
        handleHospitalizationFormSuccess() {
            this.hospitalizationFormVisible = false;
            this.$message.success('患者已成功转为住院');
            // 可以在这里添加其他成功后的逻辑，比如刷新患者列表等
        },
        
        // 添加测试数据加载方法
        /* loadTestData() {
            // 设置模拟的患者信息
            this.patientInfo = {
                userId: 'test123',
                patientId: 'P10001',
                mpiId: 'MPI10001',
                revisitId: 'R10001',
                outpatientCode: 'OUT10001',
                patientName: '测试患者',
                sex: '1', // 1代表男性
                age: 35,
                idCard: '110101198501010011',
                phoneNo: '13800138000',
                avatar: '',
                deptName: '内科',
                docScheduleId: 'SCH10001',
                scheduleStartTime: new Date().getTime(),
                scheduleEndTime: new Date().getTime() + 30 * 60 * 1000,
            };

            // 设置患者详细信息
            this.patientDetailInfo = {
                healthinfo: {
                    medicalAllergy: '青霉素',
                    foodAllergy: '海鲜',
                    familyDiseaseHistory: '父亲高血压',
                    operationOrTrauma: '阑尾切除术(2015年)'
                },
                healthinfoAndSpecialVo: {
                    healthinfo: {
                        medicalAllergy: '青霉素',
                        foodAllergy: '海鲜',
                        familyDiseaseHistory: '父亲高血压',
                        operationOrTrauma: '阑尾切除术(2015年)'
                    }
                }
            };

            // 默认显示医疗相关模块
            this.patientListTabIndex = 1;

            // 提示用户
            this.$message({
                message: '测试患者数据已加载，现在可以使用病历、处方等功能',
                type: 'success'
            });
        } */
    },

    components: {
        patientList,
        patientInfo,
        onlineConsultation,
        recordCaseHistory,
        recordPrescription,
        visitRecord,
        Examine,
        LabTest,
        defaultImage,
        hospitalizationForm,
        patient360View,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.visitDispensingIndex {
    height: 100%;
    overflow: hidden;
    position: relative;
    
    .dev-panel {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: #f0f9eb;
        padding: 5px 10px;
        border-radius: 4px;
        border: 1px solid #67c23a;
        display: flex;
        align-items: center;
        
        .dev-button {
            background: #67c23a;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 12px;
            
            &:hover {
                background: #85ce61;
            }
        }
        
        .dev-label {
            color: #67c23a;
            font-size: 12px;
            font-weight: bold;
        }
    }
    
    .content {
        display: flex;
        .leftContent {
            width: 300px;
        }
        .rightContent {
            flex: 1;
            padding: 30px;
            padding-top: 0;
            height: 100vh;
            border-bottom: 98px solid transparent;
            overflow-y: auto;
            .title {
                height: 50px;
                line-height: 50px;
                font-size: 16px;
            }
            .tab {
                li {
                    float: left;
                    width: 130px;
                    padding-top: 5px;
                    height: 40px;
                    line-height: 35px;
                    text-align: center;
                    border: 1px solid transparent;
                    border-radius: 10px 10px 0 0;
                    font-size: 14px;
                    position: relative;
                    cursor: pointer;
                    span {
                        display: block;
                        height: 100%;
                        background-color: #f4f5f7;
                        color: #666;
                        border-radius: 10px 10px 0 0;
                    }
                    &.active {
                        background-color: #fff;
                        border-color: #ddd;
                        span {
                            background-color: #fff;
                            color: #409eff;
                        }
                        &:before {
                            content: "";
                            width: 129px;
                            height: 2px;
                            background-color: #fff;
                            border-left: 1px solid #ddd;
                            position: absolute;
                            left: -1px;
                            bottom: -2px;
                            z-index: 10;
                        }
                    }
                    &:first-of-type {
                        &.active:before {
                            height: 10px;
                            left: -1px;
                            bottom: -10px;
                        }
                    }
                }
            }
            
            // 标签页容器样式
            .tab-container {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                margin-bottom: 20px;
                
                .tab {
                    flex: 1;
                    margin-right: 20px;
                }
                
                .hospitalization-action {
                    flex-shrink: 0;
                    margin-bottom: 5px;
                }
            }
            
            // 转为住院按钮样式
            .hospitalization-action {
                margin-top: 20px;
                padding: 15px;
                background: #fff;
                border-radius: 4px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                text-align: center;
                
                .el-button {
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: 500;
                }
            }
            
            .external-iframe-container {
                width: 100%;
                height: calc(100vh - 150px);
                background: #fff;
                border-radius: 4px;
                padding: 15px;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                
                .medical-tech-iframe {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
            }
            
            // 医技申请子标签样式
            .medical-tech-tabs {
                background-color: #fff;
                border-radius: 4px;
                padding: 15px;
                min-height: 500px;

                .sub-tab-container {
                    display: flex;
                    margin-bottom: 20px;
                    border-bottom: 1px solid #ebeef5;
                    
                    .sub-tab {
                        padding: 10px 20px;
                        font-size: 14px;
                        cursor: pointer;
                        position: relative;
                        color: #606266;
                        
                        &.active {
                            color: #409eff;
                            font-weight: 500;
                            
                            &:after {
                                content: '';
                                position: absolute;
                                bottom: -1px;
                                left: 0;
                                right: 0;
                                height: 2px;
                                background-color: #409eff;
                            }
                        }
                        
                        &:hover {
                            color: #409eff;
                        }
                    }
                }
            }
        }
    }
}

</style>

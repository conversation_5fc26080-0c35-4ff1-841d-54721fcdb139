<template>
    <div :class="{
            checkApp:true,
            active:show,
        }">
        <section class="content">
            <div class="checkApp">
                <div class="checkAppTem">
                    <div class="checkAppTemTitle">检查单模板</div>
                    <div class="checkAppTemCon">
                        <ul>
                            <li v-for="(item,index) in checkAppTemList" :key="index" :class="{active: index == tabIndex}" @click="tabChange(item, index)">
                                <span>{{item.tempName}}</span>
                                <span v-if="index == tabIndex" class="arrow">＞</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="tableWrapBox">
                    <div class="tableWrap">
                        <el-table :data="tableList" border row-key="id" :default-expand-all="isExpand" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" height="350">
                            <el-table-column v-for="(item,index) in defineTable" :key="index" :label="item.label" :prop="item.prop" :width="item.width?item.width:''">
                                <template slot-scope="scope">
                                    <template v-if="index==0">
                                        <span v-if="scope.row.hasChild">
                                            <el-checkbox style="padding: 0 2px" v-model="scope.row.selectStatus"  @change="selectionCheckedChange(scope.row)"></el-checkbox>
                                            {{scope.row[item.prop]}}
                                        </span>
                                        <span v-else>
                                            <span v-if="scope.row.type=='1'" style="width:5px;display:inline-block"></span>
                                            <el-checkbox style="padding: 0 2px" v-model="scope.row.selectStatus"  @change="selectionCheckedChange(scope.row)"></el-checkbox>
                                            {{scope.row[item.prop]}}
                                        </span>
                                    </template>
                                    <template v-else>
                                        {{scope.row[item.prop]}}
                                    </template>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <ul class="additionalInfo">
                    <li>
                        <h3><i>*</i>临床信息：</h3>
                        <div class="textareaWrap">
                            <textarea v-model="clinicInfo" maxlength="30" placeholder="请输入临床信息"></textarea>
                        </div>
                    </li>
                    <li>
                        <h3><i>*</i>检查目的：</h3>
                        <div class="textareaWrap">
                            <textarea v-model="examPurpose" maxlength="30" placeholder="请输入检查目的"></textarea>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hascheckApp">
                <div class="titel">已选项目</div>
                <div class="content">
                    <div class="tableWrapBox">
                        <div class="tableWrap">
                            <el-table :data="hasTableList" border row-key="id" :default-expand-all="isExpand" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" height="350">
                                <el-table-column v-for="(item,index) in defineTable" :key="index" :label="item.label" :prop="item.prop" :width="item.width?item.width:''">
                                    <template slot-scope="scope">
                                        <template v-if="index==0">
                                            <span v-if="scope.row.hasChild">
                                                {{scope.row[item.prop]}}
                                            </span>
                                            <span v-else>
                                                <span style="width:15px;display:inline-block"></span>
                                                {{scope.row[item.prop]}}
                                            </span>
                                        </template>
                                        <template v-else-if="index==1">
                                            {{scope.row[item.prop]}}
                                        </template>
                                        <template v-else-if="index==2">
                                            {{scope.row.attachItemAmount?addFn(scope.row.itemPrice,scope.row.attachItemAmount):scope.row.itemPrice}}
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                    <div class="total">
                        <div class="end">
                            <div class="priceTotal">合计: {{totalPrice}}</div>
                            <div class="handleWrap">
                                <a @click="submit()">提交</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <previewInsApp v-if="showpreviewInsApp" :parent="this" :show="true" :showName="'showpreviewInsApp'" :previewDetail="previewDetail" :signature="applyMedicalSkillFn" />
    </div>
</template>
<script>
import previewInsApp from 'components/common/previewInsApp';
import { getMedicalSkillTmpl, findMedicalSkillItem, applyMedicalSkill, findMedicalSkillInfoByRevisitId, applyMedicalSkillValid } from 'services';
import { sStore, alerts, alertss, Dec } from 'js/yydjs';

export default {
    data() {
        return {
            orgId: sStore.get('orgId'),
            showpreviewInsApp: false,
            tabIndex: 0,
            activeName: 'first',
            isExpand: false,
            query: this.$router.currentRoute.query,
            defineTable: [{
                    label: '项目代码',
                    prop: 'itemCode',
                    width: '150'
                },
                {
                    label: '项目名称',
                    prop: 'itemName',
                },
                {
                    label: '单价(元)',
                    prop: 'itemPrice',
                    width: '150'
                },
            ],
            tableList: [],
            hasTableList: [],
            applyTableList: [],
            checkAppTemList: [],
            skillItemAttachs: [],
            currentTab: {},
            clinicInfo: '',
            examPurpose: '',
        }
    },

    computed: {
        mpiId() {
            let { patientDetailInfo = {} } = this;
            let { mpiId } = patientDetailInfo;

            return mpiId;
        },
        previewDetail() {
            let result = {};
            let { patientInfo = {}, patientDetailInfo = {}, prescription = {}, diagnoseList = [] } = this;
            let { drugs = [] } = prescription;

            prescription.drugs = drugs;
            result = Object.assign({}, patientInfo, patientDetailInfo, prescription, { mzDiagnoses: diagnoseList });
            console.log('详情', result);
            return result;
        },
        revisitId() {
            let { patientInfo = {} } = this;
            let { revisitId } = patientInfo;

            return revisitId;
        },
        totalPrice() {
            let total = this.hasTableList.reduce((total, v) => {
                let { attachItemAmount, itemPrice } = v;
                let price = attachItemAmount ? new Dec(itemPrice).add(new Dec(attachItemAmount)) : itemPrice;

                return new Dec(total).add(new Dec(price)).toNumber()
            }, 0)
            console.log(total)
            return total + '元'
        }
    },

    props: {
        show: { //是否显示该组件
            type: Boolean,
            default: false,
        },
        patientInfo: { //当前患者的信息
            type: Object,
            default () {
                return {};
            },
        },
        patientDetailInfo: { //当前患者的详细信息
            type: Object,
            default () {
                return { healthinfo: {} };
            },
        },
        chiefComplaint: { //当前主诉
            type: String,
            default: '',
        },
        diagnose: { //当前诊断
            type: String,
            default: '',
        },
    },

    watch: {
        mpiId(newVal, oldVal) {
            if (newVal && newVal != oldVal) {
                this.getMedicalSkillTmpl()
            }
        },
    },

    created() {
        this.setInitInfo();
    },

    mounted() {
        this.getMedicalSkillTmpl()
    },

    methods: {
        addFn(a, b) {
            return new Dec(a).add(new Dec(b)).toNumber();
        },
        setInitInfo() {
            let recordJson = sStore.get('recordJson') || {};
            let { chiefComplaint } = recordJson;
            let diagnoseJson = sStore.get('diagnoseJson') || {};
            let { list = [] } = diagnoseJson;

            this.clinicInfo = chiefComplaint;
            this.examPurpose = '明确诊断';
        },
        findMedicalSkillItem(tempId) {
            findMedicalSkillItem([tempId], (res) => {
                if (res.code == 200) {
                    console.log(res.body, 'body')
                    this.tableList = res.body.map((v) => {
                        let { medicalSkillItemChildren = [], itemAttachs = [], itemId, itemName, itemPrice, type = '0', itemCode, execDeptId, execDeptName, itemCatalog, itemType, selectStatus = false } = v
                        let itemPId = itemId
                        let obj = {
                            itemAttachs,
                            tempId,
                            itemId,
                            itemName,
                            itemPrice,
                            type,
                            itemCode,
                            execDeptId,
                            execDeptName,
                            itemCatalog,
                            itemType,
                            hasChild: false,
                            selectStatus
                        }
                        obj.id = type + itemId
                        let children = JSON.parse(JSON.stringify(medicalSkillItemChildren))
                        console.log(medicalSkillItemChildren)
                        if (children.length) {
                            children = children.map((item) => {
                                let { itemId, itemName, itemPrice, type = '1', itemCode, execDeptId, execDeptName, itemCatalog, itemType, selectStatus = false } = item
                                let itemObj = {
                                    tempId,
                                    itemId,
                                    itemName,
                                    itemPrice,
                                    type,
                                    itemCode,
                                    execDeptId,
                                    execDeptName,
                                    itemCatalog,
                                    itemType,
                                    itemPId,
                                    selectStatus
                                }
                                itemObj.id = type + itemId
                                return itemObj
                            })
                            obj.hasChild = true
                            obj.children = children
                        }
                        return obj
                    })
                    let hasList = JSON.parse(JSON.stringify(this.hasTableList))
                    let currentTempIdArr = hasList.filter((v) => {
                        return v.tempId == tempId
                    })
                    console.log(currentTempIdArr, 'currentTempIdArr')
                    currentTempIdArr.map((v, i) => {
                        console.log(v, i)
                        this.selectOpt(v)
                    })
                    console.log(this.tableList, 'tablist')
                }
            })
        },
        getMedicalSkillTmpl() {
            getMedicalSkillTmpl([this.orgId], (res) => {
                if (res.code == 200) {
                    this.checkAppTemList = res.body.filter((v) => {
                        return v.tempCatalog == 2
                    })
                    this.tabChange(this.checkAppTemList[0], 0)
                }
            })
        },
        tabChange(item, index) {
            this.tabIndex = index
            this.currentTab = item
            this.findMedicalSkillItem(item.tempId)
        },
        selectOpt(item) {
            let oneIndex = 0
            let retArr = []
            let retList = JSON.parse(JSON.stringify(this.tableList))
            retList.map((v, i) => {
                if (item.hasChild) {
                    if (item.id == v.id) {
                        oneIndex = i
                        console.log(oneIndex, 'three')
                        item.children = item.children.map((child, childIndex) => {
                            child.selectStatus = item.selectStatus
                            return child
                        })
                        this.tableList.splice(oneIndex, 1, item)
                    }
                } else {
                    if (item.type == '1') {
                        if (item.itemPId == v.itemId) {
                            oneIndex = i
                            console.log(oneIndex, 'two')
                            let selectStatusArr = []
                            v.children = v.children.map((child, childIndex) => {
                                if (item.id == child.id) {
                                    child.selectStatus = item.selectStatus
                                }
                                selectStatusArr.push(child.selectStatus)
                                return child
                            })
                            if (!~selectStatusArr.indexOf(false)) {
                                v.selectStatus = true
                            } else {
                                v.selectStatus = false
                            }
                            this.tableList.splice(oneIndex, 1, v)
                        }
                    } else {
                        if (item.id == v.id) {
                            oneIndex = i
                            console.log(oneIndex, 'one')
                            this.tableList.splice(oneIndex, 1, item)
                        }
                    }
                }
            })

        },
        itemAttachsJudge(item) {
            if (item.itemAttachs.length) {
                let { itemId, itemName, selectStatus, itemCatalogDetail } = item;
                let { attachItemId, attachItemName, attachItemCount, attachItemAmount } = item.itemAttachs[0];
                let idArr = this.skillItemAttachs.map((item) => item.itemId);
                let posIndex = idArr.indexOf(attachItemId);

                //console.log(this.skillItemAttachs, posIndex);
                if (itemCatalogDetail == '01') {
                    let selectCount = this.hasTableList.filter((item) => item.selectStatus).length;

                    if (selectCount > 1) {
                        alerts('已存在项目，无法添加');
                        this.hasTableList = this.hasTableList.filter((item1) => {
                            return item1.itemId != item.itemId;
                        });
                        this.tableList = this.tableList.map((item1) => {
                            if (item1.itemId == item.itemId) item1.selectStatus = false;
                            return item1;
                        });
                    }
                }
                if (selectStatus) {
                    this.$msgbox({
                        title: '',
                        message: `接受${itemName}检查之后，患者或家属是否需要支付${attachItemAmount}元的${attachItemName}`,
                        showCancelButton: true,
                        showConfirmButton: true,
                        cancelButtonText: '否',
                        confirmButtonText: '是',
                    }).then(() => {
                        if (!~posIndex) {
                            this.skillItemAttachs.push({
                                itemId: attachItemId,
                                itemPId: itemId,
                                itemName: attachItemName,
                                itemCount: attachItemCount,
                                attachItemFlag: 1,
                            });
                            this.hasTableList = this.hasTableList.map((item1) => {
                                if (item1.itemId == itemId) {
                                    item1.attachItemAmount = attachItemAmount;
                                }
                                return item1;
                            });
                        }
                    });
                } else {
                    if (~posIndex) {
                        this.skillItemAttachs.splice(posIndex, 1);
                        this.hasTableList = this.hasTableList.map((item1) => {
                            if (item1.itemId == itemId) {
                                item1.attachItemAmount = 0;
                            }
                            return item1;
                        });
                    }
                }
            }
        },
        selectionCheckedChange(item) {
            console.log(item, 'item')
            let retArr = []
            this.selectOpt(item)
            let hasList = JSON.parse(JSON.stringify(this.hasTableList))
            let retList = JSON.parse(JSON.stringify(this.tableList))
            retList.map((v, i) => {
                if (v.hasChild) {
                    if (v.selectStatus) {
                        retArr.push(v)
                    } else {
                        v.children = v.children.map((child, childIndex) => {
                            if (child.selectStatus) {
                                retArr.push(child)
                            }
                        })
                    }
                } else {
                    if (v.selectStatus) {
                        retArr.push(v)
                    }
                }
            })
            let otherTempIdArr = hasList.filter((v) => {
                return v.tempId != this.currentTab.tempId
            })
            this.hasTableList = otherTempIdArr.concat(retArr)
            console.log(otherTempIdArr, this.hasTableList)
            this.itemAttachsJudge(item);
        },
        submit() {
            if (this.hasTableList.length) {
                let { clinicInfo, examPurpose } = this;
                let arr = [
                    { if: !clinicInfo, hint: '请输入临床信息' },
                    { if: !examPurpose, hint: '请输入检查目的' },
                ];

                alertss(arr, () => {
                    this.previewDetail.hasTableList = this.hasTableList
                    this.previewDetail.currentTab = this.currentTab
                    this.doctorCheck();
                });
            } else {
                alerts('请选择申请项目')
            }
        },
        getParams() {
            let { clinicInfo, examPurpose, hasTableList, skillItemAttachs } = this;
            let params = {
                revisitId: this.revisitId,
                applyType: this.currentTab.tempCatalog,
                applyName: this.currentTab.tempName
            }
            let skillItems = []
            let concatArr = [].concat(hasTableList, skillItemAttachs);
            concatArr.map((v) => {
                let obj = {}
                let { itemId, itemName, itemPrice, itemCount = 1, hasChild, children = [], attachItemFlag, itemPId } = v
                obj = {
                    itemId,
                    itemName,
                    itemPrice,
                    itemCount
                }
                if (attachItemFlag) obj.attachItemFlag = attachItemFlag;
                if (itemPId) obj.itemPId = itemPId;
                skillItems.push(obj)
                if (hasChild) {
                    let childObj = {}
                    children.map(item => {
                        let { itemId, itemName, itemPrice, itemCount = 1, itemPId } = item
                        childObj = {
                            itemId,
                            itemName,
                            itemPrice,
                            itemCount,
                            itemPId,
                        }
                        skillItems.push(childObj)
                    })
                }
            })
            params.skillItems = skillItems
            params.clinicInfo = clinicInfo;
            params.examPurpose = examPurpose;
            return params;
        },
        applyMedicalSkillFn() {
            let params = this.getParams();

            applyMedicalSkill([params], (res) => {
                if (res.code == 200) {
                    setTimeout(() => {
                        alerts('申请单提交成功', 'success');
                        this.showpreviewInsApp = false
                        this.tableList = this.tableList.map(v => {
                            v.selectStatus = false
                            if (v.hasChild) {
                                v.children = v.children.map(item => {
                                    item.selectStatus = false
                                    return item
                                })
                            }
                            return v
                        })
                        this.hasTableList = []
                        this.skillItemAttachs = [];
                        // this.activeName = 'second'
                    }, 300);
                }
            })
        },
        doctorCheck() {
            let params = this.getParams();

            applyMedicalSkillValid([params], (res) => {
                let { code, msg } = res;

                if (code == 200) {
                    this.showpreviewInsApp = true;
                } else if (code == 100) {
                    this.$msgbox({
                        title: '',
                        message: msg,
                        showCancelButton: true,
                        showConfirmButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '确定',
                    }).then(() => {
                        this.showpreviewInsApp = true;
                    });
                } else {
                    alerts(msg);
                }
            });
        },
    },

    components: {
        previewInsApp,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.checkApp {
    &.active {
        display: block;
    }
    .content {
        min-height: 200px;
        width: 100%;
        .checkApp {
            width: 100%;
            display: flex;
            padding: 20px;
            border: 1px solid #ebeef5;
            ;
            border-radius: 10px 10px 10px 10px;
            .checkAppTem {
                min-width: 200px;
                max-width: 200px;
                padding-top: 5px;
                border: 1px solid #ebeef5;
                border-radius: 10px 10px 10px 10px;
                font-size: 14px;
                .checkAppTemTitle {
                    font-size: 18px;
                    text-align: center;
                    font-weight: bold;
                    padding: 10px 20px;
                }
                .checkAppTemCon {
                    min-height: 300px;
                    max-height: 400px;
                    overflow-y: scroll;
                    width: 100%;
                    ul {
                        padding: 0 30px;
                        li {
                            cursor: pointer;
                            display: flex;
                            padding: 5px 0px;
                            align-items: center;
                            span {
                                display: inline-block;
                                line-height: 20px;
                                color: #666;
                            }
                            .arrow {
                                padding-left: 5px;
                            }
                            &.active {
                                span {
                                    color: $main;
                                    border-color: $main;
                                }
                            }
                        }
                    }
                }
            }
            .tableWrapBox {
                width: 100%;
                padding: 0 20px;
                overflow-x: scroll;
                .cell {
                    display: flex;
                }
            }
            .additionalInfo {
                width: 400px;
                li {
                    h3 {
                        padding: 10px 0;
                        i {
                            color: #ff0000;
                            font-size: 12px;
                        }
                    }
                    .textareaWrap {
                        padding: 10px;
                        background-color: $bg;
                        border: $border1;
                        border-radius: $borderRadius1;
                        textarea {
                            width: 100%;
                            height: 100px;
                            line-height: 20px;
                            border: none;
                            background-color: $bg;
                        }
                    }
                }
            }
        }
        .hascheckApp {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ebeef5;
            ;
            border-radius: 10px 10px 10px 10px;
            .title {
                font-size: 18px;
                font-weight: bold;
            }
            .content {
                display: flex;
                .total {
                    min-width: 300px;
                    max-width: 300px;
                    padding-left: 20px;
                    position: relative;
                    .end {
                        display: flex;
                        align-items: center;
                        position: absolute;
                        bottom: 0px;
                        left: 20px;
                        .handleWrap {
                            width: 100%;
                            @include handleWrap;
                        }
                        .priceTotal {
                            min-width: 150px;
                            padding-right: 20px;
                        }
                    }
                }
                .tableWrapBox {
                    width: 100%;
                    padding: 0 20px;
                    overflow-x: scroll;
                    .cell {
                        display: flex;
                    }
                }
            }
        }
    }
}

</style>

<template>
    <div
        :class="{
            caseHistoryInputDialog:true,
            active:showDialog,
        }"
        ref="caseHistoryInputDialog"
    >
        <a
            :class="{
                onOff:true,
                active:showDialog,
            }"
            @click="controlDialog"
        ></a>
        <a
            v-show="!showDialog"
            class="textWrap"
            @click="controlDialog"
        >
            <span>病历模板</span>
        </a>
        <div v-show="showDialog" class="wrap">
            <div class="title">
                病历模板
            </div>
            <div class="main">
                <ol class="tabList">
                    <li
                        v-for="(item,index) in ['个人','病历记录','科室']"
                        :key="index"
                        :class="{
                            active:index==tabIndex,
                        }"
                        @click="tabChange(index)"
                    >
                        <a>{{item}}</a>
                    </li>
                </ol>
                <div class="scrollWrap">
                    <table v-if="tabIndex!=1">
                        <thead>
                            <tr>
                                <th v-for="(item, index) in ['模板名称','诊断名称','修改日期','']" :key="index">
                                    <div class="item">
                                        {{item}}
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(item,index) in templateList"
                                :key="index"
                                @dblclick="selectFn(item,index)"
                            >
                                <td>
                                    <div class="item">
                                        {{item.templateName||item.name&&`历史就诊病历${item.name}`}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{getDiagnoseName(item)}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{(item.lastModify||item.createDt)|date('yyyy.MM.dd')}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        <a
                                            class="check"
                                            @click="checkTemplate(item,index)"
                                        >
                                            预览
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <table v-else>
                        <thead>
                            <tr>
                                <th v-for="item in ['就诊日期','就诊科室','诊断名称','就诊医生','']">
                                    <div class="item">
                                        {{item}}
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(item,index) in templateList"
                                :key="index"
                                @dblclick="selectFn(item,index)"
                            >
                                <td>
                                    <div class="item">
                                        {{item.visitDateTime|date('yyyy.MM.dd')}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.deptName}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{getDiagnoseName(item)}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.doctorName}}
                                    </div>
                                </td>
                                <!-- <td>
                                    <div class="item">
                                        <a
                                            class="check"
                                            @click="checkTemplate(item,index)"
                                        >
                                            预览
                                        </a>
                                    </div>
                                </td> -->
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <caseHistoryCheckDialog
            v-if="showCaseHistoryCheckDialog"
            :parent="this"
            :show="true"
            :showName="'showCaseHistoryCheckDialog'"
            :templateId="templateId"
        />
    </div>
</template>

<script>
    import vm from 'src/main';
    import caseHistoryCheckDialog from 'components/templateManage/children/caseHistoryCheckDialog';
    import {copyJson,sStore,bind,unbind} from 'js/yydjs';
    import {queryTemplateMzMedicalRecordForPatient} from 'services';

    export default{
        data(){
            let standardDeptId=sStore.get('standardDeptId');
            let doctorId=sStore.get('docId');

            return{
                showCaseHistoryCheckDialog:false,
                showDialog:false,
                tabIndex:0,
                params:[],
                templateList:[],
                templateId:-1,
                standardDeptId,
                doctorId,
            }
        },

        /*
            <caseHistoryInputDialog
                :revisitId="revisitId"
                :select="selectCaseHistory"
            />
        */

        props:{
            revisitId:{// 复诊id
                type:Number,
                default:-1,
            },
            select:{//选择模板时触发的函数
                type:Function,
                default:(item,index,list)=>{},//选择的模板数据，选择模板数据的索引，模板数据列表
            },
        },

        watch:{
            revisitId(newVal,oldVal){
                if(newVal&&newVal!=-1&&newVal!=oldVal){
                    this.getTemplateList();
                }
            },
        },

        created(){
            vm.$on('caseHistoryInputDialogUpdate',this.getTemplateList);
            bind(document,'click',this.outerClose);
        },

        beforeDestroy(){
            vm.$off('caseHistoryInputDialogUpdate',this.getTemplateList);
            unbind(document,'click',this.outerClose);
        },

        methods:{
            outerClose(ev){
                let {target}=ev;
                let {caseHistoryInputDialog}=this.$refs;

                if(!caseHistoryInputDialog.contains(target)&&!target.classList.contains('el-loading-mask')){
                    this.showDialog=false;
                }
            },
            checkTemplate(item,index){
                let {templateId}=item;

                this.currentHandleIndex=index;
                this.templateId=templateId;
                this.showCaseHistoryCheckDialog=false;
                setTimeout(()=>{
                    this.showCaseHistoryCheckDialog=true;
                },300);
            },
            controlDialog(){
                this.showDialog=!this.showDialog;
            },
            tabChange(index){
                this.tabIndex=index;
                this.templateList=[];
                this.getTemplateList();
            },
            getDiagnoseName(item){
                let {icdName,mzDiagnoses=[]}=item;

                return icdName||mzDiagnoses.map((item)=>item.icdName).join('|');
            },
            getTemplateList(){
                let {revisitId,tabIndex,standardDeptId,doctorId}=this;

                this.params[0]=revisitId;
                this.params[1] = tabIndex == 2 ? 1 : tabIndex == 1 ? 3 : 2;
                this.params[2]=tabIndex==2?standardDeptId:doctorId;
                queryTemplateMzMedicalRecordForPatient(this.params,(res)=>{
                    if(res.body){
                        let templateList=[];
                        let {templateMzMedicalRecord=[],revisitOrder=[]}=res.body;

                        for(let i=0;i<revisitOrder.length;i++){
                            revisitOrder[i].name=i+1;
                        }

                        switch(tabIndex){
                            case 0:
                                    templateList=templateMzMedicalRecord;
                                break;
                            case 1:
                                    templateList=revisitOrder;
                                break;
                            case 2:
                                    templateList=templateMzMedicalRecord;
                                break;
                        }

                        this.templateList=templateList;
                    }
                });
            },
            selectFn(item,index){
                let {select,templateList}=this;
                let template=copyJson(item);
                let {mzDiagnoses}=template;

                if(mzDiagnoses){
                    template.mzDiagnoses=mzDiagnoses.map((item)=>{
                        delete item.diagnoseRecordId;
                        return item;
                    });
                }
                select&&select(template,index,templateList);
            },
        },

        components:{
            caseHistoryCheckDialog,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .caseHistoryInputDialog{
        width: 50px;
        padding: 20px 0;
        height: 400px;
        background-color: #fff;
        box-shadow: 0 0 8px rgba(0,0,0,.1);
        position: fixed;
        right: 0;
        top: 60px;
        z-index: 1900;
        transition: width .3s ease-out;
        &.active{
            width: 500px;
            padding: 20px;
        }
        .onOff{
            width: 40px;
            height: 40px;
            background: url('../../../assets/img/openMenu.png') no-repeat center center;
            background-size: cover;
            border-radius: 2px;
            position: absolute;
            left: -30px;
            top: -5px;
            z-index: 1000;
            &.active{
                background-image: url('../../../assets/img/closeMenu.png');
            }
        }
        .textWrap{
            display: flex;
            align-items: center;
            width: 50px;
            padding: 0 15px;
            height: 100%;
            line-height: 30px;
            text-align: center;
            span{
                font-size: 14px;
            }
        }
        .wrap{
            .title{
                padding: 0 6px;
                line-height: 30px;
                font-size: 16px;
            }
            .main{
                .tabList{
                    padding: 0 6px;
                    margin-bottom: 20px;
                    overflow: hidden;
                    li{
                        float: left;
                        padding: 0 20px;
                        a{
                            display: block;
                            width: 80px;
                            padding-left: 20px;
                            height: 40px;
                            line-height: 40px;
                            text-align: center;
                            border-bottom: 2px solid transparent;
                            background: url('../../../assets/img/ic_user_adduser.png') no-repeat left center;
                            background-size: 20px;
                        }
                        &:first-of-type{
                            padding-left: 0;
                        }
                        &:nth-of-type(2){
                            a{
                                background-image: url('../../../assets/img/ic_bljl_ychz.png');
                            }
                        }
                        &:nth-of-type(3){
                            a{
                                background-image: url('../../../assets/img/ic_syks_ychz.png');
                            }
                        }
                        &.active{
                            a{
                                border-color: $main;
                                color: $main;
                            }
                        }
                    }
                }
                .scrollWrap{
                    height: 260px;
                    overflow-y: auto;
                    table{
                        @include table2('../../../');
                        .check{
                            color: $main;
                        }
                    }
                }
            }
        }
    }
</style>
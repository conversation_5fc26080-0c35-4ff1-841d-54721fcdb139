<template>
    <div class="patientList">
        <section class="content">
            <ol class="statusTab">
                <li v-for="(item,index) in statusList" :key="index" :class="{
                        active:index==tabIndex,
                        unread:unreadArr[index],
                    }" @click="tabChange(index,true)">
                    <span>{{item}}（{{totalArr[index]||0}}）</span>
                </li>
            </ol>
            <div class="searchWrap">
                <div class="inputWrap">
                    <input v-model="params[0].personName" type="text" placeholder="请输入患者姓名" @keydown.enter="search" />
                    <a class="search" @click="search"></a>
                </div>
            </div>
            <div class="patientListWrap" @scroll="scrollLoad($event)">
                <ul class="patientList">
                    <li v-for="(item,index) in patientList" :key="index" :class="{
                            active:index==patientIndex,
                        }" @click="patientChange(item,index)">
                        <div :class="{
                                portrait:true,
                                unread:item.unread,
                            }" :style="{
                                backgroundImage:item.avatar?`url(${item.avatar})`:'',
                            }"></div>
                        <div class="rightContent">
                            <h3>
                                <span>{{item.patientName}}</span>
                                <em :class="{
                                        female:item.sex!='1',
                                    }">{{item.age}}岁</em>
                            </h3>
                            <h4 v-if="type=='visit'&&tabIndex==0 && item.docScheduleId" class="multiLine">
                                <em>预约时间：</em>
                                <em>{{item.scheduleStartTime|date('MM-dd')}}</em>
                                <em>{{item.scheduleStartTime|date('hh:mm')}}-{{item.scheduleEndTime|date('hh:mm')}}</em>
                            </h4>
                            <h4 v-if="type!='visit'&&tabIndex==0 && item.docScheduleId" class="multiLine">
                                <em>预约时间：</em>
                                <em>{{item.startTime|date('MM-dd')}}</em>
                                <em>{{item.startTime|date('hh:mm')}}-{{item.endTime|date('hh:mm')}}</em>
                            </h4>
                            <h4 v-if="type=='visit'&&tabIndex==1 && item.endDt" class="multiLine">
                                <!-- <em>结束时间：</em> -->
                                <em style="color: red">订单将在当天20点结束</em>
                                <!-- <em>{{item.endDt|date('MM-dd hh:mm')}}</em> -->
                            </h4>
                            <h4 v-if="type!='visit'&&tabIndex==1 && item.consutleEndAt" class="multiLine">
                                <!-- <em>结束时间：</em> -->
                                <em style="color: red">订单将在当天20点结束</em>
                                <!-- <em>{{item.consutleEndAt|date('MM-dd hh:mm')}}</em> -->
                            </h4>
                            <h4 v-if="type=='visit'" class="multiLine">
                                诊断：{{item.diagnoseName || '无'}}
                            </h4>
                        </div>
                    </li>
                </ul>
                <defaultImage :show="loaded&&!patientList.length" :showIndex="2" />
                <!-- <div
                    :class="{
                        loadingWrap:true,
                        loading,
                        finished,
                    }"
                >
                    <h3>
                        <span>正在加载中</span>
                    </h3>
                    <h4>
                        <span>加载完毕</span>
                    </h4>
                </div> -->
            </div>
        </section>
    </div>
</template>
<script>
import vm from 'src/main';
import defaultImage from 'components/common/defaultImage';
import { sStore, getStyle, copyJson } from 'js/yydjs';
import { imgview, queryPatient, getConsultList, updateMsg } from 'services';

export default {
    data() {
        let query = this.$router.currentRoute.query;
        let { tabIndex = 0, from } = query;

        return {
            query: this.$router.currentRoute.query,
            statusJson: {
                imgText: ['待接诊', '咨询中'],
                video: ['待接诊', '咨询中'],
                visit: ['待接诊', '复诊中'],
            },
            tabIndex,
            patientIndex: -1,
            params: [{
                orgId: sStore.get('orgId'),
                doctorId: sStore.get('docId'),
                personName: '', // 患者姓名搜索关键字
                pageNo: 1,
                pageSize: 1000,
            }],
            totalArr: [0, 0],
            patientList: [],
            otherList: [],
            loaded: false,
            loading: false,
            finished: false,
            imgview,
        }
    },

    watch: {
        $route: {
            handler(newVal, oldVal) {
                let { query: newQuery } = newVal;
                let { query: oldQuery } = oldVal;
                let { buzId: newBuzId } = newQuery;
                let { buzId: oldBuzId } = oldQuery;
                let { tabIndex = 0 } = newQuery;

                this.query = newQuery;
                if (newBuzId != oldBuzId) {
                    this.tabChange(tabIndex);
                }
            },
            deep: true,
        },
    },

    created() {
        vm.$on('patientListRefreshPatientList', this.refreshPatientList);
        vm.$on('patientListTabChange', this.tabChange);
        vm.$emit('patientListShowTabIndex', this.tabIndex);
    },

    computed: {
        statusList() {
            let { type, statusJson } = this;

            return statusJson[type];
        },
        unreadArr() {
            let result = [0, 0];
            let { patientList = [], otherList = [], tabIndex = 0 } = this;
            let patientUnread = patientList.map((item) => item.unread ? 1 : 0);
            let otherUnread = otherList.map((item) => item.unread ? 1 : 0);
            let patientPosIndex = patientUnread.indexOf(1);
            let otherPosIndex = otherUnread.indexOf(1);

            result[0] = tabIndex == 0 ? (~patientPosIndex ? 1 : 0) : (~otherPosIndex ? 1 : 0);
            result[1] = tabIndex == 0 ? (~otherPosIndex ? 1 : 0) : (~patientPosIndex ? 1 : 0);
            // console.log(patientUnread, 'patientUnread', otherUnread, 'otherUnread')
            // console.log(result, 'result')
            return result;
        },
    },

    beforeDestroy() {
        vm.$off('patientListRefreshPatientList', this.refreshPatientList);
        vm.$off('patientListTabChange', this.tabChange);
    },

    /*
        <patientList
            :type="'visit'"
        />
    */

    props: {
        type: { //患者列表咨询类型
            type: String,
            default: 'visit', //图文：imgText，视频：video，复诊配药：visit
        },
    },

    mounted() {
        //获取病人列表
        this.getPatientList();
    },

    methods: {
        closeSidebar() {
            vm.$emit('closeSidebar');
        },
        tabChange(index, clearQuery) {
            if (clearQuery) this.query = {};
            this.tabIndex = index;
            this.refreshPatientList();
        },
        refreshPatientList(endFn) {
            this.patientList = [];
            this.otherList = [];
            this.loading = false;
            this.finished = false;
            this.params[0].pageNo = 1;
            this.getPatientList(null, endFn);
        },
        patientChange(item, index) {
            let { tabIndex } = this;
            let { patientAccId: to, orderDetailId, revisitId } = item;

            this.patientIndex = index;
            sStore.set('nimChat', {
                scene: 'p2p',
                to,
            });
            updateMsg([orderDetailId || revisitId, +new Date() + ''], (res) => {
                this.patientList[index].unread = 0;
            });
            vm.$emit('patientListPatientChange', tabIndex, item);
        },
        getOtherTotal() {
            let { tabIndex, type } = this;
            let api = null;
            let params = copyJson(this.params);

            tabIndex = tabIndex == 0 ? 1 : 0;
            switch (type) {
                case 'imgText':
                    params[0].status = tabIndex == 0 ? '1' : '2';
                    api = getConsultList;
                    break;
                case 'video':
                    params[0].status = tabIndex == 0 ? '1' : '2';
                    api = getConsultList;
                    break;
                case 'visit':
                    params[0].orderStatus = tabIndex == 0 ? '01' : '02';
                    api = queryPatient;
                    break;
            }
            // params[0].pageSize=0;
            api(params, (res) => {
                if (res.body) {
                    let { data = [], total } = res.body;
                    let totalArr = [].concat(this.totalArr);

                    totalArr[tabIndex] = total;
                    this.totalArr = totalArr;
                    this.otherList = data;
                    // console.log(params[0].status, this.otherList, 'status-otherList')
                }
            });
        },
        getPatientList(noSwitch, endFn) {
            let { tabIndex, type } = this;
            let api = null;

            this.loaded = false;
            switch (type) {
                case 'imgText':
                    this.params[0].status = tabIndex == 0 ? '1' : '2';
                    this.params[0].itemCode = '01';
                    this.params[0].patientName = this.params[0].personName;
                    api = getConsultList;
                    break;
                case 'video':
                    this.params[0].status = tabIndex == 0 ? '1' : '2';
                    this.params[0].itemCode = '02';
                    this.params[0].patientName = this.params[0].personName;
                    api = getConsultList;
                    break;
                case 'visit':
                    this.params[0].orderStatus = tabIndex == 0 ? '01' : '02';
                    api = queryPatient;
                    break;
            }
            api(this.params, (res) => {
                this.loaded = true;
                endFn && endFn();
                if (res.body) {
                    let { body = {} } = res;
                    let { data = [], total } = body;
                    let firstPatient = data[0];
                    let { tabIndex } = this;
                    let totalArr = [].concat(this.totalArr);

                    totalArr[tabIndex] = total;
                    this.totalArr = totalArr;
                    this.patientList = [].concat(this.patientList, data);
                    this.params[0].personName = '';

                    if (noSwitch) {
                        this.patientIndex = -1;
                    } else {
                        let { buzId } = this.query;
                        let selectPatient = null;
                        let selectIndex = -1;
                        const selectFirstPatient = () => {
                            //选中第一个患者
                            if (firstPatient) {
                                this.patientChange(firstPatient, 0);
                            } else {
                                vm.$emit('patientListPatientChange', tabIndex, {});
                            }
                        };

                        if (buzId) {
                            for (let i = 0; i < data.length; i++) {
                                let item = data[i];
                                let { orderDetailId, revisitId } = item;
                                let itemBuzId = orderDetailId || revisitId;

                                selectIndex = i;
                                if (itemBuzId == buzId) {
                                    selectPatient = item;
                                    break;
                                }
                            }

                            //选中对应消息的患者
                            if (selectPatient) {
                                this.patientChange(selectPatient, selectIndex);
                            } else if (tabIndex == 1) {
                                this.tabIndex = 0;
                                this.refreshPatientList();
                            } else {
                                selectFirstPatient();
                            }
                        } else {
                            selectFirstPatient();
                        }
                    }

                    //上拉加载相关
                    setTimeout(() => {
                        this.loading = false;
                    }, 300);
                    if (this.patientList.length >= total && this.patientList.length > this.params[0].pageSize) {
                        this.finished = true;
                    }
                    //去除分页
                    this.finished = true;
                    // console.log(this.params[0].status, this.patientList, 'status-patientList')
                }
            });

            if (!noSwitch) {
                //获取另一个状态的统计字段
                this.getOtherTotal();
            }
        },
        scrollLoad(ev) {
            let { loading, finished, patientList, params } = this;
            let { pageSize } = params[0];
            let { currentTarget } = ev;
            let parentHeight = parseInt(getStyle(currentTarget, 'height')) - 198;
            let scrollTop = currentTarget.scrollTop;
            let childHeight = parseInt(getStyle(currentTarget.children[0], 'height'));

            if (parentHeight + scrollTop > childHeight - 50) {
                if (!finished && !loading && patientList.length >= pageSize) {
                    this.loading = true;
                    this.params[0].pageNo++;
                    this.getPatientList();
                }
            }
        },
        search(noSwitch) {
            this.patientList = [];
            this.getPatientList(noSwitch);
        },
    },

    components: {
        defaultImage,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.patientList {
    background: url('../../../assets/img/bg_fzpy_leftbar.png') no-repeat center center;
    background-size: cover;
    box-shadow: 0 2px 6px rgba(82, 163, 252, .2);
    .content {
        .statusTab {
            display: flex;
            padding: 0 40px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            background-color: #e9ecfb;
            li {
                flex: 1;
                cursor: pointer;
                span {
                    display: inline-block;
                    height: 100%;
                    color: #666;
                    position: relative;
                    border-bottom: 3px solid transparent;
                    position: relative;
                    &:before {
                        content: "";
                        width: 10px;
                        height: 10px;
                        background-color: #ff0000;
                        border-radius: 50%;
                        position: absolute;
                        right: -10px;
                        top: 15px;
                        z-index: 10;
                        display: none;
                    }
                }
                &.active {
                    span {
                        color: $main;
                        border-color: $main;
                    }
                }
                &.unread {
                    span {
                        &:before {
                            display: block;
                        }
                    }
                }
            }
        }
        .searchWrap {
            padding: 20px 15px;
            .inputWrap {
                display: flex;
                padding: 0 10px;
                height: 30px;
                border: 1px solid $gray;
                border-radius: 15px;
                background-color: #fff;
                input {
                    flex: 1;
                    height: 100%;
                    font-size: 12px;
                    background-color: transparent;
                    border: none;
                    &::-webkit-input-placeholder {
                        font-size: 12px;
                    }
                }
                .search {
                    width: 20px;
                    height: 100%;
                    background: url('../../../assets/img/ic_navbar_search_gray.png') no-repeat left center;
                    background-size: 14px;
                }
            }
        }
        .patientListWrap {
            height: 100vh;
            border-bottom: 200px solid transparent;
            overflow-y: auto;
            .patientList {
                li {
                    @include patientInfo('../../../', 48, 19);
                    border-left: 2px solid transparent;
                    &.active {
                        border-color: $main;
                    }
                }
            }
            .loadingWrap {
                @include loadingWrap('../../../');
            }
        }
    }
}

</style>

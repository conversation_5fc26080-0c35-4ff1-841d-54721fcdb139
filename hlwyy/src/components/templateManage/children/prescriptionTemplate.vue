<template>
    <div class="prescriptionTemplate">
        <section class="content">
            <div class="title">
                <ol>
                    <li>
                        <span>模板名称</span>
                        <div class="inputWrap">
                            <input
                                v-model="params[0].templateName"
                                type="text"
                                placeholder="请输入模板名称"
                                @keydown.enter="search"
                            />
                        </div>
                    </li>
                    <li>
                        <span>诊断名称</span>
                        <div class="inputWrap">
                            <input
                                v-model="params[0].icdName"
                                type="text"
                                placeholder="请输入诊断名称"
                                @keydown.enter="search"
                            />
                        </div>
                    </li>
                </ol>
                <a
                    class="search"
                    @click="search"
                >搜索</a>
            </div>
            <div class="title">
                <a
                    class="search"
                    @click="addTemplate"
                >新增模板</a>
            </div>
            <div class="main">
                <table>
                    <thead>
                        <tr>
                            <th v-for="(item,index) in ['类别','模板名称','诊断名称','修改日期','操作']" :key="index">
                                <div class="item">
                                    {{item}}
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr
                            v-for="(item,index) in templateList"
                            :key="index"
                            :class="{
                                active:index==currentHandleIndex,
                            }"
                        >
                            <td>
                                <div class="item">
                                    {{item.tmpBelongObjType==2?'个人':'科室'}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.templateName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.icdName}}
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    {{item.lastModify|date('yyyy.MM.dd')}}
                                </div>
                            </td>
                            <td>
                                <div class="item handle">
                                    <a
                                        @click="checkTemplate(item,index)"
                                    >查看</a>
                                    <a
                                        @click="editTemplate(item,index)"
                                    >编辑</a>
                                    <a
                                        @click="deleteTemplate(item,index)"
                                    >删除</a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="end">
                <el-pagination
                    layout="total,sizes,prev,pager,next,jumper"
                    :total="total"
                    :current-page="params[0].page"
                    :page-size="params[0].limit"
                    :page-sizes="[10,20,50]"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                />
            </div>
        </section>

        <prescriptionCheckDialog
            v-if="showPrescriptionCheckDialog"
            :parent="this"
            :show="true"
            :showName="'showPrescriptionCheckDialog'"
            :templateId="templateId"
        />

        <prescriptionEditDialog
            v-if="showPrescriptionEditDialog"
            :parent="this"
            :show="true"
            :showName="'showPrescriptionEditDialog'"
            :templateId="templateId"
        />
    </div>
</template>

<script>
    import vm from 'src/main';
    import prescriptionCheckDialog from './prescriptionCheckDialog';
    import prescriptionEditDialog from './prescriptionEditDialog';
    import {sStore,alerts} from 'js/yydjs';
    import {pageTemplateRecipeList,deleteTemplateRecipe} from 'services';

    export default{
        data(){
            return{
                query:this.$router.currentRoute.query,
                showPrescriptionCheckDialog:false,
                showPrescriptionEditDialog:false,
                total:0,
                currentHandleIndex:-1,
                params:[{
                    tmpBelongObjType:2,  //模板所属对象类型：1科室、2医生
                    tmpBelongObjId:sStore.get('docId'),   //医生id或者科室id
                    templateName:'',  //模板名称
                    icdName:'',  //诊断名称
                    page:1,
                    limit:10,
                }],
                templateList:[],
                templateId:-1,
            }
        },

        created(){
            vm.$on('prescriptionTemplateUpdate',this.getTemplateList);
        },

        mounted(){
            //获取模板列表
            this.getTemplateList();
        },

        beforeDestroy(){
            vm.$off('prescriptionTemplateUpdate',this.getTemplateList);
        },

        methods:{
            sizeChange(value){
                this.params[0].limit=value;
                this.getTemplateList();
            },
            currentChange(value){
                this.params[0].page=value;
                this.getTemplateList();
            },
            getTemplateList(endFn){
                this.currentHandleIndex=-1;
                pageTemplateRecipeList(this.params,(res)=>{
                    if(res.body){
                        let {data=[],total}=res.body;

                        this.templateList=data;
                        this.total=total;
                    }
                    endFn&&endFn(res);
                });
            },
            search(){
                this.getTemplateList(()=>{
                    this.params[0].templateName='';
                    this.params[0].icdName='';
                });
            },
            addTemplate(){
                this.currentHandleIndex = -1;
                this.templateId = -1;
                this.showPrescriptionEditDialog=false;
                setTimeout(()=>{
                    this.showPrescriptionEditDialog=true;
                },300);
            },
            checkTemplate(item,index){
                let {templateId}=item;

                this.currentHandleIndex=index;
                this.templateId=templateId;
                this.showPrescriptionCheckDialog=false;
                setTimeout(()=>{
                    this.showPrescriptionCheckDialog=true;
                },300);
            },
            editTemplate(item,index){
                let {templateId}=item;

                this.currentHandleIndex=index;
                this.templateId=templateId;
                this.showPrescriptionEditDialog=false;
                setTimeout(()=>{
                    this.showPrescriptionEditDialog=true;
                },300);
            },
            deleteTemplate(item,index){
                let {templateId}=item;

                this.currentHandleIndex=index;
                this.$msgbox({
                    title:'',
                    message:'是否删除该处方模板？',
                    showCancelButton:true,
                    showConfirmButton:true,
                    cancelButtonText:'取消',
                    confirmButtonText:'确定',
                }).then(()=>{
                    deleteTemplateRecipe([templateId],(res)=>{
                        this.getTemplateList();
                        alerts('处方模板删除成功','success');
                    });
                });
            },
        },

        components:{
            prescriptionCheckDialog,
            prescriptionEditDialog,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .prescriptionTemplate{
        .content{
            padding: 20px;
            .title{
                display: flex;
                padding-bottom: 30px;
                ol{
                    display: flex;
                    li{
                        display: flex;
                        padding-right: 20px;
                        line-height: 34px;
                        span{
                            color: #666;
                        }
                        .inputWrap{
                            margin-left: 15px;
                            width: 200px;
                            padding: 0 20px;
                            height: 34px;
                            background-color: #fff;
                            border-radius: 17px;
                            input{
                                width: 100%;
                                background-color: transparent;
                                border: none;
                                font-size: 12px;
                                &::-webkit-input-placeholder{
                                    font-size: 12px;
                                }
                            }
                        }
                    }
                }
                .search{
                    width: 98px;
                    height: 34px;
                    line-height: 34px;
                    text-align: center;
                    background-color: $main;
                    color: #fff;
                    border-radius: 17px;
                }
            }
            .main{
                table{
                    @include table1;
                    .item{
                        &.handle{
                            display: flex;
                            justify-content: center;
                            a{
                                padding-left: 15px;
                                margin-right: 15px;
                                height: 20px;
                                background: url('../../../assets/img/icon_look.png') no-repeat left center;
                                background-size: 12px;
                                cursor: pointer;
                                &:nth-of-type(2){
                                    background-image: url('../../../assets/img/icon_write.png');
                                }
                                &:nth-of-type(3){
                                    background-image: url('../../../assets/img/icon_delete.png');
                                }
                            }
                        }
                    }
                    tr{
                        &.active{
                            .item{
                                &.handle{
                                    a{
                                        color: $main;
                                        &:nth-of-type(3){
                                            color: $orange;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .end{
                display: flex;
                justify-content: flex-end;
                padding: 20px 0;
                /deep/ .el-input__inner{
                    height: 28px!important;
                    line-height: 28px!important;
                }
            }
        }
    }
</style>
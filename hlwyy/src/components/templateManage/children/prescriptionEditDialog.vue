<template>
    <div class="prescriptionEditDialog">
        <yydDialog :parent="parent" :show="show" :showName="showName" :maskClose="false" :width="1000" :height="542" :title="'处方模板'">
            <ul class="caseHistory">
                <li :class="{
                        hasHint,
                    }">
                    <div class="left">
                        <span>模板名称：</span>
                    </div>
                    <div class="right">
                        <input v-model="detail.templateRecipe.templateName" maxlength="15" type="text" />
                        <div class="hint">已存在相同名称的模板！</div>
                    </div>
                </li>
                <li :class="{
                        hasHint:currentHintIndex==0,
                    }">
                    <div class="left">
                        <span><em>*</em>诊断：</span>
                    </div>
                    <div class="right">
                        <div class="zdWrap">
                            <queryDiagnoseList :componentId="'prescriptionEditDialog1'" :changeFn="changeDiagnoseList" />
                        </div>
                        <div class="hint">请录入搜索结果中的诊断，否则无法保存！</div>
                    </div>
                </li>
            </ul>
            <div class="prescriptionWrap" ref="prescriptionWrap">
                <table>
                    <thead>
                        <tr>
                            <th v-for="(item1, index) in ['药品名称','药品代码','规格','剂型','用药方式','剂量（单位）','频次','天数','总量','单位', '备注']" :key="index">
                                <div class="item">
                                    <span>{{item1}}</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="detail&&detail.drugs">
                        <tr v-for="(item1,index1) in detail.drugs" :key="index1" :class="{
                                active:index1==componentIndex,
                            }">
                            <td>
                                <div class="item">
                                    <span>
                                        <template>
                                            <queryDrugList v-if="onOff" :zIndex="1000-index1" :keyWord="item1.drugName" :componentIndex="index1" :focusFn="focusFn" :changeFn="changeDrugList" />
                                            </queryDrugList>
                                        </template>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>{{item1.drugCodeStandard}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>{{item1.drugSpecifications}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>{{item1.pharmaceuticalDosageFormName}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>
                                        <select v-model="item1.drugUsePathwaysCode" @focus="componentIndex=index1" @keydown.enter="enterAdd(index1)">
                                            <option v-for="(item2, index2) in drugUsePathways_gd" :key="index2" :value="item2.key">
                                                {{item2.text}}
                                            </option>
                                        </select>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>
                                        <input v-model.trim="item1.drugUseDose" type="text" @input="getDrugUseDose($event, index1, item1)" @focus="componentIndex=index1" @keydown.enter="enterAdd(index1)" />
                                    </span>
                                    <em>{{item1.drugUseDoseUnitText}}</em>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>
                                        <select v-model="item1.drugUsingRate" @change="getDrugUsingRate($event, index1, item1)" @focus="componentIndex=index1" @keydown.enter="enterAdd(index1)">
                                            <option v-for="(item2, index2) in drugUsingRate_gd" :key="index2" :value="item2.key">
                                                {{item2.text}}
                                            </option>
                                        </select>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>
                                        <input v-model.trim="item1.drugUseDays" type="text" @input="getDrugUseDays($event, index1, item1)" @focus="componentIndex=index1" @keydown.enter="enterAdd(index1)" />
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>
                                        <input v-model.trim="item1.drugSendNumber" type="text" @input="getDrugSendNumber($event, index1, item1)" @focus="componentIndex=index1" @keydown.enter="enterAdd(index1)" />
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>{{item1.drugPackSpecificationUnitText}}</span>
                                </div>
                            </td>
                            <td>
                                <div class="item">
                                    <span>
                                        <input noCheck="true" v-model.trim="item1.remark" type="text" maxlength="20" @focus="componentIndex=index1" @keydown.enter="enterAdd(index1)" />
                                        <select v-model="item1.remark" @focus="componentIndex=index1">
                                            <option v-for="(item2, index2) in recipe_remark" :key="index2" :value="item2.text">
                                                {{item2.text}}
                                            </option>
                                        </select>
                                    </span>
                                    <a class="deleteDrug" @click="deleteDrug(index1)"></a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <a class="add" @click="addDrug">
                    <i>+</i>
                    <span>添加</span>
                </a>
                <div :class="{
                        hintWrap:true,
                        hasHint:currentHintIndex==1,
                    }">
                    <div class="hint">请输入完整的药品信息，否则无法保存！</div>
                </div>
                <div class="date" v-if="templateId!=-1">修改日期：{{detail.templateRecipe.lastModify|date('yyyy.MM.dd')}}</div>
                <div class="handleWrap">
                    <a @click="saveTemplate">保存</a>
                    <a class="red" @click="deleteTemplate">删除</a>
                </div>
            </div>
        </yydDialog>
    </div>
</template>
<script>
import vm from 'src/main';
import yydDialog from 'components/common/yydDialog';
import queryDiagnoseList from 'components/common/queryDiagnoseList';
import queryDrugList from 'components/common/queryDrugList';
import { alerts, alertss, copyJson, sStore } from 'js/yydjs';
import { getDictionaries, getTemplateRecipeContent, checkTemplateName, saveOrUpdateTemplateRecipe, deleteTemplateRecipe } from 'services';

export default {
    data() {
        return {
            drugUsePathways_gd: [],
            drugUsePathways_gdJson: {},
            drugUsingRate_gd: [],
            drugUsingRate_gdJson: {},
            piecesChineseMedicineMethod: [],
            piecesChineseMedicineMethodJson: {},
            agentNumbPerDay: [],
            agentNumbPerDayJson: {},
            drugUsePathwaysCodeChina: [],
            drugUsePathwaysCodeChinaJson: {},
            drugTakeDemand: [],
            drugTakeDemandJson: {},
            frequencyJson: {},
            recipe_remark: [],
            recipe_remarkJson: {},
            expansionArr: [],
            tabIndex: 0,
            templateIndex: 0,
            detail: {
                templateRecipe: {},
                drugs: [],
            },
            params1: [
                2,
            ],
            oldTemplateName: '',
            hasHint: false,
            currentHintIndex: -1,
            componentIndex: 0,
            diagnoseList: [],
            onOff: true,
        }
    },

    /*
        <prescriptionEditDialog
            v-if="showPrescriptionEditDialog"
            :parent="this"
            :show="true"
            :showName="'showPrescriptionEditDialog'"
            :templateId="templateId"
        />
    */

    props: {
        parent: { //父组件的this
            type: Object,
            default: null,
            required: true,
        },
        show: { //是否显示组件
            type: Boolean,
            default: false,
        },
        showName: { //控制显示组件的名称
            type: String,
            default: '',
        },
        templateId: { //模板id
            type: Number,
            default: -1,
        },
    },

    mounted() {
        //获取字典
        this.getDictionariesFn();

        //获取模板详情
        if (this.templateId == -1) {
            this.initTemplateDetail()
        } else {
            this.getTemplateDetail()
        }
    },

    methods: {
        getDictionariesFn() {
            getDictionaries((res) => {
                console.log('字典', res);
                for (let attr in res) {
                    this[attr] = res[attr];
                }
            });
        },
        getDrugUseDose($event, index1, item1) {
            item1.drugUseDose = item1.drugUseDose.replace(/[^\d.]+/g, '')
            item1.drugSendNumber = ''
            item1.drugUseDays = ''
            this.$set(this.detail.drugs, index1, item1)
        },
        getDrugUsingRate($event, index1, item1) {
            item1.drugSendNumber = ''
            item1.drugUseDays = ''
            this.$set(this.detail.drugs, index1, item1)
        },
        getDrugUseDays($event, index1, item1) {
            item1.drugUseDays = item1.drugUseDays.replace(/[^\d.]+/g, '')
            this.$set(this.detail.drugs, index1, item1)
            this.computedDrugSendNumber(index1, item1)
        },
        getDrugSendNumber($event, index1, item1) {
            item1.drugSendNumber = item1.drugSendNumber.replace(/[^\d.]+/g, '')
            this.$set(this.detail.drugs, index1, item1)
            this.computedDrugUseDays(index1, item1)
        },
        computedDrugSendNumber(index1, item1) {
            let { drugUsingRate, drugUseDose, drugUseDays, drugUseDoseTotal } = item1
            if (drugUsingRate && drugUseDose && drugUseDays && drugUseDoseTotal) {
                let frequency = this.frequencyJson[drugUsingRate]
                item1.drugSendNumber = Math.ceil((drugUseDose * frequency * drugUseDays) / drugUseDoseTotal)
            }
            this.$set(this.detail.drugs, index1, item1)
        },
        computedDrugUseDays(index1, item1) {
            let { drugUsingRate, drugUseDose, drugSendNumber, drugUseDoseTotal } = item1
            if (drugUsingRate && drugUseDose && drugSendNumber && drugUseDoseTotal) {
                let frequency = this.frequencyJson[drugUsingRate]
                item1.drugUseDays = Math.floor((drugUseDoseTotal * drugSendNumber) / (drugUseDose * frequency))
            }
            this.$set(this.detail.drugs, index1, item1)
        },
        renderQueryDrugList() {
            this.onOff = false;
            setTimeout(() => {
                this.onOff = true;
            }, 300);
        },
        focusFn(componentIndex) {
            this.componentIndex = componentIndex;
        },
        autoFocus(index) {
            let oWrap = this.$refs.prescriptionWrap;
            let oTbody = oWrap.getElementsByTagName('tbody')[0];
            let oInput = oTbody.children[index].querySelectorAll('input,select');
            let emptyIndex = -1;

            for (let i = 0; i < oInput.length; i++) {
                if (!oInput[i].value && !oInput[i].getAttribute('noCheck')) {
                    emptyIndex = i;
                    break;
                }
            }

            if (~emptyIndex) {
                oInput[emptyIndex].focus();
            }
            return ~emptyIndex;
        },
        changeDiagnoseList(list, item, index) {
            console.log(list);
            this.diagnoseList = list;
        },
        closeFn() {
            let { parent, showName } = this;

            if (parent && showName && parent[showName]) {
                parent[showName] = false;
            }
        },
        checkDrugList() {
            let drugs = this.detail.drugs;
            let pass = true;
            let keyArr = ['drugName', 'drugUsePathwaysCode', 'drugUseDose', 'drugUsingRate', 'drugUseDays', 'drugSendNumber'];

            if (drugs.length) {
                for (let item of drugs) {
                    for (let key of keyArr) {
                        if (!item[key] && item[key] !== 0) {
                            pass = false;
                            break;
                        }
                    }
                }
            } else {
                pass = false;
            }

            return pass;
        },
        getTemplateDetail() {
            getTemplateRecipeContent([this.templateId], (res) => {
                if (res.body) {
                    let { templateRecipe = {} } = res.body;
                    let { icdCode: icdCodeList = '', icdName: icdNameList = '', templateName } = templateRecipe;
                    let diagnoseList = [];

                    this.detail = res.body;
                    this.oldTemplateName = templateName;
                    icdCodeList = icdCodeList.split('|');
                    icdNameList = icdNameList.split('|');
                    diagnoseList = icdCodeList.map((item, index) => {
                        let icdCode = icdCodeList[index];
                        let icdName = icdNameList[index];

                        return {
                            icdCode,
                            icdName,
                        };
                    });
                    vm.$emit('queryDiagnoseListSave', diagnoseList);
                }
            });
        },
        initTemplateDetail() {
            this.detail = {
                templateRecipe: {
                    tmpBelongObjType: 2, //模板所属对象类型：1科室、2医生
                    tmpBelongObjId: sStore.get('docId'), //医生或者科室id
                    templateName: '',
                    recipeTypeCode: '21', //处方类别代码：21西药、22成药、23中草药
                    mnemonic: 'fjh', //助记码
                    icdCode: '',
                    icdName: '',
                },
                drugs: [],
            }
            vm.$emit('queryDiagnoseListSave', []);
        },
        saveTemplate() {
            let { detail, diagnoseList, oldTemplateName } = this;
            let { templateRecipe = {} } = detail;
            let { templateName, tmpBelongObjId, chiefComplaint } = templateRecipe;
            let saveFn = () => {
                if (!this.checkDrugList()) {
                    alerts('请输入完整的药品信息，否则无法保存');
                    this.currentHintIndex = 1;
                } else {
                    let arr = [
                        { if: !diagnoseList.length, hint: '请录入搜索结果中的诊断，否则无法保存' },
                    ];

                    alertss(arr, () => {
                        this.$msgbox({
                            title: '',
                            message: '是否保存本次编辑？',
                            showCancelButton: true,
                            showConfirmButton: true,
                            cancelButtonText: '取消',
                            confirmButtonText: '保存',
                        }).then(() => {
                            this.detail.templateRecipe.icdCode = diagnoseList.map(item => item.icdCode).join('|');
                            this.detail.templateRecipe.icdName = diagnoseList.map(item => item.icdName).join('|');

                            delete this.detail.templateRecipe.createDt;
                            delete this.detail.templateRecipe.createUser;
                            delete this.detail.templateRecipe.lastModify;
                            delete this.detail.templateRecipe.lastModifyUser;
                            delete this.detail.templateRecipe.status;

                            saveOrUpdateTemplateRecipe([this.detail], (res) => {
                                this.closeFn();
                                vm.$emit('prescriptionTemplateUpdate');
                                alerts('处方编辑成功', 'success');
                            });
                        });
                    }, (errorIndex) => {
                        this.currentHintIndex = errorIndex;
                    });
                }
            };

            if (!templateName) {
                this.currentHintIndex = -1;
                alerts('请输入模板名称');
            } else {
                if (templateName == oldTemplateName) {
                    saveFn();
                } else {
                    this.params1[1] = tmpBelongObjId;
                    this.params1[2] = templateName;
                    checkTemplateName(this.params1, (res) => {
                        this.hasHint = !res.body;
                        if (this.hasHint) {
                            alerts('已存在相同名称的模板');
                        } else {
                            this.showNameDialog = false;
                            saveFn();
                        }
                    });
                }
            }
        },
        deleteTemplate() {
            let { templateId } = this.detail.templateRecipe;

            this.$msgbox({
                title: '',
                message: '是否删除该处方模板？',
                showCancelButton: true,
                showConfirmButton: true,
                cancelButtonText: '取消',
                confirmButtonText: '确定',
            }).then(() => {
                deleteTemplateRecipe([templateId], (res) => {
                    this.closeFn();
                    vm.$emit('prescriptionTemplateUpdate');
                    alerts('处方模板删除成功', 'success');
                });
            });
        },
        changeDrug(componentIndex, item, index, clearKeyWord) {
            let { detail } = this;

            if (detail && detail.drugs) {
                let drugs = copyJson(this.detail.drugs);
                let { drugCodeStandard } = item;
                let same = false;

                for (let item1 of drugs) {
                    let { drugCodeStandard: currentDrugCode } = item1;

                    if (currentDrugCode == drugCodeStandard) {
                        same = true;
                        break;
                    }
                }

                if (!same) {
                    drugs[componentIndex] = item;
                    this.detail.drugs = drugs;
                } else {
                    clearKeyWord();
                    alerts('不能添加相同的药品');
                }
            }
        },
        changeDrugList(componentIndex, item, index, clearKeyWord) {
            this.changeDrug(componentIndex, item, index, clearKeyWord);
            this.autoFocus(componentIndex);
        },
        addDrug() {
            let { detail } = this;

            if (detail && detail.drugs) {
                if (detail.drugs.length >= 5) {
                    alerts('同一处方不允许添加超过5种药品');
                } else {
                    this.detail.drugs.push({});
                    this.renderQueryDrugList();
                }
            }
        },
        deleteDrug(componentIndex) {
            let { detail } = this;

            if (detail && detail.drugs) {
                this.detail.drugs.splice(componentIndex, 1);
                this.renderQueryDrugList();
            }
        },
        enterAdd(componentIndex) {
            if (!this.autoFocus(componentIndex)) {
                this.addDrug();
            }
        },
    },

    components: {
        yydDialog,
        queryDiagnoseList,
        queryDrugList,
    },
}

</script>
<style lang="scss" scoped>
@import '~css/public.scss';

.prescriptionEditDialog {
    .caseHistory {
        @include caseHistory('../../../');
        padding: 30px 0;
        padding-bottom: 0;
        >li>.right>input, /deep/ .queryDiagnoseList, >li>.right .cfcsWrap, >li>.right .clyjWrap {
            width: 570px;
        }
    }
    .prescriptionWrap {
        padding: 0 30px;
        table {
            @include table2('../../../');
            thead {
                tr th:last-of-type {
                    max-width: 130px;
                    min-width: 130px;
                    width: 130px;
                }
            }
        }
    }
    .add {
        display: flex;
        padding: 0 15px;
        height: 40px;
        line-height: 40px;
        background-color: #fafbfd;
        color: $main;
        cursor: pointer;
        i {
            padding-right: 5px;
            font-size: 20px;
        }
    }
    .hintWrap {
        margin-bottom: 30px;
        position: relative;
        @include hintText('../../../');
    }
    .date {
        line-height: 30px;
        color: #666;
    }
    .handleWrap {
        @include handleWrap;
        padding: 40px 0;
    }
}

</style>

<template>
    <div class="prescriptionCheckDialog">
        <yydDialog
            :parent="parent"
            :show="show"
            :showName="showName"
            :width="1000"
            :height="542"
            :title="'处方模板'"
        >
            <div class="wrap">
                <div class="title multiLine-2">
                    <span>
                        <em>模板名称：</em>
                        <b>{{detail.templateRecipe.templateName}}</b>
                    </span>
                    <span>
                        <em>诊断：</em>
                        <b>{{detail.templateRecipe.icdName}}</b>
                    </span>
                </div>
                <div class="main">
                    <table>
                        <thead>
                            <tr>
                                <td v-for="(item, index) in ['药品名称','药品代码','规格','剂型','用药方式','剂量（单位）','频次','天数','总量','单位','备注']" :key="index">
                                    <div class="item">
                                        {{item}}
                                    </div>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in detail.drugs" :key="index">
                                <td>
                                    <div class="item">
                                        {{item.drugName}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugCodeStandard}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugSpecifications}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.pharmaceuticalDosageFormName}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugUsePathwaysCodeText}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugUseDose}}{{item.drugUseDoseUnitText}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugUsingRateText}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugUseDays}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugSendNumber}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.drugPackSpecificationUnitText}}
                                    </div>
                                </td>
                                <td>
                                    <div class="item">
                                        {{item.remark}}
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="end">
                    修改日期：{{detail.templateRecipe.lastModify|date('yyyy.MM.dd')}}
                </div>
            </div>
        </yydDialog>
    </div>
</template>

<script>
    import yydDialog from 'components/common/yydDialog';
    import {getTemplateRecipeContent} from 'services';

    export default{
        data(){
            return{
                tabIndex:0,
                templateIndex:0,
                detail:{
                    templateRecipe:{},
                },
            }
        },

        /*
            <prescriptionCheckDialog
                v-if="showPrescriptionCheckDialog"
                :parent="this"
                :show="true"
                :showName="'showPrescriptionCheckDialog'"
                :templateId="templateId"
            />
        */

        props:{
            parent:{//父组件的this
                type:Object,
                default:null,
                required:true,
            },
            show:{//是否显示组件
                type:Boolean,
                default:false,
            },
            showName:{//控制显示组件的名称
                type:String,
                default:'',
            },
            templateId:{//模板id
                type:Number,
                default:-1,
            },
        },

        mounted(){
            //获取模板详情
            this.getTemplateDetail();
        },

        methods:{
            tabChange(index){
                this.tabIndex=index;
            },
            templateChange(item,index){
                this.templateIndex=index;
            },
            getTemplateDetail(){
                getTemplateRecipeContent([this.templateId],(res)=>{
                    if(res.body){
                        this.detail=res.body;
                    }
                });
            },
        },

        components:{
            yydDialog,
        },
    }
</script>

<style lang="scss" scoped>
    @import '~css/public.scss';

    .prescriptionCheckDialog{
        .wrap{
            padding: 20px 30px;
            .title{
                max-height: 60px;
                line-height: 30px;
                margin-bottom: 20px;
                overflow: hidden;
                span{
                    padding-right: 56px;
                    &:last-of-type{
                        padding-right: 0;
                    }
                    em{
                        color: #666;
                    }
                }
            }
            .main{
                max-height: 340px;
                overflow: hidden;
                overflow-y: auto;
                table{
                    @include table2('../../../');
                }
            }
            .end{
                line-height: 30px;
                color: #666;
            }
        }
    }
</style>
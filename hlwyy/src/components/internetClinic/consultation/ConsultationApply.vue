<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>新建会诊申请</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
        <el-divider content-position="left">选择会诊患者</el-divider>
        <el-form-item label="患者信息" prop="patientId">
          <el-input
            v-model="searchPatientInput"
            placeholder="请输入患者姓名或住院号查询"
            class="input-with-select"
            style="width: 500px"
            @keyup.enter.native="searchPatient"
          >
            <el-button slot="append" icon="el-icon-search" @click="searchPatient"></el-button>
          </el-input>
          
          <!-- 患者信息展示 -->
          <div v-if="form.patientId" class="selected-patient-info">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="姓名">{{ patientInfo.name }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ patientInfo.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
              <el-descriptions-item label="年龄">{{ patientInfo.age }}岁</el-descriptions-item>
              <el-descriptions-item label="住院号">{{ patientInfo.hospitalNo }}</el-descriptions-item>
              <el-descriptions-item label="科室">{{ patientInfo.department }}</el-descriptions-item>
              <el-descriptions-item label="主治医生">{{ patientInfo.primaryDoctor }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 患者搜索结果 -->
          <el-dialog title="选择患者" :visible.sync="patientSelectVisible" width="800px">
            <el-table :data="patientSearchResults" border @row-click="handlePatientSelect">
              <el-table-column prop="name" label="姓名" width="100" />
              <el-table-column prop="gender" label="性别" width="80">
                <template slot-scope="scope">
                  {{ scope.row.gender === 'male' ? '男' : '女' }}
                </template>
              </el-table-column>
              <el-table-column prop="age" label="年龄" width="80" />
              <el-table-column prop="hospitalNo" label="住院号" width="120" />
              <el-table-column prop="department" label="科室" />
              <el-table-column prop="primaryDoctor" label="主治医生" />
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click.stop="handlePatientSelect(scope.row)" type="text" size="small">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-dialog>
        </el-form-item>
        
        <el-divider content-position="left">会诊基本信息</el-divider>
        <el-form-item label="会诊团队" prop="teamId">
          <el-select 
            v-model="form.teamId" 
            placeholder="请选择会诊团队" 
            style="width: 500px"
            @change="handleTeamChange"
          >
            <el-option 
              v-for="team in teamOptions" 
              :key="team.teamId" 
              :label="team.teamName" 
              :value="team.teamId" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="会诊类型" prop="consultationType">
          <el-select v-model="form.consultationType" placeholder="请选择会诊类型" style="width: 500px">
            <el-option label="多学科会诊(MDT)" value="MDT" />
            <el-option label="紧急会诊" value="emergency" />
            <el-option label="常规会诊" value="regular" />
            <el-option label="复诊会诊" value="followup" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="计划开始时间" prop="plannedStartTime">
          <el-date-picker
            v-model="form.plannedStartTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 500px"
          />
        </el-form-item>
        
        <el-form-item label="预计时长(分钟)" prop="plannedDuration">
          <el-input-number v-model="form.plannedDuration" :min="30" :max="180" :step="10" />
        </el-form-item>
        
        <el-form-item label="会诊目的" prop="purpose">
          <el-input 
            v-model="form.purpose" 
            type="textarea" 
            :rows="2"
            placeholder="请简述本次会诊的目的和期望" 
            style="width: 500px"
          />
        </el-form-item>
        
        <el-form-item label="病情摘要" prop="caseSummary">
          <el-input 
            v-model="form.caseSummary" 
            type="textarea" 
            :rows="4"
            placeholder="请简述患者的主要病情和病史" 
            style="width: 500px"
          />
        </el-form-item>
        
        <el-divider content-position="left">参与医生</el-divider>
        
        <el-form-item label="主持医生" prop="hostDoctorId">
          <el-select 
            v-model="form.hostDoctorId" 
            placeholder="请选择主持医生"
            style="width: 500px"
            filterable
          >
            <el-option 
              v-for="doctor in teamDoctors" 
              :key="doctor.id" 
              :label="`${doctor.name} - ${doctor.department} - ${doctor.title}`" 
              :value="doctor.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="邀请医生" prop="invitedDoctorIds">
          <el-select 
            v-model="form.invitedDoctorIds" 
            multiple
            placeholder="请选择邀请的医生"
            style="width: 500px"
            filterable
          >
            <el-option 
              v-for="doctor in teamDoctors" 
              :key="doctor.id" 
              :label="`${doctor.name} - ${doctor.department} - ${doctor.title}`" 
              :value="doctor.id" 
              :disabled="doctor.id === form.hostDoctorId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="添加外部医生">
          <el-button type="primary" icon="el-icon-plus" @click="showAddExternalDoctorDialog">添加外部专家</el-button>
        </el-form-item>
        
        <el-table 
          v-if="externalDoctors.length > 0" 
          :data="externalDoctors" 
          border 
          style="width: 80%; margin-top: 10px; margin-left: 120px;"
        >
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="hospital" label="医院" />
          <el-table-column prop="department" label="科室" />
          <el-table-column prop="title" label="职称" width="100" />
          <el-table-column prop="phone" label="联系方式" width="120" />
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button 
                @click.native.prevent="removeExternalDoctor(scope.$index)" 
                type="text" 
                size="small"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-dialog title="添加外部专家" :visible.sync="externalDoctorDialogVisible" width="600px">
          <el-form :model="externalDoctorForm" label-width="100px" :rules="externalDoctorRules" ref="externalDoctorForm">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="externalDoctorForm.name" placeholder="请输入医生姓名" />
            </el-form-item>
            <el-form-item label="医院" prop="hospital">
              <el-input v-model="externalDoctorForm.hospital" placeholder="请输入所在医院" />
            </el-form-item>
            <el-form-item label="科室" prop="department">
              <el-input v-model="externalDoctorForm.department" placeholder="请输入所在科室" />
            </el-form-item>
            <el-form-item label="职称" prop="title">
              <el-select v-model="externalDoctorForm.title" placeholder="请选择医生职称">
                <el-option label="主任医师" value="主任医师" />
                <el-option label="副主任医师" value="副主任医师" />
                <el-option label="主治医师" value="主治医师" />
                <el-option label="住院医师" value="住院医师" />
              </el-select>
            </el-form-item>
            <el-form-item label="联系方式" prop="phone">
              <el-input v-model="externalDoctorForm.phone" placeholder="请输入联系方式" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="externalDoctorForm.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="externalDoctorDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="addExternalDoctor">确 定</el-button>
          </div>
        </el-dialog>
        
        <el-divider content-position="left">资料上传</el-divider>
        <el-form-item label="上传资料">
          <el-upload
            action="#"
            :http-request="handleFileUpload"
            :file-list="fileList"
            :limit="5"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            multiple
          >
            <el-button type="primary" icon="el-icon-upload">上传患者资料</el-button>
            <div slot="tip" class="el-upload__tip">支持上传检查报告、影像资料等(大小不超过20MB)</div>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交申请</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
// import { searchPatient } from '@/api/modules/patient'
// import { getTeamList, getTeamDoctors } from '@/api/modules/consultation'
// import { createConsultation, uploadConsultationFile } from '@/api/modules/consultation'

export default {
  data() {
    return {
      // 搜索患者输入
      searchPatientInput: '',
      // 患者选择弹窗可见性
      patientSelectVisible: false,
      // 患者搜索结果
      patientSearchResults: [],
      // 外部医生弹窗可见性
      externalDoctorDialogVisible: false,
      
      // 表单数据
      form: {
        patientId: '',
        teamId: '',
        consultationType: '',
        plannedStartTime: '',
        plannedDuration: 60,
        purpose: '',
        caseSummary: '',
        hostDoctorId: '',
        invitedDoctorIds: []
      },
      
      // 表单验证规则
      rules: {
        patientId: [
          { required: true, message: '请选择会诊患者', trigger: 'change' }
        ],
        teamId: [
          { required: true, message: '请选择会诊团队', trigger: 'change' }
        ],
        consultationType: [
          { required: true, message: '请选择会诊类型', trigger: 'change' }
        ],
        plannedStartTime: [
          { required: true, message: '请选择计划开始时间', trigger: 'change' }
        ],
        plannedDuration: [
          { required: true, message: '请设置预计时长', trigger: 'blur' }
        ],
        purpose: [
          { required: true, message: '请输入会诊目的', trigger: 'blur' }
        ],
        caseSummary: [
          { required: true, message: '请输入病情摘要', trigger: 'blur' }
        ],
        hostDoctorId: [
          { required: true, message: '请选择主持医生', trigger: 'change' }
        ]
      },
      
      // 患者信息
      patientInfo: {},
      
      // 团队选项
      teamOptions: [],
      
      // 团队医生列表
      teamDoctors: [],
      
      // 外部医生列表
      externalDoctors: [],
      
      // 外部医生表单
      externalDoctorForm: {
        name: '',
        hospital: '',
        department: '',
        title: '',
        phone: '',
        email: ''
      },
      
      // 外部医生表单验证规则
      externalDoctorRules: {
        name: [
          { required: true, message: '请输入医生姓名', trigger: 'blur' }
        ],
        hospital: [
          { required: true, message: '请输入所在医院', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '请输入所在科室', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请选择医生职称', trigger: 'change' }
        ],
        phone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      
      // 上传文件列表
      fileList: []
    }
  },
  created() {
    this.getTeamOptions()
  },
  methods: {
    // 获取团队选项
    getTeamOptions() {
      // 模拟团队数据
      setTimeout(() => {
        this.teamOptions = [
          { teamId: 'TM001', teamName: '神经内科MDT团队' },
          { teamId: 'TM002', teamName: '心脑血管专家团队' },
          { teamId: 'TM003', teamName: '肿瘤科内部会诊团队' }
        ]
      }, 500)
      
      // 实际接口调用
      // getTeamList().then(res => {
      //   this.teamOptions = res.data.map(team => ({
      //     teamId: team.id,
      //     teamName: team.name
      //   }))
      // })
    },
    
    // 搜索患者
    searchPatient() {
      if (!this.searchPatientInput) {
        this.$message.warning('请输入患者姓名或住院号')
        return
      }
      
      // 模拟患者搜索结果
      setTimeout(() => {
        this.patientSearchResults = [
          { 
            id: 'P001', 
            name: '陈晓', 
            gender: 'male', 
            age: 45, 
            hospitalNo: 'ZY202405001', 
            department: '神经内科', 
            primaryDoctor: '刘明华' 
          },
          { 
            id: 'P002', 
            name: '王雄', 
            gender: 'male', 
            age: 62, 
            hospitalNo: 'ZY202405002', 
            department: '心内科', 
            primaryDoctor: '张志强' 
          },
          { 
            id: 'P003', 
            name: '张雨欣', 
            gender: 'female', 
            age: 38, 
            hospitalNo: 'ZY202405003', 
            department: '内分泌科', 
            primaryDoctor: '赵建国' 
          }
        ].filter(patient => 
          patient.name.includes(this.searchPatientInput) || 
          patient.hospitalNo.includes(this.searchPatientInput)
        )
        
        this.patientSelectVisible = true
      }, 500)
      
      // 实际接口调用
      // searchPatient({ keyword: this.searchPatientInput }).then(res => {
      //   this.patientSearchResults = res.data || []
      //   this.patientSelectVisible = true
      // })
    },
    
    // 处理患者选择
    handlePatientSelect(row) {
      this.form.patientId = row.id
      this.patientInfo = { ...row }
      this.patientSelectVisible = false
    },
    
    // 处理团队变更
    handleTeamChange(teamId) {
      this.form.hostDoctorId = ''
      this.form.invitedDoctorIds = []
      
      // 模拟获取团队医生
      setTimeout(() => {
        if (teamId === 'TM001') {
          this.teamDoctors = [
            { id: 'DOC001', name: '刘明华', department: '神经内科', title: '主任医师' },
            { id: 'DOC002', name: '李京辉', department: '影像科', title: '副主任医师' },
            { id: 'DOC003', name: '王思远', department: '神经外科', title: '主治医师' }
          ]
        } else if (teamId === 'TM002') {
          this.teamDoctors = [
            { id: 'DOC004', name: '赵建国', department: '心内科', title: '主任医师' },
            { id: 'DOC005', name: '钱文彬', department: '心外科', title: '副主任医师' },
            { id: 'DOC006', name: '孙雅静', department: '神经内科', title: '主治医师' },
            { id: 'DOC007', name: '周天浩', department: '放射科', title: '副主任医师' }
          ]
        } else {
          this.teamDoctors = [
            { id: 'DOC008', name: '吴国伟', department: '肿瘤科', title: '主任医师' },
            { id: 'DOC009', name: '郑宇航', department: '肿瘤科', title: '副主任医师' },
            { id: 'DOC010', name: '冯小雨', department: '肿瘤科', title: '主治医师' }
          ]
        }
      }, 500)
      
      // 实际接口调用
      // getTeamDoctors(teamId).then(res => {
      //   this.teamDoctors = res.data || []
      // })
    },
    
    // 显示添加外部医生对话框
    showAddExternalDoctorDialog() {
      this.externalDoctorForm = {
        name: '',
        hospital: '',
        department: '',
        title: '',
        phone: '',
        email: ''
      }
      this.externalDoctorDialogVisible = true
    },
    
    // 添加外部医生
    addExternalDoctor() {
      this.$refs.externalDoctorForm.validate(valid => {
        if (valid) {
          // 添加外部医生到列表
          this.externalDoctors.push({
            id: `EXT${Date.now()}`,
            ...this.externalDoctorForm
          })
          
          this.externalDoctorDialogVisible = false
          this.$message.success('已添加外部专家')
        }
      })
    },
    
    // 移除外部医生
    removeExternalDoctor(index) {
      this.externalDoctors.splice(index, 1)
    },
    
    // 处理文件上传
    handleFileUpload(param) {
      const { file } = param
      
      // 文件大小限制(20MB)
      const isOverSize = file.size / 1024 / 1024 > 20
      if (isOverSize) {
        this.$message.error('文件大小不能超过20MB')
        return
      }
      
      // 模拟上传成功
      setTimeout(() => {
        this.fileList.push({
          name: file.name,
          url: URL.createObjectURL(file)
        })
        this.$message.success('文件上传成功')
      }, 1000)
      
      // 实际接口调用
      // const formData = new FormData()
      // formData.append('file', file)
      // uploadConsultationFile(formData).then(res => {
      //   this.fileList.push({
      //     name: file.name,
      //     url: res.data.url
      //   })
      //   this.$message.success('文件上传成功')
      // }).catch(() => {
      //   this.$message.error('文件上传失败')
      // })
    },
    
    // 处理超出上传限制
    handleExceed() {
      this.$message.warning('最多只能上传5个文件')
    },
    
    // 处理移除文件
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 组装提交数据
          const formData = {
            ...this.form,
            externalDoctors: this.externalDoctors,
            files: this.fileList.map(file => file.url)
          }
          
          // 模拟提交成功
          console.log('提交的数据', formData)
          this.$message.success('会诊申请提交成功！')
          
          // 跳转到会诊列表页
          setTimeout(() => {
            this.$router.push('/internet-clinic/consultation/list')
          }, 1500)
          
          // 实际接口调用
          // createConsultation(formData).then(res => {
          //   this.$message.success('会诊申请提交成功！')
          //   this.$router.push('/internet-clinic/consultation/list')
          // }).catch(err => {
          //   this.$message.error('提交失败：' + err.message)
          // })
        } else {
          this.$message.error('请完善表单信息')
          return false
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
      this.form.plannedDuration = 60
      this.patientInfo = {}
      this.externalDoctors = []
      this.fileList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .selected-patient-info {
    margin-top: 15px;
    margin-bottom: 20px;
  }
  
  .el-divider {
    margin: 20px 0;
  }
  
  .el-upload {
    text-align: left;
  }
  
  .el-upload__tip {
    color: #909399;
    line-height: 1.5;
  }
}
</style> 
<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>会诊团队管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-plus"
          @click="handleCreateTeam"
        >新建团队</el-button>
      </div>
      
      <div class="filter-container">
        <el-form :inline="true" :model="queryParams" size="small">
          <el-form-item label="团队名称">
            <el-input
              v-model="queryParams.teamName"
              placeholder="输入团队名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.status" placeholder="全部状态">
              <el-option label="全部" value="" />
              <el-option label="启用" value="active" />
              <el-option label="禁用" value="inactive" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table
        v-loading="loading"
        :data="teamList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="团队ID" width="100" />
        <el-table-column prop="name" label="团队名称" width="160" />
        <el-table-column prop="description" label="团队描述" min-width="200" />
        <el-table-column label="团队负责人" width="150">
          <template slot-scope="scope">
            <div>{{ scope.row.leaderName }}</div>
            <div class="info-secondary">{{ scope.row.leaderDepartment }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="memberCount" label="成员数量" width="90" align="center" />
        <el-table-column label="团队类型" width="120">
          <template slot-scope="scope">
            {{ formatTeamType(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'active'"
              :inactive-value="'inactive'"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="150">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit"
                @click="handleEditTeam(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="info"
                icon="el-icon-user"
                @click="handleManageMembers(scope.row)"
              >成员</el-button>
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="handleDeleteTeam(scope.row)"
              >删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page.sync="queryParams.pageNum"
          :page-size.sync="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 团队编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="teamDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="teamForm"
        :model="teamForm"
        :rules="teamRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="团队名称" prop="name">
          <el-input v-model="teamForm.name" placeholder="请输入团队名称" />
        </el-form-item>
        <el-form-item label="团队类型" prop="type">
          <el-select v-model="teamForm.type" placeholder="请选择团队类型" style="width: 100%">
            <el-option label="多学科团队(MDT)" value="MDT" />
            <el-option label="专家会诊团队" value="expert" />
            <el-option label="学科内部团队" value="department" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="团队负责人" prop="leaderId">
          <el-select
            v-model="teamForm.leaderId"
            placeholder="请选择团队负责人"
            filterable
            remote
            :remote-method="searchDoctors"
            style="width: 100%"
          >
            <el-option
              v-for="doctor in doctorOptions"
              :key="doctor.id"
              :label="`${doctor.name} - ${doctor.department}`"
              :value="doctor.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="团队描述" prop="description">
          <el-input
            v-model="teamForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入团队描述"
          />
        </el-form-item>
        <el-form-item label="团队状态">
          <el-radio-group v-model="teamForm.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="teamDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitTeamForm">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 团队成员管理对话框 -->
    <el-dialog
      title="团队成员管理"
      :visible.sync="memberDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentTeam" class="team-info">
        <div class="team-name">{{ currentTeam.name }}</div>
        <div class="team-description">{{ currentTeam.description }}</div>
      </div>
      
      <div class="member-search">
        <el-input
          v-model="memberSearch"
          placeholder="搜索医生（姓名/科室）"
          style="width: 250px; margin-right: 10px;"
        >
          <el-button slot="append" icon="el-icon-search" @click="searchDoctorsForMember"/>
        </el-input>
        <el-button type="primary" icon="el-icon-plus" @click="showAddMemberForm">添加成员</el-button>
      </div>
      
      <el-table
        v-loading="memberLoading"
        :data="memberList"
        border
        style="width: 100%; margin-top: 15px;"
      >
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="department" label="科室" width="120" />
        <el-table-column prop="title" label="职称" width="100" />
        <el-table-column prop="specialties" label="专长" min-width="200" />
        <el-table-column label="角色" width="100">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.isLeader">负责人</el-tag>
            <el-tag v-else>成员</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              :disabled="scope.row.isLeader"
              @click="handleRemoveMember(scope.row)"
            >移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="memberDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 添加成员表单对话框 -->
    <el-dialog
      title="添加团队成员"
      :visible.sync="addMemberDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form ref="memberForm" :model="memberForm" label-width="80px">
        <el-form-item label="搜索">
          <el-input
            v-model="doctorSearchKeyword"
            placeholder="输入姓名或科室搜索医生"
            @input="handleDoctorSearch"
          />
        </el-form-item>
        <el-form-item label="医生列表" v-if="searchedDoctors.length > 0">
          <el-table
            :data="searchedDoctors"
            border
            style="width: 100%"
            height="300px"
            @row-click="handleSelectDoctor"
          >
            <el-table-column prop="name" label="姓名" width="100" />
            <el-table-column prop="department" label="科室" width="120" />
            <el-table-column prop="title" label="职称" width="100" />
            <el-table-column prop="specialties" label="专长" min-width="200" />
            <el-table-column width="80">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click.stop="handleSelectDoctor(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="已选医生" v-if="memberForm.doctorId">
          <div class="selected-doctor">
            <span>{{ memberForm.doctorName }} - {{ memberForm.doctorDepartment }}</span>
            <el-button 
              size="mini" 
              type="danger" 
              icon="el-icon-delete"
              @click="clearSelectedDoctor"
            ></el-button>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addMemberDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitMemberForm" :disabled="!memberForm.doctorId">添 加</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import { getTeamList, createTeam, updateTeam, deleteTeam, changeTeamStatus } from '@/api/modules/consultation'
// import { getTeamMembers, addTeamMember, removeTeamMember } from '@/api/modules/consultation'
// import { searchDoctorList } from '@/api/modules/doctor'
// import { parseTime } from '@/utils'

export default {
  name: 'TeamManagement',
  data() {
    return {
      // 列表加载状态
      loading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teamName: '',
        status: ''
      },
      // 团队列表数据
      teamList: [],
      // 总记录数
      total: 0,
      
      // 对话框显示控制
      teamDialogVisible: false,
      memberDialogVisible: false,
      addMemberDialogVisible: false,
      
      // 对话框标题
      dialogTitle: '',
      // 当前操作的团队
      currentTeam: null,
      
      // 团队表单
      teamForm: {
        id: null,
        name: '',
        type: '',
        leaderId: '',
        description: '',
        status: 'active'
      },
      // 表单校验规则
      teamRules: {
        name: [
          { required: true, message: '请输入团队名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择团队类型', trigger: 'change' }
        ],
        leaderId: [
          { required: true, message: '请选择团队负责人', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入团队描述', trigger: 'blur' },
          { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
        ]
      },
      
      // 医生选项列表
      doctorOptions: [],
      
      // 成员管理相关
      memberLoading: false,
      memberList: [],
      memberSearch: '',
      
      // 添加成员表单
      memberForm: {
        teamId: null,
        doctorId: '',
        doctorName: '',
        doctorDepartment: ''
      },
      
      // 医生搜索
      doctorSearchKeyword: '',
      searchedDoctors: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true
      
      // 模拟数据，实际项目中应替换为API调用
      setTimeout(() => {
        this.teamList = [
          {
            id: 'TM001',
            name: '神经内科MDT团队',
            description: '专注于复杂神经系统疾病的多学科诊疗团队',
            leaderId: 'DOC001',
            leaderName: '陈明宇',
            leaderDepartment: '神经内科',
            memberCount: 8,
            type: 'MDT',
            status: 'active',
            createTime: '2024-04-15 10:30:00'
          },
          {
            id: 'TM002',
            name: '心脑血管专家团队',
            description: '由心内科、神经内科、放射科等多学科专家组成的会诊团队',
            leaderId: 'DOC010',
            leaderName: '张伟',
            leaderDepartment: '心内科',
            memberCount: 12,
            type: 'expert',
            status: 'active',
            createTime: '2024-04-10 14:20:00'
          },
          {
            id: 'TM003',
            name: '肿瘤科内部会诊团队',
            description: '肿瘤科室内部疑难病例讨论团队',
            leaderId: 'DOC015',
            leaderName: '刘伟',
            leaderDepartment: '肿瘤科',
            memberCount: 6,
            type: 'department',
            status: 'inactive',
            createTime: '2024-03-20 09:15:00'
          }
        ]
        this.total = this.teamList.length
        this.loading = false
      }, 500)
      
      // 实际接口调用
      // getTeamList(this.queryParams).then(res => {
      //   this.teamList = res.data.list
      //   this.total = res.data.total
      //   this.loading = false
      // }).catch(() => {
      //   this.loading = false
      // })
    },
    
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    
    // 重置查询条件
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        teamName: '',
        status: ''
      }
      this.getList()
    },
    
    // 每页条数变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size
      this.getList()
    },
    
    // 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page
      this.getList()
    },
    
    // 格式化团队类型
    formatTeamType(type) {
      const typeMap = {
        MDT: '多学科团队',
        expert: '专家会诊团队',
        department: '学科内部团队',
        other: '其他'
      }
      return typeMap[type] || type
    },
    
    // 格式化日期时间
    formatDateTime(time) {
      // 实际项目中可使用parseTime等工具函数
      return time || '-'
    },
    
    // 处理创建团队
    handleCreateTeam() {
      this.dialogTitle = '新建团队'
      this.teamForm = {
        id: null,
        name: '',
        type: '',
        leaderId: '',
        description: '',
        status: 'active'
      }
      this.teamDialogVisible = true
    },
    
    // 处理编辑团队
    handleEditTeam(row) {
      this.dialogTitle = '编辑团队'
      this.teamForm = {
        id: row.id,
        name: row.name,
        type: row.type,
        leaderId: row.leaderId,
        description: row.description,
        status: row.status
      }
      this.teamDialogVisible = true
    },
    
    // 提交团队表单
    submitTeamForm() {
      this.$refs.teamForm.validate(valid => {
        if (valid) {
          if (this.teamForm.id) {
            // 更新团队
            console.log('更新团队', this.teamForm)
            // 实际API调用
            // updateTeam(this.teamForm).then(res => {
            //   this.$message.success('更新团队成功')
            //   this.teamDialogVisible = false
            //   this.getList()
            // })
            
            // 模拟调用成功
            this.$message.success('更新团队成功')
            this.teamDialogVisible = false
            this.getList()
          } else {
            // 创建团队
            console.log('创建团队', this.teamForm)
            // 实际API调用
            // createTeam(this.teamForm).then(res => {
            //   this.$message.success('创建团队成功')
            //   this.teamDialogVisible = false
            //   this.getList()
            // })
            
            // 模拟调用成功
            this.$message.success('创建团队成功')
            this.teamDialogVisible = false
            this.getList()
          }
        }
      })
    },
    
    // 处理删除团队
    handleDeleteTeam(row) {
      this.$confirm(`确认删除团队"${row.name}"吗？删除后无法恢复`, '提示', {
        type: 'warning'
      }).then(() => {
        // 实际API调用
        // deleteTeam(row.id).then(() => {
        //   this.$message.success('删除成功')
        //   this.getList()
        // })
        
        // 模拟调用成功
        this.$message.success('删除成功')
        this.getList()
      }).catch(() => {})
    },
    
    // 处理团队状态变更
    handleStatusChange(row) {
      const statusText = row.status === 'active' ? '启用' : '禁用'
      this.$message.success(`已${statusText}团队: ${row.name}`)
      
      // 实际API调用
      // changeTeamStatus({
      //   id: row.id,
      //   status: row.status
      // }).then(() => {
      //   this.$message.success(`已${statusText}团队: ${row.name}`)
      // })
    },
    
    // 搜索医生
    searchDoctors(query) {
      if (query) {
        // 实际API调用
        // searchDoctorList({ keyword: query }).then(res => {
        //   this.doctorOptions = res.data || []
        // })
        
        // 模拟数据
        setTimeout(() => {
          this.doctorOptions = [
            { id: 'DOC001', name: '陈明宇', department: '神经内科' },
            { id: 'DOC002', name: '王俊杰', department: '影像科' },
            { id: 'DOC003', name: '张伟', department: '心内科' }
          ].filter(item => 
            item.name.includes(query) || 
            item.department.includes(query)
          )
        }, 300)
      } else {
        this.doctorOptions = []
      }
    },
    
    // 处理管理成员
    handleManageMembers(row) {
      this.currentTeam = row
      this.memberDialogVisible = true
      this.getMemberList(row.id)
    },
    
    // 获取团队成员列表
    getMemberList(teamId) {
      this.memberLoading = true
      
      // 实际API调用
      // getTeamMembers(teamId).then(res => {
      //   this.memberList = res.data || []
      //   this.memberLoading = false
      // }).catch(() => {
      //   this.memberLoading = false
      // })
      
      // 模拟数据
      setTimeout(() => {
        this.memberList = [
          {
            id: 'MEM001',
            name: '陈明宇',
            department: '神经内科',
            title: '主任医师',
            specialties: '癫痫、帕金森病、神经退行性疾病',
            isLeader: true
          },
          {
            id: 'MEM002',
            name: '王俊杰',
            department: '影像科',
            title: '副主任医师',
            specialties: '神经影像学、MRI诊断',
            isLeader: false
          },
          {
            id: 'MEM003',
            name: '刘伟',
            department: '神经内科',
            title: '主任医师',
            specialties: '脑血管疾病、癫痫外科治疗',
            isLeader: false
          }
        ]
        this.memberLoading = false
      }, 500)
    },
    
    // 显示添加成员表单
    showAddMemberForm() {
      this.memberForm = {
        teamId: this.currentTeam.id,
        doctorId: '',
        doctorName: '',
        doctorDepartment: ''
      }
      this.doctorSearchKeyword = ''
      this.searchedDoctors = []
      this.addMemberDialogVisible = true
    },
    
    // 搜索医生(用于添加成员)
    handleDoctorSearch() {
      if (this.doctorSearchKeyword) {
        // 实际API调用
        // searchDoctorList({ keyword: this.doctorSearchKeyword }).then(res => {
        //   this.searchedDoctors = res.data || []
        // })
        
        // 模拟数据
        setTimeout(() => {
          this.searchedDoctors = [
            { 
              id: 'DOC005', 
              name: '李伟', 
              department: '神经内科',
              title: '副主任医师',
              specialties: '帕金森病、运动障碍'
            },
            { 
              id: 'DOC006', 
              name: '赵云', 
              department: '神经心理科',
              title: '主治医师',
              specialties: '失语症、认知障碍'
            },
            { 
              id: 'DOC007', 
              name: '钱文', 
              department: '康复科',
              title: '主治医师',
              specialties: '神经康复、中风后康复'
            }
          ].filter(item => 
            item.name.includes(this.doctorSearchKeyword) || 
            item.department.includes(this.doctorSearchKeyword) ||
            (item.specialties && item.specialties.includes(this.doctorSearchKeyword))
          )
        }, 300)
      } else {
        this.searchedDoctors = []
      }
    },
    
    // 处理选择医生
    handleSelectDoctor(row) {
      this.memberForm.doctorId = row.id
      this.memberForm.doctorName = row.name
      this.memberForm.doctorDepartment = row.department
    },
    
    // 清除已选医生
    clearSelectedDoctor() {
      this.memberForm.doctorId = ''
      this.memberForm.doctorName = ''
      this.memberForm.doctorDepartment = ''
    },
    
    // 为团队搜索医生
    searchDoctorsForMember() {
      this.getMemberList(this.currentTeam.id)
    },
    
    // 提交成员表单
    submitMemberForm() {
      if (!this.memberForm.doctorId) {
        this.$message.warning('请选择要添加的医生')
        return
      }
      
      // 实际API调用
      // addTeamMember(this.memberForm).then(res => {
      //   this.$message.success('添加成员成功')
      //   this.addMemberDialogVisible = false
      //   this.getMemberList(this.currentTeam.id)
      // })
      
      // 模拟调用成功
      this.$message.success('添加成员成功')
      this.addMemberDialogVisible = false
      this.getMemberList(this.currentTeam.id)
    },
    
    // 处理移除成员
    handleRemoveMember(row) {
      this.$confirm(`确认从团队中移除"${row.name}"吗？`, '提示', {
        type: 'warning'
      }).then(() => {
        // 实际API调用
        // removeTeamMember({
        //   teamId: this.currentTeam.id,
        //   memberId: row.id
        // }).then(() => {
        //   this.$message.success('移除成功')
        //   this.getMemberList(this.currentTeam.id)
        // })
        
        // 模拟调用成功
        this.$message.success('移除成功')
        this.getMemberList(this.currentTeam.id)
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    margin-bottom: 15px;
  }
  
  .info-secondary {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
  }
  
  .operation-buttons {
    .el-button {
      margin: 2px;
    }
  }
  
  .pagination-container {
    margin-top: 15px;
    text-align: right;
  }
  
  .team-info {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #EBEEF5;
    
    .team-name {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .team-description {
      color: #606266;
      font-size: 14px;
    }
  }
  
  .member-search {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .selected-doctor {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
  }
}
</style> 
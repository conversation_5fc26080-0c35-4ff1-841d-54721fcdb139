<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>我的会诊列表</span>
      </div>
      
      <div class="filter-container">
        <el-form :inline="true" :model="queryParams" size="small">
          <el-form-item label="会诊状态">
            <el-select v-model="queryParams.status" placeholder="全部状态">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="pending" />
              <el-option label="已批准" value="approved" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item label="会诊类型">
            <el-select v-model="queryParams.type" placeholder="全部类型">
              <el-option label="全部" value="" />
              <el-option label="多学科会诊(MDT)" value="MDT" />
              <el-option label="紧急会诊" value="emergency" />
              <el-option label="常规会诊" value="regular" />
              <el-option label="复诊会诊" value="followup" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="handleDateRangeChange"
            />
          </el-form-item>
          <el-form-item label="关键词">
            <el-input
              v-model="queryParams.keyword"
              placeholder="患者姓名/会诊主题"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="我发起的会诊" name="initiated" />
        <el-tab-pane label="我参与的会诊" name="participated" />
      </el-tabs>
      
      <el-table
        v-loading="loading"
        :data="consultationList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="会诊ID" width="80" />
        <el-table-column label="患者信息" width="160">
          <template slot-scope="scope">
            <div>{{ scope.row.patientName }}</div>
            <div class="info-secondary">{{ scope.row.patientGender === 'male' ? '男' : '女' }} | {{ scope.row.patientAge }}岁</div>
            <div class="info-secondary">{{ scope.row.patientNo }}</div>
          </template>
        </el-table-column>
        <el-table-column label="会诊信息" width="180">
          <template slot-scope="scope">
            <div>{{ scope.row.title || '会诊-' + scope.row.id }}</div>
            <div class="info-secondary">类型: {{ formatConsultationType(scope.row.type) }}</div>
            <div class="info-secondary">{{ scope.row.department }}</div>
          </template>
        </el-table-column>
        <el-table-column label="主持人" width="120">
          <template slot-scope="scope">
            <div>{{ scope.row.hostName }}</div>
            <div class="info-secondary">{{ scope.row.hostDepartment }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="plannedStartTime" label="计划开始时间" width="150">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.plannedStartTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="plannedDuration" label="预计时长" width="100">
          <template slot-scope="scope">
            {{ scope.row.plannedDuration }}分钟
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              type="primary" 
              @click="viewDetail(scope.row)"
            >
              详情
            </el-button>
            
            <el-button 
              v-if="scope.row.status === 'approved' || scope.row.status === 'in_progress'"
              size="mini" 
              type="success" 
              @click="joinMeeting(scope.row)"
            >
              进入会议
            </el-button>
            
            <el-button 
              v-if="scope.row.status === 'pending' && isInitiator(scope.row)"
              size="mini" 
              type="danger" 
              @click="cancelConsultation(scope.row)"
            >
              取消申请
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page.sync="queryParams.pageNum"
          :page-size.sync="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
// import { getMyConsultationList, cancelConsultation } from '@/api/modules/consultation'
// import { parseTime } from '@/utils'

export default {
  data() {
    return {
      loading: false,
      activeTab: 'initiated', // 默认显示"我发起的会诊"
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: '',
        type: '',
        startTime: '',
        endTime: '',
        keyword: '',
        role: 'initiator' // 默认查询我发起的会诊
      },
      total: 0,
      consultationList: [],
      // 登录用户信息
      currentUser: {
        id: 'DOC001',
        name: '陈明宇',
        department: '神经内科'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true
      
      // 模拟数据
      setTimeout(() => {
        this.consultationList = [
          {
            id: 'CSL2024050001',
            patientName: '李华',
            patientGender: 'male',
            patientAge: 45,
            patientNo: 'MR202405001',
            title: '多发性硬化诊断会诊',
            type: 'MDT',
            department: '神经内科',
            hostId: 'DOC001',
            hostName: '陈明宇',
            hostDepartment: '神经内科',
            status: 'approved',
            plannedStartTime: '2024-05-15 09:00:00',
            plannedDuration: 60,
            createTime: '2024-05-01 10:30:45'
          },
          {
            id: 'CSL2024050002',
            patientName: '王雄',
            patientGender: 'male',
            patientAge: 62,
            patientNo: 'MR202405002',
            title: '冠心病疑难病例讨论',
            type: 'regular',
            department: '心内科',
            hostId: 'DOC001',
            hostName: '陈明宇',
            hostDepartment: '神经内科',
            status: 'approved',
            plannedStartTime: '2024-05-10 14:30:00',
            plannedDuration: 45,
            createTime: '2024-05-02 15:20:10'
          },
          {
            id: 'CSL2024050003',
            patientName: '张雨欣',
            patientGender: 'female',
            patientAge: 38,
            patientNo: 'MR202405003',
            title: '糖尿病并发症治疗会诊',
            type: 'followup',
            department: '内分泌科',
            hostId: 'DOC010',
            hostName: '刘伟',
            hostDepartment: '神经内科',
            status: 'in_progress',
            plannedStartTime: '2024-05-08 10:00:00',
            plannedDuration: 60,
            createTime: '2024-05-03 08:45:22'
          }
        ]
        this.total = this.consultationList.length
        this.loading = false
      }, 500)
      
      // 实际接口调用
      // getMyConsultationList(this.queryParams).then(res => {
      //   this.consultationList = res.data.list || []
      //   this.total = res.data.total
      //   this.loading = false
      // }).catch(() => {
      //   this.loading = false
      // })
    },
    // 处理日期范围变化
    handleDateRangeChange(dates) {
      if (dates) {
        this.queryParams.startTime = dates[0]
        this.queryParams.endTime = dates[1]
      } else {
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
    },
    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置查询条件
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        status: '',
        type: '',
        startTime: '',
        endTime: '',
        keyword: '',
        role: this.activeTab === 'initiated' ? 'initiator' : 'participant'
      }
      this.getList()
    },
    // 处理标签页切换
    handleTabClick() {
      this.queryParams.role = this.activeTab === 'initiated' ? 'initiator' : 'participant'
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 每页条数变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size
      this.getList()
    },
    // 当前页变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page
      this.getList()
    },
    // 格式化会诊类型
    formatConsultationType(type) {
      const typeMap = {
        MDT: '多学科会诊',
        emergency: '紧急会诊',
        regular: '常规会诊',
        followup: '复诊会诊'
      }
      return typeMap[type] || type
    },
    // 格式化会诊状态
    formatStatus(status) {
      const statusMap = {
        pending: '待审核',
        approved: '已批准',
        rejected: '已拒绝',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    },
    // 获取状态对应的标签类型
    getStatusTagType(status) {
      const statusTagMap = {
        pending: 'info',
        approved: 'success',
        rejected: 'danger',
        in_progress: 'warning',
        completed: 'success',
        cancelled: 'info'
      }
      return statusTagMap[status] || 'info'
    },
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      // 实际项目中应使用统一的日期格式化工具
      // return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}')
      return dateTime
    },
    // 查看详情
    viewDetail(row) {
      this.$router.push({
        path: '/internet-clinic/consultation/detail',
        query: { id: row.id }
      })
    },
    // 进入会议
    joinMeeting(row) {
      this.$router.push({
        path: '/internet-clinic/consultation/meeting',
        query: { id: row.id }
      })
    },
    // 判断是否是会诊发起者
    isInitiator(row) {
      return row.hostId === this.currentUser.id
    },
    // 取消会诊申请
    cancelConsultation(row) {
      this.$confirm('确认取消此会诊申请？取消后需要重新提交申请。', '提示', {
        type: 'warning'
      }).then(() => {
        // 模拟取消
        this.$message.success('会诊申请已取消')
        row.status = 'cancelled'
        
        // 实际接口调用
        // cancelConsultation(row.id).then(() => {
        //   this.$message.success('会诊申请已取消')
        //   this.getList()
        // }).catch(() => {
        //   this.$message.error('取消失败')
        // })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    margin-bottom: 15px;
  }
  
  .info-secondary {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
  }
  
  .pagination-container {
    margin-top: 15px;
    text-align: right;
  }
}
</style> 
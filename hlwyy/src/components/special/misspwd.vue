<template>
    <div class="miss-first">
        <div class="steps">
            <el-steps :active="active" finish-status="success" align-center>
                <el-step title="填写登录名"></el-step>
                <el-step title="验证身份"></el-step>
                <el-step title="设置新密码"></el-step>
                <el-step title="完成"></el-step>
            </el-steps>
        </div>
        <div v-if="active == 0" style="text-align:center">
            <div class="account">
                <label>登录名：</label>
                <input class="login_name" spellcheck="false" v-model="loginName" />
            </div>
            <div class="account">
                <label>验证码：</label>
                <input class="login_name login_psd" spellcheck="false" v-model="loginMsgVer" />
                <div style="display:inline-block;width:90px;height:35px;cursor: pointer;" @click="getVerCode">
                    <img :src="verImgUrl" alt="验证码">
                </div>
            </div>
            <div class="login_btn">
                <el-button style="width:200px;height:50px;border-radius:30px;font-size:18px;background: #409eff;color:#fff;" @click="nextStart">下一步</el-button>
            </div>
        </div>
        <div v-if="active == 1" style="text-align:center">
            <div class="account">
                <label>登录名：</label>
                <p class="ver_account" disabled>{{loginName}}</p>
            </div>
            <div class="account">
                <label>已验证手机：</label>
                <p class="ver_account ver_phone" disabled>{{loginName}}</p>
                <el-button type="primary" :disabled="disabled" @click="getMsgVer">{{getMsgText}}</el-button>
            </div>
            <div class="account">
                <label>短信验证码：</label>
                <input class="ver_account" v-model="verCode" spellcheck="false" />
            </div>
            <div class="login_btn">
                <el-button style="width:200px;height:50px;border-radius:30px;font-size:18px;background: #409eff;color:#fff;" @click="next">下一步</el-button>
            </div>
        </div>
        <div v-if="active == 2" style="text-align:center">
            <div class="account">
                <label>设置新密码</label>
                <input class="login_name" spellcheck="false" v-model="pwd" type="password" />
            </div>
            <div class="account">
                <label>确认新密码</label>
                <input class="login_name" spellcheck="false" v-model="newPwd" type="password" />
            </div>
            <div class="login_btn">
                <el-button style="width:200px;height:50px;border-radius:30px;font-size:18px;background: #409eff;color:#fff;" @click="nextEnd">下一步</el-button>
            </div>
        </div>
        <div v-if="active == 4" style="text-align:center">
            <div class="account">
                <div class="ver_account pwd_success">
                    <i class="el-icon-success" style="color:#409eff"></i>
                    密码重置成功
                </div>
            </div>
            <div class="login_btn">
                <el-button style="width:200px;height:50px;border-radius:30px;font-size:18px;background: #409eff;color:#fff;" @click="goLogin">返回登录</el-button>
            </div>
        </div>
    </div>
</template>
<script>
import { commonAjax, imgview } from '../../api/api.js';
import axios from 'axios';
import md5 from 'md5';
export default {
    data() {
        return {
            active: 0,
            verImgUrl: "",
            loginName: "",
            loginMsgVer: "",
            verCon: "",
            verStatus: "",
            getMsgText: "获取验证码",
            disabled: false,
            timer: null,
            count: '',

            verCode: "", //短信验证码

            pwd: "",
            newPwd: "",
            proCode: sessionStorage.getItem('peoCode')
        }
    },
    methods: {
        //获取验证码图片
        getVerCode() {
            let url = location.origin + '/cas-admin/codeImg/getCode';
            axios.get(url).then(res => {
                if (res.status == 200) {
                    this.verImgUrl = imgview + res.data.filedId;
                    this.verCon = res.data.code;
                } else {
                    this.$message({
                        type: "error",
                        message: res.msg,
                    });
                    return;
                }
            })
        },
        //进入短信验证身份阶段
        nextStart() {
            let reg = /^1[34578]\d{9}$/;
            if (!reg.test(this.loginName)) {
                this.$message({
                    type: "warning",
                    message: "请输入正确的登录名",
                });
                return;
            } else if (!this.loginMsgVer) {
                this.$message({
                    type: "warning",
                    message: "请输入验证码",
                });
                return;
            } else {
                if (this.verCon !== this.loginMsgVer) {
                    this.$message({
                        type: "error",
                        message: "验证码错误",
                    });
                    this.getVerCode();
                    return;
                } else {
                    this.active = 1;
                }
            }

        },
        //获取短信验证码
        getMsgVer() {
            const TIME_COUNT = 60;
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.disabled = false;
                this.timer = setInterval(() => {
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                        this.disabled = true;
                        this.getMsgText = `(${this.count}s)重发`;
                    } else {
                        this.disabled = false;
                        this.getMsgText = "获取验证码";
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000)
            }

            commonAjax("cas.appealAdminService", "smsCode", `['${this.proCode}','${this.loginName}']`).then(res => {
                if (res && res.code == 200) {
                    this.$message({
                        type: "success",
                        message: "验证码发送成功",
                    })
                }
            });
        },
        //验证身份进入重置密码阶段
        next() {
            commonAjax("cas.appealAdminService", "validateSmsCode", `["${this.proCode}","${this.loginName}","${this.verCode}"]`).then(res => {
                if (res && res.code == 200) {
                    if (res.body.result == 'success') {
                        this.active = 2;
                    } else {
                        this.$message({
                            type: "error",
                            message: "验证码有误",
                        });
                        return;
                    }
                }
            })

        },
        nextEnd() {
            let reg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,20}$/
            if (!this.pwd) {
                this.$message({
                    type: "warning",
                    message: "请输入新密码",
                });
                return;
            } else if (!reg.test(this.pwd)) {
                this.$message({
                    type: "warning",
                    message: "新密码为8-20位英文字母、数字或符号，至少3种字符",
                });
                return
            } else if (!this.newPwd) {
                this.$message({
                    type: "warning",
                    message: "请确认新密码",
                });
                return;
            } else if (!reg.test(this.newPwd)) {
                this.$message({
                    type: "warning",
                    message: "确认密码为8-20位英文字母、数字或符号，至少3种字符",
                });
                return
            } else if (this.pwd !== this.newPwd) {
                this.$message({
                    type: "warning",
                    message: "两次密码输入不一致，请重新输入",
                });
                this.pwd = "";
                this.newPwd = "";
                return;
            } else {
                let pwd = md5(this.pwd)
                commonAjax("cas.appealAdminService", "setPassword", `["${this.proCode}","${this.loginName}","${pwd}"]`).then(res => {
                    if (res && res.code == 200) {
                        this.active = 4;
                    } else {
                        this.$message({
                            type: "error",
                            message: res.msg,
                        });
                        this.pwd = "";
                        this.newPwd = "";
                        return;
                    }
                })

            }
        },



        goLogin() {
            this.$router.push("/login");
        }
    },
    mounted() {
        this.getVerCode();
    }
}

</script>
<style scoped lang="scss">
.steps {
    margin-top: 100px;
    margin-bottom: 100px;
    /deep/ .el-steps {
        /deep/ .el-step {
            /deep/ .is-success {
                color: #409eff;
                border-color: #409eff;
            }
            /deep/ .is-process {
                color: #ccc;
                border-color: #ccc;
            }
        }
    }
}

// text-align: center;

.account {
    // width: 600px;
    overflow: hidden;
    margin: 0 20%;
    // text-align: center;
    .login_name {
        width: 500px;
        height: 35px;
        border: none;
        border-bottom: 1px solid #ccc;
        outline: none;
        font-size: 18px;
        line-height: 24px;
        margin-top: 50px;

    }
    .login_psd {
        width: 400px;
    }
    label {
        width: 100px;
        text-align: right;
        height: 35px;
        display: inline-block;
    }
    img {
        width: 90px;
        height: 35px;
    }
}
.login_btn {
    margin-top: 50px;
    text-align: center;
}
.ver_account {
    width: 500px;
    height: 35px;
    border: none;
    border-bottom: 1px solid #ccc;
    text-align: left;
    outline: none;
    font-size: 18px;
    line-height: 24px;
    margin-top: 50px;
    display: inline-block;
}
.ver_phone {
    width: 400px;
}
.pwd_success {
    text-align: center;
    border: none;
    height: 50px;
    line-height: 50px;
}

</style>

<template>
  <div class="main">
    <top-header></top-header>
    <div class="container">
      <sidebar></sidebar>
      <div class="content">
        <transition>
            <router-view class="router"></router-view>
        </transition>
      </div>
    </div>
    <div class="messageWrap">
        <newMsgList/>
        <todoList/>
    </div>
  </div>
</template>

<script>
    import nimInit from 'components/NIM/nimInit';
    import SideBar from 'components/common/Sidebar.vue';
    import topHeader from 'components/common/Header.vue';
    import newMsgList from 'components/common/newMsgList';
    import todoList from 'components/common/todoList';

    export default {
        name: "Home",
        components: {
            sidebar: SideBar,
            topHeader: topHeader,
            newMsgList,
            todoList,
        },
        data() {
            return {
                isCollapse: false,
                romMsg:{},
            }

        },
        methods:{

        },
        mounted() {
            //初始化nim
            nimInit();
        }
    };
</script>

<style scoped lang="scss">
    @import "../../assets/css/var.scss";

    .main {
        width: 100%;
        height: 100%;
        .container {
            display: flex;
            padding-top: 60px;
            width: 100%;
            height: 100%;
            background: $background_main;
        }
        .messageWrap{
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 1900;
        }

        .content {
            flex: 1;
            width: 100%;
            min-height: 100%;
            height: 100%;
            background: $background_main;
            border: 10px solid transparent;
            overflow-y: auto;
            position: relative;
        }

        .content::-webkit-scrollbar {
            width: 4px;
            background-color: #f5f5f5;
        }

        & /deep/ .el-menu {
            background: transparent;
            overflow: hidden;
            border: 0;
            .el-menu-item {
                color: #575c61;
            }
            .el-menu-item-group__title {
                color: #409eff;
            }
            .el-submenu__title {
                color: #333;
            }
            .el-menu-item:focus,
            .el-menu-item:hover {
                outline: 0;
                overflow: hidden;
                //   background-color: rgb(48, 65, 86);
                color: #409eff;
            }
            .el-submenu__title:hover {
                outline: 0;
                overflow: hidden;
                background-color: rgb(215, 243, 242);
            }
        }
    }


    .sidebar::-webkit-scrollbar {
        width: 4px;
        background-color: #f5f5f5;
    }
    .content::-webkit-scrollbar-thumb {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
        // background-color: #3e8dbb;
    }

    .min {
        display: none;
    }

    .router {
        border-radius: 5px;
    }
</style>

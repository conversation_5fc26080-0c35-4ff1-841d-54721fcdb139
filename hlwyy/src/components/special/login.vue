<template>
    <div class="login-wrap">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
            <div class="decoration-line line-1"></div>
            <div class="decoration-line line-2"></div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="login-container">
            <!-- 左侧插图区域 -->
            <div class="illustration-section">
                <div class="illustration-content">
                    <div class="medical-illustration">
                        <div class="doctor doctor-1">
                            <div class="doctor-avatar"></div>
                            <div class="doctor-body"></div>
                            <div class="stethoscope"></div>
                            <div class="clipboard"></div>
                        </div>
                        <div class="doctor doctor-2">
                            <div class="doctor-avatar"></div>
                            <div class="doctor-body"></div>
                            <div class="stethoscope"></div>
                            <div class="clipboard"></div>
                        </div>
                        <div class="doctor doctor-3">
                            <div class="doctor-avatar"></div>
                            <div class="doctor-body"></div>
                            <div class="stethoscope"></div>
                            <div class="clipboard"></div>
                        </div>
                    </div>
                    <div class="illustration-text">
                        <h2>智慧医疗 · 关爱健康</h2>
                        <p>基于互联网的现代化医疗管理系统</p>
                    </div>
                </div>
            </div>
            
            <!-- 右侧登录表单区域 -->
            <div class="login-form-section">
                <div class="login-card">
                    <div class="login-header">
                        <div class="logo-container">
                            <div class="logo-icon">
                                <i class="el-icon-first-aid-kit"></i>
                            </div>
                        </div>
                        <h1 class="login-title">基于互联网的养老院系统</h1>
                        <p class="login-subtitle">欢迎使用智慧医疗管理平台</p>
                    </div>
                    
                    <div class="login-form">
                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="input-icon el-icon-user"></i>
                                <input 
                                    class="form-input" 
                                    v-model="ruleForm.userName" 
                                    placeholder="请输入账号" 
                                    v-on:keyup.enter="submitForm"
                                    :class="{ 'has-value': ruleForm.userName }"
                                />
                                <div class="input-line"></div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="input-wrapper">
                                <i class="input-icon el-icon-lock"></i>
                                <input 
                                    class="form-input" 
                                    type="password" 
                                    v-model="ruleForm.passWord" 
                                    placeholder="请输入密码" 
                                    v-on:keyup.enter="submitForm"
                                    :class="{ 'has-value': ruleForm.passWord }"
                                />
                                <div class="input-line"></div>
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <el-checkbox v-model="checked" class="remember-checkbox">
                                <span class="checkbox-text">记住密码</span>
                            </el-checkbox>
                            <el-button type='text' class="forgot-link" @click="misspwd">
                                忘记密码?
                            </el-button>
                        </div>
                        
                        <div class="form-submit">
                            <el-button 
                                class="login-button" 
                                @click="submitForm" 
                                type="primary"
                                :loading="loading"
                                :disabled="!ruleForm.userName || !ruleForm.passWord"
                            >
                                <span v-if="!loading">立即登录</span>
                                <span v-else>登录中...</span>
                            </el-button>
                        </div>
                    </div>
                    
                    <div class="login-footer">
                        <p class="footer-text">© 2024 智慧医疗系统 版权所有</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    requestLoginon,
    commonAjax,
    imgview
} from '../../api/api.js'
import md5 from 'md5'
import backLogo from '../../assets/img/bg_login2.png'
import { isUpdatePassWord } from 'services';
import { sStore, lStore } from 'js/yydjs';

export default {
    data() {
        var validatePass = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入密码'))
            }
        }

        return {
            query: {
                pro: 'hcn.shs4thyy.doctor_pc'
            },
            ruleForm: {
                userName: lStore.get('keyName') ?
                    lStore.get('keyName') : '',
                passWord: lStore.get('keyPass') ?
                    lStore.get('keyPass') : ''
            },
            backLogo,
            checked: false,
            loading: false
        }
    },
    methods: {
        //忘记密码
        misspwd() {
            let { pro: peoCode = sStore.get('peoCode') } = this.query;

            sStore.set('peoCode', peoCode);
            this.$router.push('/misspwd')
        },

        isUpdatePassWordFn() {
            isUpdatePassWord([], (res) => {
                if (res.body) {
                    this.$msgbox({
                        title: '',
                        message: '您的密码已长时间未修改，为确定您的账号安全，是否前往修改密码？',
                        showCancelButton: true,
                        showConfirmButton: true,
                        cancelButtonText: '暂不修改',
                        confirmButtonText: '前往修改',
                        distinguishCancelAndClose: true,
                    }).then(() => {
                        this.$router.push('/pwdManage');
                    }).catch((action) => {
                        if (action == 'cancel') {
                            this.$router.push('/home');
                        }
                    });
                } else {
                    this.$router.push('/home');
                }
            }, (res) => {
                this.$router.push('/home');
            });
        },

        submitForm() {
            this.loading = true;
            let tenantId = '';
            let { pro: peoCode = sStore.get('peoCode') } = this.query;

            sStore.set('peoCode', peoCode);
            let temarr = peoCode ? peoCode.split('.') : '';

            if (temarr.length == 3) {
                tenantId = temarr[0] + '.' + temarr[1]
            } else {
                tenantId = temarr[0]
            }

            let loginParams = {
                tenantId: tenantId,
                loginName: this.ruleForm.userName + '',
                pwd: this.ruleForm.passWord === lStore.get('keyPass') ?
                    this.ruleForm.passWord : md5(this.ruleForm.passWord),
                forAccessToken: true
            }
            lStore.set('keyName', loginParams.loginName)
            if (this.checked) {
                lStore.set('keyPass', loginParams.pwd)
            } else {
                lStore.remove('keyPass')
            }
            let JSESSIONID = window.readCookie('JSESSIONID')
            if (JSESSIONID) {
                window.delCookie('JSESSIONID')
            }

            requestLoginon(loginParams)
                .then(res => {
                    this.loading = false
                    if (res.code == 200) {
                        sStore.set('accessToken', res.properties.accessToken)
                        // 备份token到localStorage，用于多端登录场景下恢复会话
                        localStorage.setItem('backup_token', res.properties.accessToken)
                        // 重置错误计数器
                        localStorage.setItem('api_error_count', 0)
                        
                        sStore.set('tenantId', res.body.tenantId)
                        sStore.set('userId', res.body.userId)

                        // 获取医生信息
                        commonAjax('cas.doctorServiceAdmin', 'doctorInfo', []).then(res => {
                            if (res.code == 200) {
                                if (res.body.doctorId) {
                                    sStore.set('doctorName', res.body.doctorName)
                                    sStore.set('docId', res.body.doctorId)
                                    sStore.set('deptId', res.body.deptId)

                                    //用户图像
                                    if (res.body.avatarFileId) {
                                        let userAvatar = imgview + res.body.avatarFileId
                                        sStore.set('userAvatar', res.body.avatarFileId)
                                        sStore.set('toAvatarUrl', userAvatar)
                                    }
                                    //默认登录网易云信
                                    this.getImToken()
                                    //判断是否需要修改密码
                                    this.isUpdatePassWordFn();
                                } else {
                                    this.$message({
                                        type: 'error',
                                        message: '该用户不是医生，禁止登陆'
                                    })
                                    return
                                }
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: res.msg
                                })
                            }
                        })
                    } else if (res.code == 501) {
                        this.$message({
                            type: 'error',
                            message: '密码不正确'
                        })
                    } else if (res.code == 404) {
                        this.$message({
                            type: 'error',
                            message: '用户ID不存在'
                        })
                    } else if (res.code == 0) {
                        this.$message({
                            type: 'error',
                            message: res.msg
                        });
                    } else {
                        this.$message({
                            type: 'error',
                            message: '登录失败，请确认账号密码是否正确！'
                        })
                    }
                })
                .catch(err => {
                    this.loading = false
                })
        },

        //获取网易IM的token
        getImToken() {
            let serviceId = 'cas.foshan_neteaseAdminService';

            commonAjax(
                serviceId,
                'getIMToken',
                [sStore.get('peoCode')]
            ).then(res => {
                if (res && res.code == 200) {
                    if (res.body) {
                        let { accId, token } = res.body;

                        window.setCookie('uid', accId);
                        window.setCookie('sdktoken', token);
                        window.userUID = accId;
                        lStore.set('nimAccount', accId);
                        lStore.set('nimToken', token);

                        // window.yunXin = new YX(res.body.accId)
                        // initNIM(config.appkey,res.body.accId,res.body.token);
                    }
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                    return
                }
            })
        }
    },
    mounted() {
        if (window.nim) {
            window.location.reload();
        }

        if (this.ruleForm.passWord) {
            this.checked = true
        } else {
            this.checked = false
        }
    }
}

</script>

<style lang="scss" scoped>
@import '../../assets/css/var.scss';

.login-wrap {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

// 背景装饰
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    
    .decoration-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
        
        &.circle-1 {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        &.circle-2 {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }
        
        &.circle-3 {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
    }
    
    .decoration-line {
        position: absolute;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        
        &.line-1 {
            width: 300px;
            height: 2px;
            top: 30%;
            right: 10%;
            transform: rotate(45deg);
        }
        
        &.line-2 {
            width: 200px;
            height: 2px;
            bottom: 30%;
            left: 10%;
            transform: rotate(-45deg);
        }
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

// 主要内容容器
.login-container {
    position: relative;
    z-index: 2;
    display: flex;
    width: 100%;
    max-width: 1200px;
    height: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

// 左侧插图区域
.illustration-section {
    flex: 1;
    background: linear-gradient(135deg, #52a3fc 0%, #3f87ea 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }
}

.illustration-content {
    text-align: center;
    color: white;
    z-index: 1;
    position: relative;
}

.medical-illustration {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    margin-bottom: 40px;
    height: 200px;
}

.doctor {
    position: relative;
    margin: 0 15px;
    animation: doctorFloat 4s ease-in-out infinite;
    
    &.doctor-1 { animation-delay: 0s; }
    &.doctor-2 { animation-delay: 1.3s; }
    &.doctor-3 { animation-delay: 2.6s; }
    
    .doctor-avatar {
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 50%;
        margin: 0 auto 10px;
        position: relative;
        
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            background: #52a3fc;
            border-radius: 50%;
        }
    }
    
    .doctor-body {
        width: 60px;
        height: 80px;
        background: #fff;
        border-radius: 30px 30px 20px 20px;
        margin: 0 auto;
        position: relative;
        
        &::before {
            content: '';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 30px;
            background: #52a3fc;
            border-radius: 25px 25px 0 0;
        }
    }
    
    .stethoscope {
        position: absolute;
        top: 15px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 20px;
        border: 3px solid #fff;
        border-radius: 50%;
        border-top: none;
    }
    
    .clipboard {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 25px;
        height: 35px;
        background: #333;
        border-radius: 3px;
        
        &::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 15px;
            height: 2px;
            background: #fff;
        }
    }
}

@keyframes doctorFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.illustration-text {
    h2 {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    p {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
    }
}

// 右侧登录表单区域
.login-form-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.login-card {
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
    
    .logo-container {
        margin-bottom: 20px;
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #52a3fc, #3f87ea);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            box-shadow: 0 8px 20px rgba(82, 163, 252, 0.3);
            
            i {
                font-size: 28px;
                color: white;
            }
        }
    }
    
    .login-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }
    
    .login-subtitle {
        font-size: 14px;
        color: #666;
        margin: 0;
    }
}

.login-form {
    .form-group {
        margin-bottom: 25px;
    }
    
    .input-wrapper {
        position: relative;
        
        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
            z-index: 2;
            transition: color 0.3s ease;
        }
        
        .form-input {
            width: 100%;
            height: 50px;
            padding: 0 20px 0 50px;
            border: none;
            background: #f8f9fa;
            border-radius: 25px;
            font-size: 16px;
            color: #333;
            transition: all 0.3s ease;
            box-sizing: border-box;
            
            &:focus {
                outline: none;
                background: #fff;
                box-shadow: 0 0 0 3px rgba(82, 163, 252, 0.1);
                
                & + .input-line {
                    transform: scaleX(1);
                }
            }
            
            &.has-value {
                background: #fff;
                
                & + .input-line {
                    transform: scaleX(1);
                }
            }
            
            &::placeholder {
                color: #999;
                transition: color 0.3s ease;
            }
            
            &:focus::placeholder {
                color: #ccc;
            }
        }
        
        .input-line {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #52a3fc, #3f87ea);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
    }
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    
    .remember-checkbox {
        .checkbox-text {
            color: #666;
            font-size: 14px;
        }
    }
    
    .forgot-link {
        color: #52a3fc;
        font-size: 14px;
        text-decoration: none;
        transition: color 0.3s ease;
        
        &:hover {
            color: #3f87ea;
        }
    }
}

.form-submit {
    .login-button {
        width: 100%;
        height: 50px;
        background: linear-gradient(135deg, #52a3fc, #3f87ea);
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(82, 163, 252, 0.3);
        
        &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(82, 163, 252, 0.4);
        }
        
        &:active:not(:disabled) {
            transform: translateY(0);
        }
        
        &:disabled {
            background: #ccc;
            cursor: not-allowed;
            box-shadow: none;
        }
    }
}

.login-footer {
    text-align: center;
    margin-top: 30px;
    
    .footer-text {
        font-size: 12px;
        color: #999;
        margin: 0;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
        height: auto;
        margin: 20px;
        max-width: none;
    }
    
    .illustration-section {
        min-height: 200px;
    }
    
    .medical-illustration {
        height: 150px;
    }
    
    .doctor {
        margin: 0 10px;
        
        .doctor-avatar {
            width: 30px;
            height: 30px;
        }
        
        .doctor-body {
            width: 45px;
            height: 60px;
        }
    }
    
    .illustration-text h2 {
        font-size: 20px;
    }
    
    .login-form-section {
        padding: 20px;
    }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
    .login-container {
        background: rgba(30, 30, 30, 0.95);
    }
    
    .login-title {
        color: #fff;
    }
    
    .login-subtitle {
        color: #ccc;
    }
    
    .form-input {
        background: #2a2a2a;
        color: #fff;
        
        &:focus {
            background: #333;
        }
        
        &.has-value {
            background: #333;
        }
    }
    
    .checkbox-text {
        color: #ccc;
    }
}
</style>

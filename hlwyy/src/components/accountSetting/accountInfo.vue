<template>
    <div class="inq-list">
        <div class="list-main-head">
            <div  class="fieldName baseWidth" >
                <label class="shopName" >账号:</label>
                <p  class="shopValue">{{docAccount[0]}}</p>
            </div>
            <div  class="fieldName baseWidth" >
                <label class="shopName" >绑定手机号:</label>
                <p  class="shopValue">{{docAccount[1]}}</p>
            </div>
        </div>
    </div>
</template>


<script>
import { commonAjax } from '../../api/api';
    export default {
        data() {
            return {
                docAccount:{},
            }
        },
        methods:{
            //获取医生个人资料
            getDocAccount() {
                commonAjax("cas.foshan_docAccountSetService","searchLoginInfo",[]).then(res => {
                    if(res && res.code == 200) {
                        this.docAccount = res.body;
                    } else {
                        this.$message({
                            type:"error",
                            message:res.msg,
                        })
                    }
                })
            },
        },
        mounted() {
            this.getDocAccount();
        }
    }
</script>



<style lang="scss" scoped>
.inq-list{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;
    .line {
            border-bottom: 1px solid #ddd;
        }
    .list-main-head{
        width: 100%;
        background: white;
        height: 100%;
        // text-align: center;
        margin-bottom: 10px;
        overflow: hidden;

        .fieldName{
            margin-bottom: 20px;
            line-height: 50px;
            min-width: 200px;
            width: 45%;
            // height: 50px;
            overflow: hidden;
            float: left;
            .shopName{
                float: left;
                height:50px;
                width:150px;
                display: block;
                line-height: 50px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue{
                width: 500px;
                min-width: 300px;
                height: auto;
                float: left;
                display: block;
                padding-left:10px;
            }
            .conTag{
                word-wrap: break-word;
                word-break: normal;
            }
        }
        .baseWidth{
            width: 100%;
        }

    }
}

</style>
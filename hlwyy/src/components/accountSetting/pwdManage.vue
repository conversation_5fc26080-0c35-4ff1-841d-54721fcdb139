<template>
    <div class="inq-list">
        <div class="list-main-head">
            <div class="fieldName baseWidth">
                <label class="shopName">原密码:</label>
                <div class="shopValue">
                    <el-input size="small" type="password" v-model="oldPwd" placeholder="8-20位英文字母、数字或符号，至少3种字符" clearable @blur="verOldPwd"></el-input>
                    <div v-if="displayOldPwdTip" style="color:red;height:30px;line-height:30px;">请输入原密码</div>
                </div>
            </div>
            <div class="fieldName baseWidth">
                <label class="shopName">新密码:</label>
                <div class="shopValue">
                    <el-input size="small" type="password" v-model="newPwd" placeholder="8-20位英文字母、数字或符号，至少3种字符" clearable @blur="verNewPwd"></el-input>
                    <div v-if="showRegNewPwd" style="color:red;height:30px;line-height:30px;">8-20位英文字母、数字或符号，至少3种字符</div>
                </div>
            </div>
            <div class="fieldName baseWidth">
                <label class="shopName">确认密码:</label>
                <div class="shopValue">
                    <el-input size="small" type="password" v-model="repNewPwd" placeholder="8-20位英文字母、数字或符号，至少3种字符" clearable @blur="verPwd"></el-input>
                    <div v-if="displayNewPwdTip" style="color:red;height:30px;line-height:30px;">两次密码输入不一致</div>
                </div>
            </div>
            <div class="fieldName baseWidth">
                <div style="width:50%;text-align:center">
                    <el-button type="primary" size="small" @click="updatePassword">确认修改</el-button>
                    <el-button type="primary" size="small" @click="goHome">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { commonAjax } from '../../api/api';
import { lStore, sStore, alerts } from 'js/yydjs';

export default {
    data() {
        return {
            showRegNewPwd: false,
            displayOldPwdTip: false,
            displayNewPwdTip: false,
            oldPwd: "",
            newPwd: "",
            repNewPwd: "",
        }
    },
    methods: {
        //验证老密码
        verOldPwd() {
            if (!this.oldPwd) {
                this.displayOldPwdTip = true
            } else {
                this.displayOldPwdTip = false;
            }
        },
        verNewPwd() {
            let reg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,20}$/
            if (!reg.test(this.newPwd)) {
                this.showRegNewPwd = true
            } else {
                this.showRegNewPwd = false
            }
        },
        //验证确认密码
        verPwd() {
            if (this.repNewPwd !== this.newPwd) {
                this.displayNewPwdTip = true;
            } else {
                this.displayNewPwdTip = false;
            }
        },
        //退出登录
        loginout() {
            let peoCode = sStore.get('peoCode');

            delCookie('uid')
            delCookie('sdktoken')
            lStore.remove('nimAccount');
            lStore.remove('nimToken');
            sStore.clear();
            sStore.set('peoCode', peoCode);

            this.$router.replace('/login');
        },
        //更新密码
        updatePassword() {
            let reg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,20}$/

            this.displayNewPwdTip = false
            if (!this.oldPwd) {
                this.displayOldPwdTip = true
                return;
            } else if (!reg.test(this.newPwd)) {
                this.showRegNewPwd = true
                return;
            } else if (this.repNewPwd !== this.newPwd) {
                this.displayNewPwdTip = true
                return;
            } else {
                let params = [`${this.oldPwd}`, `${this.newPwd}`, `${this.repNewPwd}`]
                commonAjax("cas.foshan_docAccountSetService", "updatePassword", params).then(res => {
                    if (res && res.code == 200) {
                        this.$message({
                            type: "success",
                            message: "修改成功",
                        })

                        this.loginout()
                    } else {
                        this.$message({
                            type: "error",
                            message: res.msg,
                        })
                        return
                    }
                })
            }
        },
        goHome() {
            this.$router.push("workbench")
        }
    },
    mounted() {

    }
}

</script>
<style lang="scss" scoped>
.inq-list {
    width: 100%;
    padding: 20px;
    background: #fff;
    .line {
        border-bottom: 1px solid #ddd;
    }
    .list-main-head {
        width: 100%;
        background: white;
        height: 100%;
        margin-bottom: 10px;
        overflow: hidden;

        .fieldName {
            line-height: 50px;
            min-width: 200px;
            width: 45%;
            overflow: hidden;
            float: left;
            .shopName {
                float: left;
                height: 50px;
                width: 150px;
                display: block;
                line-height: 50px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue {
                width: 500px;
                min-width: 300px;
                height: 80px;
                float: left;
                display: block;
                padding-left: 10px;
            }
            .conTag {
                word-wrap: break-word;
                word-break: normal;
            }
        }
        .baseWidth {
            width: 100%;
        }

    }
}

</style>

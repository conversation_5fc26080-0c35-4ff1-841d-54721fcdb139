<template>
    <div class="warning-knowledge">
        <!-- 搜索区域 -->
        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="规则名称">
                    <el-input v-model="searchForm.ruleName" placeholder="请输入规则名称"></el-input>
                </el-form-item>
                <el-form-item label="监测类型">
                    <el-select v-model="searchForm.monitorType" placeholder="请选择监测类型">
                        <el-option label="血压" value="bloodPressure"></el-option>
                        <el-option label="血糖" value="bloodSugar"></el-option>
                        <el-option label="血氧饱和度" value="bloodOxygen"></el-option>
                        <el-option label="心率" value="heartRate"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="警示等级">
                    <el-select v-model="searchForm.warningLevel" placeholder="请选择警示等级">
                        <el-option label="一级(紧急)" value="1"></el-option>
                        <el-option label="二级(重要)" value="2"></el-option>
                        <el-option label="三级(普通)" value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                    <el-button type="success" @click="showAddDialog">新增规则</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 数据表格 -->
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="ruleName" label="规则名称" width="180"></el-table-column>
            <el-table-column prop="monitorType" label="监测类型" width="120">
                <template slot-scope="scope">
                    {{getMonitorTypeName(scope.row.monitorType)}}
                </template>
            </el-table-column>
            <el-table-column prop="warningLevel" label="警示等级" width="120">
                <template slot-scope="scope">
                    <el-tag :type="getWarningLevelType(scope.row.warningLevel)">
                        {{getWarningLevelName(scope.row.warningLevel)}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="conditions" label="触发条件" min-width="200">
                <template slot-scope="scope">
                    <div v-for="(condition, index) in scope.row.conditions" :key="index">
                        {{getConditionText(condition)}}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
            <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.status"
                        active-color="#13ce66"
                        inactive-color="#ff4949"
                        @change="(val) => handleStatusChange(val, scope.row)">
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>

        <!-- 新增/编辑规则弹窗 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
                <el-form-item label="规则名称" prop="ruleName">
                    <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称"></el-input>
                </el-form-item>
                <el-form-item label="监测类型" prop="monitorType">
                    <el-select v-model="ruleForm.monitorType" placeholder="请选择监测类型">
                        <el-option label="血压" value="bloodPressure"></el-option>
                        <el-option label="血糖" value="bloodSugar"></el-option>
                        <el-option label="血氧饱和度" value="bloodOxygen"></el-option>
                        <el-option label="心率" value="heartRate"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="警示等级" prop="warningLevel">
                    <el-select v-model="ruleForm.warningLevel" placeholder="请选择警示等级">
                        <el-option label="一级(紧急)" value="1"></el-option>
                        <el-option label="二级(重要)" value="2"></el-option>
                        <el-option label="三级(普通)" value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="触发条件" prop="conditions">
                    <div v-for="(condition, index) in ruleForm.conditions" :key="index" class="condition-item">
                        <el-row :gutter="10">
                            <el-col :span="6">
                                <el-select v-model="condition.operator" placeholder="请选择条件">
                                    <el-option label="大于" value="gt"></el-option>
                                    <el-option label="小于" value="lt"></el-option>
                                    <el-option label="等于" value="eq"></el-option>
                                    <el-option label="区间" value="between"></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="12">
                                <el-input-number 
                                    v-if="condition.operator !== 'between'"
                                    v-model="condition.value" 
                                    :precision="getPrecision(ruleForm.monitorType)"
                                    :step="getStep(ruleForm.monitorType)"
                                    :placeholder="getPlaceholder(ruleForm.monitorType)">
                                </el-input-number>
                                <div v-else class="between-inputs">
                                    <el-input-number 
                                        v-model="condition.minValue"
                                        :precision="getPrecision(ruleForm.monitorType)"
                                        :step="getStep(ruleForm.monitorType)"
                                        :placeholder="'最小值'">
                                    </el-input-number>
                                    <span class="separator">-</span>
                                    <el-input-number 
                                        v-model="condition.maxValue"
                                        :precision="getPrecision(ruleForm.monitorType)"
                                        :step="getStep(ruleForm.monitorType)"
                                        :placeholder="'最大值'">
                                    </el-input-number>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <el-button type="text" icon="el-icon-delete" @click="removeCondition(index)">删除</el-button>
                            </el-col>
                        </el-row>
                    </div>
                    <el-button type="text" icon="el-icon-plus" @click="addCondition">添加条件</el-button>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'WarningKnowledge',
    data() {
        return {
            searchForm: {
                ruleName: '',
                monitorType: '',
                warningLevel: ''
            },
            tableData: [
                {
                    id: 1,
                    ruleName: '高血压预警',
                    monitorType: 'bloodPressure',
                    warningLevel: '1',
                    conditions: [
                        { operator: 'gt', value: 140, unit: 'mmHg' }
                    ],
                    createTime: '2024-03-20 10:00:00',
                    status: true
                },
                {
                    id: 2,
                    ruleName: '血糖异常预警',
                    monitorType: 'bloodSugar',
                    warningLevel: '2',
                    conditions: [
                        { operator: 'between', minValue: 3.9, maxValue: 6.1, unit: 'mmol/L' }
                    ],
                    createTime: '2024-03-20 11:00:00',
                    status: true
                },
                {
                    id: 3,
                    ruleName: '血氧不足预警',
                    monitorType: 'bloodOxygen',
                    warningLevel: '1',
                    conditions: [
                        { operator: 'lt', value: 95, unit: '%' }
                    ],
                    createTime: '2024-03-20 12:00:00',
                    status: true
                }
            ],
            currentPage: 1,
            pageSize: 10,
            total: 3,
            dialogVisible: false,
            dialogTitle: '新增规则',
            ruleForm: {
                ruleName: '',
                monitorType: '',
                warningLevel: '',
                conditions: []
            },
            rules: {
                ruleName: [
                    { required: true, message: '请输入规则名称', trigger: 'blur' }
                ],
                monitorType: [
                    { required: true, message: '请选择监测类型', trigger: 'change' }
                ],
                warningLevel: [
                    { required: true, message: '请选择警示等级', trigger: 'change' }
                ]
            }
        }
    },
    methods: {
        handleSearch() {
            // 模拟搜索请求
            console.log('搜索条件：', this.searchForm)
        },
        resetSearch() {
            this.searchForm = {
                ruleName: '',
                monitorType: '',
                warningLevel: ''
            }
        },
        showAddDialog() {
            this.dialogTitle = '新增规则'
            this.ruleForm = {
                ruleName: '',
                monitorType: '',
                warningLevel: '',
                conditions: [{ operator: 'gt', value: null }]
            }
            this.dialogVisible = true
        },
        handleEdit(row) {
            this.dialogTitle = '编辑规则'
            this.ruleForm = JSON.parse(JSON.stringify(row))
            this.dialogVisible = true
        },
        handleDelete(row) {
            this.$confirm('确认删除该规则吗？', '提示', {
                type: 'warning'
            }).then(() => {
                // 模拟删除请求
                const index = this.tableData.findIndex(item => item.id === row.id)
                this.tableData.splice(index, 1)
                this.$message.success('删除成功')
            }).catch(() => {})
        },
        handleStatusChange(val, row) {
            // 模拟状态更新请求
            console.log('状态更新：', row.id, val)
        },
        handleSizeChange(val) {
            this.pageSize = val
            // 重新加载数据
        },
        handleCurrentChange(val) {
            this.currentPage = val
            // 重新加载数据
        },
        addCondition() {
            this.ruleForm.conditions.push({
                operator: 'gt',
                value: null
            })
        },
        removeCondition(index) {
            this.ruleForm.conditions.splice(index, 1)
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 模拟提交请求
                    if (this.ruleForm.id) {
                        // 更新
                        const index = this.tableData.findIndex(item => item.id === this.ruleForm.id)
                        this.tableData.splice(index, 1, {...this.ruleForm})
                    } else {
                        // 新增
                        this.tableData.push({
                            ...this.ruleForm,
                            id: this.tableData.length + 1,
                            createTime: new Date().toLocaleString(),
                            status: true
                        })
                    }
                    this.dialogVisible = false
                    this.$message.success('保存成功')
                }
            })
        },
        getMonitorTypeName(type) {
            const map = {
                bloodPressure: '血压',
                bloodSugar: '血糖',
                bloodOxygen: '血氧饱和度',
                heartRate: '心率'
            }
            return map[type] || type
        },
        getWarningLevelType(level) {
            const map = {
                '1': 'danger',
                '2': 'warning',
                '3': 'info'
            }
            return map[level] || 'info'
        },
        getWarningLevelName(level) {
            const map = {
                '1': '一级(紧急)',
                '2': '二级(重要)',
                '3': '三级(普通)'
            }
            return map[level] || level
        },
        getConditionText(condition) {
            const operatorMap = {
                gt: '大于',
                lt: '小于',
                eq: '等于',
                between: '介于'
            }
            const unitMap = {
                bloodPressure: 'mmHg',
                bloodSugar: 'mmol/L',
                bloodOxygen: '%',
                heartRate: '次/分'
            }
            const unit = condition.unit || unitMap[this.ruleForm.monitorType] || ''
            
            if (condition.operator === 'between') {
                return `${operatorMap[condition.operator]} ${condition.minValue}-${condition.maxValue}${unit}`
            }
            return `${operatorMap[condition.operator]} ${condition.value}${unit}`
        },
        getPrecision(type) {
            const map = {
                bloodPressure: 0,
                bloodSugar: 1,
                bloodOxygen: 0,
                heartRate: 0
            }
            return map[type] || 0
        },
        getStep(type) {
            const map = {
                bloodPressure: 1,
                bloodSugar: 0.1,
                bloodOxygen: 1,
                heartRate: 1
            }
            return map[type] || 1
        },
        getPlaceholder(type) {
            const map = {
                bloodPressure: '请输入血压值',
                bloodSugar: '请输入血糖值',
                bloodOxygen: '请输入血氧饱和度',
                heartRate: '请输入心率值'
            }
            return map[type] || '请输入值'
        }
    }
}
</script>

<style lang="scss" scoped>
.warning-knowledge {
    padding: 20px;

    .search-area {
        margin-bottom: 20px;
        background: #fff;
        padding: 20px;
        border-radius: 4px;
    }

    .condition-item {
        margin-bottom: 10px;
        
        .between-inputs {
            display: flex;
            align-items: center;
            
            .separator {
                margin: 0 10px;
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }
}
</style> 
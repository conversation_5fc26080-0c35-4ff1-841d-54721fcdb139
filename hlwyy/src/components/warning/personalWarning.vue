<template>
    <div class="personal-warning">
        <!-- 搜索区域 -->
        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="患者姓名">
                    <el-input v-model="searchForm.patientName" placeholder="请输入患者姓名"></el-input>
                </el-form-item>
                <el-form-item label="病种类型">
                    <el-select v-model="searchForm.diseaseType" placeholder="请选择病种类型">
                        <el-option label="高血压" value="hypertension"></el-option>
                        <el-option label="糖尿病" value="diabetes"></el-option>
                        <el-option label="冠心病" value="coronary"></el-option>
                        <el-option label="慢性肾病" value="kidney"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="主诊医生">
                    <el-input v-model="searchForm.doctorName" placeholder="请输入主诊医生姓名"></el-input>
                </el-form-item>
                <el-form-item label="配置状态">
                    <el-select v-model="searchForm.status" placeholder="请选择配置状态">
                        <el-option label="已配置" value="configured"></el-option>
                        <el-option label="未配置" value="unconfigured"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                    <el-button type="success" @click="showAddDialog">新增配置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 数据表格 -->
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="patientName" label="患者姓名" width="120"></el-table-column>
            <el-table-column prop="patientId" label="患者ID" width="120"></el-table-column>
            <el-table-column prop="diseaseType" label="病种类型" width="120">
                <template slot-scope="scope">
                    {{getDiseaseTypeName(scope.row.diseaseType)}}
                </template>
            </el-table-column>
            <el-table-column prop="doctorName" label="主诊医生" width="120"></el-table-column>
            <el-table-column prop="department" label="科室" width="120"></el-table-column>
            <el-table-column prop="warningRules" label="警示规则" min-width="300">
                <template slot-scope="scope">
                    <div v-for="(rule, index) in scope.row.warningRules" :key="index">
                        {{getWarningRuleText(rule)}}
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" width="160"></el-table-column>
            <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 'configured' ? 'success' : 'info'">
                        {{scope.row.status === 'configured' ? '已配置' : '未配置'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
        </div>

        <!-- 新增/编辑配置弹窗 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%">
            <el-form :model="configForm" :rules="rules" ref="configForm" label-width="100px">
                <el-form-item label="患者信息" required>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item prop="patientName">
                                <el-input v-model="configForm.patientName" placeholder="请输入患者姓名">
                                    <template slot="prepend">姓名</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item prop="patientId">
                                <el-input v-model="configForm.patientId" placeholder="请输入患者ID">
                                    <template slot="prepend">ID</template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="病种类型" prop="diseaseType">
                    <el-select v-model="configForm.diseaseType" placeholder="请选择病种类型" style="width: 100%">
                        <el-option label="高血压" value="hypertension"></el-option>
                        <el-option label="糖尿病" value="diabetes"></el-option>
                        <el-option label="冠心病" value="coronary"></el-option>
                        <el-option label="慢性肾病" value="kidney"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="警示规则" prop="warningRules">
                    <div v-for="(rule, index) in configForm.warningRules" :key="index" class="rule-item">
                        <el-row :gutter="10">
                            <el-col :span="6">
                                <el-select v-model="rule.type" placeholder="监测指标">
                                    <el-option label="血压" value="bloodPressure"></el-option>
                                    <el-option label="血糖" value="bloodSugar"></el-option>
                                    <el-option label="血氧" value="bloodOxygen"></el-option>
                                    <el-option label="心率" value="heartRate"></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="6">
                                <el-select v-model="rule.operator" placeholder="条件">
                                    <el-option label="大于" value="gt"></el-option>
                                    <el-option label="小于" value="lt"></el-option>
                                    <el-option label="区间" value="between"></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="8">
                                <div v-if="rule.operator !== 'between'">
                                    <el-input-number 
                                        v-model="rule.value" 
                                        :precision="getPrecision(rule.type)"
                                        :step="getStep(rule.type)"
                                        :placeholder="getPlaceholder(rule.type)">
                                    </el-input-number>
                                </div>
                                <div v-else class="between-inputs">
                                    <el-input-number 
                                        v-model="rule.minValue"
                                        :precision="getPrecision(rule.type)"
                                        :step="getStep(rule.type)"
                                        style="width: 110px"
                                        :placeholder="'最小值'">
                                    </el-input-number>
                                    <span class="separator">-</span>
                                    <el-input-number 
                                        v-model="rule.maxValue"
                                        :precision="getPrecision(rule.type)"
                                        :step="getStep(rule.type)"
                                        style="width: 110px"
                                        :placeholder="'最大值'">
                                    </el-input-number>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <el-button type="text" icon="el-icon-delete" @click="removeRule(index)">删除</el-button>
                            </el-col>
                        </el-row>
                    </div>
                    <el-button type="text" icon="el-icon-plus" @click="addRule">添加规则</el-button>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" v-model="configForm.remark" placeholder="请输入备注信息"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('configForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'PersonalWarning',
    data() {
        return {
            searchForm: {
                patientName: '',
                diseaseType: '',
                doctorName: '',
                status: ''
            },
            tableData: [
                {
                    id: 1,
                    patientName: '陈建国',
                    patientId: 'P2024001',
                    diseaseType: 'hypertension',
                    doctorName: '王俊杰',
                    department: '心内科',
                    warningRules: [
                        { type: 'bloodPressure', operator: 'gt', value: 150, unit: 'mmHg' }
                    ],
                    updateTime: '2024-03-20 10:00:00',
                    status: 'configured'
                },
                {
                    id: 2,
                    patientName: '刘晓华',
                    patientId: 'P2024002',
                    diseaseType: 'diabetes',
                    doctorName: '刘伟',
                    department: '内分泌科',
                    warningRules: [
                        { type: 'bloodSugar', operator: 'between', minValue: 4.0, maxValue: 7.0, unit: 'mmol/L' }
                    ],
                    updateTime: '2024-03-20 11:00:00',
                    status: 'configured'
                },
                {
                    id: 3,
                    patientName: '赵明珠',
                    patientId: 'P2024003',
                    diseaseType: 'coronary',
                    doctorName: '陈明宇',
                    department: '心内科',
                    warningRules: [
                        { type: 'heartRate', operator: 'gt', value: 100, unit: '次/分' },
                        { type: 'bloodOxygen', operator: 'lt', value: 95, unit: '%' }
                    ],
                    updateTime: '2024-03-20 12:00:00',
                    status: 'configured'
                }
            ],
            currentPage: 1,
            pageSize: 10,
            total: 3,
            dialogVisible: false,
            dialogTitle: '新增配置',
            configForm: {
                patientName: '',
                patientId: '',
                diseaseType: '',
                warningRules: [],
                remark: ''
            },
            rules: {
                patientName: [
                    { required: true, message: '请输入患者姓名', trigger: 'blur' }
                ],
                patientId: [
                    { required: true, message: '请输入患者ID', trigger: 'blur' }
                ],
                diseaseType: [
                    { required: true, message: '请选择病种类型', trigger: 'change' }
                ]
            }
        }
    },
    methods: {
        handleSearch() {
            // 模拟搜索请求
            console.log('搜索条件：', this.searchForm)
        },
        resetSearch() {
            this.searchForm = {
                patientName: '',
                diseaseType: '',
                doctorName: '',
                status: ''
            }
        },
        showAddDialog() {
            this.dialogTitle = '新增配置'
            this.configForm = {
                patientName: '',
                patientId: '',
                diseaseType: '',
                warningRules: [{ type: '', operator: 'gt', value: null }],
                remark: ''
            }
            this.dialogVisible = true
        },
        handleEdit(row) {
            this.dialogTitle = '编辑配置'
            this.configForm = JSON.parse(JSON.stringify(row))
            this.dialogVisible = true
        },
        handleDelete(row) {
            this.$confirm('确认删除该配置吗？', '提示', {
                type: 'warning'
            }).then(() => {
                // 模拟删除请求
                const index = this.tableData.findIndex(item => item.id === row.id)
                this.tableData.splice(index, 1)
                this.$message.success('删除成功')
            }).catch(() => {})
        },
        handleSizeChange(val) {
            this.pageSize = val
            // 重新加载数据
        },
        handleCurrentChange(val) {
            this.currentPage = val
            // 重新加载数据
        },
        addRule() {
            this.configForm.warningRules.push({
                type: '',
                operator: 'gt',
                value: null
            })
        },
        removeRule(index) {
            this.configForm.warningRules.splice(index, 1)
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 模拟提交请求
                    if (this.configForm.id) {
                        // 更新
                        const index = this.tableData.findIndex(item => item.id === this.configForm.id)
                        this.tableData.splice(index, 1, {
                            ...this.configForm,
                            updateTime: new Date().toLocaleString(),
                            status: 'configured'
                        })
                    } else {
                        // 新增
                        this.tableData.push({
                            ...this.configForm,
                            id: this.tableData.length + 1,
                            doctorName: '当前医生',
                            department: '当前科室',
                            updateTime: new Date().toLocaleString(),
                            status: 'configured'
                        })
                    }
                    this.dialogVisible = false
                    this.$message.success('保存成功')
                }
            })
        },
        getDiseaseTypeName(type) {
            const map = {
                hypertension: '高血压',
                diabetes: '糖尿病',
                coronary: '冠心病',
                kidney: '慢性肾病'
            }
            return map[type] || type
        },
        getWarningRuleText(rule) {
            const typeMap = {
                bloodPressure: '血压',
                bloodSugar: '血糖',
                bloodOxygen: '血氧',
                heartRate: '心率'
            }
            const operatorMap = {
                gt: '大于',
                lt: '小于',
                between: '介于'
            }
            const type = typeMap[rule.type] || rule.type
            const operator = operatorMap[rule.operator] || rule.operator
            
            if (rule.operator === 'between') {
                return `${type}${operator} ${rule.minValue}-${rule.maxValue}${rule.unit}`
            }
            return `${type}${operator} ${rule.value}${rule.unit}`
        },
        getPrecision(type) {
            const map = {
                bloodPressure: 0,
                bloodSugar: 1,
                bloodOxygen: 0,
                heartRate: 0
            }
            return map[type] || 0
        },
        getStep(type) {
            const map = {
                bloodPressure: 1,
                bloodSugar: 0.1,
                bloodOxygen: 1,
                heartRate: 1
            }
            return map[type] || 1
        },
        getPlaceholder(type) {
            const map = {
                bloodPressure: '请输入血压值',
                bloodSugar: '请输入血糖值',
                bloodOxygen: '请输入血氧值',
                heartRate: '请输入心率值'
            }
            return map[type] || '请输入值'
        }
    }
}
</script>

<style lang="scss" scoped>
.personal-warning {
    padding: 20px;

    .search-area {
        margin-bottom: 20px;
        background: #fff;
        padding: 20px;
        border-radius: 4px;
    }

    .rule-item {
        margin-bottom: 10px;
        
        .between-inputs {
            display: flex;
            align-items: center;
            
            .separator {
                margin: 0 10px;
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }
}
</style> 
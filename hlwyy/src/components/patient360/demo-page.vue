<template>
  <div class="patient360-demo">
    <div class="demo-header">
      <h2>Patient360 集成示例</h2>
      <p class="description">本页面展示如何在第三方应用中集成 Patient360 组件。下面展示的是完整的 Patient360 组件，包含所有原始数据。</p>
    </div>
    
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="组件展示" name="component">
        <div class="component-container">
          <patient360-component />
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="集成说明" name="instructions">
        <div class="instructions-container">
          <h3>集成方式</h3>
          
          <div class="integration-method">
            <h4>方式一：使用 iframe 嵌入（推荐，最简单）</h4>
            <p>最简单的集成方式是通过 iframe 直接嵌入 Patient360 页面。这种方式不需要第三方处理任何数据或组件交互。</p>
            
            <div class="code-block">
              <h5>开发环境：</h5>
              <pre><code>&lt;iframe src="http://localhost:2222/home/<USER>" width="100%" height="700px" frameborder="0"&gt;&lt;/iframe&gt;</code></pre>
              
              <h5>生产环境：</h5>
              <pre><code>&lt;iframe src="https://你的域名/home/<USER>" width="100%" height="700px" frameborder="0"&gt;&lt;/iframe&gt;</code></pre>
            </div>
          </div>
          
          <div class="integration-method">
            <h4>方式二：使用JS API集成</h4>
            <p>您可以使用我们提供的JS API来创建iframe：</p>
            
            <div class="code-block">
              <pre><code>// 引入脚本
import { createPatient360Iframe } from 'path/to/patient360/export.js';

// 创建并挂载 iframe
const iframe = createPatient360Iframe('#container', {
  width: '100%',
  height: '700px',
  src: 'http://localhost:2222/home/<USER>' // 指定实际运行的应用地址
});</code></pre>
            </div>
          </div>
          
          <div class="integration-method">
            <h4>方式三：在Vue项目中使用组件</h4>
            <p>如果您的应用是Vue项目，可以直接导入并使用组件：</p>
            
            <div class="code-block">
              <pre><code>import { Patient360 } from 'path/to/patient360/export.js';

export default {
  components: {
    Patient360
  }
}</code></pre>

              <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;Patient360 /&gt;
  &lt;/div&gt;
&lt;/template&gt;</code></pre>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="iframe示例" name="iframe">
        <div class="iframe-example">
          <h3>iframe示例</h3>
          <p>下面是使用iframe嵌入Patient360组件的示例：</p>
          
          <div class="iframe-container">
            <iframe src="/home/<USER>" frameborder="0"></iframe>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Patient360Component from './index.vue'

export default {
  components: {
    Patient360Component
  },
  data() {
    return {
      activeTab: 'component'
    }
  }
}
</script>

<style lang="scss" scoped>
.patient360-demo {
  padding: 20px;
  
  .demo-header {
    margin-bottom: 20px;
    
    h2 {
      font-size: 24px;
      color: #303133;
      margin-bottom: 10px;
    }
    
    .description {
      color: #606266;
      font-size: 14px;
    }
  }
  
  .component-container {
    margin-top: 20px;
  }
  
  .instructions-container {
    padding: 20px 0;
    
    h3 {
      font-size: 18px;
      margin-bottom: 20px;
      color: #303133;
    }
    
    .integration-method {
      margin-bottom: 30px;
      
      h4 {
        font-size: 16px;
        margin-bottom: 10px;
        color: #303133;
      }
      
      p {
        color: #606266;
        margin-bottom: 15px;
      }
      
      .code-block {
        background: #f5f7fa;
        padding: 15px;
        border-radius: 4px;
        margin: 15px 0;
        
        h5 {
          margin-top: 0;
          margin-bottom: 10px;
          color: #303133;
        }
        
        pre {
          margin: 0 0 15px 0;
          padding: 10px;
          background: #f0f2f5;
          border-radius: 2px;
          
          code {
            font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
            color: #606266;
            font-size: 13px;
          }
        }
        
        pre:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .iframe-example {
    padding: 20px 0;
    
    h3 {
      font-size: 18px;
      margin-bottom: 10px;
      color: #303133;
    }
    
    p {
      color: #606266;
      margin-bottom: 20px;
    }
    
    .iframe-container {
      height: 700px;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
      overflow: hidden;
      
      iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
  
  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }
}
</style> 
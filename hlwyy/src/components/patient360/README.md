# Patient360 集成文档

本文档介绍如何在第三方应用中集成 Patient360 组件。Patient360 组件已包含完整的数据，第三方无需提供任何数据即可直接使用。

## 访问授权

Patient360 组件及演示页面受密码保护，需要授权才能访问。如需获取访问权限，请联系管理员获取密码。

- 演示页面密码：已设置为复杂密码，请联系管理员获取
- 文档访问密码：已设置为复杂密码，请联系管理员获取
- 组件接口调用：需要在请求头中包含授权信息

> **安全说明**: 在生产环境中，所有 Patient360 相关页面都受到密码保护，未授权用户无法查看内容。密码定期更新以确保安全性。

## 集成方式

### 方式一：使用 iframe 嵌入（推荐，最简单）

最简单的集成方式是通过 iframe 直接嵌入 Patient360 页面。这种方式不需要第三方处理任何数据或组件交互。

```html
<iframe src="https://yszt-prm.sh4th.com/#/patient360-embed" width="100%" height="600px" frameborder="0"></iframe>
```

> **注意**: 系统使用hash模式路由，URL中必须包含#符号，例如`https://yszt-prm.sh4th.com/#/patient360-embed`。访问时需要提供授权凭证。

或者使用我们提供的辅助函数：

```javascript
import { createPatient360Iframe } from 'path/to/patient360/export.js'

// 在指定容器中创建并挂载 iframe
const iframe = createPatient360Iframe('#container', {
  width: '100%',
  height: '700px',
  // 使用hash模式URL
  src: 'https://yszt-prm.sh4th.com/#/patient360-embed',
  // 可选：提供授权信息
  auth: {
    token: 'YOUR_ACCESS_TOKEN' // 请联系管理员获取授权token
  }
})
```

### 方式二：在 Vue 项目中使用组件

如果您的应用是基于 Vue 开发的，可以直接导入并使用组件：

```javascript
import { Patient360 } from 'path/to/patient360/export.js'

export default {
  components: {
    Patient360
  },
  data() {
    return {
      // 授权信息，请从管理员处获取
      authToken: 'YOUR_ACCESS_TOKEN'
    }
  }
}
```

```html
<template>
  <div>
    <Patient360 :auth-token="authToken" />
  </div>
</template>
```

### 方式三：在非 Vue 项目中挂载组件

```javascript
import { mountPatient360 } from 'path/to/patient360/export.js'

// 挂载到指定 DOM 元素
const instance = mountPatient360('#container', {
  onSelectPatient: function(patient) {
    console.log('选中患者:', patient)
  },
  // 授权信息
  auth: {
    token: 'YOUR_ACCESS_TOKEN' // 请联系管理员获取
  }
})
```

## API 文档

### 授权API

#### 获取访问Token

```javascript
import { getAuthToken } from 'path/to/patient360/auth.js'

// 获取授权Token
async function authenticate() {
  const token = await getAuthToken({
    username: 'your_username',
    password: 'your_password'
  })
  
  // 保存token用于后续请求
  localStorage.setItem('patient360Token', token)
  return token
}
```

### iframe 集成方式

#### createPatient360Iframe(el, options)

创建并挂载一个 iframe，加载完整的 Patient360 页面。

##### 参数

- `el`: String | HTMLElement - 要挂载 iframe 的 DOM 元素或 CSS 选择器
- `options`: Object - 配置选项
  - `width`: String - iframe 宽度，默认 '100%'
  - `height`: String - iframe 高度，默认 '600px'
  - `src`: String - iframe 的 src 地址，默认为自动检测的地址
  - `auth`: Object - 授权信息，格式为 `{ token: 'YOUR_TOKEN' }`

##### 返回值

- HTMLIFrameElement - 创建的 iframe 元素

### Vue 组件集成方式

直接使用 `Patient360` 组件，需要提供授权token。

#### Props

- `auth-token`: String - 授权token，必须提供

### JavaScript API 集成方式

#### mountPatient360(el, options)

将 Patient360 组件挂载到指定 DOM 元素。

##### 参数

- `el`: String | HTMLElement - 要挂载的 DOM 元素或 CSS 选择器
- `options`: Object - 配置选项
  - `onSelectPatient`: Function - 当用户点击"查看详情"时的回调函数，参数为选中的患者信息
  - `auth`: Object - 授权信息，格式为 `{ token: 'YOUR_TOKEN' }`

##### 返回值

- Vue 实例

## 使用示例

### 使用 iframe 嵌入（最简单方式）

```html
<!DOCTYPE html>
<html>
<head>
  <title>Patient360 集成示例</title>
  <style>
    .container {
      width: 100%;
      height: 800px;
      border: 1px solid #eaeaea;
    }
  </style>
</head>
<body>
  <h1>Patient360 集成示例</h1>
  
  <div class="container">
    <iframe src="https://yszt-prm.sh4th.com/#/patient360-embed" width="100%" height="100%" frameborder="0"></iframe>
  </div>
  
  <script>
    // 可以添加监听器来处理iframe中的授权问题
    window.addEventListener('message', function(event) {
      if (event.data.type === 'patient360-auth-required') {
        // 处理授权请求
        const iframe = document.querySelector('iframe');
        iframe.contentWindow.postMessage({
          type: 'patient360-auth',
          token: 'YOUR_ACCESS_TOKEN'
        }, '*');
      }
    });
  </script>
</body>
</html>
```

### 使用辅助函数创建 iframe

```html
<!DOCTYPE html>
<html>
<head>
  <title>Patient360 集成示例</title>
  <script src="path/to/patient360/export.js"></script>
</head>
<body>
  <h1>Patient360 集成示例</h1>
  
  <div id="patient360-container"></div>
  
  <script>
    // 创建并挂载 iframe
    window.createPatient360Iframe('#patient360-container', {
      height: '700px',
      src: 'https://yszt-prm.sh4th.com/#/patient360-embed',
      auth: {
        token: 'YOUR_ACCESS_TOKEN' // 请从管理员处获取
      }
    });
  </script>
</body>
</html>
```

### 在 Vue 项目中使用

```javascript
import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import { Patient360 } from 'path/to/patient360/export.js'

Vue.use(ElementUI)

new Vue({
  el: '#app',
  data: {
    authToken: 'YOUR_ACCESS_TOKEN' // 请从管理员处获取
  },
  components: {
    Patient360
  },
  template: '<Patient360 :auth-token="authToken" />'
})
``` 
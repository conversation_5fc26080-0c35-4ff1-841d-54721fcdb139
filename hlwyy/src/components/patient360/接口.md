# 患者系统接口文档

## 接口概述

本文档提供了第三方机构向患者360视图系统推送数据的接口规范。系统支持接收患者基本信息、医疗记录、随访计划等数据。

## 基础信息

- **基础URL**: `/api/patient360`
- **请求方式**: POST
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token (详见认证部分)

## 认证

所有API请求需要在HTTP请求头中包含认证Token:

```
Authorization: Bearer {your_token}
```

获取Token请联系系统管理员。

## 接口列表

### 1. 推送患者基本信息

**接口路径**: `/api/patient360/patient-info`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patients | Array | 是 | 患者信息数组 |

**患者对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| patientName | String | 是 | 患者姓名 |
| gender | String | 是 | 性别，1表示男，0表示女 |
| age | Number | 是 | 年龄 |
| phone | String | 否 | 联系电话 |
| idCard | String | 是 | 身份证号 |
| address | String | 否 | 居住地址 |
| BRXB | String | 是 | 性别代码，同gender |
| NL | Number | 是 | 年龄，同age |
| SFZH | String | 是 | 身份证号，同idCard |
| LXDH | String | 否 | 联系电话，同phone |
| BRXZ | String | 否 | 医保类型 |
| insuranceNo | String | 否 | 医保卡号 |
| livingStatus | String | 否 | 居住状态 |
| emergencyContact | Object | 否 | 紧急联系人信息 |
| emergencyContact.name | String | 否 | 紧急联系人姓名 |
| emergencyContact.phone | String | 否 | 紧急联系人电话 |
| medicalHistory | Array | 否 | 病史数组，如["高血压", "糖尿病"] |
| medications | Array | 否 | 用药数组，如["缬沙坦胶囊", "格列美脲片"] |
| currentDoctor | String | 否 | 当前签约医生姓名 |
| community | String | 否 | 签约社区名称 |
| familyDoctor | String | 否 | 家庭医生姓名 |
| doctorPhone | String | 否 | 医生联系方式 |
| manageGroup | String | 否 | 管理小组名称 |
| generalDoctor | String | 否 | 全科医生姓名 |
| specialist | String | 否 | 专科医生姓名 |
| nurse | String | 否 | 护士姓名 |

**请求示例**:

```json
{
  "patients": [
    {
      "patientId": "29",
      "patientName": "黄翠娥",
      "gender": "0",
      "age": 89,
      "phone": "",
      "idCard": "31010919360620004X",
      "address": "上海市长宁区XX路XX号",
      "BRXB": "0",
      "NL": 89,
      "SFZH": "31010919360620004X",
      "LXDH": "",
      "BRXZ": "城镇职工医保",
      "insuranceNo": "SHYB12345678",
      "livingStatus": "与家人同住",
      "emergencyContact": {
        "name": "黄小明",
        "phone": "13812345678"
      },
      "medicalHistory": ["高血压", "糖尿病"],
      "medications": ["缬沙坦胶囊", "格列美脲片"],
      "currentDoctor": "李医生",
      "community": "长宁区社区医院",
      "familyDoctor": "张医生",
      "doctorPhone": "021-12345678",
      "manageGroup": "慢病管理小组A",
      "generalDoctor": "王医生",
      "specialist": "赵医生",
      "nurse": "刘护士"
    }
  ]
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | Number | 状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.successCount | Number | 成功处理的患者数量 |
| data.failCount | Number | 处理失败的患者数量 |
| data.failItems | Array | 处理失败的患者ID列表 |

**响应示例**:

```json
{
  "code": 200,
  "message": "处理成功",
  "data": {
    "successCount": 1,
    "failCount": 0,
    "failItems": []
  }
}
```

### 2. 推送门诊记录

**接口路径**: `/api/patient360/outpatient-records`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| records | Array | 是 | 门诊记录数组 |

**门诊记录对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| visitDate | String | 是 | 就诊日期时间，格式：YYYY-MM-DD HH:mm:ss |
| opIp | String | 是 | 就诊类型，普通门诊为空，emergency表示急诊 |
| departmentName | String | 是 | 科室名称 |
| doctorName | String | 是 | 医生姓名 |
| chiefComplaint | String | 否 | 主诉 |
| diagnosis | String | 否 | 诊断 |
| preliminaryDiagnosis | String | 否 | 初步诊断，JSON字符串 |
| presentHistory | String | 否 | 现病史 |
| pastHistory | String | 否 | 既往史 |
| physicalExam | String | 否 | 体格检查 |
| plan | String | 否 | 处理计划 |
| drug | String | 否 | 药物信息，JSON字符串 |
| recipe | String | 否 | 处方信息，JSON字符串 |
| familyHistory | String | 否 | 家族史 |
| personalHistory | String | 否 | 个人史 |

**请求示例**:

```json
{
  "records": [
    {
      "patientId": "29",
      "visitDate": "2024-05-01 09:30:00",
      "opIp": "",
      "departmentName": "内科",
      "doctorName": "赵宝骏",
      "chiefComplaint": "头痛，头晕3天",
      "diagnosis": "高血压",
      "preliminaryDiagnosis": "[{\"diagName\":\"高血压\"}]",
      "presentHistory": "患者3天前无明显诱因出现头痛头晕症状",
      "pastHistory": "有高血压病史5年",
      "physicalExam": "BP 150/90mmHg",
      "plan": "1. 降压治疗\n2. 定期复查",
      "drug": "[{\"drugName\":\"缬沙坦胶囊\",\"drugSpecifications\":\"80mg*7片\",\"drugUseFrequencyName\":\"每日一次\"}]",
      "recipe": "[{\"drugName\":\"缬沙坦胶囊\",\"drugSpecifications\":\"80mg*7片\",\"drugUseOneDosage\":\"80\",\"drugUseOneDosageUnit\":\"mg\",\"drugUseFrequencyName\":\"每日一次\",\"doseWayName\":\"口服\"}]",
      "familyHistory": "父亲有高血压病史",
      "personalHistory": "无烟酒嗜好"
    }
  ]
}
```

**响应结构**与患者基本信息接口相同。

### 3. 推送住院记录

**接口路径**: `/api/patient360/inpatient-records`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| records | Array | 是 | 住院记录数组 |

**住院记录对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| recordId | String | 是 | 住院记录唯一标识 |
| admitTime | String | 是 | 入院时间，格式：YYYY-MM-DD HH:mm:ss |
| dischargeTime | String | 否 | 出院时间，格式：YYYY-MM-DD HH:mm:ss |
| dischargeDept | String | 是 | 出院科室 |
| depatDirectorDoctor | String | 否 | 科主任医生 |
| doctorName | String | 是 | 主治医生姓名 |
| dischargeDiagnosisMain | String | 否 | 出院主要诊断 |
| diagnosis | String | 否 | 诊断 |
| chiefComplaint | String | 否 | 主诉 |
| presentHistory | String | 否 | 现病史 |
| pastHistory | String | 否 | 既往史 |
| physicalExam | String | 否 | 体格检查 |
| auxiliaryExam | String | 否 | 辅助检查 |
| drug | String | 否 | 药物信息，JSON字符串 |
| sumDays | Number | 否 | 住院天数 |
| totalFee | Number | 否 | 总费用 |
| surgeryAndOpName | String | 否 | 手术名称，多个手术用逗号分隔 |
| surgeryAndOpDate | String | 否 | 手术日期，多个日期用逗号分隔，与手术名称一一对应 |
| pathologyDiag | String | 否 | 病理诊断 |
| pathologyNum | String | 否 | 病理号 |
| patientStatus | String | 否 | 患者状态，如"已出院" |

**请求示例**:

```json
{
  "records": [
    {
      "patientId": "29",
      "recordId": "IPR12345",
      "admitTime": "2024-03-01 14:30:00",
      "dischargeTime": "2024-03-10 11:00:00",
      "dischargeDept": "心内科",
      "depatDirectorDoctor": "张主任",
      "doctorName": "王医生",
      "dischargeDiagnosisMain": "心力衰竭",
      "diagnosis": "心力衰竭",
      "chiefComplaint": "胸闷气短3天",
      "presentHistory": "患者3天前出现胸闷气短症状，活动后加重",
      "pastHistory": "高血压病史10年，糖尿病史5年",
      "physicalExam": "双肺底可闻及湿罗音",
      "auxiliaryExam": "心电图：窦性心律，ST-T改变",
      "drug": "[{\"drugName\":\"呋塞米片\",\"drugSpecifications\":\"20mg*10片\",\"drugUseFrequencyName\":\"每日一次\"}]",
      "sumDays": 10,
      "totalFee": 8500.50,
      "surgeryAndOpName": "冠状动脉造影术",
      "surgeryAndOpDate": "2024-03-03 10:00:00",
      "pathologyDiag": "",
      "pathologyNum": "",
      "patientStatus": "已出院"
    }
  ]
}
```

**响应结构**与患者基本信息接口相同。

### 4. 推送随访计划

**接口路径**: `/api/patient360/followup-plans`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| plans | Array | 是 | 随访计划数组 |

**随访计划对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| id | String | 是 | 随访计划唯一标识 |
| planType | String | 是 | 随访类型 |
| planDate | String | 是 | 计划日期，格式：YYYY-MM-DD |
| status | String | 是 | 状态，如"未完成"、"已完成" |
| departmentName | String | 是 | 科室名称 |
| doctorName | String | 是 | 医生姓名 |
| description | String | 是 | 随访内容描述 |

**请求示例**:

```json
{
  "plans": [
    {
      "patientId": "29",
      "id": "F001",
      "planType": "高血压随访",
      "planDate": "2024-05-20",
      "status": "未完成",
      "departmentName": "心内科",
      "doctorName": "王强",
      "description": "测量血压、评估用药情况、生活方式指导"
    }
  ]
}
```

**响应结构**与患者基本信息接口相同。

### 5. 推送体征数据

**接口路径**: `/api/patient360/vital-signs`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| vitalSigns | Array | 是 | 体征数据数组 |

**体征数据对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| measureTime | String | 是 | 测量时间，格式：YYYY-MM-DD HH:mm:ss |
| bmi | Number | 否 | BMI指数 |
| bloodPressure | String | 否 | 血压值，格式：收缩压/舒张压，如"120/80" |
| heartRate | Number | 否 | 心率，单位：次/分钟 |
| oxygenSaturation | Number | 否 | 血氧饱和度，单位：% |
| temperature | Number | 否 | 体温，单位：℃ |
| respiratoryRate | Number | 否 | 呼吸频率，单位：次/分钟 |
| bloodSugar | Number | 否 | 血糖，单位：mmol/L |

**请求示例**:

```json
{
  "vitalSigns": [
    {
      "patientId": "29",
      "measureTime": "2024-05-01 09:30:00",
      "bmi": 23.8,
      "bloodPressure": "150/90",
      "heartRate": 85,
      "oxygenSaturation": 98,
      "temperature": 36.5,
      "respiratoryRate": 18,
      "bloodSugar": 5.6
    }
  ]
}
```

**响应结构**与患者基本信息接口相同。

### 6. 推送检验报告

**接口路径**: `/api/patient360/test-reports`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| reports | Array | 是 | 检验报告数组 |

**检验报告对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| reportId | String | 是 | 报告唯一标识 |
| reportName | String | 是 | 报告名称 |
| reportDate | String | 是 | 报告日期，格式：YYYY-MM-DD HH:mm:ss |
| items | Array | 是 | 检验项目数组 |

**检验项目对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| itemName | String | 是 | 项目名称 |
| result | String | 是 | 检验结果 |
| unit | String | 否 | 单位 |
| referenceRange | String | 否 | 参考范围 |
| abnormalFlag | String | 否 | 异常标志，H表示偏高，L表示偏低，N表示正常 |

**请求示例**:

```json
{
  "reports": [
    {
      "patientId": "29",
      "reportId": "LR12345",
      "reportName": "血常规",
      "reportDate": "2024-05-01 10:30:00",
      "items": [
        {
          "itemName": "白细胞计数",
          "result": "6.5",
          "unit": "10^9/L",
          "referenceRange": "4.0-10.0",
          "abnormalFlag": "N"
        },
        {
          "itemName": "血红蛋白",
          "result": "135",
          "unit": "g/L",
          "referenceRange": "110-160",
          "abnormalFlag": "N"
        },
        {
          "itemName": "血小板计数",
          "result": "250",
          "unit": "10^9/L",
          "referenceRange": "100-300",
          "abnormalFlag": "N"
        }
      ]
    }
  ]
}
```

**响应结构**与患者基本信息接口相同。

### 7. 推送检查报告

**接口路径**: `/api/patient360/examination-reports`

**请求方式**: POST

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| reports | Array | 是 | 检查报告数组 |

**检查报告对象结构**:

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| patientId | String | 是 | 患者唯一标识 |
| reportId | String | 是 | 报告唯一标识 |
| reportName | String | 是 | 报告名称 |
| reportDate | String | 是 | 报告日期，格式：YYYY-MM-DD HH:mm:ss |
| examCategName | String | 是 | 检查类别名称，如"CT"、"MRI"、"超声" |
| examDesc | String | 否 | 检查描述 |
| examResult | String | 否 | 检查结果 |
| examConclusion | String | 否 | 检查结论 |
| imageUrls | Array | 否 | 影像URL数组 |

**请求示例**:

```json
{
  "reports": [
    {
      "patientId": "29",
      "reportId": "ER12345",
      "reportName": "胸部CT",
      "reportDate": "2024-05-02 14:30:00",
      "examCategName": "CT",
      "examDesc": "平扫+增强",
      "examResult": "双肺野未见明显异常密度影，纵隔内未见明显肿大淋巴结",
      "examConclusion": "未见明显异常",
      "imageUrls": ["http://example.com/images/12345-1.jpg", "http://example.com/images/12345-2.jpg"]
    }
  ]
}
```

**响应结构**与患者基本信息接口相同。

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或Token无效 |
| 403 | 无权访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有日期时间字段应使用ISO 8601格式：YYYY-MM-DD HH:mm:ss
2. 接口有请求大小限制，建议单次请求数据量不超过100条
3. 推送数据时应确保患者ID的一致性
4. 数据更新采用增量更新方式，仅推送新增或变更的数据
5. 接口调用频率限制为每分钟不超过60次 
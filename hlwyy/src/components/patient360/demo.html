<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Patient360 集成演示</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Aria<PERSON>, sans-serif;
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eaeaea;
    }
    
    h1 {
      color: #333;
    }
    
    #patient360-container {
      margin-top: 20px;
    }
    
    .demo-controls {
      margin: 20px 0;
      padding: 15px;
      background: #f5f7fa;
      border-radius: 4px;
    }
    
    button {
      padding: 8px 15px;
      background: #409EFF;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    
    button:hover {
      background: #66b1ff;
    }
    
    pre {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      overflow: auto;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Patient360 集成演示</h1>
    <p>本页面演示如何在普通HTML页面中集成Patient360组件</p>
  </div>
  
  <div class="demo-controls">
    <button id="load-mock-data">加载模拟数据</button>
    <button id="reset-data">重置</button>
  </div>
  
  <div id="output"></div>
  
  <div id="patient360-container"></div>
  
  <!-- 引入Vue和Element UI -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/index.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/theme-chalk/index.css">
  
  <!-- 引入Patient360导出脚本 -->
  <script src="./export.js"></script>
  
  <script>
    // 输出日志到页面
    function log(message) {
      const output = document.getElementById('output');
      const pre = document.createElement('pre');
      pre.textContent = typeof message === 'object' ? JSON.stringify(message, null, 2) : message;
      output.appendChild(pre);
    }
    
    // 模拟数据
    const mockPatients = [
      {
        patientId: '2',
        patientName: '张三',
        gender: '1',
        age: 45,
        phone: '13812345678',
        idCard: '310101197801012345',
        address: '上海市黄浦区南京路123号',
        medicalHistory: ['高血压'],
        medications: ['降压药'],
        recentVisits: [
          {
            date: '2024-03-15',
            type: '初诊',
            diagnosis: '高血压',
            doctor: '李医生'
          }
        ],
        vitalSigns: {
          bloodPressure: '145/90',
          heartRate: '80',
          bloodSugar: '5.8'
        }
      },
      {
        patientId: '3',
        patientName: '李四',
        gender: '1',
        age: 56,
        phone: '13998765432',
        idCard: '310101196801234567',
        address: '上海市静安区静安寺路789号',
        medicalHistory: ['冠心病', '高血脂'],
        medications: ['阿司匹林', '他汀类药物'],
        recentVisits: [
          {
            date: '2024-04-01',
            type: '复诊',
            diagnosis: '冠心病',
            doctor: '王医生'
          }
        ],
        vitalSigns: {
          bloodPressure: '135/85',
          heartRate: '72',
          bloodSugar: '5.5'
        }
      }
    ];
    
    // 初始化Patient360组件
    let patient360Instance = window.mountPatient360('#patient360-container', {
      onSelectPatient: function(patient) {
        log('选中患者:');
        log(patient);
      }
    });
    
    // 加载模拟数据按钮事件
    document.getElementById('load-mock-data').addEventListener('click', function() {
      if (patient360Instance) {
        patient360Instance.tableData = mockPatients;
        patient360Instance.total = mockPatients.length;
        log('已加载模拟数据');
      }
    });
    
    // 重置按钮事件
    document.getElementById('reset-data').addEventListener('click', function() {
      // 销毁旧实例
      if (patient360Instance) {
        patient360Instance.$destroy();
        document.getElementById('patient360-container').innerHTML = '';
      }
      
      // 创建新实例
      patient360Instance = window.mountPatient360('#patient360-container', {
        onSelectPatient: function(patient) {
          log('选中患者:');
          log(patient);
        }
      });
      
      log('已重置Patient360组件');
    });
    
    // 初始日志
    log('Patient360组件已加载');
  </script>
</body>
</html> 
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Patient360 页面</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, sans-serif;
    }
    
    #app {
      width: 100%;
      height: 100%;
    }
  </style>
  <!-- 引入Vue和Element UI -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/index.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/theme-chalk/index.css">
</head>
<body>
  <div id="app"></div>
  
  <script>
    // 重定向到实际的应用页面
    window.location.href = '/';
  </script>
</body>
</html> 
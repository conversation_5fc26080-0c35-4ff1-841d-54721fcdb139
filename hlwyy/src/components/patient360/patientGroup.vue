<script>
export default {
  data() {
    return {
      groupId: null,
      groupName: '',
      patients: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      
      // 表单搜索数据
      searchForm: {
        status: '',
        name: '',
        idCard: '',
        phone: '',
        disease: ''
      },
      
      // 导入对话框
      importDialogVisible: false,
      
      // 批量导入患者结果
      importResult: {
        total: 0,
        successful: 0,
        failed: 0
      },
      
      // 新增患者对话框
      addPatientDialogVisible: false,
      
      // 患者表单
      patientForm: {
        name: '',
        gender: '',
        age: '',
        phone: '',
        idCard: '',
        disease: '',
        joinMethod: '院内渠道'
      },
      
      // 表单验证规则
      patientRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },

      // 选中的患者id列表
      selectedPatients: []
    }
  },
  created() {
    // 从路由参数获取分组ID和名称
    this.groupId = this.$route.params.id;
    this.groupName = this.$route.params.name;
    // 加载分组内的患者列表
    this.fetchPatients();
  },
  methods: {
    fetchPatients() {
      this.loading = true;
      
      const params = {
        page: this.currentPage,
        size: this.pageSize,
        groupId: this.groupId,
        status: this.searchForm.status,
        name: this.searchForm.name,
        idCard: this.searchForm.idCard,
        phone: this.searchForm.phone,
        disease: this.searchForm.disease
      };
      
      this.$axios.get(`/api/patient/group/${this.groupId}/patients`, { params })
        .then(res => {
          if (res.data.code === 200) {
            this.patients = res.data.data.records;
            this.total = res.data.data.total;
          } else {
            this.$message.error(res.data.message || '获取患者列表失败');
          }
        })
        .catch(err => {
          console.error('获取患者列表失败', err);
          this.$message.error('获取患者列表失败，请稍后重试');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchPatients();
    },
    
    // 处理当前页变化
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchPatients();
    },
    
    // 处理表单搜索
    handleSearch() {
      this.currentPage = 1;
      this.fetchPatients();
    },
    
    // 重置搜索表单
    resetSearchForm() {
      this.searchForm = {
        status: '',
        name: '',
        idCard: '',
        phone: '',
        disease: ''
      };
      this.handleSearch();
    },
    
    // 选择行变化
    handleSelectionChange(selection) {
      this.selectedPatients = selection.map(item => item.id);
    },
    
    // 显示新增患者对话框
    showAddPatientDialog() {
      this.patientForm = {
        name: '',
        gender: '',
        age: '',
        phone: '',
        idCard: '',
        disease: '',
        joinMethod: '院内渠道'
      };
      this.addPatientDialogVisible = true;
    },
    
    // 提交患者表单
    submitPatientForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 先创建患者
          this.$axios.post('/api/patient', this.patientForm)
            .then(res => {
              if (res.data.code === 200) {
                const patientId = res.data.data;
                // 然后将患者添加到分组
                return this.$axios.post(`/api/patient/group/${this.groupId}/patients`, [patientId], {
                  params: { joinMethod: this.patientForm.joinMethod }
                });
              } else {
                this.$message.error(res.data.message || '创建患者失败');
                return Promise.reject('创建患者失败');
              }
            })
            .then(res => {
              if (res.data.code === 200) {
                this.$message.success('添加患者成功');
                this.addPatientDialogVisible = false;
                this.fetchPatients();
              } else {
                this.$message.error(res.data.message || '添加患者到分组失败');
              }
            })
            .catch(err => {
              if (err !== '创建患者失败') {
                console.error('添加患者失败', err);
                this.$message.error('添加患者失败，请稍后重试');
              }
            });
        } else {
          return false;
        }
      });
    },
    
    // 从分组移除患者
    removePatientFromGroup(patientId) {
      this.$confirm('确认将该患者从分组中移除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.delete(`/api/patient/group/${this.groupId}/patients`, {
          data: [patientId]
        })
          .then(res => {
            if (res.data.code === 200) {
              this.$message.success('移除成功');
              this.fetchPatients();
            } else {
              this.$message.error(res.data.message || '移除失败');
            }
          })
          .catch(err => {
            console.error('移除患者失败', err);
            this.$message.error('移除患者失败，请稍后重试');
          });
      }).catch(() => {
        this.$message.info('已取消移除');
      });
    },
    
    // 批量移除患者
    batchRemovePatients() {
      if (this.selectedPatients.length === 0) {
        this.$message.warning('请先选择要移除的患者');
        return;
      }
      
      this.$confirm(`确认将选中的 ${this.selectedPatients.length} 名患者从分组中移除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.delete(`/api/patient/group/${this.groupId}/patients`, {
          data: this.selectedPatients
        })
          .then(res => {
            if (res.data.code === 200) {
              this.$message.success('批量移除成功');
              this.fetchPatients();
              this.selectedPatients = [];
            } else {
              this.$message.error(res.data.message || '批量移除失败');
            }
          })
          .catch(err => {
            console.error('批量移除患者失败', err);
            this.$message.error('批量移除患者失败，请稍后重试');
          });
      }).catch(() => {
        this.$message.info('已取消移除');
      });
    },
    
    // 导入患者对话框
    showImportDialog() {
      this.importDialogVisible = true;
      this.importResult = {
        total: 0,
        successful: 0,
        failed: 0
      };
    },
    
    // 处理文件上传
    handleFileUpload(file) {
      // 这里应该有处理文件上传的逻辑
      // 模拟导入结果
      setTimeout(() => {
        this.importResult = {
          total: 10,
          successful: 8,
          failed: 2
        };
      }, 1000);
      return false; // 阻止默认上传行为
    },
    
    // 返回患者列表
    goBack() {
      this.$router.push('/patient/groups');
    }
  }
}
</script> 
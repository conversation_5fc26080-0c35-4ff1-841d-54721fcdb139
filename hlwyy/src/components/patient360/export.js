import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import Patient360Component from './index.vue'

// Make sure ElementUI is installed
Vue.use(ElementUI)

/**
 * Mount the Patient360 component to the specified DOM element
 * This retains the original data from the component
 * @param {string|HTMLElement} el - DOM element or selector where to mount the component
 * @param {object} options - Configuration options
 * @param {function} options.onSelectPatient - Callback when a patient is selected
 * @returns {Vue} Vue instance
 */
export function mountPatient360(el, options = {}) {
  const container = typeof el === 'string' ? document.querySelector(el) : el
  
  if (!container) {
    console.error('Target element not found')
    return null
  }
  
  const ComponentClass = Vue.extend(Patient360Component)
  const instance = new ComponentClass({
    propsData: {}
  })
  
  // Mount the component
  instance.$mount()
  container.appendChild(instance.$el)
  
  // Add listener for patient selection if callback provided
  if (typeof options.onSelectPatient === 'function') {
    const originalViewPatientDashboard = instance.viewPatientDashboard
    instance.viewPatientDashboard = function(patient) {
      options.onSelectPatient(patient)
      originalViewPatientDashboard.call(instance, patient)
    }
  }
  
  return instance
}

/**
 * Create a Patient360 component that can be used in Vue applications
 * @returns {Object} Vue component
 */
export const Patient360 = Patient360Component

/**
 * Create an iframe that loads the Patient360 component
 * This is the simplest integration method for third parties
 * @param {string|HTMLElement} el - DOM element or selector where to mount the iframe
 * @param {object} options - Configuration options
 * @param {string} options.width - Width of the iframe (default: '100%')
 * @param {string} options.height - Height of the iframe (default: '600px')
 * @param {string} options.src - Source URL for the iframe (default: auto-detected)
 * @returns {HTMLIFrameElement} The created iframe element
 */
export function createPatient360Iframe(el, options = {}) {
  const container = typeof el === 'string' ? document.querySelector(el) : el
  
  if (!container) {
    console.error('Target element not found')
    return null
  }
  
  // 确定iframe的源URL
  let iframeSrc = options.src || '/home/<USER>'
  
  // 如果未提供src且不是绝对URL，尝试构建基于当前域的URL
  if (!options.src && !iframeSrc.startsWith('http')) {
    // 在生产环境使用iframe.html，在开发环境使用根路径
    const isDevMode = window.location.hostname === 'localhost' || 
                      window.location.hostname === '127.0.0.1'
    
    if (isDevMode) {
      iframeSrc = '/home/<USER>'
    } else {
      const currentPath = window.location.pathname
      const basePath = currentPath.substring(0, currentPath.lastIndexOf('/'))
      iframeSrc = `${basePath}/iframe.html`
    }
  }
  
  // 创建iframe
  const iframe = document.createElement('iframe')
  iframe.width = options.width || '100%'
  iframe.height = options.height || '600px'
  iframe.style.border = 'none'
  iframe.src = iframeSrc
  
  // 添加到容器
  container.appendChild(iframe)
  
  return iframe
}

export default {
  mountPatient360,
  Patient360,
  createPatient360Iframe
} 
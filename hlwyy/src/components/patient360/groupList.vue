<script>
export default {
  data() {
    return {
      tableData: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchName: '',
      dialogVisible: false,
      form: {
        name: '',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入组名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ]
      },
      formTitle: '新建分组',
      editingId: null,
    }
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 获取用户信息并编码处理
    getUserHeaders() {
      const userId = sessionStorage.getItem('docId') || '1';
      const userName = sessionStorage.getItem('doctorName') || 'System Admin';
      
      // 如果userId是UUID格式，使用默认数字ID
      let finalUserId = userId;
      if (userId && userId.includes('-')) {
        finalUserId = '1'; // 使用默认用户ID
      }
      
      return {
        'X-User-Id': finalUserId,
        'X-User-Name': encodeURIComponent(userName)
      };
    },
    fetchData() {
      this.loading = true;
      // 调用分页获取患者分组列表API
      const params = {
        page: this.currentPage,
        size: this.pageSize,
        name: this.searchName
      };
      
      this.$axios.get('/api/patient/group/page', { 
        params,
        headers: this.getUserHeaders()
      })
        .then(res => {
          if (res.data.code === 200) {
            this.tableData = res.data.data.records;
            this.total = res.data.data.total;
          } else {
            this.$message.error(res.data.message || '获取分组列表失败');
          }
        })
        .catch(err => {
          console.error('获取分组列表失败', err);
          this.$message.error('获取分组列表失败，请稍后重试');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchData();
    },
    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },
    resetSearch() {
      this.searchName = '';
      this.handleSearch();
    },
    addGroup() {
      this.formTitle = '新建分组';
      this.form = {
        name: '',
        description: ''
      };
      this.editingId = null;
      this.dialogVisible = true;
    },
    editGroup(row) {
      this.formTitle = '编辑分组';
      this.form = {
        name: row.name,
        description: row.description
      };
      this.editingId = row.id;
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.editingId) {
            // 调用更新分组API
            this.$axios.put(`/api/patient/group/${this.editingId}`, this.form, {
              headers: this.getUserHeaders()
            })
              .then(res => {
                if (res.data.code === 200) {
                  this.$message.success('更新分组成功');
                  this.dialogVisible = false;
                  this.fetchData();
                } else {
                  this.$message.error(res.data.message || '更新分组失败');
                }
              })
              .catch(err => {
                console.error('更新分组失败', err);
                this.$message.error('更新分组失败，请稍后重试');
              });
          } else {
            // 调用创建分组API
            this.$axios.post('/api/patient/group', this.form, {
              headers: this.getUserHeaders()
            })
              .then(res => {
                if (res.data.code === 200) {
                  this.$message.success('创建分组成功');
                  this.dialogVisible = false;
                  this.fetchData();
                } else {
                  this.$message.error(res.data.message || '创建分组失败');
                }
              })
              .catch(err => {
                console.error('创建分组失败', err);
                this.$message.error('创建分组失败，请稍后重试');
              });
          }
        } else {
          return false;
        }
      });
    },
    deleteGroup(id) {
      this.$confirm('此操作将永久删除该分组, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除分组API
        this.$axios.delete(`/api/patient/group/${id}`, {
          headers: this.getUserHeaders()
        })
          .then(res => {
            if (res.data.code === 200) {
              this.$message.success('删除成功');
              this.fetchData();
            } else {
              this.$message.error(res.data.message || '删除失败');
            }
          })
          .catch(err => {
            console.error('删除分组失败', err);
            this.$message.error('删除分组失败，请稍后重试');
          });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 查看组内患者
    viewPatients(id, name) {
      // 跳转到患者分组详情页面，携带分组ID和名称
      this.$router.push({
        name: 'patientGroup',
        params: { id, name }
      });
    }
  }
}
</script> 
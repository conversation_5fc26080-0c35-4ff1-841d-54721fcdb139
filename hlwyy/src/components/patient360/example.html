<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Patient360 嵌入示例</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    h1 {
      color: #333;
      border-bottom: 1px solid #eaeaea;
      padding-bottom: 10px;
    }
    
    .intro {
      margin-bottom: 20px;
      color: #666;
    }
    
    .example-container {
      margin-top: 20px;
      border: 1px solid #eaeaea;
      border-radius: 4px;
      overflow: hidden;
      height: 700px;
    }
    
    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .code-sample {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
    }
    
    pre {
      margin: 0;
      white-space: pre-wrap;
    }
    
    .tabs {
      margin-top: 20px;
    }
    
    .tab-buttons {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
    }
    
    .tab-button {
      padding: 8px 15px;
      background: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .tab-button.active {
      background: #409EFF;
      color: white;
      border-color: #409EFF;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <h1>Patient360 嵌入示例</h1>
  
  <div class="intro">
    <p>这个示例展示了如何在第三方应用中嵌入 Patient360 组件。下面的框中展示的是直接嵌入的 Patient360 组件，包含组件原有的所有数据。</p>
    <p><strong>注意</strong>：在开发环境中，请将iframe指向运行中的Vue应用地址（例如 http://localhost:8080/）</p>
  </div>
  
  <div class="tabs">
    <div class="tab-buttons">
      <div class="tab-button active" onclick="switchTab('dev')">开发环境</div>
      <div class="tab-button" onclick="switchTab('prod')">生产环境</div>
    </div>
    
    <div class="tab-content active" id="tab-dev">
      <div class="example-container">
        <iframe src="http://localhost:8080/" id="dev-iframe"></iframe>
      </div>
      
      <div class="code-sample">
        <h3>开发环境嵌入代码：</h3>
        <pre><code>&lt;iframe src="http://localhost:8080/" width="100%" height="700px" frameborder="0"&gt;&lt;/iframe&gt;</code></pre>
        <p>请将端口号改为您实际运行的Vue应用端口</p>
      </div>
    </div>
    
    <div class="tab-content" id="tab-prod">
      <div class="example-container">
        <iframe src="./iframe.html" id="prod-iframe"></iframe>
      </div>
      
      <div class="code-sample">
        <h3>生产环境嵌入代码：</h3>
        <pre><code>&lt;iframe src="https://你的域名/patient360/iframe.html" width="100%" height="700px" frameborder="0"&gt;&lt;/iframe&gt;</code></pre>
      </div>
    </div>
  </div>
  
  <div class="code-sample">
    <h3>JavaScript 嵌入代码：</h3>
    <pre><code>// 引入脚本
import { createPatient360Iframe } from 'path/to/patient360/export.js';

// 创建并挂载 iframe（开发环境）
const iframe = createPatient360Iframe('#container', {
  width: '100%',
  height: '700px',
  src: 'http://localhost:8080/' // 开发环境中指定实际运行的Vue应用地址
});</code></pre>
  </div>
  
  <script>
    function switchTab(tabId) {
      // 隐藏所有内容并取消所有按钮激活状态
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
      });
      
      document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
      });
      
      // 显示选中的内容并激活按钮
      document.getElementById('tab-' + tabId).classList.add('active');
      document.querySelector(`.tab-button[onclick="switchTab('${tabId}')"]`).classList.add('active');
    }
    
    // 自动检测并更新iframe的端口
    window.addEventListener('load', function() {
      const currentPort = window.location.port;
      if (currentPort) {
        const devIframe = document.getElementById('dev-iframe');
        const newSrc = 'http://localhost:' + currentPort + '/';
        devIframe.src = newSrc;
        
        const codeExample = document.querySelector('#tab-dev .code-sample pre code');
        codeExample.textContent = codeExample.textContent.replace('8080', currentPort);
      }
    });
  </script>
</body>
</html> 
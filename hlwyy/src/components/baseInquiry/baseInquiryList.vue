<template>
    <div class="inq-list">
         <div class="list-main-head">
            <div  class="fieldName" >
                <label class="shopName" >患者姓名:</label>
                <el-input size="small" v-model="patientName" clearable  class="shopValue" placeholder="患者姓名"></el-input>
            </div>
            <div  class="fieldName" >
                <label class="shopName" >会诊开始时间:</label>
                <el-date-picker
                    v-model="startTime"
                    type="date"

                    size="small"
                    value-format="yyyy-MM-dd"
                    class="shopValue"
                    :picker-options="pickerOptions0">
                </el-date-picker>
                <span style="float:left;margin-right:10px;">至</span>
                <el-date-picker
                    v-model="endTime"
                    type="date"

                    size="small"
                    value-format="yyyy-MM-dd"
                    class="shopValue"
                    :picker-options="pickerOptions1">
                </el-date-picker>
            </div>
            <div  class="fieldName" >
                <label class="shopName" >状态:</label>
                <el-select size="small" clearable class="shopValue" v-model="currentStatus" placeholder="启用情况">
                    <el-option label="全部状态" value="-1"></el-option>
                    <el-option label="等待会诊" value="58"></el-option>
                    <el-option label="会诊结束" value="57"></el-option>
                </el-select>
            </div>

            <div class="fieldName">
                <el-button type="primary" size="small" @click="searchList">搜索</el-button>
                <el-button type="primary" size="small" @click="clearParams">清除</el-button>
            </div>
        </div>
        <el-table :data="iquList" border style="width: 100%">
            <el-table-column label="基层医生名称" prop="userName" width="150">
            </el-table-column>
            <el-table-column label="患者名称" prop="patientName" width="150">
            </el-table-column>
            <el-table-column label="性别">
                <template slot-scope="scope">
                    <span v-if='scope.row.sex==1'>男</span>
                    <span v-if='scope.row.sex==2'>女</span>
                </template>
            </el-table-column>
            <el-table-column label="年龄" prop="age"></el-table-column>
            <el-table-column label="会诊时间" prop="consultStartAt">
                <template slot-scope="scope">
                    <span v-if="scope.row.consultStartAt">{{scope.row.consultStartAt}} - {{scope.row.consutleEndAt}}</span>
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="currentStatusText">
            </el-table-column>
            <el-table-column label="操作" >
                <template slot-scope="scope">
                    <el-button size="small" type="text" @click="showChat(scope.row)" v-if="scope.row.conStatus == '58'">回复</el-button>
                    <el-button size="small" type="text" @click="checkFbDetail(scope.row)" v-else>会诊记录</el-button>

                </template>
            </el-table-column>
        </el-table>
        <!--分页-->
        <div class="pagination">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :current-page="pageNo" :page-sizes="[10, 20, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="pageSize">
            </el-pagination>
        </div>
    </div>
</template>
<script>
import {commonAjax,imgview} from '../../api/api.js';
import patientAvatar from '@/assets/img/pat_avatar.png';
export default {
    data() {
        let that = this;
        return {
            pickerOptions0: {
                disabledDate(time) {
                    if(new Date(that.endTime).getTime()){
                        return time.getTime() > (new Date(that.endTime)).getTime() - 1*60*60*24*1000;
                    }

                }
            },
            pickerOptions1: {
                disabledDate(time) {
                    if(new Date(that.startTime).getTime()){
                        return time.getTime() < (new Date(that.startTime)).getTime()
                    }
                }
            },
            iquList:[],
            currentStatusList:[],
            patientName:"",
            startTime:"",
            endTime:"",
            currentStatus:"-1",
            total:0,
            pageNo:1,
            pageSize:10,
            doctorId:sessionStorage.getItem("docId"),
            orgId:sessionStorage.getItem("orgId"),
        }
    },
    methods:{
        //获取字典
        dictionaryRequest() {
            let arr = ["cfs.dic.ih_consultStatus"];
            commonAjax("cas.multipleDictionaryService", "findDic", '[' + JSON.stringify(arr) + ']').then(res => {
                if (res.code == 200) {
                    this.currentStatusList = res.body[0].items;
                    for(let i = 0; i<this.currentStatusList.length; i++) {
                        if(this.currentStatusList[i].key == '51') {
                            this.currentStatusList.splice(i,1)
                        }
                    }
                } else {
                    this.$message({
                        type: 'error',
                        message: res.msg
                    });
                }
            });
        },
        //获取全部会诊列表
        getBaseRecord() {
            let params = {
                "itemCode":"03",   //{会诊类型(01图文，02视频，03咨询)
                "doctorId":this.doctorId,    //
                "orgId":this.orgId,
                "patientName":this.patientName,
                "startTime":this.startTime,
                "endTime":this.endTime,
                "currentStatus":this.currentStatus,
                "pageNo":this.pageNo,
                "pageSize":this.pageSize,
            }
            commonAjax("cas.foshan_doctorConsultService","queryOnlineProject",[params]).then(res => {
                if(res.code == 200) {
                    this.iquList = res.body.data;
                    this.total = res.body.total;
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    })
                }
            })
        },
        //回复咨询
        showChat(row) {
            sessionStorage.setItem("accId",row.patientAccId)
            sessionStorage.setItem("buzType","60");   //30是图文，40是视频，60是会诊
            sessionStorage.setItem("buzId",row.orderDetailId);
            sessionStorage.setItem("patientName",row.userName);
            let avatarUrl = row.avatarFileId?imgview + row.avatarFileId:patientAvatar; //本地处理用户头像
            sessionStorage.setItem("avatarUrl",avatarUrl);
            let params = {
                "orderDetailId":row.orderDetailId,    //必传（数值类型）咨询订单主键
                "itemCode":"03",    //必传，咨询类型：01图文 02视频
                "userType":"10" //必传，用户类型 10 医生 11 患者

            }
            commonAjax("cas.foshan_consultProcessService","startConsult",[params]).then(res => {
                if(res && res.code == 200) {
                    window.yunXin.openChatBox(row.patientAccId,"p2p",'03');
                } else {
                    this.$message({
                        type:"error",
                        message:res.msg,
                    });
                    return;
                }
            })
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getBaseRecord();
        },
        handleCurrentChange(val) {
            this.pageNo = val;
            this.getBaseRecord();
        },
        searchList() {
            this.getBaseRecord();
        },
        clearParams() {
            this.patientName = "";
            this.startTime = "";
            this.endTime = "";
            this.currentStatus = "-1";
            this.pageNo = 1;
            this.pageSize = 10;
            this.getBaseRecord();
        },
        //跳转咨询记录详情
        checkFbDetail(row) {
            sessionStorage.setItem("recordPatientAccId",row.patientAccId);
            sessionStorage.setItem("orderDetailId",row.orderDetailId);
            this.$router.push("iquRecord");
        }
    },
    mounted() {
        this.dictionaryRequest();
        this.getBaseRecord();
    }
}
</script>

<style lang="scss" scoped>
.inq-list{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;
    .pagination {
        margin-top: 20px;
    }
    .list-main-head{
        width: 80%;
        // padding: 20px;
        background: white;
        float: left;
        // border-radius: 10px;
        margin-bottom: 10px;
        display: flex;
        flex-wrap: wrap;
        .fieldName{
            margin-bottom: 20px;
            margin-right: 10px;
            line-height: 30px;
            .shopName{
                float: left;
                height:30px;
                // width:100px;
                line-height: 30px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue{
                width:180px;
                height:30px;
                float: left;
                margin-right:10px;
            }
        }
    }
}
</style>


<template>
    <div class="inq-list">
        <el-table :data="iquList" border style="width: 100%">
            <el-table-column label="发言人" prop="userNane" width="150">
            </el-table-column>
            <el-table-column label="发言时间" prop="sexText"></el-table-column>
            <el-table-column label="发言内容" prop="age"></el-table-column>
        </el-table>
        <!--分页-->
        <div class="pagination">
            <el-pagination layout="total, sizes, prev, pager, next, jumper" :total="total" :current-page="pageNo" :page-sizes="[10, 20, 50]" @size-change="handleSizeChange" @current-change="handleCurrentChange" :page-size="pageSize">
            </el-pagination>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        let that = this;
        return {
            pickerOptions0: {
                disabledDate(time) {
                    if(new Date(that.searchContent.endDate).getTime()){
                        return time.getTime() > (new Date(that.searchContent.endDate)).getTime()
                    }

                }
            },
            pickerOptions1: {
                disabledDate(time) {
                    if(new Date(that.searchContent.startDate).getTime()){
                        return time.getTime() < (new Date(that.searchContent.startDate)).getTime() - 1*60*60*24*1000;
                    }
                }
            },
            iquList:[],
            total:0,
            pageNo:1,
            
            pageSize:10,
        }
    },
    methods:{

    },
    mounted() {
        
    }
}
</script>

<style lang="scss" scoped>
.inq-list{
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: #fff;

    .list-main-head{
        width: 80%;
        // padding: 20px;
        background: white;
        float: left;
        // border-radius: 10px;
        margin-bottom: 10px;
        display: flex;
        flex-wrap: wrap;
        .fieldName{
            margin-bottom: 20px;
            margin-right: 10px;
            .shopName{
                float: left;
                height:30px;
                // width:100px;
                line-height: 30px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue{
                width:180px;
                height:30px;
                float: left;
                margin-right:10px;
            }
        }
    }
}
</style>


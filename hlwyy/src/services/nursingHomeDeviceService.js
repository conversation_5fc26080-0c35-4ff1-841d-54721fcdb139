import { commonAjax } from '../api/api.js';

/**
 * 养老机构设备数据服务
 */
class NursingHomeDeviceService {
    
    /**
     * 获取设备数据列表
     * @param {Object} params 查询参数
     * @returns {Promise}
     */
    getDeviceDataList(params) {
        return commonAjax("cas.nursingHome_deviceService", "getDeviceDataList", [params]);
    }
    
    /**
     * 获取设备统计信息
     * @returns {Promise}
     */
    getDeviceStats() {
        return commonAjax("cas.nursingHome_deviceService", "getDeviceStats", []);
    }
    
    /**
     * 获取设备列表
     * @returns {Promise}
     */
    getDeviceList() {
        return commonAjax("cas.nursingHome_deviceService", "getDeviceList", []);
    }
    
    /**
     * 同步所有设备数据
     * @returns {Promise}
     */
    syncAllDevices() {
        return commonAjax("cas.nursingHome_deviceService", "syncAllDevices", []);
    }
    
    /**
     * 同步单个设备数据
     * @param {string} deviceId 设备ID
     * @returns {Promise}
     */
    syncSingleDevice(deviceId) {
        return commonAjax("cas.nursingHome_deviceService", "syncSingleDevice", [{ deviceId }]);
    }
    
    /**
     * 批量上传数据到云端
     * @param {Array} dataIds 数据ID数组
     * @returns {Promise}
     */
    batchUploadData(dataIds) {
        return commonAjax("cas.nursingHome_deviceService", "batchUploadData", [{ dataIds }]);
    }
    
    /**
     * 上传单条数据
     * @param {number} dataId 数据ID
     * @returns {Promise}
     */
    uploadSingleData(dataId) {
        return commonAjax("cas.nursingHome_deviceService", "uploadSingleData", [{ dataId }]);
    }
    
    /**
     * 重新检查数据质量
     * @param {number} dataId 数据ID
     * @returns {Promise}
     */
    recheckDataQuality(dataId) {
        return commonAjax("cas.nursingHome_deviceService", "recheckDataQuality", [{ dataId }]);
    }
    
    /**
     * 添加新设备
     * @param {Object} deviceInfo 设备信息
     * @returns {Promise}
     */
    addDevice(deviceInfo) {
        return commonAjax("cas.nursingHome_deviceService", "addDevice", [deviceInfo]);
    }
    
    /**
     * 更新设备信息
     * @param {Object} deviceInfo 设备信息
     * @returns {Promise}
     */
    updateDevice(deviceInfo) {
        return commonAjax("cas.nursingHome_deviceService", "updateDevice", [deviceInfo]);
    }
    
    /**
     * 删除设备
     * @param {string} deviceId 设备ID
     * @returns {Promise}
     */
    removeDevice(deviceId) {
        return commonAjax("cas.nursingHome_deviceService", "removeDevice", [{ deviceId }]);
    }
    
    /**
     * 获取设备详细信息
     * @param {string} deviceId 设备ID
     * @returns {Promise}
     */
    getDeviceDetail(deviceId) {
        return commonAjax("cas.nursingHome_deviceService", "getDeviceDetail", [{ deviceId }]);
    }
    
    /**
     * 导出设备数据
     * @param {Object} params 导出参数
     * @returns {Promise}
     */
    exportDeviceData(params) {
        return commonAjax("cas.nursingHome_deviceService", "exportDeviceData", [params]);
    }
    
    /**
     * 获取数据采集配置
     * @returns {Promise}
     */
    getCollectionConfig() {
        return commonAjax("cas.nursingHome_deviceService", "getCollectionConfig", []);
    }
    
    /**
     * 更新数据采集配置
     * @param {Object} config 配置信息
     * @returns {Promise}
     */
    updateCollectionConfig(config) {
        return commonAjax("cas.nursingHome_deviceService", "updateCollectionConfig", [config]);
    }
    
    /**
     * 获取上传日志
     * @param {Object} params 查询参数
     * @returns {Promise}
     */
    getUploadLogs(params) {
        return commonAjax("cas.nursingHome_deviceService", "getUploadLogs", [params]);
    }
    
    /**
     * 重试失败的上传
     * @param {Array} logIds 日志ID数组
     * @returns {Promise}
     */
    retryFailedUploads(logIds) {
        return commonAjax("cas.nursingHome_deviceService", "retryFailedUploads", [{ logIds }]);
    }
    
    /**
     * 实时数据监控
     * @param {Array} deviceIds 设备ID数组
     * @returns {Promise}
     */
    getRealTimeData(deviceIds) {
        return commonAjax("cas.nursingHome_deviceService", "getRealTimeData", [{ deviceIds }]);
    }
    
    /**
     * 设备健康检查
     * @returns {Promise}
     */
    performHealthCheck() {
        return commonAjax("cas.nursingHome_deviceService", "performHealthCheck", []);
    }
    
    /**
     * 获取设备告警信息
     * @param {Object} params 查询参数
     * @returns {Promise}
     */
    getDeviceAlerts(params) {
        return commonAjax("cas.nursingHome_deviceService", "getDeviceAlerts", [params]);
    }
    
    /**
     * 处理设备告警
     * @param {number} alertId 告警ID
     * @param {string} action 处理动作
     * @returns {Promise}
     */
    handleDeviceAlert(alertId, action) {
        return commonAjax("cas.nursingHome_deviceService", "handleDeviceAlert", [{ alertId, action }]);
    }
}

// 创建单例实例
const nursingHomeDeviceService = new NursingHomeDeviceService();

export default nursingHomeDeviceService; 
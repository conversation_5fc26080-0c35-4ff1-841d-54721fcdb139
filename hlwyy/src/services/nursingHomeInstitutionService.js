import axios from 'axios';

// 配置axios默认值
axios.defaults.headers.post['Content-Type'] = 'application/json';

/**
 * 养老院机构管理服务类
 * 提供养老院机构的增删改查等操作
 */
class NursingHomeInstitutionService {
    
    /**
     * 获取机构列表（分页查询）
     * @param {Object} params - 查询参数
     * @param {string} params.name - 机构名称（模糊查询）
     * @param {number} params.status - 机构状态（0-禁用，1-启用）
     * @param {string} params.province - 省份
     * @param {string} params.city - 城市
     * @param {string} params.district - 区县
     * @param {number} params.pageNum - 页码，默认1
     * @param {number} params.pageSize - 每页条数，默认10
     * @returns {Promise} API响应
     */
    async getInstitutionList(params = {}) {
        try {
            const response = await axios.post('/api/emr/nursing-home/getInstitutionList', params);
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('获取机构列表失败:', error);
            return {
                success: false,
                message: '获取机构列表失败',
                data: null
            };
        }
    }

    /**
     * 创建新机构
     * @param {Object} institutionData - 机构信息
     * @returns {Promise} API响应
     */
    async createInstitution(institutionData) {
        try {
            const response = await axios.post('/api/emr/nursing-home/createInstitution', institutionData);
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('创建机构失败:', error);
            return {
                success: false,
                message: '创建机构失败',
                data: null
            };
        }
    }

    /**
     * 更新机构信息
     * @param {Object} institutionData - 机构信息（必须包含id）
     * @returns {Promise} API响应
     */
    async updateInstitution(institutionData) {
        try {
            const response = await axios.post('/api/emr/nursing-home/updateInstitution', institutionData);
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('更新机构失败:', error);
            return {
                success: false,
                message: '更新机构失败',
                data: null
            };
        }
    }

    /**
     * 删除机构（软删除）
     * @param {number} institutionId - 机构ID
     * @returns {Promise} API响应
     */
    async deleteInstitution(institutionId) {
        try {
            const response = await axios.post('/api/emr/nursing-home/deleteInstitution', { id: institutionId });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('删除机构失败:', error);
            return {
                success: false,
                message: '删除机构失败',
                data: null
            };
        }
    }

    /**
     * 批量删除机构
     * @param {Array} institutionIds - 机构ID数组
     * @returns {Promise} API响应
     */
    async batchDeleteInstitutions(institutionIds) {
        try {
            const response = await axios.post('/api/emr/nursing-home/batchDeleteInstitutions', { ids: institutionIds });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('批量删除机构失败:', error);
            return {
                success: false,
                message: '批量删除机构失败',
                data: null
            };
        }
    }

    /**
     * 批量更新机构状态
     * @param {Array} institutionIds - 机构ID数组
     * @param {number} status - 新状态（0-禁用，1-启用）
     * @returns {Promise} API响应
     */
    async batchUpdateStatus(institutionIds, status) {
        try {
            const response = await axios.post('/api/emr/nursing-home/batchUpdateStatus', { ids: institutionIds, status });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('批量更新状态失败:', error);
            return {
                success: false,
                message: '批量更新状态失败',
                data: null
            };
        }
    }

    /**
     * 检查机构代码是否存在
     * @param {string} code - 机构代码
     * @param {number} excludeId - 排除的机构ID（编辑时使用）
     * @returns {Promise} API响应
     */
    async checkCodeExists(code, excludeId = null) {
        try {
            const response = await axios.post('/api/emr/nursing-home/checkCodeExists', { code, excludeId });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('检查代码失败:', error);
            return {
                success: false,
                message: '检查代码失败',
                data: false
            };
        }
    }

    /**
     * 获取所有启用状态的机构（用于下拉选择）
     * @returns {Promise} API响应
     */
    async getAllActiveInstitutions() {
        try {
            const response = await axios.post('/api/emr/nursing-home/getAllActiveInstitutions', {});
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('获取启用机构失败:', error);
            return {
                success: false,
                message: '获取启用机构失败',
                data: []
            };
        }
    }

    /**
     * 获取机构统计信息
     * @returns {Promise} API响应
     */
    async countInstitutions() {
        try {
            const response = await axios.post('/api/emr/nursing-home/countInstitutions', {});
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('统计机构失败:', error);
            return {
                success: false,
                message: '统计机构失败',
                data: null
            };
        }
    }

    /**
     * 根据地理位置获取附近机构
     * @param {number} latitude - 纬度
     * @param {number} longitude - 经度
     * @param {number} radiusKm - 搜索半径（公里），默认10公里
     * @returns {Promise} API响应
     */
    async getNearbyInstitutions(latitude, longitude, radiusKm = 10) {
        try {
            const response = await axios.post('/api/emr/nursing-home/getNearbyInstitutions', { 
                latitude, 
                longitude, 
                radiusKm 
            });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('获取附近机构失败:', error);
            return {
                success: false,
                message: '获取附近机构失败',
                data: []
            };
        }
    }

    /**
     * 复制机构
     * @param {number} sourceId - 源机构ID
     * @param {string} newName - 新机构名称
     * @param {string} newCode - 新机构代码
     * @returns {Promise} API响应
     */
    async copyInstitution(sourceId, newName, newCode) {
        try {
            const response = await axios.post('/api/emr/nursing-home/copyInstitution', { 
                sourceId, 
                newName, 
                newCode 
            });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('复制机构失败:', error);
            return {
                success: false,
                message: '复制机构失败',
                data: null
            };
        }
    }

    /**
     * 导出机构数据
     * @param {Object} params - 查询参数
     * @returns {Promise} API响应
     */
    async exportInstitutions(params = {}) {
        try {
            const response = await axios.post('/api/emr/nursing-home/exportInstitutions', params, {
                responseType: 'blob'
            });
            return {
                success: true,
                message: '导出成功',
                data: response.data
            };
        } catch (error) {
            console.error('导出机构数据失败:', error);
            return {
                success: false,
                message: '导出机构数据失败',
                data: null
            };
        }
    }

    /**
     * 根据ID获取机构详情
     * @param {number} institutionId - 机构ID
     * @returns {Promise} API响应
     */
    async getInstitutionDetail(institutionId) {
        try {
            const response = await axios.post('/api/emr/nursing-home/getInstitutionById', { id: institutionId });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('获取机构详情失败:', error);
            return {
                success: false,
                message: '获取机构详情失败',
                data: null
            };
        }
    }

    /**
     * 更新机构状态
     * @param {number} institutionId - 机构ID
     * @param {number} status - 新状态（0-禁用，1-启用）
     * @returns {Promise} API响应
     */
    async updateInstitutionStatus(institutionId, status) {
        try {
            const response = await axios.post('/api/emr/nursing-home/updateInstitutionStatus', { 
                id: institutionId, 
                status 
            });
            return {
                success: response.data.success || (response.data.code === 200),
                message: response.data.message || response.data.msg,
                data: response.data.data
            };
        } catch (error) {
            console.error('更新机构状态失败:', error);
            return {
                success: false,
                message: '更新机构状态失败',
                data: null
            };
        }
    }
}

// 创建并导出服务实例
const nursingHomeInstitutionService = new NursingHomeInstitutionService();
export default nursingHomeInstitutionService; 
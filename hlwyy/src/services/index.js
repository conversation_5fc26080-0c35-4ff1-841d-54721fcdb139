import vm from 'src/main.js';
import allApi from 'api';
import { cookie, lStore, sStore, alerts, strToJson, isWeixin, axios, axiosWrap, Socket, WXSDK } from 'js/yydjs.js';

const {
    base, //代理或者线上环境地址
    imgview, //图片预览地址
    imguploadurl, //上传地址
    filedowned, //下载地址
    profile, //环境
    commonAjax, //网络请求
    socketUrl, //socket请求地址
} = allApi;

const textAxios = (params, success) => {
    commonAjax('cas.foshan_doctorServiceAdmin', 'doctorInfo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    []
*/
const findDic = (params, success) => {
    commonAjax('cas.multipleDictionaryService', 'findDic', [params]).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

const getDictionaries = (success) => {
    findDic(['cfs.dic.base_drugUsePathways_gd', 'cfs.dic.base_drugUsingRate_gd', 'cfs.dic.recipe_remark'], (res) => {
        let result = {};

        if (res.body) {
            for (let item of res.body) {
                let { dicId = '', items = [] } = item;
                let itemsJson = {};
                let frequencyJson = {};

                dicId = dicId.replace('cfs.dic.base_', '');
                dicId = dicId.replace('cfs.dic.', '');
                for (let item1 of items) {
                    let { key, text, properties = {} } = item1;
                    let { frequency } = properties;

                    itemsJson[key] = text;
                    if (dicId == 'drugUsingRate_gd') {
                        frequencyJson[key] = +frequency;
                    }
                }
                result[dicId] = items;
                result[`${dicId}Json`] = itemsJson;
                if (dicId == 'drugUsingRate_gd') {
                    result.frequencyJson = frequencyJson;
                }
            }
        }
        success && success(result);
    });
};

/*
    []
*/
const getDoctorWorkbench = (params, success) => {
    commonAjax('cas.foshan_workbenchService', 'getDoctorWorkbench', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "pageSize": 2,
        "page": 1,
        "drugType": "",//23查询中药,不传或者为空默认西药/中成药
        "orgId": "f03c2662-d237-11e7-a383-005056bb5884",
        "keyWord": ""
    }]
*/
const findDrugStandardCatalog = (params, success) => {
    commonAjax('cas.foshan_drugCatalogAdminService', 'findDrugStandardCatalog', params, true).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "pageSize": 2,
        "page": 1,
        "icdVersion": "",//icd版本
        "keyWord": ""
    }]
*/
const findDiseases = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'findDiseases', params, true).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1046// revisitId复诊记录id
    ]
*/
const getRecordByRevisitId = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'getRecordByRevisitId', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "8862f103e118405b912b652c51a04cfc"// mpiId居民主索引
    ]
*/
const getRecordByMpiId = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'getRecordByMpiId', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        {
            "medicalRecordId": 2,// 主键，有则为修改，无则为新增
            "mpiId": "8862f103e118405b912b652c51a04cfc",
            "revisitId": 1048,
            "chiefComplaint": "主诉2",
            "allergicHistory": "过敏史2",
            "presentHistory": "现病史",
            "pastHistory": "既往史",
            "physicalExamination": "体格检查",
            "assistExamResult": "辅助检查",
            "temperature": 38.6,
            "pulseRate": 70,
            "diastolicPressure": 130,
            "systolicPressure": 90,
            "beathingRate": 40,
            "height": 173,
            "weight": 60,
            "treatMethod": "处理措施",
            "treatOpinion": "处理意见",
            "blfyWbs": "1",
            "blfyKs": "0",
            "blfyKt": "1",
            "blfyXt": "0",
            "blfyXm": "1",
            "blfyDh": "1",
            "blfyEx": "1",
            "blfySybz": "0",
            "blfyOt": "1",
            "blfyPfsy": "0",
            "blfyPz": "1",
            "blfyTy": "0",
            "blfyEm": "1",
            "blfyFx": "0",
            "blfyGjt": "1",
            "blfySlxj": "0",
            "blfyQt": "1",
            "blfyQrsm": "1",
            "medicationDelayFlag": "1"
        }
    ]
*/
const saveMzMedicalRecord = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'saveMzMedicalRecord', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1046// revisitId复诊记录id
    ]
*/
const findMzDiagnose = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'findMzDiagnose', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        {
            "diagnoseRecordId": 2,// 主键，有则为修改，无则为新增
            "mpiId": "8862f103e118405b912b652c51a04cfc",
            "revisitId": 1048,
            "masterFlag": 1,
            "icdCode": "A01.003",
            "icdName": "伤寒杆菌性败血症"
        }
    ]
*/
const saveMzDiagnose = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'saveMzDiagnose', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        2// 主键diagnoseRecordId
    ]
*/
const removeMzDiagnose = (params, success) => {
    commonAjax('cas.foshan_diseaseAdminService', 'removeMzDiagnose', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [revisitId,status]//status 0未提交、1审核中、3审核通过、4审核未通过
*/
const findRecipeDrugInfoByStatus = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findRecipeDrugInfoByStatus', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "revisitId": 2,//复诊订单id
        "drugs": [{//西药
            "recordId": 21,//记录id,新增药品没有不传(新增字段)
            "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
            "drugCodeStandard": "96982365000029",
            "drugCode": "11",
            "drugName": "阿莫西林胶囊",
            "drugProductionPlaceId": "cd001",
            "drugProductionPlaceName": "华东制药厂",
            "drugSpecifications": "25mg",//药品规格drugSpecifications
            "pharmaceuticalDosageFormCode": "01",//药品剂型代码CV08_50_002
            "drugPackSpecificationUnit": "01",//包装单位base_drugPackSpecificationUnit
            "drugPackSpecificationRatio": 12,//包装系数
            "drugUseDays": 3,
            "drugUsingRate": "tid",
            "drugUseDose": 1,
            "drugUseDoseUnit": "05",
            "drugUsePathwaysCode": "po",//用药途径,字典,drugUsePathways
            "drugSendNumber": 1,//发药数量
            "drugSendUnit": "01",//发药单位 base_drugPackSpecificationUnit
            "remark": "备注"
        }],
        "herbalMedicines": [{//中药
            "recordId": 21,//记录id,新增中药组没有不传(新增字段)
            "piecesChineseMedicineAgentNumb ": 5,//剂数
            "piecesChineseMedicineMethod": "水煎煮", //中药煎煮法
            "drugUsingRate": "bid",//用用频次  base_drugUsingRate
            "chineseHerbalMedicineMethod": "口服,一次两次",//中药服用描述
            "drugUsePathwaysCode": "po",//用药途径,字典,drugUsePathways
            "medicineDecoctingFlag": "1",//是否代煎
            "concentrationInfo": "100ml",//浓缩信息
            "drugs": [{
                "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
                "drugCodeStandard": "96982365000028",
                "drugCode": "21",
                "drugName": "当归",
                "drugSendNumber": 10,//发药数量
                "drugSendUnit": "11",//发药单位 base_drugPackSpecificationUnit
                "remark": "备注"////每个草药界面后都有,如先煎等,如输入,需传
            }, {
                "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
                "drugCodeStandard": "96982365000027",
                "drugCode": "22",
                "drugName": "陈皮",
                "drugSendNumber": 20,
                "drugSendUnit": "11",
                "remark": "备注"
            }]
        }]
    }]
*/
const getRecipeNeedCheckInfo = (params, success) => {
    commonAjax('cas.foshan_recipeService', 'getRecipeNeedCheckInfo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "revisitId": 2,//复诊订单id
        "drugs": [{//西药
            "recordId": 21,//记录id,新增药品没有不传(新增字段)
            "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
            "drugCodeStandard": "96982365000029",
            "drugCode": "11",
            "drugName": "阿莫西林胶囊",
            "drugProductionPlaceId": "cd001",
            "drugProductionPlaceName": "华东制药厂",
            "drugSpecifications": "25mg",//药品规格drugSpecifications
            "pharmaceuticalDosageFormCode": "01",//药品剂型代码CV08_50_002
            "drugPackSpecificationUnit": "01",//包装单位base_drugPackSpecificationUnit
            "drugPackSpecificationRatio": 12,//包装系数
            "drugUseDays": 3,
            "drugUsingRate": "tid",
            "drugUseDose": 1,
            "drugUseDoseUnit": "05",
            "drugUsePathwaysCode": "po",//用药途径,字典,drugUsePathways
            "drugSendNumber": 1,//发药数量
            "drugSendUnit": "01",//发药单位 base_drugPackSpecificationUnit
            "remark": "备注"
        }],
        "herbalMedicines": [{//中药
            "recordId": 21,//记录id,新增中药组没有不传(新增字段)
            "piecesChineseMedicineAgentNumb ": 5,//剂数
            "piecesChineseMedicineMethod": "水煎煮", //中药煎煮法
            "drugUsingRate": "bid",//用用频次  base_drugUsingRate
            "chineseHerbalMedicineMethod": "口服,一次两次",//中药服用描述
            "drugUsePathwaysCode": "po",//用药途径,字典,drugUsePathways
            "medicineDecoctingFlag": "1",//是否代煎
            "concentrationInfo": "100ml",//浓缩信息
            "drugs": [{
                "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
                "drugCodeStandard": "96982365000028",
                "drugCode": "21",
                "drugName": "当归",
                "drugSendNumber": 10,//发药数量
                "drugSendUnit": "11",//发药单位 base_drugPackSpecificationUnit
                "remark": "备注"////每个草药界面后都有,如先煎等,如输入,需传
            }, {
                "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
                "drugCodeStandard": "96982365000027",
                "drugCode": "22",
                "drugName": "陈皮",
                "drugSendNumber": 20,
                "drugSendUnit": "11",
                "remark": "备注"
            }]
        }]
    }]
*/
const doRecipeSync = (params, success, error) => {
    commonAjax('cas.foshan_doctorRevisitService', 'doRecipeSync', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
            error && error(res);
        }
    });
};

/*
    [
        {
            "orgId": "48c9496e-38bc-11e9-b553-005056be6229",
            "doctorId": "1d61c09d-38c8-11e9-b553-005056be6229",
            "orderStatus": "01",// 01待接诊 02复诊中
            "personName": "老",// 患者姓名搜索关键字
            "pageNo": 1,
            "pageSize": 10
        }
    ]
*/
const queryPatient = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'queryPatient', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1018//主键、复诊id
    ]
*/
const getPatientRevisitDetail = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'getPatientRevisitDetail', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "revisitId":1213, //必传，复诊订单主键
        "choose":1, //必传，医生操作：1=确认接诊，2=暂不接诊
        "remark":"暂不接诊" //非必传，备注信息
    }]
*/
const confirmVisit = (params, success, finallyFn) => {
    commonAjax('cas.foshan_doctorRevisitService', 'confirmVisit', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else if (res.code != 201 && res.code != 202 && res.code != 203 && res.code != 204) {
            if (res.msg) alerts(res.msg, 'error');
        }
        finallyFn && finallyFn(res);
    });
};

/*
    [
        1213, //必传，复诊订单主键revisitId
    ]
*/
const finishRevisit = (params, success, finallyFn) => {
    commonAjax('cas.foshan_doctorRevisitService', 'finishRevisit', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else if (res.code != 800 && res.code != 801 && res.code != 802 && res.code != 803) {
            if (res.msg) alerts(res.msg, 'error');
        }
        finallyFn && finallyFn(res);
    });
};

/*
    [
        1213, //必传，复诊订单主键revisitId
    ]
*/
const validDataBeforeFinishRevisit = (params, success, finallyFn) => {
    commonAjax('cas.foshan_doctorRevisitService', 'validDataBeforeFinishRevisit', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else if (res.code != 800 && res.code != 801 && res.code != 802 && res.code != 803) {
            if (res.msg) alerts(res.msg, 'error');
        }
        finallyFn && finallyFn(res);
    });
};

/*
    [
        {
            "orgId": "48c9496e-38bc-11e9-b553-005056be6229",
            "doctorId": "1d61c09d-38c8-11e9-b553-005056be6229",
            "status": "1",// 状态： 1待接诊 2咨询中
            "itemCode": "01",// 类型：01图文 02视频
            "pageNo": 1,
            "pageSize": 10
        }
    ]
*/
const getConsultList = (params, success) => {
    commonAjax('cas.foshan_doctorConsultService', 'getConsultList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1060// 主键-咨询记录ID(orderDetailId)
    ]
*/
const getConsultDetail = (params, success) => {
    commonAjax('cas.foshan_doctorConsultService', 'getConsultDetail', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "orderDetailId":1213, //必传，咨询订单主键
        "choose":1,//必传，医生操作：1=确认接诊，2=暂不接诊
        "remark":"工作繁忙" //非必传，退诊原因
    }]
*/
const confirmConsult = (params, success, finallyFn) => {
    commonAjax('cas.foshan_consultProcessService', 'confirmConsult', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else if (res.code != 201 && res.code != 202 && res.code != 203 && res.code != 204) {
            if (res.msg) alerts(res.msg, 'error');
        }
        finallyFn && finallyFn(res);
    });
};

/*
    [{
        "orderDetailId":123,    //必传（数值类型）咨询订单主键
        "itemCode":"01",    //必传，咨询类型：01图文 02视频
        "userType":"11" //必传，用户类型 10 医生 11 患者
    }]
*/
const endConsult = (params, success, finallyFn) => {
    commonAjax('cas.foshan_consultProcessService', 'endConsult', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else if (res.code != 802 && res.code != 803) {
            if (res.msg) alerts(res.msg, 'error');
        }
        finallyFn && finallyFn(res);
    });
};

/*
    [{
        "orderDetailId":123,    //必传（数值类型）咨询订单主键
        "itemCode":"01",    //必传，咨询类型：01图文 02视频
        "userType":"11" //必传，用户类型 10 医生 11 患者
    }]
*/
const validConsultBeforeEndConsult = (params, success, finallyFn) => {
    commonAjax('cas.foshan_consultProcessService', 'validConsultBeforeEndConsult', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else if (res.code != 802 && res.code != 803) {
            if (res.msg) alerts(res.msg, 'error');
        }
        finallyFn && finallyFn(res);
    });
};

/*
    //提交处方 处方预览
    [{
            "pdfStatus":"",////0提交处方首次加载 1签名 2非签名展示
        "revisitId": 2,//复诊订单id
        "drugs": [{//西药
            "recordId": 21,//记录id,新增药品没有不传(新增字段)
            "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
            "drugCodeStandard": "96982365000029",
            "drugCode": "11",
            "drugName": "阿莫西林胶囊",
            "drugProductionPlaceId": "cd001",
            "drugProductionPlaceName": "华东制药厂",
            "drugSpecifications": "25mg",//药品规格drugSpecifications
            "pharmaceuticalDosageFormCode": "01",//药品剂型代码CV08_50_002
            "pharmaceuticalDosageFormName": "01",//药品剂型代码名称(新增)
            "drugPackSpecificationUnit": "01",//包装单位base_drugPackSpecificationUnit
            "drugPackSpecificationRatio": 12,//包装系数
            "drugUseDays": 3,
            "drugUsingRate": "tid",
            "drugUseDose": 1,
            "drugUseDoseUnit": "05",
            "drugUsePathwaysCode": "po",//用药途径,字典,drugUsePathways
            "drugSendNumber": 1,//发药数量
            "drugSendUnit": "01",//发药单位 base_drugPackSpecificationUnit
            "remark": "备注"
        }],
        "herbalMedicines": [{//中药
            "recordId": 21,//记录id,新增中药组没有不传(新增字段)
            "piecesChineseMedicineAgentNumb ": 5,//剂数
            "piecesChineseMedicineMethod": "水煎煮", //中药煎煮法
            "drugUsingRate": "bid",//用用频次  base_drugUsingRate
            "chineseHerbalMedicineMethod": "口服,一次两次",//中药服用描述
            "drugUsePathwaysCode": "po",//用药途径,字典,drugUsePathways
            "medicineDecoctingFlag": "1",//是否代煎
            "concentrationInfo": "100ml",//浓缩信息
            "drugs": [{
                "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
                "drugCodeStandard": "96982365000028",
                "drugCode": "21",
                "drugName": "当归",
                "drugSendNumber": 10,//发药数量
                "drugSendUnit": "11",//发药单位 base_drugPackSpecificationUnit
                "remark": "备注"////每个草药界面后都有,如先煎等,如输入,需传
            }, {
                "drugType": "21",//药品类别,21西药,22成药,23中药(新增字段)
                "drugCodeStandard": "96982365000027",
                "drugCode": "22",
                "drugName": "陈皮",
                "drugSendNumber": 20,
                "drugSendUnit": "11",
                "remark": "备注"
            }]
        }]
    }]

    //签名 处方预览
    [{
            "pdfStatus":"",////0提交处方首次加载 1签名 2非签名展示
        "revisitId": 2,//复诊订单id
            "batch":"",//提交批次 首次加载返回
            "doctor1":"",//处方医生签名  非必填 会从token中取
            "doctor2":"",//调配药师医生签名   非必填
            "doctor3":""//核发药师医生签名   非必填
    }]

    //非签名 处方预览
    [{
            "pdfStatus":"",////0提交处方首次加载 1签名 2非签名展示
        "revisitId": 2,//复诊订单id
            "recipeId":"",//处方id
            "doctor1":"",//处方医生签名  非必填 会从token中取
            "doctor2":"",//调配药师医生签名   非必填
            "doctor3":""//核发药师医生签名   非必填
    }]
*/
const doRecipeSyncPdf = (params, success) => {
    commonAjax('cas.foshan_recipeService', 'doRecipeSyncPdf', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "orgId": "48c9496e-38bc-11e9-b553-005056be6229",
        "doctorId": "1d61c09d-38c8-11e9-b553-005056be6229",
        "patientName": "q",// 患者姓名关键字
        "itemCode": "01",// 类型 01：图文，02:视频 revisit：复诊配药
        "status": "2",// 状态：1待支付 2已取消 3待接诊 4待开始 5进行中 6已完成
        "pageNo": 1,
        "pageSize": 10
    }]
*/
const doctorOrderList = (params, success) => {
    commonAjax('cas.foshan_orderManageService', 'doctorOrderList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "buzId": "937",// orderDetailId
        "buzType": "40",// 30图文 40视频 70复诊
        "pageNo": 1,
        "pageSize": 10
    }]
*/
const doctorOrderImHistory = (params, success) => {
    commonAjax('cas.foshan_orderManageService', 'doctorOrderImHistory', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        itemCode,//itemCode咨询项目code 01  图文咨询 02 视频咨询 04复诊
        orderNo,//orderNo订单id
    ]
*/
const isOrderFinish = (params, success) => {
    commonAjax('cas.foshan_consultProcessService', 'isOrderFinish', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "tmpBelongObjType": 2,  //模板所属对象类型：1科室、2医生
        "tmpBelongObjId": 1,   //医生id或者科室id
        "templateName": "常用1",  //模板名称
        "icdName": "",  //诊断名称
        "page":1,
        "limit":10
    }]
*/
const pageTemplateRecipeList = (params, success) => {
    commonAjax('cas.foshan_templateService', 'pageTemplateRecipeList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1//模板id
    ]
*/
const getTemplateRecipeContent = (params, success) => {
    commonAjax('cas.foshan_templateService', 'getTemplateRecipeContent', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "templateRecipe": {
            "templateId": 1, //模板id  无则新增,有则更新
            "tmpBelongObjType": 2,//模板所属对象类型：1科室、2医生
            "tmpBelongObjId": "2",//医生或者科室id
            "templateName": "常用模板12",
            "recipeTypeCode": "1",//处方类别代码：21西药、22成药、23中草药
            "mnemonic": "fjh",//助记码
            "icdCode": "1",
            "icdName": "肺结核"
        },
        "drugs": [
            {

                "drugCodeStandard": "10183",//标准药品编码：云平台药品唯一编码
                "drugName": "头孢克肟分散片",
                "drugSpecifications": "0.1克/片*6片/盒",//药品规格：25mg、30ml
                "pharmaceuticalDosageFormCode": "",//药品剂型代码
                "pharmaceuticalDosageFormName": "片剂",//药品剂型名称
                "drugPackSpecificationUnit": "8",//药品包装规格单位：字典HCN_drugPackSpecificationUnit
                "drugPackSpecificationRatio": 6,//药品包装规格系数：例如：12指12片/每瓶
                "drugUseDays": 0,//用药天数
                "drugUsingRate": "TID",//用药频次：字典HCN_drugUsingRate
                "drugUseDose": 2,//每次使用剂量
                "drugUseDoseUnit": "20",//使用剂量单位：字典HCN_drugUnit
                "drugUsePathwaysCode": "1",//给药途径代码：字典HCN_drugUsePathways
                "drugSendNumber": 2,//发药数量
                "drugSendUnit": "00",//发药单位：字典HCN_drugSendUnit，对于西药，同药品包装规格单位；对于中草药，应为克
                "remark": "sss"//备注
                对于中草药可以填写
                先煎
                后下
                包煎
                另煎
                捣碎
                烊化
                冲服
                煎汤代水
            }
        ]
    }]
*/
const saveOrUpdateTemplateRecipe = (params, success) => {
    commonAjax('cas.foshan_templateService', 'saveOrUpdateTemplateRecipe', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1//模板id
    ]
*/
const deleteTemplateRecipe = (params, success) => {
    commonAjax('cas.foshan_templateService', 'deleteTemplateRecipe', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "tmpBelongObjType": 2,  //模板所属对象类型：1科室、2医生
        "tmpBelongObjId": 1,   //医生id或者科室id
        "templateName": "常用1",  //模板名称
        "icdName": "",  //诊断名称
        "page":1,
        "limit":10
    }]
*/
const pageTemplateMzMedicalRecordList = (params, success) => {
    commonAjax('cas.foshan_templateService', 'pageTemplateMzMedicalRecordList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1//模板id
    ]
*/
const getTemplateMzMedicalRecord = (params, success) => {
    commonAjax('cas.foshan_templateService', 'getTemplateMzMedicalRecord', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "templateId": 1,//模板id  无则新增,有则更新
        "tmpBelongObjType": 2,
        "tmpBelongObjId": "1",
        "templateName": "常用病历1",
        "mnemonic": "cybl",
        "chiefComplaint": "咽喉疼痛",
        "allergicHistory": "无药物及食物过敏史",
        "presentHistory": "患者诉3天前出现咽喉疼痛不适",
        "pastHistory": "有慢性扁桃体炎及高血压病",
        "physicalExamination": "神志清，咽部充血",
        "assistExamResult": "血常规",
        "treatOpinion": "处理意见",
        "treatMethod": "处理措施",
        "icdCode": "1",
        "icdName": "肺结核"
    }]
*/
const saveOrUpdateTemplateMzMedicalRecord = (params, success) => {
    commonAjax('cas.foshan_templateService', 'saveOrUpdateTemplateMzMedicalRecord', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1//模板id
    ]
*/
const deleteTemplateMzMedicalRecord = (params, success) => {
    commonAjax('cas.foshan_templateService', 'deleteTemplateMzMedicalRecord', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    ["fddfe4c14e144e7bae8a06f6ef9817ed",2,"9ba6eb5e-7633-11e9-a9fc-005056be2191"]
    //mpiId, tmpBelongObjType//模板所属对象类型：1科室、2医生 ,tmpBelongObjId   //科室id或者医生id
*/
const queryTemplateRecipeForPatient = (params, success) => {
    commonAjax('cas.foshan_templateService', 'queryTemplateRecipeForPatient', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        1 //recipeId
    ]
*/
const getRecipeContent = (params, success) => {
    commonAjax('cas.foshan_templateService', 'getRecipeContent', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    ["dce73b7f-4f54-4276-beed-91fcd7d0c890","2","9ba6eb5e-7633-11e9-a9fc-005056be2191"]
    //userId, tmpBelongObjType模板所属对象类型：1科室、2医生, tmpBelongObjId科室id或者医生id
*/
const queryTemplateMzMedicalRecordForPatient = (params, success) => {
    commonAjax('cas.foshan_templateService', 'queryTemplateMzMedicalRecordForPatient', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [42]                        //revisitId
*/
const getTemplateMzMedicalRecordForPatient = (params, success) => {
    commonAjax('cas.foshan_templateService', 'getTemplateMzMedicalRecordForPatient', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [1,"1111","常用"]                      // type 1病历 2处方,医生id,模板名称
*/
const checkTemplateName = (params, success) => {
    commonAjax('cas.foshan_templateService', 'checkTemplateName', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "areaRegionId":"440304",//所在区编码 如：福田区：440303 允许为空
        "name":"测试"//社区药房名称 允许为空
    }]
*/
const findHispital = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findHispital', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orderNo]//订单id
*/
const findRecipeInfoByOrderNo = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findRecipeInfoByOrderNo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orderNo,recipeId]//订单id,recipeId
*/
const getRecipeInfo = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'getRecipeInfo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    []
*/
const getSchedule = (params, success) => {
    commonAjax('cas.foshan_workbenchService', 'getSchedule', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

const updateMsg = (params, success) => {
    commonAjax('cas.foshan_doctorConsultService', 'updateMsg', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId, mpiId,standardDeptId]//orgId或者standardDeptId未知,传null或者空字符串
*/
const findDiagnosisRecords = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findDiagnosisRecords', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId, sourceVisitId,mpiId]    //新增入参mpiId   sourceVisitId业务系统内部的就诊流水号 由01.获取患者就诊记录返回
*/
const findDiagnosisRecordDetail = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findDiagnosisRecordDetail', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId, visitId,mpiId]   //新增入参mpiId
*/
const findDisposal = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findDisposal', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [revisitId]
*/
const findDisposalByRevisitId = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'findDisposalByRevisitId', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId,visitId,mpiId,reportType,clinicId]//报告列表1检查,2检验    新增入参clinicId门诊id
*/
const queryRevisitHistoryReport = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'queryRevisitHistoryReport', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId,mpiId,sourceId,reportViewType]    新增入参reportViewType  报告展示类型
*/
const getRevisitLabReport = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'getRevisitLabReport', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId,mpiId,sourceId]
*/
const getRevisitExamReport = (params, success) => {
    commonAjax('cas.foshan_doctorRevisitService', 'getRevisitExamReport', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [orgId, visitId,patientId]    //新增入参patientId
*/
const findRecipeByVisitId = (params, success) => {
    commonAjax('cas.foshan_recipeService', 'findRecipeByVisitId', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

const findCommonExpressionsByTypeAndContent = (params, success) => {
    commonAjax('cas.foshan_CommonExpressionAdminService', 'findCommonExpressionsByTypeAndContent', params, true).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

const sendVideoMessage = (params, success) => {
    commonAjax('cas.foshan_consultProcessService', 'sendVideoMessage', params, true).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    []
*/
const isUpdatePassWord = (params, success, error) => {
    commonAjax('cas.auditLogService', 'isUpdatePassWord', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    }).catch((res) => {
        error && error(res);
    });
};

const getMedicalSkillTmpl = (params, success, error) => {
    commonAjax('cas.foshan_medicalSkillItemService', 'getMedicalSkillTmpl', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    }).catch((res) => {
        error && error(res);
    });
};

const findMedicalSkillItem = (params, success, error) => {
    commonAjax('cas.foshan_medicalSkillItemService', 'findMedicalSkillItem', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    }).catch((res) => {
        error && error(res);
    });
};

const applyMedicalSkill = (params, success, error) => {
    commonAjax('cas.foshan_medicalSkillItemService', 'applyMedicalSkill', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    }).catch((res) => {
        error && error(res);
    });
};

const findMedicalSkillInfoByRevisitId = (params, success, error) => {
    commonAjax('cas.foshan_medicalSkillItemService', 'findMedicalSkillInfoByRevisitId', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    }).catch((res) => {
        error && error(res);
    });
};

const getMedicalSkillApplyDetail = (params, success, error) => {
    commonAjax('cas.foshan_medicalSkillItemService', 'getMedicalSkillApplyDetail', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    }).catch((res) => {
        error && error(res);
    });
};

/*
    [  recipeId ]
*/
const recipeInvalid = (params, success) => {
    commonAjax('cas.foshan_recipeService', 'recipeInvalid', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [  recipeId ]
*/
const recipeRevoke = (params, success) => {
    commonAjax('cas.foshan_recipeService', 'recipeRevoke', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "revisitId": 1, //复诊id
        "applyType": "2", //申请单类型：3检验、2检查
        "applyName": "申请单名称", //申请单名称
        "remark": "备注", //备注,
        "tempId": 2,
        "clinicInfo": "临床信息",
        "examPurpose": "检查目的",
        "skillItems": [{//检查检验项目
                "itemId": 1, //项目id
                "itemName": "项目名称",
                "itemCount": 1, //数量
                "itemPId": 1, //上级项目id,1级为空,
                "attachItemFlag": 0 //是否是附加项目
            },
            {//附加项目
                "itemId": 1, //附加项目id
                "itemName": "附加项目名称",
                "itemCount": 1, //数量
                "attachItemFlag": 1 //是否是附加项目
            }
        ]
    }]
*/
const applyMedicalSkillValid = (params, finallyFn) => {
    commonAjax('cas_ih_foshan.medicalSkillService', 'applyMedicalSkillValid', params).then((res) => {
        finallyFn && finallyFn(res);
    });
};

/*
    [applyId] //申请单id
*/
const deleteMedicalSkillApply = (params, success) => {
    commonAjax('cas.foshan_medicalSkillItemService', 'deleteMedicalSkillApply', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "mpiId": "",//患者mpiId
        "userId": "", //用户id(可不传)
        "doctorId": "", //医生id
        "diseaseType": "", //随访慢病类型 1高血压随访 2糖尿病随访
        "personName": "", //患者姓名
        "visitType": "", //随访方式 01在线问卷
        "pageNo": 1,
        "pageSize": 10
    }]
*/
const visitPageList = (params, success, error) => {
    commonAjax('cas.foshan_visitService', 'pageList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "mpiId"
    ]
*/
const getPatientInfo = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'getPatientInfo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [visitId] //随访id
*/
const getIhForm = (params, success, error) => {
    commonAjax('cas.foshan_visitService', 'getIhForm', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [visitId] //随访id
*/
const getVisitPaperInfo = (params, success, error) => {
    commonAjax('cas.foshan_visitService', 'getVisitPaperInfo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
            error && error(res);
        }
    }).catch((res) => {
        error && error(res);
    });
};

/*
    [{
        "visitId": 1, //随访id
        "content": "json" //问卷填写内容json
    }]
*/
const writePaper = (params, success, error) => {
    commonAjax('cas.foshan_visitService', 'writePaper', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "personName": "",
        "documentType": "", // 06:膳食指导;07运动指导;08:健康宣教
        "mpiId": ""
    }]
*/
const queryHealthyInstruction = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'queryHealthyInstruction', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "personName": "", // 患者姓名
        "sex": "", // 性别
        "tagCode": "", // 标签code（包含）
        "excTagCode": "", // 标签code（除外）
        "pageNo": "",
        "pageSize": ""
    }]
*/
const queryPatientList = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'queryPatient', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "01", // tagCode
        ["e8e31c32583549b3a611c72a363f5bda"] // mpiId
    ]
*/
const addPatientIllTag = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'addPatientIllTag', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        ["01"], // tagCode
        "e8e31c32583549b3a611c72a363f5bda" // mpiId
    ]
*/
const deletePatientIllTag = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'deletePatientIllTag', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "f56f663061ce4380bc0269441e7f4332" // mpiId
    ]
*/
const findDiagnosisList = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'findDiagnosisList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
            error && error(res);
        }
    }).catch((res) => {
        error && error(res);
    });
};

/*
    [
        "e8e31c32583549b3a611c72a363f5bda", // mpiId
        1, // pageNo
        10 // pageSize
    ]
*/
const findRecipeList = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'findRecipeList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
            error && error(res);
        }
    }).catch((res) => {
        error && error(res);
    });
};

/*
    [
        "e8e31c32583549b3a611c72a363f5bda" // mpiId
    ]
*/
const findMpiIllnessTagList = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'findMpiIllnessTagList', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "65fdec0fce95418aa3e81596d5d9fc7e", // mpiId
        [{
                "tagCode": "01",
                "tagName": "高血压"
            },
            {
                "tagCode": "02",
                "tagName": "糖尿病"
            }
        ]
    ]
*/
const updateIllnessTag = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'updateIllnessTag', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "doctorOnlineExtraId": 1, //互联网医院医生id
        "mpiId": "", //居民mpi
        "diseaseType": "", //随访慢病类型 1高血压随访 2糖尿病随访
        "vistiType": "", //随访方式 01在线问卷
        "questionnaireId": 1 //问卷id 目前只有1高血压 2糖尿病
    }]
*/
const createVisit = (params, success, error) => {
    commonAjax('cas.foshan_visitService', 'createVisit', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "06" // 文档类型，06:膳食指导;07运动指导;08:健康宣教
    ]
*/
const findHealthyInstructionDocument = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'findHealthyInstructionDocument', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [{
        "mpiId": "65fdec0fce95418aa3e81596d5d9fc7e",
        "instructionId": "34" // 文档id
    }]
*/
const addHealthyInstruction = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'addHealthyInstruction', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
    [
        "15957191964", // 医生手机号
        "黄翠娥" // 医生姓名
    ]
*/
const getMDTUrlForWeb = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'getMDTUrlForWeb', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

/*
   获取慢病分组名称
*/
const getDiseaseTagInfo = (params, success, error) => {
    commonAjax('cas.foshan_managePatientService', 'queryTagInfo', params).then((res) => {
        if (res.code == 200) {
            success && success(res);
        } else {
            if (res.msg) alerts(res.msg, 'error');
        }
    });
};

// //WebSocket请求
// const socket=new Socket();
// const createSocketApi=()=>{
//     let socketInit=null;

//     return ()=>{
//         if(socketInit){
//             return socketInit;
//         }else{
//             socketInit=socket.init({
//                 url:`${socketUrl}cas-admin/webSocket/${sStore.get('userId')}`,//配置socket接口地址
//                 heartbeatJson:{
//                     timestamp:+new Date(),
//                     username:sStore.get('userId'),
//                 },//配置后台心跳参数
//             });

//             return socketInit;
//         }
//     };
// };
// const socketApi=createSocketApi();

// /*
//     {
//         "message":{
//             code":1100   //获取全部待处理事项
//         }
//         "username":"userId"  //医生userId
//     }
// */
// const sendCode1100=(params,success)=>{
//     var params=Object.assign({},params,{
//         message:{
//             code:1100,
//         },
//         username:sStore.get('userId'),
//     });

//     socketApi().send(params,null,(res)=>{
//         let {message}=res||{};

//         if(message){
//             message=message.filter((item)=>item.code==1000||item.code==1001||item.code==1002);
//             success&&success(message);
//         }
//     });
// };


export {
    imgview, //图片预览地址
    imguploadurl, //上传地址
    filedowned, //下载地址
    profile, //环境
    commonAjax, //网络请求

    textAxios, //测试请求
    findDic, //获取字典接口
    getDictionaries, //获取组装好的字典
    getDoctorWorkbench, //获取工作台

    findDrugStandardCatalog, //查询药品列表
    findDiseases, //获取诊断病种列表

    getRecordByRevisitId, //病历查询(根据revisitId)
    getRecordByMpiId, //病历查询(根据mpiId)
    saveMzMedicalRecord, //病历录入
    findMzDiagnose, //诊断查询
    saveMzDiagnose, //诊断保存
    removeMzDiagnose, //诊断删除

    findRecipeDrugInfoByStatus, //根据状态查询处方药品信息
    getRecipeNeedCheckInfo, //获取合理用药参数
    doRecipeSync, //提交处方

    queryPatient, //复诊-待接诊、复诊中会话列表
    getPatientRevisitDetail, //复诊-待接诊、复诊中会话详情
    confirmVisit, //复诊-订单确认接诊、退诊
    finishRevisit, //复诊-结束复诊
    validDataBeforeFinishRevisit, //结束复诊前判断

    getConsultList, //咨询-待接诊、咨询中会话列表
    getConsultDetail, //咨询-待接诊、咨询中会话详情
    confirmConsult, //咨询-订单确认接诊、退诊
    endConsult, //咨询-结束咨询
    validConsultBeforeEndConsult, //结束咨询前判断

    doRecipeSyncPdf, //提交处方  处方预览
    doctorOrderList, //中台--订单列表
    doctorOrderImHistory, //中台--订单im聊天记录
    isOrderFinish, //判断咨询是否结束

    pageTemplateRecipeList, //处方模板管理-处方模板列表
    getTemplateRecipeContent, //处方模板管理-处方模板详情
    saveOrUpdateTemplateRecipe, //处方模板管理-保存或更新处方模板
    deleteTemplateRecipe, //处方模板管理-删除处方模板

    pageTemplateMzMedicalRecordList, //病历模板管理-病历模板列表
    getTemplateMzMedicalRecord, //病历模板管理-病历模板详情
    saveOrUpdateTemplateMzMedicalRecord, //病历模板管理-保存或更新病历模板
    deleteTemplateMzMedicalRecord, //病历模板管理-删除病历模板

    queryTemplateRecipeForPatient, //处方模板列表医生端
    getRecipeContent, //获取历史处方详情医生端

    queryTemplateMzMedicalRecordForPatient, //病历模板列表医生端
    getTemplateMzMedicalRecordForPatient, //获取历史病历详情医生端
    checkTemplateName, //模板名称校验

    findHispital, //获取社区医院（取药点）
    findRecipeInfoByOrderNo, //根据复诊订单id查询电子处方
    getRecipeInfo, //获取处方明细
    getSchedule, //获取待办事项
    updateMsg, //更新已读


    socketApi, //socket请求
    sendCode1100, //获取待处理事项
    findCommonExpressionsByTypeAndContent, // 快捷常用语

    findDiagnosisRecords, //获取患者就诊记录
    findDiagnosisRecordDetail, //门诊病历查询
    findDisposal, //处置信息查询
    findDisposalByRevisitId, //处置信息查询
    queryRevisitHistoryReport, //报告列表
    getRevisitLabReport, //检验报告查询
    getRevisitExamReport, //检查报告查询
    findRecipeByVisitId, //根据就诊记录获取处方信息
    sendVideoMessage, // 发起视频通知用户
    isUpdatePassWord, //是否需要更新密码
    getMedicalSkillTmpl, // 医技项目模板列表
    findMedicalSkillItem, // 医技项目列表
    applyMedicalSkill, // 医技申请
    findMedicalSkillInfoByRevisitId, // 已开申请单
    getMedicalSkillApplyDetail, // 医嘱详情

    recipeInvalid, //处方作废
    recipeRevoke, //处方撤回
    applyMedicalSkillValid, //医技申请参数校验
    deleteMedicalSkillApply, //申请单作废

    visitPageList, //慢病随访分页
    getVisitPaperInfo, //问卷详情
    getPatientInfo, //患者信息
    getIhForm, //获取初始化问卷
    writePaper, //问卷填写

    queryHealthyInstruction, //患者健康指导列表

    queryPatientList, //患者列表
    addPatientIllTag, //添加患者标签（患者分组 添加患者）
    deletePatientIllTag, //删除患者标签（患者分组）

    findDiagnosisList, //就诊记录列表
    findRecipeList, //处方记录
    findMpiIllnessTagList, //患者疾病标签列表
    updateIllnessTag, //标签维护
    createVisit, //新建随访
    findHealthyInstructionDocument, //获取健康指导文档
    addHealthyInstruction, //患者健康指导添加
    getMDTUrlForWeb, //获取远程会诊URL
    getDiseaseTagInfo //获取慢病分组名称
};

import 'babel-polyfill';
import Vue from 'vue';
import App from './App';
import router from './router';
import Vuex from 'vuex'
import store from './store/index.js';
import Element from 'element-ui';
import * as filter from './filter';
import { commonAjax } from './api/api';
// import 'element-ui/lib/theme-chalk/index.css';
import '../static/css/theme-green/index.css'; // 蓝色主题
import "./assets/css/main.scss";
import '@fortawesome/fontawesome-free/css/all.css'
import axios from 'axios'

// Initialize Stagewise toolbar in development mode only
if (process.env.NODE_ENV === 'development' || process.env.cfg === 'develop') {
    // Load stagewise via CDN to avoid webpack compilation issues
    const script = document.createElement('script');
    script.type = 'module';
    script.innerHTML = `
        import { initToolbar } from 'https://cdn.jsdelivr.net/npm/@stagewise/toolbar@latest/dist/index.es.js';
        initToolbar({
            plugins: [],
        });
    `;
    document.head.appendChild(script);
}

Vue.use(Element, {
    size: 'small' // set element-ui default size
});

Vue.config.productionTip = false;
// 将 axios 挂载到 Vue 原型上
Vue.prototype.$axios = axios

//挂载过滤器
for (let attr in filter) {
    Vue.filter(attr, filter[attr]);
}

router.beforeEach((to, from, next) => {
    // 添加可以不需要登录就访问的路由名单
    const publicRoutes = ["login", "misspwd", "patient360-embed", "patient360-demo-embed"];
    
    // 先检查路由是否需要密码验证
    if (to.matched.some(record => record.meta.requiresAuth)) {
        // 从SessionStorage获取授权状态
        const isAuthorized = sessionStorage.getItem('patient360Auth') === 'authorized';
        
        if (!isAuthorized) {
            // 如果没有授权，重定向到密码验证页面
            const password = prompt('请输入Patient360访问密码：');
            if (password === 'P@t1ent360#2023') {
                // 密码正确，保存授权状态并继续
                sessionStorage.setItem('patient360Auth', 'authorized');
                next();
            } else {
                // 密码错误，重定向到首页或登录页
                alert('密码错误，无法访问');
                next('/');
                return;
            }
        } else {
            // 已经有授权，直接通过
            next();
        }
    }
    // 然后检查是否需要登录验证
    else if (!publicRoutes.includes(to.name) && !sessionStorage.getItem('accessToken')) {
        next("/");
    } else {
        next();
    }
});

router.afterEach((to, from) => {
    // console.log(from.matched);
});

const vm = new Vue({});
const mainvm = new Vue({
    data: {
        num: 1
    },
    el: '#app',
    router,
    store,
    components: { App },
    template: '<App/>'
});

window.vm = vm;
window.mainvm = mainvm;

console.log('2020/4/28 10:36');
export default vm;

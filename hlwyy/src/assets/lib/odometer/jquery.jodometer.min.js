/*
* jOdometer (1.2) // 2012.02.14 // <http://www.frontendmatters.com/projects/jquery-plugins/jodometer/>
* 
* REQUIRES jQuery 1.2.3+ <http://jquery.com/>
* 
* Copyright (c) 2008 TrafficBroker <http://www.trafficbroker.co.uk>
* Licensed under GPL and MIT licenses
* 
* <AUTHOR> <<EMAIL>>
*/
(function(a){a.fn.jOdometer=function(b){function s(a,c,d){if(c!=d){a.stop();if(c==0){a.animate({top:10*b.heightNumber*-1+e},b.speed,b.easing).animate({top:e},1,"linear")}else{if(c<d){a.animate({top:10*b.heightNumber*-1+e},b.speed*((10-d)/10),"linear").animate({top:e},1,"linear").animate({top:c*b.heightNumber*-1+e},b.speed*d/10,b.easing)}else{a.animate({top:c*b.heightNumber*-1+e},b.speed,b.easing)}}}}function r(b){i=String(b).split(".");if(h.length>0){for(m=0;m<h.length;m++){oldDigit=h[m];if(i[1]){h[m]=i[1].charAt(m)}if(h[m]==""){h[m]="0"}s(a(".jodometer_decimal_"+m,c),parseInt(h[m]),parseInt(oldDigit))}}n=i[0];l=n.length-1;for(m=0;m<g.length;m++){oldDigit=g[m];g[m]=n.charAt(l);if(g[m]==""){g[m]="0"}s(a(".jodometer_integer_"+m,c),parseInt(g[m]),parseInt(oldDigit));l--}}function q(a){if(a!=undefined){clearInterval(p);f=a;r(f)}else{r(f);f=f+b.increment}if(b.counterEnd!=false&&f>=b.counterEnd){clearInterval(p);r(b.counterEnd)}}if(this.length>1){this.each(function(){a(this).jOdometer(b)});return this}b=a.extend({},a.fn.jOdometer.defaults,b);this.goToNumber=function(a){q(a)};var c=a(this);var d=this;var e=-b.heightNumber;var f=parseFloat(b.counterStart);var g=[];var h=[];var i=String(b.counterStart).split(".");var j=0;var k=0;if(i[1]){var l=0;for(var m=i[1].length-1;m>-1;m--){h[m]=i[1].charAt(m);a(this).append('<img style="position:absolute; right:'+(l*b.widthNumber+b.offsetRight+l*b.spaceNumbers)+"px; top:"+(parseInt(h[m])*b.heightNumber*-1+e)+'px;" class="jodometer_decimal_'+m+'" src="'+b.numbersImage+'" alt="Decimal '+(m+1)+'" />');l++}a(this).append('<div style="position:absolute; width:'+b.widthDot+"px; height:"+b.heightNumber+"px; background:url("+b.numbersImage+") no-repeat center "+(12*b.heightNumber*-1+e)+"px; right:"+(i[1].length*b.widthNumber+b.offsetRight+i[1].length*b.spaceNumbers)+'px;" class="jodometer_dot"></div>');j=i[1].length;k=b.widthDot}var n=i[0];var l=n.length-1;var o=0;for(var m=0;m<n.length;m++){g[m]=n.charAt(l);if(b.formatNumber&&m>0&&m%3==0){a(this).append('<div style="position:absolute; width:'+b.widthDot+"px; height:"+b.heightNumber+"px; background:url("+b.numbersImage+") no-repeat center bottom; right:"+(m*b.widthNumber+j*b.widthNumber+k+o+b.offsetRight+j*b.spaceNumbers+m*b.spaceNumbers+b.spaceNumbers)+'px;" class="jodometer_dot"></div>');o+=b.widthDot+b.spaceNumbers}a(this).append('<img style="position:absolute; right:'+(m*b.widthNumber+j*b.widthNumber+k+o+b.offsetRight+j*b.spaceNumbers+m*b.spaceNumbers+b.spaceNumbers)+"px; top:"+(parseInt(g[m])*b.heightNumber*-1+e)+'px;" class="jodometer_integer_'+m+'" src="'+b.numbersImage+'" alt="Integer '+(m+1)+'" />');l--}if(parseFloat(b.counterStart)!=b.counterEnd||b.counterEnd.toString()=="false"&&parseFloat(b.counterStart)==0){var p=setInterval(function(){q()},b.delayTime)}return this};a.fn.jOdometer.defaults={counterStart:"0000.00",counterEnd:false,delayTime:1e3,increment:.01,speed:500,easing:"swing",numbersImage:"/images/jodometer-numbers.png",formatNumber:false,heightNumber:31,widthNumber:14,offsetRight:0,spaceNumbers:0,widthDot:10}})(jQuery)
/* reset */
html,body,h1,h2,h3,h4,h5,h6,div,dl,dt,dd,ul,ol,li,p,blockquote,pre,hr,figure,table,caption,th,td,form,fieldset,legend,input,button,textarea,menu{margin:0;padding:0;}
header,footer,section,article,aside,nav,hgroup,address,figure,figcaption,menu,details{display:block;}
table{border-collapse:collapse;border-spacing:0;}
caption,th{text-align:left;font-weight:normal;}
html,body,fieldset,img,iframe,abbr{border:0;}
i,cite,em,var,address,dfn{font-style:normal;}
[hidefocus],summary{outline:0;}
li{list-style:none;}
h1,h2,h3,h4,h5,h6,small{font-size:100%;}
sup,sub{font-size:83%;}
pre,code,kbd,samp{font-family:inherit;}
q:before,q:after{content:none;}
textarea{overflow:auto;resize:none;}
label,summary{cursor:default;}
a,button{cursor:pointer;}
h1,h2,h3,h4,h5,h6,em,strong,b{font-weight:bold;}
del,ins,u,s,a,a:hover{text-decoration:none;}
body,textarea,input,button,select,keygen,legend{font:12px/1.14 arial,\5b8b\4f53;color:#333;outline:0;}
body{background:#fff;}
a,a:hover{color:#333;}
html,body{height: 100%;}
body{position: relative;min-height: 500px;}
body{color:#333;font-family:arial,helvetica,sans-serif;-webkit-font-smoothing:antialiased;-webkit-text-size-adjust: none;}
/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
//   background-color: #8899a7;
  border-radius: 4px;
}

::-moz-scrollbar {
  width: 6px;
  background-color: transparent;
}
::-moz-scrollbar-thumb {
  background-color: #8899a7;
  border-radius: 4px;
}

/* util */
.tc{text-align: center;}
.tl{text-align: left;}
.tr{text-align: right;}
.hide{display:none !important;}
.radius4px{border-radius:4px;-webkit-border-radius:4px;-moz-border-radius:4px;}
.radius5px{border-radius:5px;-webkit-border-radius:5px;-moz-border-radius:5px;}
.radius10px{border-radius:10px;-webkit-border-radius:10px;-moz-border-radius:10px;}
.radius20px{border-radius:20px;-webkit-border-radius:20px;-moz-border-radius:20px;}
.radius-circle{border-radius:50%;-webkit-border-radius:50%;-moz-border-radius:50%;}
.box-sizing{box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;}
.clr{clear: both;height: 1px;overflow: hidden;display: block;}

/** buttons */
.btn{border:0;font-size: 1.0625rem;color: #fff;}
.btn-ok{background-color: #0888ff;width: 100px;line-height: 34px;border:1px solid #0070d9}
.btn-ok.disabled{background-color: #bdd0fd;border: 1px solid #bdd0fd;}
.btn-cancel,.btn-del{border:1px solid #ccc;background-color:#eee;color:#666;width: 100px;line-height: 34px;margin-right: 14px;}
.btn-del{background-color: #eb493d;color: #fff;border-color:#c23227;}
/** 普通对话框 */
.dialog{background-color: #fff;position: fixed;top: 50%;left: 50%;width: 380px;height: 120px;margin-left: -190px;margin-top: -80px;padding: 20px 0;z-index: 10;}
.dialog .content{margin: 29px 0 35px;}
.dialog .buttons{position:relative}

/** 从下往上对话框 */
.dialog-slideUp{position:fixed;width:100%;left:0;bottom:0;height:150px;z-index:10;background-color:#fff;padding:20px 5%}
.dialog-slideUp .content{margin:10px 0}
.dialog-slideUp .buttons{margin-top:30px;position:relative}
/** 遮罩层 */
.mask{position:fixed;top:0;bottom:0;left:0;width:100%;background-color:rgba(0,0,0,.65);z-index:8}
/** toast提示 */
.toast{position: fixed;top: 50%;left: 50%;line-height: 1.5rem;background-color: rgba(0,0,0,.5);width: 50%;height: 1.5rem;color:#fff;margin:-0.75rem 0 0 -25%;font-size: 0.875rem;}
/** datalist */
.arrow-right:after{content:' ';width:0;height:0;display:inline-block;border-left:6px solid #00d3a9;border-top:6px solid transparent;border-bottom:6px solid transparent;position:absolute;right:5%;top:50%;margin-top:-3px}
.arrow-top:after{content:' ';width:0;height:0;display:inline-block;border-top:6px solid #00d3a9;border-left:6px solid transparent;border-right:6px solid transparent;position:absolute;right:5%;top:50%;margin-top:-3px}
.arrow-left:after{content:' ';width:0;height:0;display:inline-block;border-right:6px solid #00d3a9;border-top:6px solid transparent;border-bottom:6px solid transparent;position:absolute;right:5%;top:50%;margin-top:-3px}
.arrow-bottom:after{content:' ';width:0;height:0;display:inline-block;border-bottom:6px solid #00d3a9;border-left:6px solid transparent;border-right:6px solid transparent;position:absolute;right:5%;top:50%;margin-top:-3px}


.icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    vertical-align: middle;
}
.effect-in{
    animation-name: effect;
    -webkit-animation-name: effect;
    -moz-animation-name: effect;
    animation-duration: 2s;
    -webkit-animation-duration: 2s;
    -moz-animation-duration: 2s;
}

.slideUp{
    animation-name: slideUp;
    -webkit-animation-name: slideUp;
    -moz-animation-name: slideUp;
    animation-duration: .5s;
    -webkit-animation-duration: .5s;
    -moz-animation-duration: .5s;
}

.slideDown{
    animation-name: slideDown;
    -webkit-animation-name: slideDown;
    -moz-animation-name: slideDown;
    animation-duration: .5s;
    -webkit-animation-duration: .5s;
    -moz-animation-duration: .5s;
}

@keyframes effect{
    from{opacity:0}
    to{opacity: 1;}
}

@-webkit-keyframes effect{
    from{opacity:0}
    to{opacity: 1;}
}

@-moz-keyframes effect{
    from{opacity:0}
    to{opacity: 1;}
}
/** 渐渐弹出 */
@keyframes slideUp{
    from{bottom:-150px}
    to{bottom:0}
}

@-webkit-keyframes slideUp{
    from{bottom:-150px}
    to{bottom:0}
}

@-moz-keyframes slideUp{
    from{bottom:-150px}
    to{bottom:0}
}
/** 渐渐隐藏 */
@keyframes slideDown{
    from{bottom:0}
    to{bottom:-150px}
}

@-webkit-keyframes slideDown{
    from{bottom:0}
    to{bottom:-150px}
}

@-moz-keyframes slideDown{
    from{bottom:0}
    to{bottom:-150px}
}

@keyframes slideDown{
    from{bottom:0}
    to{bottom:-150px}
}

@-webkit-keyframes slideDown{
    from{bottom:0}
    to{bottom:-150px}
}

@-moz-keyframes slideDown{
    from{bottom:0}
    to{bottom:-150px}
}

/*element-ui组件库样式修改*/
.el-select-dropdown__list li{ padding: 0 15px!important; }
.el-message-box{ width: 360px!important; }
.el-message-box__header{ padding: 0!important; }
.el-message-box__close{ position: absolute; right: 5px; top: 5px; }
.el-message-box__title{ padding-top: 60px!important; padding-bottom: 15px!important; line-height: 20px!important; text-align: center; font-size: 14px!important; color: #666!important; }
.el-message-box__content{ padding: 10px 40px!important; padding-top: 0!important; font-size: 16px!important; color: #666!important; text-align: center; font-weight: bold; }
.el-message-box__status{ position: static;}
.el-message-box__btns{ display: flex; justify-content: center; padding: 20px 40px!important; overflow: hidden; }
.el-message-box__btns button{ width: 50%; border: none!important; background-color: transparent!important; margin: 0!important; }
.el-message-box__btns button:first-of-type{ padding-right: 10px!important; }
.el-message-box__btns button:last-of-type{ padding-left: 10px!important; }
.el-message-box__btns button span{ display: block; height: 36px; line-height: 36px; text-align: center; background-color: transparent; color: #2775e4; border: 1px solid #2775e4; border-radius: 18px; }
.el-message-box__btns button span:active{ opacity: .8; }
.el-message-box__btns button:last-of-type span{ background-color: #2775e4; color: #fff; }

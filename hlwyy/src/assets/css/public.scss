@charset"utf-8";
/* CSS Document */

/*reset*/
header, nav, section, footer, article, aside {
    display: block;
    box-sizing: border-box;
}
html, body, div, ul, ol, li, dl, dt, dd, h1, h2, h3, h4, h5, h6, p, img, span, em, i, b, a, table, thead, tbody, tfoot, th, tr, td, form, input, textarea, select, option, pre, hr, label, button {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}
*:before, *:after {
    box-sizing: border-box;
}
li {
    list-style: none;
}
img {
    display: inline-block;
    border: none;
    vertical-align: top;
    position: relative;
}
img:before {
    display: inline-block;
    content: attr(alt);
    width: 100%;
    padding: 10px;
    height: 100%;
    background-color: #eee;
    position: absolute;
    left: 0;
    top: 0;
}
em, i {
    font-style: normal;
    font-weight: normal;
}
h1, h2, h3, h4, h5, h6, b, label, th {
    font-weight: normal;
}
a, button {
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
table {
    border-collapse: collapse;
}
input, textarea, select, button {
    outline: none;
    font-family: arial, helvetica, sans-serif;
    color: #333;
    font-size: 12px;
}
textarea {
    resize: none;
    overflow: auto;
    vertical-align: top;
}
input[type="text"], input[type="number"], input[type="password"], input[type="email"], input[type="url"], input[type="submit"] {
    -webkit-appearance: none;
    border: 1px solid #ddd;
}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    font-size: 12px;
}
select, textarea {
    border: 1px solid #ddd;
}
select:focus {
    border-color: #52a3fc;
}
iframe {
    vertical-align: top;
    -webkit-overflow-scrolling: touch;
    overflow: auto;
    color: #fff;
}

/*兼容*解决按钮在IE7及更早浏览器下随着value增多两边留白也随着增加的问题(true)*/
input, button {
    overflow: visible;
}
/*兼容*解决IE7及更早浏览器下当li中出现2个或以上的浮动时，li之间产生的空白间隙的BUG(false)*/
li {
    vertical-align: top;
}
/*优化*让文本垂直对齐文本输入框*/
input {
    vertical-align: middle;
}
/*优化*让ios下输入框光标不过长*/
input {
    padding: 5px 0;
}

/*public*/
html {
    width: 100%;
    font-size: 62.5%;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
body {
    width: 100%;
    line-height: 1.5;
    font-size: 14px;
    font-family: arial, helvetica, sans-serif;
    background-color: #f2f2f2;
    color: #333;
    overflow-x: hidden;
}
h1 {
    font-size: 18px;
}
h2 {
    font-size: 16px;
}
h3, h4, h5, h6 {
    font-size: 12px;
}
a {
    color: inherit;
}
a:active, button:active {
    opacity: 0.8;
}
a, button, select {
    cursor: pointer;
}
img {
    max-width: 100% !important;
}
hr {
    border: none;
    border-bottom: 1px solid #000;
}
::-webkit-input-placeholder {
    color: #999;
    font-size: 14px;
}
input:-webkit-autofill {
    -webkit-box-shadow: inset 0 0 0px 1000px #fff;
}
.clear {
    zoom: 1;
}
.clear:after {
    content: "";
    display: block;
    clear: both;
}
.fl {
    float: left !important;
}
.fr {
    float: right !important;
}
.tc {
    text-align: center !important;
}
.tl {
    text-align: left !important;
}
.tr {
    text-align: right !important;
}
.show {
    display: block;
}
.hide {
    display: none;
}

/*label绑定单选复选框*/
.labelFor {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
}
.labelFor input {
    display: none;
}

/*微小字体方案(pc端看看能会模糊，是因为屏幕像素不高，手机上很清晰)*/
.tinyFontSize {
    display: inline-block;
    transform: scale(.5) translate3d(-50%, 0, 0);
    -webkit-transform: scale(.5) translate3d(-50%, 0, 0);
    font-size: 18px;
    line-height: 24px;
}

/*滤镜*/
.filter-grayscale {
    filter: grayscale(1);
    -webkit-filter: grayscale(1);
}
/*灰度,值为0-1之间的小数*/
.filter-sepia {
    filter: sepia(1);
    -webkit-filter: sepia(1);
}
/*褐色,值为0-1之间的小数*/
.filter-saturate {
    filter: saturate(10);
    -webkit-filter: saturate(10);
}
/*饱和度,值为num*/
.filter-hue-rotate {
    filter: hue-rotate(180deg);
    -webkit-filter: hue-rotate(180deg);
}
/*色相旋转    ,值为angle*/
.filter-invert {
    filter: invert(0.3);
    -webkit-filter: invert(0.3);
}
/*反色,值为0-1之间的小数*/
.filter-opacity {
    filter: opacity(1);
    -webkit-filter: opacity(1);
}
/*透明度,值为0-1之间的小数*/
.filter-brightness {
    filter: brightness(0.5);
    -webkit-filter: brightness(0.5);
}
/*亮度,值为0-1之间的小数*/
.filter-contrast {
    filter: contrast(5);
    -webkit-filter: contrast(5);
}
/*对比度,值为num*/
.filter-blur {
    filter: blur(2px);
    -webkit-filter: blur(2px);
}
/*模糊,值为length*/
.filter-drop-shadow {
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
    -webkit-filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
}
/*阴影,左偏移,上偏移,模糊半径,颜色*/

/*单行与多行省略*/
.overflow {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    word-break: break-all;
}
*[class*="multiLine"] {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    word-break: break-all;
}
.multiLine-2 {
    -webkit-line-clamp: 2;
}
.multiLine-3 {
    -webkit-line-clamp: 3;
}
.multiLine-4 {
    -webkit-line-clamp: 4;
}
.multiLine-5 {
    -webkit-line-clamp: 5;
}

/*分上下两边的遮罩*/
.bothMask:before {
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)), linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
    background-repeat: no-repeat;
    background-position: top, bottom;
    background-size: 100% 90px;
}
.bothMask:after {
    content: "";
    width: 100%;
    height: 30px;
    border-top: 1px solid #bbb;
    border-bottom: 1px solid #ddd;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    -webkit-transform: translate3d(-50%, -50%, 0);
}

/*颜色填满*/
.fullColor {
    width: 100%;
    outline: 9999px solid #fff;
    clip: rect(0 9999px 9999px 0);
    position: absolute;
}

/*0.5px细线条*/
*[class*="fineLine"] {
    position: relative;
}
*[class*="fineLine"]:before {
    content: "";
    width: 100%;
    height: 0;
    border: 1px solid transparent;
    border-bottom-color: #f0f0f0;
    transform: scaleY(.5) translate3d(0, 50%, 0);
    position: absolute;
    left: 0;
    bottom: 0;
}
.fineLine-top:before {
    bottom: auto;
    top: 0;
    border-bottom-color: transparent;
    border-top-color: #f0f0f0;
    transform: scaleY(.5) translate3d(0, -50%, 0);
}
.fineLine-left:before {
    width: 0;
    height: 100%;
    border-bottom-color: transparent;
    border-left-color: #f0f0f0;
    transform: scale(.5, 1) translate3d(-50%, 0, 0);
}
.fineLine-right:before {
    left: auto;
    right: 0;
    width: 0;
    height: 100%;
    border-bottom-color: transparent;
    border-right-color: #f0f0f0;
    transform: scale(.5, 1) translate3d(50%, 0, 0);
}

/*开启硬件加速*/
.willChange {
    will-change: transform;
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
}

/*过渡与动画*/
.transition1 {
    transition: all 1s ease-out 0s;
    -webkit-transition: all 1s ease-out 0s;
}
.animation1 {
    display: inline-block;
    animation: autoplay1 1s linear infinite reverse;
    -webkit-animation: autoplay1 1s linear infinite reverse;
}
.animation1:hover {
    animation-play-state: paused;
    -webkit-animation-play-state: paused;
}
@keyframes autoplay1 {
    0% {
        transform: rotateX(0) translate3d(0, 0, 0);
    }
    50% {
        transform: rotateX(0) translate3d(0, -20%, 0);
    }
    100% {
        transform: rotateX(0) translate3d(0, 0, 0);
    }
}
.animation2 {
    display: inline-block;
    width: 0;
    height: 20px;
    animation: autoplay2 1s linear infinite;
    -webkit-animation: autoplay2 1s linear infinite;
    position: relative;
    overflow: hidden;
}
.animation2:before {
    content: "...";
    width: 30px;
    height: 20px;
    line-height: 20px;
    font-size: 20px;
    position: absolute;
    left: 0;
    top: 0;
}
@keyframes autoplay2 {
    0% {
        width: 0;
    }
    50% {
        width: 30px;
    }
    100% {
        width: 0;
    }
}
@media all and (max-width:320px) {}
@media all and (min-width:321px) and (max-width:375px) {}
@media all and (min-width:376px) {}

/*公共会话框*/
.yyd-dialog {
    width: 100%;
    height: 100%;
    background: #fff;
    transition: transform 0.3s ease-out 0s;
    -webkit-transition: -webkit-transform 0.3s ease-out 0s;
    transform: translate3d(100%, 0, 0);
    -webkit-transform: translate3d(100%, 0, 0);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    overflow-y: auto;
}
.yyd-dialog.active {
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
}

/*效果选项卡*/
.yyd-tabBar {
    height: 3px;
    background-color: #f20532;
    position: absolute;
    left: 50%;
    right: 50%;
    bottom: 2px;
}
.yyd-tabBar.forward {
    transition: right 0.3s cubic-bezier(0.61, 0.01, 0.25, 1), left 0.3s cubic-bezier(0.35, 0, 0.25, 1) 0.09s;
    -webkit-transition: right 0.3s cubic-bezier(0.35, 0, 0.25, 1), left 0.3s cubic-bezier(0.35, 0, 0.25, 1) 0.09s;
}
.yyd-tabBar.backward {
    transition: right 0.3s cubic-bezier(0.35, 0, 0.25, 1) 0.09s, left 0.3s cubic-bezier(0.35, 0, 0.25, 1);
    -webkit-transition: right 0.3s cubic-bezier(0.35, 0, 0.25, 1) 0.09s, left 0.3s cubic-bezier(0.35, 0, 0.25, 1);
}

/*checkbox开关按钮样式(建议用在label上，火狐下不支持input伪类)*/
.yyd-checkSwitch {
    appearance: none;
    -webkit-appearance: none;
    display: inline-block;
    width: 50px;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 15px;
    background-color: #dfdfdf;
    transition: background-color 0.1s, border 0.1s;
    -webkit-transition: background-color 0.1s, border 0.1s;
    outline: none;
    position: relative;
    cursor: pointer;
}
.yyd-checkSwitch:before {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 15px;
    background-color: #fdfdfd;
    transition: transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
    -webkit-transition: -webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
    position: absolute;
    left: 0;
    top: 0;
}
.yyd-checkSwitch:after {
    content: "";
    width: 28px;
    height: 28px;
    border-radius: 14px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    background-color: #fff;
    position: absolute;
    left: 0;
    top: 0;
    transition: transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
    -webkit-transition: -webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
}

.yyd-checkSwitch.active {
    background-color: #52a3fc;
    border-color: #52a3fc;
}
.yyd-checkSwitch.active:before {
    transform: scale(0, 0);
    -webkit-transform: scale(0, 0);
}
.yyd-checkSwitch.active:after {
    transform: translate3d(20px, 0, 0);
    -webkit-transform: translate3d(20px, 0, 0);
}
.yyd-checkSwitch input {
    display: none;
}

/*layout*/
#webpack-hot-middleware-clientOverlay {
    display: none !important;
}
#app {
    max-width: 768px;
    margin: 0 auto;
    position: relative;
}

/*路由过渡*/
.router-fade-leave-active {
    transition: opacity .1s;
    opacity: 0;
}
.router-fade-enter {
    opacity: 0;
}
.router-fade-enter-active {
    transition: opacity .1s;
}

/*列表过渡*/
.list-leave-active {
    transition: all .3s ease-out 0s;
    opacity: 0.8;
    transform: translate3d(-100%, 0, 0);
}

/*vux组件库样式修改*/
.vux-alert .weui-dialog__ft .weui-dialog__btn_primary, .vux-confirm .weui-dialog__ft .weui-dialog__btn_primary, .vux-datetime-confirm {
    color: #33adff !important;
}
.vux-prompt input {
    padding: 5px 15px;
    height: 30px;
    border-radius: 2px;
}
.vux-popup-dialog {
    z-index: 9999 !important;
}
.vux-popup-mask {
    top: -9999px !important;
}
.vux-popup-mask.vux-popup-show {
    top: 0 !important;
}

/*mini-ui组件库样式修改*/
.mint-msgbox {
    width: 564px;
}
.mint-msgbox-header {
    padding-top: 30px !important;
    font-size: 36px !important;
}
.mint-msgbox-title {
    font-weight: normal !important;
}
.mint-msgbox-content {
    padding: 0 !important;
    border-bottom: none !important;
    position: relative;
}
.mint-msgbox-content:before {
    content: "";
    width: 100%;
    height: 0;
    border: 1px solid transparent;
    border-bottom-color: #f0f0f0;
    transform: scaleY(.5) translate3d(0, 50%, 0);
    position: absolute;
    left: 0;
    bottom: 0;
}
.mint-msgbox-message {
    line-height: 40px !important;
    padding: 40px !important;
    font-size: 30px !important;
}
.mint-msgbox-btns {
    height: 100px !important;
    font-size: 36px !important;
    background-color: #fff !important;
}
.mint-msgbox-btn {
    background-color: transparent !important;
}
.mint-msgbox-cancel {
    border-right: none !important;
    position: relative;
}
.mint-msgbox-cancel:before {
    content: "";
    width: 0;
    height: 100%;
    border: 1px solid transparent;
    border-right-color: #f0f0f0;
    transform: scale(.5, 1) translate3d(50%, 0, 0);
    position: absolute;
    right: 0;
    bottom: 0;
}
.mint-msgbox-confirm {
    color: #66c875 !important;
}


/*首页*/

/*sass定义*/
/*全局变量*/
$main: #52a3fc; //主色
$bg: #fafafc; //背景色
$red: #ec6c6d; //红色
$redBg: #ffd5d6; //红色背景
$orange: #ffbd4d; //橙色
$orangeBg: #ff7955; //橙色背景
$yellowBg: #fffad0; //黄色背景
$greenBg: #67cc70; //绿色背景
$blueBg: #e2f2ff; //蓝色背景
$gray: #ccc; //灰色
$red: #ff4133; //红色
$padding: 15px; //边距
$height1: 44px; //常用高度1
$border1: 1px solid #ccc; //常用边框1
$border2: 1px solid #bbbcc6; //常用边框2，头像边框
$borderRadius1: 8px; //常用圆角

/*0.5px细线条*/
@mixin fineLine() {
    position: relative;
    &:before {
        content: "";
        width: 100%;
        height: 0;
        border: 1px solid transparent;
        border-bottom-color: #f0f0f0;
        transform: scaleY(.5) translate3d(0, 50%, 0);
        position: absolute;
        left: 0;
        bottom: 0;
    }
}
@mixin fineLine-top() {
    position: relative;
    &:before {
        content: "";
        width: 100%;
        height: 0;
        border: 1px solid transparent;
        border-top-color: #f0f0f0;
        transform: scaleY(.5) translate3d(0, -50%, 0);
        position: absolute;
        left: 0;
        top: 0;
    }
}
@mixin fineLine-left() {
    position: relative;
    &:before {
        content: "";
        width: 0;
        height: 100%;
        border: 1px solid transparent;
        border-left-color: #f0f0f0;
        transform: scale(.5, 1) translate3d(-50%, 0, 0);
        position: absolute;
        left: 0;
        bottom: 0;
    }
}
@mixin fineLine-right() {
    position: relative;
    &:before {
        content: "";
        width: 0;
        height: 100%;
        border: 1px solid transparent;
        border-right-color: #f0f0f0;
        transform: scale(.5, 1) translate3d(50%, 0, 0);
        position: absolute;
        right: 0;
        bottom: 0;
    }
}

/*按钮样式*/
@mixin buttonWrap() {
    padding: 10px 50px;
    & button {
        width: 100%;
        height: 40px;
        line-height: 40px;
        text-align: center;
        background-color: $gray;
        color: #fff;
        border: none;
        border-radius: $borderRadius1;
        font-size: 32px;
        &:active {
            opacity: 1;
        }
        &.active {
            background-color: $greenBg;
            &:active {
                opacity: 0.8;
            }
        }
    }
}

/*头像样式患者*/
@mixin portrait($path) {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: $bg url($path + 'assets/img/pat_avatar.png') no-repeat center center;
    background-size: cover;
    position: relative;
    &:before {
        content: "";
        width: 10px;
        height: 10px;
        background-color: #ff0000;
        border-radius: 50%;
        position: absolute;
        left: 2px;
        top: 2px;
        z-index: 10;
        display: none;
    }
    &.unread {
        &:before {
            display: block;
        }
    }
}

/*带头像个人信息样式*/
@mixin patientInfo($path, $width, $lineHeight) {
    display: flex;
    align-items: center;
    padding: 15px;
    cursor: pointer;
    .portrait {
        @include portrait($path);
        width: #{$width}px;
        height: #{$width}px;
    }
    .rightContent {
        flex: 1;
        padding: 5px 0;
        padding-left: 10px;
        line-height: #{$lineHeight}px;
        h3 {
            display: flex;
            >span {
                max-width: 150px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
                word-break: break-all;
                font-size: 14px;
            }
            >em {
                padding-left: 20px;
                margin-left: 10px;
                font-size: 12px;
                background: url($path + 'assets/img/Male.png') no-repeat left center;
                background-size: 10px;
                color: #666;
                &.female {
                    background-image: url($path + 'assets/img/Female.png');
                }
            }
            >a {
                display: inline-block;
                padding: 0 10px;
                margin-left: 10px;
                height: 26px;
                line-height: 24px;
                text-align: center;
                color: $main;
                font-size: 12px;
                border: 1px solid $main;
                border-radius: 13px;
                span {
                    padding-right: 15px;
                    background: url($path + 'assets/img/arrow_right.png') no-repeat right center;
                    background-size: 8px;
                }
            }
        }
        h4 {
            color: #666;
            span {
                padding-right: 40px;
            }
        }
    }
    &.active {
        background-color: #fff;
        .rightContent {
            h3 {
                >span {
                    font-weight: bold;
                }
            }
        }
    }
}

/*复诊配药content样式*/
@mixin content() {
    background-color: #fff;
    border: $border1;
    border-radius: 10px;
    zoom: 1;
    &:after {
        content: "";
        display: block;
        clear: both;
    }
}

/*基本健康信息样式*/
@mixin basicTitle() {
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
    background-color: #f6f7fb;
    color: #000;
    border-radius: 1px;
}

/*element-dialog样式*/
@mixin elementDialog() {
    /deep/ .el-dialog {
        width: 360px;
        border-radius: 5px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: 0 !important;
        transform: translate3d(-50%, -50%, 0);
        .el-dialog__body {
            padding: 0;
            .wrap {
                padding: 10px 50px;
                .title {
                    padding: 5px 0;
                    line-height: 26px;
                    color: #666;
                }
                .main {
                    /deep/ .el-select {
                        width: 100%;
                        .el-input__inner {
                            border-color: #ccc;
                            border-radius: 16px;
                        }
                    }
                }
                .end {
                    @include handleWrap;
                }
            }
        }
    }
}

/*table样式1*/
@mixin table1() {
    width: 100%;
    thead {
        tr {
            background-color: #fff;
        }
    }
    tbody {
        tr {
            &:nth-of-type(odd) {
                background-color: $bg;
            }
            &:nth-of-type(even) {
                background-color: #fff;
            }
        }
    }
    th, td {
        max-width: 200px;
        .item {
            padding: 24px 10px;
            line-height: 20px;
            text-align: center;
            &.handle {
                display: flex;
                justify-content: center;
                a {
                    margin-right: 15px;
                    height: 20px;
                    cursor: pointer;
                }
                &.blue {
                    a {
                        color: $main;
                    }
                }
            }
        }
    }
}

/*table样式4*/
@mixin table4() {
    /deep/ {
        .el-table {
            border: none;
            th, td {
                padding: 10px 5px;
            }
        }
        .el-table--border::after, .el-table--group::after, .el-table::before {
            display: none !important;
        }
        .el-table__body {
            border-right: none;
            tr {
                &:last-of-type {
                    td {
                        border-bottom: none;
                    }
                }
                .el-button {
                    span {
                        color: $main !important;
                    }
                }
            }
        }
    }
}

/*随访问卷个人信息样式*/
@mixin patientInfo1($path) {
    display: flex;
    margin-bottom: 20px;
    .avatar {
        width: 50px;
        min-width: 50px;
        height: 50px;
        background: url($path + 'assets/img/pat_avatar.png') no-repeat center center;
        background-size: cover;
        border-radius: 50%;
    }
    .rightContent {
        line-height: 25px;
        padding-left: 20px;
        h3 {
            display: flex;
            >span {
                overflow: hidden;
                word-break: break-all;
                font-size: 14px;
            }
            >em {
                padding-left: 20px;
                margin-left: 10px;
                font-size: 12px;
                background: url($path + 'assets/img/Male.png') no-repeat left center;
                background-size: 10px;
                color: #666;
                &.female {
                    background-image: url($path + 'assets/img/Female.png');
                }
            }
            >a {
                display: inline-block;
                padding: 0 10px;
                margin-left: 10px;
                height: 26px;
                line-height: 24px;
                text-align: center;
                color: $main;
                font-size: 12px;
                border: 1px solid $main;
                border-radius: 13px;
                span {
                    padding-right: 10px;
                    background: url($path + 'assets/img/arrow_right.png') no-repeat right center;
                    background-size: 4px;
                }
            }
        }
    }
}

/*随访问卷按钮样式*/
@mixin buttonWrap1() {
    display: flex;
    justify-content: center;
    padding-top: 50px;
    button {
        padding: 0 20px;
        margin: 0 10px;
        height: 30px;
        border: 1px solid #ddd;
        border-radius: 3px;
        color: #666;
        &.blue {
            background-color: $main;
            color: #fff;
            border-color: $main;
        }
    }
}

/*处方table改造样式*/
@mixin tableTitle() {
  .item span {
    text-align: left;
  }
}

/*table样式1*/
@mixin table2($path) {
    width: 100%;
    thead {
        tr {
            background-color: #f8f8f8;
            color: #666;
        }
    }
    tbody {
        tr {
            &:nth-of-type(odd) {
                background-color: #fff;
            }
            &:nth-of-type(even) {
                background-color: $bg;
            }
            &.active {
                border: 1px solid $main;
            }
        }
    }
    th, td {
        max-width: 100px;
        .item {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 14px 10px;
            line-height: 20px;
            text-align: center;
            >span {
                flex: 1;
                input {
                    width: 100%;
                    padding: 0;
                    height: 20px;
                    text-align: center;
                    border: none;
                    border-bottom: $border1;
                    background-color: transparent;
                    font-size: 12px;
                    appearance: none;
                    &:focus {
                        border-color: #52a3fc;
                    }
                }
                /deep/ .queryDrugList {
                    >.inputWrap {
                        >input {
                            border-bottom: $border1;
                            &:focus {
                                border-color: #52a3fc;
                            }
                        }
                    }
                }
                select {
                    width: 100%;
                    font-size: 12px;
                }
            }
            >em {
                width: 50px;
                height: 20px;
            }
            .deleteDrug {
                width: 30px;
                height: 20px;
                background: url($path + 'assets/img/icon_del.png') no-repeat right center;
                background-size: 14px;
            }
        }
    }
}

/*table样式3*/
@mixin table3($path) {
    width: 100%;
    border: $border1;
    thead {
        tr {
            background-color: #f8f8f8;
        }
    }
    tbody {
        tr {
            &:nth-of-type(odd) {
                background-color: #fff;
            }
            &:nth-of-type(even) {
                background-color: $bg;
            }
        }
    }
    th, td {
        max-width: 200px;
        .item {
            padding: 12px 10px;
            line-height: 20px;
            text-align: center;
            &.handle {
                display: flex;
                justify-content: center;
                a {
                    padding-left: 15px;
                    margin-right: 15px;
                    height: 20px;
                    background: url($path + 'assets/img/icon_look.png') no-repeat left center;
                    background-size: 12px;
                    cursor: pointer;
                }
            }
        }
    }
    tr {
        &.active {
            .item {
                &.handle {
                    a {
                        color: $main;
                    }
                }
            }
        }
    }
}

/*处方table改造样式*/
@mixin tableMain($path) {
  display: flex;
  .leftContent {
    flex: 1;
  }
  .rightContent {
    min-width: 180px;
    background-color: #f8f8f8;
    .expansion {
      li {
        float: left;
        padding: 7px 0;
        padding-left: 10px;
        a {
          display: inline-block;
          padding-left: 25px;
          line-height: 34px;
          background: url($path + 'assets/img/icon_off.png') no-repeat left center;
          background-size: 16px;
          position: relative;
          label {
            cursor: pointer;
          }
        }
        &.active {
          a {
            background-image: url($path + 'assets/img/icon_on.png');
          }
        }
      }
    }
  }
}

/*滚动table样式(加在table父级的div需要循环两次)*/
/*
    示例：
    <div
        v-for="tableName in ['theadWrap','tbodyWrap']"
        :class="{
            tableWrap:true,
            [tableName]:true,
        }"
    >
        //请在此处写table
    </div>
*/

@mixin scrollTable() {
    &.theadWrap {
        border: $border1;
        border-bottom: none;
        height: 46px;
        overflow: hidden;
        table {
            border: none;
        }
    }
    &.tbodyWrap {
        border: $border1;
        border-top: none;
        max-height: 442px;
        overflow-y: auto;
        table {
            border: none;
            thead {
                display: none;
            }
        }
    }
}

/*带头部的table样式*/
@mixin titleAndTable() {
    width: 100%;
    padding: 20px;
    .pagination {
        margin-top: 20px;
    }
    .list-main-head {
        padding-bottom: 10px;
        overflow: hidden;
        .fieldName {
            float: left;
            margin-right: 10px;
            margin-bottom: 20px;
            height: 30px;
            line-height: 30px;
            .shopName {
                float: left;
                height: 30px;
                line-height: 30px;
                text-align: right;
                margin-right: 5px;
            }
            .shopValue {
                width: 150px;
                height: 30px;
                float: left;
                margin-right: 10px;
                /deep/ .el-input__inner {
                    border: none;
                    border-radius: 16px;
                }
            }
        }
        .handleWrap {
            button {
                width: 100px;
                height: 30px;
                background-color: $main;
                color: #fff;
                border: none;
                border-radius: 15px;
                font-size: 12px;
                &:active {
                    opacity: .8;
                    filter: alpha(opacity=.8);
                }
                &:last-of-type {
                    background-color: #fe8081;
                }
            }
        }
        .allRead {
            float: right;
            margin-right: 0;
            button {
                width: 120px;
                height: 30px;
                font-size: 12px;
                background-color: $blueBg;
                color: $main;
                border: none;
                border-radius: 15px;
            }
        }
        .line {
            float: left;
            margin-right: 10px;
            color: #ccc;
        }
    }
    /deep/ .el-table {
        background-color: #f7f7f7;
        tr {
            height: 68px;
            background-color: transparent !important;
            &:nth-of-type(odd) {
                background-color: #fff !important;
            }
            .cell {
                text-align: center;
                button {
                    width: 50px;
                    margin: 0 5px;
                    height: 20px;
                    background-color: $main;
                    color: #fff;
                    border: none;
                    border-radius: 15px;
                    font-size: 12px;
                }
            }
        }
        tbody {
            tr {
                cursor: pointer;
            }
        }
    }
    /deep/ .pagination {
        .el-input__inner {
            height: 28px;
        }
    }
}

/*多个操作按钮样式*/
@mixin handleWrap() {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    &.left {
        justify-content: flex-start;
    }
    &.right {
        justify-content: flex-end;
    }
    >a {
        width: 120px;
        margin: 0 10px;
        height: 36px;
        line-height: 34px;
        text-align: center;
        background-color: $main;
        color: #fff;
        border: 1px solid transparent;
        border-radius: 18px;
        &:first-of-type {
            margin-left: 0;
        }
        &:last-of-type {
            margin-right: 0;
        }
        &.border {
            background-color: #fff;
            border-color: $main;
            color: $main;
        }
        &.borderGray {
            background-color: #fff;
            border-color: #ccc;
            color: #666;
        }
        &.orange {
            background-color: $orange;
        }
        &.red {
            background-color: #fe8081;
        }
    }
}

/*红色文字提示*/
@mixin hintText($path) {
    .hint {
        display: none;
        width: 624px;
        line-height: 30px;
        padding-left: 20px;
        background: url($path + 'assets/img/ico_tixing.png') no-repeat left center;
        background-size: 14px;
        color: $red;
        position: absolute;
        left: 0;
        bottom: -30px;
    }
    &.hasHint {
        >.right {
            >input, .zdWrap /deep/ .queryDiagnoseList {
                border-color: $red;
                box-shadow: 0 0 7px rgba(255, 65, 51, .2);
            }
        }
        .hint {
            display: block;
        }
    }
}

/*录入病历样式*/
@mixin caseHistory($path) {
    padding-left: 130px;
    padding-top: 30px;
    >li {
        display: flex;
        padding-bottom: 30px;
        &.tz {
            padding-bottom: 10px;
        }
        >.left {
            display: flex;
            width: 100px;
            margin-right: 24px;
            line-height: 30px;
            text-align: right;
            font-size: 12px;
            span {
                em {
                    color: $red;
                }
                color: #666;
                width: 90px;
                display: inline-block;
            }
        }
        >.right {
            position: relative;
            >input {
                width: 624px;
                padding: 5px 10px;
                height: 34px;
                border: $border1;
                border-radius: $borderRadius1;
                font-size: 12px;
            }
            .xbsWrap, .jwsWrap {
                width: 624px;
                padding: 10px;
                height: 97px;
                border: $border1;
                border-radius: $borderRadius1;
                textarea {
                    width: 100%;
                    height: 100%;
                    line-height: 25px;
                    border: none;
                }
            }
            .tzList {
                width: 624px;
                line-height: 20px;
                overflow: hidden;
                li {
                    float: left;
                    padding-right: 36px;
                    padding-bottom: 24px;
                    input {
                        width: 50px;
                        padding: 0 5px;
                        text-align: center;
                        border: none;
                        border-bottom: 1px solid #333;
                    }
                }
            }
            .zdWrap {}
            .cfcsWrap {
                display: flex;
                width: 624px;
                .leftContent {
                    flex: 1;
                    .cfcsList {
                        display: flex;
                        padding: 12px 0;
                        >span {
                            width: 50px;
                            margin-top: 10px;
                            height: 22px;
                            line-height: 22px;
                            text-align: center;
                            background-color: $orange;
                            color: #fff;
                            border-radius: 11px;
                        }
                        ul {
                            flex: 1;
                            li {
                                padding: 0 16px;
                                padding-bottom: 10px;
                                line-height: 24px;
                                h3 {
                                    span {
                                        padding-left: 110px;
                                        &:first-of-type {
                                            padding-left: 0;
                                        }
                                    }
                                }
                                h4 {
                                    color: #999;
                                    span {
                                        padding-left: 64px;
                                        &:first-of-type {
                                            padding-left: 0;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    .treatMethod {
                        border: $border1;
                        border-radius: $borderRadius1;
                        overflow: hidden;
                        textarea {
                            width: 100%;
                            height: 100%;
                            line-height: 20px;
                            font-size: 12px;
                            border: 10px solid transparent;
                        }
                    }
                }
                .cfcsButtonWrap {
                    width: 108px;
                    padding: 0 10px;
                    a {
                        display: block;
                        width: 100%;
                        height: 36px;
                        line-height: 36px;
                        text-align: center;
                        background-color: $blueBg;
                        color: $main;
                        border-radius: 18px;
                    }
                }
            }
            .clyjWrap {
                width: 624px;
                padding: 10px;
                height: 72px;
                border: $border1;
                border-radius: $borderRadius1;
                flex: 1;
                textarea {
                    width: 100%;
                    height: 100%;
                    line-height: 25px;
                    border: none;
                }
            }
            .blfyWrap {
                .blfyList {
                    width: 700px;
                    overflow: hidden;
                    li {
                        float: left;
                        width: 20%;
                        padding-bottom: 10px;
                        a {
                            display: inline-block;
                            padding-left: 25px;
                            line-height: 34px;
                            background: url($path + 'assets/img/icon_off.png') no-repeat left center;
                            background-size: 16px;
                            position: relative;
                            label {
                                cursor: pointer;
                            }
                        }
                        &.active {
                            a {
                                background-image: url($path + 'assets/img/icon_on.png');
                            }
                        }
                        &:nth-last-of-type(2) {
                            width: auto;
                            padding-right: 30px;
                        }
                        &:last-of-type {
                            input {
                                width: 264px;
                                padding: 0 10px;
                                height: 34px;
                                border: $border1;
                                border-radius: $borderRadius1;
                            }
                        }
                    }
                }
            }
            .yyywList {
                li {
                    float: left;
                    width: 65px;
                    a {
                        display: inline-block;
                        padding-left: 25px;
                        line-height: 34px;
                        background: url($path + 'assets/img/icon_off2.png') no-repeat left center;
                        background-size: 16px;
                        position: relative;
                        label {
                            cursor: pointer;
                        }
                    }
                    &.active {
                        a {
                            background-image: url($path + 'assets/img/icon_on2.png');
                        }
                    }
                }
            }
        }
        @include hintText($path);
    }
}

/*输入模板名称弹窗样式*/
@mixin nameDialogWrap($path) {
    @include elementDialog;
    .main {
        @include hintText($path);
        margin-bottom: 30px;
        position: relative;
        .templateName {
            width: 100%;
            padding: 5px 20px;
            height: 36px;
            border-radius: 18px;
        }
        .hint {
            width: 100%;
        }
    }
    .end {
        padding-top: 10px !important;
    }
}

/*加载更多样式*/
@mixin loadingWrap($path) {
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #999;
    overflow: hidden;
    display: none;
    h3 {
        span {
            display: inline-block;
            padding-right: 20px;
            background: url($path + 'assets/img/loading.gif') no-repeat right center;
            background-size: 14px;
        }
    }
    h3, h4 {
        display: none;
    }
    &.loading {
        display: block;
        h3 {
            display: block;
        }
    }
    &.finished {
        display: block;
        h4 {
            display: block;
        }
    }
}

/*病情图片样式*/
@mixin illnessPic() {
    float: left;
    width: 160px;
    margin-right: 10px;
    margin-bottom: 10px;
    height: 120px;
    background: no-repeat center center;
    background-size: cover;
    position: relative;
    .check {
        width: 100%;
        height: 28px;
        line-height: 28px;
        text-align: center;
        background-color: rgba(82, 163, 252, .9);
        color: #fff;
        position: absolute;
        left: 0;
        bottom: 0;
    }
    &.delete {
        filter: grayscale(1);
    }
}

/*药品列表样式*/
@mixin drugList() {
    padding-left: 30px;
    padding-bottom: 10px;
    position: relative;
    &:before {
        content: "西";
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        background-color: $main;
        color: #fff;
        border-radius: 2px;
        position: absolute;
        left: 0;
        top: 4px;
    }
    li {
        overflow: hidden;
        .wrap {
            float: left;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            line-height: 24px;
            .left {
                flex: 1;
                padding-right: 80px;
                h3 {
                    span {
                        padding-left: 10px;
                        &:first-of-type {
                            padding-left: 0;
                        }
                    }
                }
                h4 {
                    display: flex;
                    color: #999;
                    span {
                        padding-left: 20px;
                        &:first-of-type {
                            padding-left: 0;
                        }
                    }
                }
            }
            .right {
                width: 50px;
            }
        }
    }
}
@mixin table() {
    /deep/ .el-table {
        thead {
            tr, th {
                text-align: center;
                background-color: $bg;
                border-right-color: transparent;
            }
        }
        td {
            text-align: center;
        }
    }
}

/*条件搜索样式*/
@mixin conditionSearch() {
    display: flex;
    padding-bottom: 30px;
    ol {
        display: flex;
        li {
            display: flex;
            padding-right: 20px;
            line-height: 34px;
            span {
                color: #666;
            }
            .inputWrap {
                margin-left: 15px;
                width: 200px;
                height: 34px;
                input {
                    width: 100%;
                    background-color: transparent;
                    border: none;
                    font-size: 12px;
                    &::-webkit-input-placeholder {
                        font-size: 12px;
                    }
                }
            }
            .selectWrap {
                margin-left: 15px;
            }
        }
    }
    .search {
        width: 98px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        background-color: $main;
        color: #fff;
        border-radius: 17px;
    }
}

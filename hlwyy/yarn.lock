# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


abbrev@1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/abbrev/download/abbrev-1.1.1.tgz#f8f2c887ad10bf67f634f005b6987fed3179aac8"

accepts@~1.3.4, accepts@~1.3.5:
  version "1.3.5"
  resolved "http://registry.npm.taobao.org/accepts/download/accepts-1.3.5.tgz#eb777df6011723a3b14e8a72c0805c8e86746bd2"
  dependencies:
    mime-types "~2.1.18"
    negotiator "0.6.1"

acorn-dynamic-import@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/acorn-dynamic-import/download/acorn-dynamic-import-2.0.2.tgz#c752bd210bef679501b6c6cb7fc84f8f47158cc4"
  dependencies:
    acorn "^4.0.3"

acorn@^4.0.3:
  version "4.0.13"
  resolved "http://registry.npm.taobao.org/acorn/download/acorn-4.0.13.tgz#105495ae5361d697bd195c825192e1ad7f253787"

acorn@^5.0.0, acorn@^5.3.0:
  version "5.7.3"
  resolved "http://registry.npm.taobao.org/acorn/download/acorn-5.7.3.tgz#67aa231bf8812974b85235a96771eb6bd07ea279"

ajv-keywords@^3.1.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.2.0.tgz#e86b819c602cf8821ad637413698f1dec021847a"

ajv@^5.0.0, ajv@^5.1.0, ajv@^5.3.0:
  version "5.5.2"
  resolved "http://registry.npm.taobao.org/ajv/download/ajv-5.5.2.tgz#73b5eeca3fab653e3d3f9422b341ad42205dc965"
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ajv@^6.1.0:
  version "6.5.3"
  resolved "http://registry.npm.taobao.org/ajv/download/ajv-6.5.3.tgz#71a569d189ecf4f4f321224fecb166f071dd90f9"
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/align-text/download/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

alphanum-sort@^1.0.1, alphanum-sort@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz#97a1119649b211ad33691d9f9f486a8ec9fbe0a3"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/amdefine/download/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-html@0.0.7:
  version "0.0.7"
  resolved "http://registry.npm.taobao.org/ansi-html/download/ansi-html-0.0.7.tgz#813584021962a9e9e6fd039f940d12f56ca7859e"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  dependencies:
    color-convert "^1.9.0"

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

aproba@^1.0.3, aproba@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"

are-we-there-yet@~1.1.2:
  version "1.1.5"
  resolved "http://registry.npm.taobao.org/are-we-there-yet/download/are-we-there-yet-1.1.5.tgz#4b35c2944f062a8bfcda66410760350fe9ddfc21"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/arr-diff/download/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  dependencies:
    arr-flatten "^1.0.1"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"

arr-flatten@^1.0.1, arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"

array-flatten@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/array-flatten/download/array-flatten-2.1.1.tgz#426bb9da84090c1838d812c8150af20a8331e296"

array-includes@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.taobao.org/array-includes/download/array-includes-3.0.3.tgz#184b48f62d92d7452bb31b323165c7f8bd02266d"
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.7.0"

array-union@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

array-unique@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/array-unique/download/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

asn1.js@^4.0.0:
  version "4.10.1"
  resolved "http://registry.npm.taobao.org/asn1.js/download/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.4"
  resolved "http://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

assert@^1.1.1:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/assert/download/assert-1.4.1.tgz#99912d591836b5a6f5b345c0f07eefc08fc65d91"
  dependencies:
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"

async-each@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/async-each/download/async-each-1.0.1.tgz#19d386a1d9edc6e7c1c85d388aedbcc56d33602d"

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/async-foreach/download/async-foreach-0.1.3.tgz#36121f845c0578172de419a97dbeb1d16ec34542"

async-limiter@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.0.tgz#78faed8c3d074ab81f22b4e985d79e8738f720f8"

async-validator@~1.8.1:
  version "1.8.5"
  resolved "http://registry.npm.taobao.org/async-validator/download/async-validator-1.8.5.tgz#dc3e08ec1fd0dddb67e60842f02c0cd1cec6d7f0"
  dependencies:
    babel-runtime "6.x"

async@^1.5.2:
  version "1.5.2"
  resolved "http://registry.npm.taobao.org/async/download/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"

async@^2.1.2, async@^2.4.1:
  version "2.6.1"
  resolved "http://registry.npm.taobao.org/async/download/async-2.6.1.tgz#b245a23ca71930044ec53fa46aa00a3e87c6a610"
  dependencies:
    lodash "^4.17.10"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

atob@^2.1.1:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"

autoprefixer@^6.3.1:
  version "6.7.7"
  resolved "http://registry.npm.taobao.org/autoprefixer/download/autoprefixer-6.7.7.tgz#1dbd1c835658e35ce3f9984099db00585c782014"
  dependencies:
    browserslist "^1.7.6"
    caniuse-db "^1.0.30000634"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^5.2.16"
    postcss-value-parser "^3.2.3"

autoprefixer@^7.1.2:
  version "7.2.6"
  resolved "http://registry.npm.taobao.org/autoprefixer/download/autoprefixer-7.2.6.tgz#256672f86f7c735da849c4f07d008abb056067dc"
  dependencies:
    browserslist "^2.11.3"
    caniuse-lite "^1.0.30000805"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^6.0.17"
    postcss-value-parser "^3.2.3"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"

aws4@^1.6.0, aws4@^1.8.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/aws4/download/aws4-1.8.0.tgz#f0e003d9ca9e7f59c7a508945d7b2ef9a04a542f"

axios@0.17.1:
  version "0.17.1"
  resolved "http://registry.npm.taobao.org/axios/download/axios-0.17.1.tgz#2d8e3e5d0bdbd7327f91bc814f5c57660f81824d"
  dependencies:
    follow-redirects "^1.2.5"
    is-buffer "^1.1.5"

babel-code-frame@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

babel-core@^6.22.1, babel-core@^6.26.0:
  version "6.26.3"
  resolved "http://registry.npm.taobao.org/babel-core/download/babel-core-6.26.3.tgz#b2e2f09e342d0f0c88e2f02e067794125e75c207"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-generator "^6.26.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.26.0"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    convert-source-map "^1.5.1"
    debug "^2.6.9"
    json5 "^0.5.1"
    lodash "^4.17.4"
    minimatch "^3.0.4"
    path-is-absolute "^1.0.1"
    private "^0.1.8"
    slash "^1.0.0"
    source-map "^0.5.7"

babel-generator@^6.26.0:
  version "6.26.1"
  resolved "http://registry.npm.taobao.org/babel-generator/download/babel-generator-6.26.1.tgz#1844408d3b8f0d35a404ea7ac180f087a601bd90"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.17.4"
    source-map "^0.5.7"
    trim-right "^1.0.1"

babel-helper-bindify-decorators@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz#14c19e5f142d7b47f19a52431e52b1ccbc40a330"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz#cce4517ada356f4220bcae8a02c2b346f9a56664"
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz#ece6aacddc76e41c3461f88bfc575bd0daa2df8d"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-helper-define-map/download/babel-helper-define-map-6.26.0.tgz#a5f56dab41a25f97ecb498c7ebaca9819f95be5f"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz#f25b82cf7dc10433c55f70592d5746400ac22caa"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-explode-class@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz#7dc2a3910dee007056e1e31d640ced3d54eaa9eb"
  dependencies:
    babel-helper-bindify-decorators "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz#1ecb27689c9d25513eadbc9914a73f5408be7a76"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-helper-regex/download/babel-helper-regex-6.26.0.tgz#325c59f902f82f24b74faceed0363954f6495e72"
  dependencies:
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz#5ec581827ad723fecdd381f1c928390676e4551b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-vue-jsx-merge-props@^2.0.0, babel-helper-vue-jsx-merge-props@^2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.3.tgz#22aebd3b33902328e513293a8e4992b384f9f1b6"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helpers/download/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-loader@^7.1.1:
  version "7.1.5"
  resolved "http://registry.npm.taobao.org/babel-loader/download/babel-loader-7.1.5.tgz#e3ee0cd7394aa557e013b02d3e492bfd07aa6d68"
  dependencies:
    find-cache-dir "^1.0.0"
    loader-utils "^1.0.2"
    mkdirp "^0.5.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-messages/download/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"

babel-plugin-syntax-async-generators@^6.5.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz#6bc963ebb16eccbae6b92b596eb7f35c342a8b9a"

babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz#d7eb23b79a317f8543962c505b827c7d6cac27de"

babel-plugin-syntax-decorators@^6.13.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz#312563b4dbde3cc806cee3e416cceeaddd11ac0b"

babel-plugin-syntax-dynamic-import@^6.18.0:
  version "6.18.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz#8d6a26229c83745a9982a441051572caa179b1da"

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"

babel-plugin-syntax-jsx@^6.18.0:
  version "6.18.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-jsx/download/babel-plugin-syntax-jsx-6.18.0.tgz#0af32a9a6e13ca7a3fd5069e62d7b0f58d0d8946"

babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz#fd6536f2bce13836ffa3a5458c4903a597bb3bf5"

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"

babel-plugin-transform-async-generator-functions@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz#f058900145fd3e9907a6ddf28da59f215258a5db"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-generators "^6.5.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-async-to-generator@^6.22.0, babel-plugin-transform-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz#6536e378aff6cb1d5517ac0e40eb3e9fc8d08761"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-properties@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz#6a79763ea61d33d36f37b611aa9def81a81b46ac"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-decorators@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz#788013d8f8c6b5222bdf7b344390dfd77569e24d"
  dependencies:
    babel-helper-explode-class "^6.24.1"
    babel-plugin-syntax-decorators "^6.13.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz#452692cb711d5f79dc7f85e440ce41b9f244d221"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz#bbc51b49f964d70cb8d8e0b94e820246ce3a6141"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.23.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.26.0.tgz#d70f5299c1308d05c12f463813b0a09e73b1895f"
  dependencies:
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    lodash "^4.17.4"

babel-plugin-transform-es2015-classes@^6.23.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz#6fe2a8d16895d5634f4cd999b6d3480a308159b3"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.23.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz#997bb1f1ab967f682d2b0876fe358d60e765c56d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz#73eb3d310ca969e3ef9ec91c53741a6f1576423e"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.23.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz#f47c95b2b613df1d3ecc2fdb7573623c75248691"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz#834c89853bc36b1af0f3a4c5dbaa94fd8eacaa8b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz#4f54a02d6cd66cf915280019a31d31925377ca2e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.22.0, babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz#3b3e54017239842d6d19c3011c4bd2f00a00d154"
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.23.0, babel-plugin-transform-es2015-modules-commonjs@^6.24.1:
  version "6.26.2"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.26.2.tgz#58a793863a9e7ca870bdc5a881117ffac27db6f3"
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.26.0"
    babel-template "^6.26.0"
    babel-types "^6.26.0"

babel-plugin-transform-es2015-modules-systemjs@^6.23.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz#ff89a142b9119a906195f5f106ecf305d9407d23"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.23.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz#ac997e6285cd18ed6176adb607d602344ad38468"
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz#24cef69ae21cb83a7f8603dad021f572eb278f8d"
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.23.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz#57ac351ab49caf14a97cd13b09f66fdf0a625f2b"
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz#24f875d6721c87661bbd99a4622e51f14de38aa0"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz#00c1cdb1aca71112cdf0cf6126c2ed6b457ccdbc"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz#a84b3450f7e9f8f1f6839d6d687da84bb1236d8d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.23.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz#dec09f1cddff94b52ac73d505c84df59dcceb372"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz#d38b12f42ea7323f729387f18a7c5ae1faeb35e9"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.22.0, babel-plugin-transform-exponentiation-operator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz#2ab0c9c7f3098fa48907772bb813fe41e8de3a0e"
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.22.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.26.0.tgz#0f36692d50fef6b7e2d4b3ac1478137a963b7b06"
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.26.0"

babel-plugin-transform-regenerator@^6.22.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.26.0.tgz#e0703696fbde27f0a3efcacf8b4dca2f7b3a8f2f"
  dependencies:
    regenerator-transform "^0.10.0"

babel-plugin-transform-runtime@^6.22.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-runtime/download/babel-plugin-transform-runtime-6.23.0.tgz#88490d446502ea9b8e7efb0fe09ec4d99479b1ee"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz#d5faf7aa578a65bbe591cf5edae04a0c67020758"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-vue-jsx@^3.5.0:
  version "3.7.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-vue-jsx/download/babel-plugin-transform-vue-jsx-3.7.0.tgz#d40492e6692a36b594f7e9a1928f43e969740960"
  dependencies:
    esutils "^2.0.2"

babel-preset-env@^1.3.2:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/babel-preset-env/download/babel-preset-env-1.7.0.tgz#dea79fa4ebeb883cd35dab07e260c1c9c04df77a"
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-to-generator "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.23.0"
    babel-plugin-transform-es2015-classes "^6.23.0"
    babel-plugin-transform-es2015-computed-properties "^6.22.0"
    babel-plugin-transform-es2015-destructuring "^6.23.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.22.0"
    babel-plugin-transform-es2015-for-of "^6.23.0"
    babel-plugin-transform-es2015-function-name "^6.22.0"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.22.0"
    babel-plugin-transform-es2015-modules-commonjs "^6.23.0"
    babel-plugin-transform-es2015-modules-systemjs "^6.23.0"
    babel-plugin-transform-es2015-modules-umd "^6.23.0"
    babel-plugin-transform-es2015-object-super "^6.22.0"
    babel-plugin-transform-es2015-parameters "^6.23.0"
    babel-plugin-transform-es2015-shorthand-properties "^6.22.0"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.22.0"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.23.0"
    babel-plugin-transform-es2015-unicode-regex "^6.22.0"
    babel-plugin-transform-exponentiation-operator "^6.22.0"
    babel-plugin-transform-regenerator "^6.22.0"
    browserslist "^3.2.6"
    invariant "^2.2.2"
    semver "^5.3.0"

babel-preset-stage-2@^6.22.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz#d9e2960fb3d71187f0e64eec62bc07767219bdc1"
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-class-properties "^6.24.1"
    babel-plugin-transform-decorators "^6.24.1"
    babel-preset-stage-3 "^6.24.1"

babel-preset-stage-3@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz#836ada0a9e7a7fa37cb138fb9326f87934a48395"
  dependencies:
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-generator-functions "^6.24.1"
    babel-plugin-transform-async-to-generator "^6.24.1"
    babel-plugin-transform-exponentiation-operator "^6.24.1"
    babel-plugin-transform-object-rest-spread "^6.22.0"

babel-register@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-register/download/babel-register-6.26.0.tgz#6ed021173e2fcb486d7acb45c6009a856f647071"
  dependencies:
    babel-core "^6.26.0"
    babel-runtime "^6.26.0"
    core-js "^2.5.0"
    home-or-tmp "^2.0.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    source-map-support "^0.4.15"

babel-runtime@6.x, babel-runtime@^6.18.0, babel-runtime@^6.22.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

babel-template@^6.24.1, babel-template@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-template/download/babel-template-6.26.0.tgz#de03e2d16396b069f46dd9fff8521fb1a0e35e02"
  dependencies:
    babel-runtime "^6.26.0"
    babel-traverse "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    lodash "^4.17.4"

babel-traverse@^6.24.1, babel-traverse@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-traverse/download/babel-traverse-6.26.0.tgz#46a9cbd7edcc62c8e5c064e2d2d8d0f4035766ee"
  dependencies:
    babel-code-frame "^6.26.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.26.0"
    babel-types "^6.26.0"
    babylon "^6.18.0"
    debug "^2.6.8"
    globals "^9.18.0"
    invariant "^2.2.2"
    lodash "^4.17.4"

babel-types@^6.19.0, babel-types@^6.24.1, babel-types@^6.26.0:
  version "6.26.0"
  resolved "http://registry.npm.taobao.org/babel-types/download/babel-types-6.26.0.tgz#a3b073f94ab49eb6fa55cd65227a334380632497"
  dependencies:
    babel-runtime "^6.26.0"
    esutils "^2.0.2"
    lodash "^4.17.4"
    to-fast-properties "^1.0.3"

babylon@^6.18.0:
  version "6.18.0"
  resolved "http://registry.npm.taobao.org/babylon/download/babylon-6.18.0.tgz#af2f3b88fa6f5c1e4c634d1a0f8eac4f55b395e3"

balanced-match@^0.4.2:
  version "0.4.2"
  resolved "http://registry.npm.taobao.org/balanced-match/download/balanced-match-0.4.2.tgz#cb3f3e3c732dc0f01ee70b403f302e61d7709838"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

base64-js@^1.0.2:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/base64-js/download/base64-js-1.3.0.tgz#cab1e6118f051095e58b5281aea8c1cd22bfc0e3"

base@^0.11.1:
  version "0.11.2"
  resolved "http://registry.npm.taobao.org/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  dependencies:
    tweetnacl "^0.14.3"

bfj-node4@^5.2.0:
  version "5.3.1"
  resolved "http://registry.npm.taobao.org/bfj-node4/download/bfj-node4-5.3.1.tgz#e23d8b27057f1d0214fc561142ad9db998f26830"
  dependencies:
    bluebird "^3.5.1"
    check-types "^7.3.0"
    tryer "^1.0.0"

big.js@^3.1.3:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/big.js/download/big.js-3.2.0.tgz#a5fc298b81b9e0dca2e458824784b65c52ba588e"

binary-extensions@^1.0.0:
  version "1.12.0"
  resolved "http://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.12.0.tgz#c2d780f53d45bba8317a8902d4ceeaf3a6385b14"

block-stream@*:
  version "0.0.9"
  resolved "http://registry.npm.taobao.org/block-stream/download/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  dependencies:
    inherits "~2.0.0"

bluebird@^3.1.1, bluebird@^3.4.7, bluebird@^3.5.1:
  version "3.5.2"
  resolved "http://registry.npm.taobao.org/bluebird/download/bluebird-3.5.2.tgz#1be0908e054a751754549c270489c1505d4ab15a"

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.1.1, bn.js@^4.4.0:
  version "4.11.8"
  resolved "http://registry.npm.taobao.org/bn.js/download/bn.js-4.11.8.tgz#2cde09eb5ee341f484746bb0309b3253b1b1442f"

body-parser@1.18.2:
  version "1.18.2"
  resolved "http://registry.npm.taobao.org/body-parser/download/body-parser-1.18.2.tgz#87678a19d84b47d859b83199bd59bce222b10454"
  dependencies:
    bytes "3.0.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.1"
    http-errors "~1.6.2"
    iconv-lite "0.4.19"
    on-finished "~2.3.0"
    qs "6.5.1"
    raw-body "2.3.2"
    type-is "~1.6.15"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "http://registry.npm.taobao.org/bonjour/download/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "http://registry.npm.taobao.org/braces/download/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

braces@^2.3.0, braces@^2.3.1:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/browserify-rsa/download/browserify-rsa-4.0.1.tgz#21e0abfaf6f2029cf2fafb133567a701d4135524"
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.0.4"
  resolved "http://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.0.4.tgz#aa4eb68e5d7b658baa6bf6a57e630cbd7a93d298"
  dependencies:
    bn.js "^4.1.1"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.2"
    elliptic "^6.0.0"
    inherits "^2.0.1"
    parse-asn1 "^5.0.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  dependencies:
    pako "~1.0.5"

browserslist@^1.3.6, browserslist@^1.5.2, browserslist@^1.7.6:
  version "1.7.7"
  resolved "http://registry.npm.taobao.org/browserslist/download/browserslist-1.7.7.tgz#0bd76704258be829b2398bb50e4b62d1a166b0b9"
  dependencies:
    caniuse-db "^1.0.30000639"
    electron-to-chromium "^1.2.7"

browserslist@^2.11.3:
  version "2.11.3"
  resolved "http://registry.npm.taobao.org/browserslist/download/browserslist-2.11.3.tgz#fe36167aed1bbcde4827ebfe71347a2cc70b99b2"
  dependencies:
    caniuse-lite "^1.0.30000792"
    electron-to-chromium "^1.3.30"

browserslist@^3.2.6:
  version "3.2.8"
  resolved "http://registry.npm.taobao.org/browserslist/download/browserslist-3.2.8.tgz#b0005361d6471f0f5952797a76fc985f1f978fc6"
  dependencies:
    caniuse-lite "^1.0.30000844"
    electron-to-chromium "^1.3.47"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"

buffer@^4.3.0:
  version "4.9.1"
  resolved "http://registry.npm.taobao.org/buffer/download/buffer-4.9.1.tgz#6d1bb601b07a4efced97094132093027c95bc298"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-modules@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/builtin-modules/download/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"

bytes@3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"

cacache@^10.0.4:
  version "10.0.4"
  resolved "http://registry.npm.taobao.org/cacache/download/cacache-10.0.4.tgz#6452367999eff9d4188aefd9a14e9d7c6a263460"
  dependencies:
    bluebird "^3.5.1"
    chownr "^1.0.1"
    glob "^7.1.2"
    graceful-fs "^4.1.11"
    lru-cache "^4.1.1"
    mississippi "^2.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.2"
    ssri "^5.2.4"
    unique-filename "^1.1.0"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

camel-case@3.0.x:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/camelcase-keys/download/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^1.0.2:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"

camelcase@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-3.0.0.tgz#32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a"

camelcase@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-4.1.0.tgz#d545635be1e33c542649c69173e5de6acfae34dd"

caniuse-api@^1.5.2:
  version "1.6.1"
  resolved "http://registry.npm.taobao.org/caniuse-api/download/caniuse-api-1.6.1.tgz#b534e7c734c4f81ec5fbe8aca2ad24354b962c6c"
  dependencies:
    browserslist "^1.3.6"
    caniuse-db "^1.0.30000529"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-db@^1.0.30000529, caniuse-db@^1.0.30000634, caniuse-db@^1.0.30000639:
  version "1.0.30000885"
  resolved "http://registry.npm.taobao.org/caniuse-db/download/caniuse-db-1.0.30000885.tgz#cdc98dd168ed59678650071f7f6a70910e275bc8"

caniuse-lite@^1.0.30000792, caniuse-lite@^1.0.30000805, caniuse-lite@^1.0.30000844:
  version "1.0.30000885"
  resolved "http://registry.npm.taobao.org/caniuse-lite/download/caniuse-lite-1.0.30000885.tgz#e889e9f8e7e50e769f2a49634c932b8aee622984"

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"

center-align@^0.1.1:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/center-align/download/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.4.1:
  version "2.4.1"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-2.4.1.tgz#18c49ab16a037b6eb0152cc83e3471338215b66e"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

charenc@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/charenc/download/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"

check-types@^7.3.0:
  version "7.4.0"
  resolved "http://registry.npm.taobao.org/check-types/download/check-types-7.4.0.tgz#0378ec1b9616ec71f774931a3c6516fad8c152f4"

chokidar@^2.0.0, chokidar@^2.0.2:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/chokidar/download/chokidar-2.0.4.tgz#356ff4e2b0e8e43e322d18a372460bbcf3accd26"
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.0"
    braces "^2.3.0"
    glob-parent "^3.1.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    lodash.debounce "^4.0.8"
    normalize-path "^2.1.1"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
    upath "^1.0.5"
  optionalDependencies:
    fsevents "^1.2.2"

chownr@^1.0.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/chownr/download/chownr-1.1.1.tgz#54726b8b8fff4df053c42187e801fb4412df1494"

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

clap@^1.0.9:
  version "1.2.3"
  resolved "http://registry.npm.taobao.org/clap/download/clap-1.2.3.tgz#4f36745b32008492557f46412d66d50cb99bce51"
  dependencies:
    chalk "^1.1.3"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@4.2.x:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/clean-css/download/clean-css-4.2.1.tgz#2d411ef76b8569b6d0c84068dabe85b0aa5e5c17"
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^1.0.1:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/cli-spinners/download/cli-spinners-1.3.1.tgz#002c1990912d0d59580c93bd36c056de99e4259a"

cliui@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/cliui/download/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/cliui/download/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

clone-deep@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/clone-deep/download/clone-deep-2.0.2.tgz#00db3a1e173656730d1188c3d6aced6d7ea97713"
  dependencies:
    for-own "^1.0.0"
    is-plain-object "^2.0.4"
    kind-of "^6.0.0"
    shallow-clone "^1.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.taobao.org/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

coa@~1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/coa/download/coa-1.0.4.tgz#a9ef153660d6a86a8bdec0289a5c684d217432fd"
  dependencies:
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.3.0, color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  dependencies:
    color-name "1.1.3"

color-name@1.1.3, color-name@^1.0.0:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

color-string@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/color-string/download/color-string-0.3.0.tgz#27d46fb67025c5c2fa25993bfbf579e47841b991"
  dependencies:
    color-name "^1.0.0"

color@^0.11.0:
  version "0.11.4"
  resolved "http://registry.npm.taobao.org/color/download/color-0.11.4.tgz#6d7b5c74fb65e841cd48792ad1ed5e07b904d764"
  dependencies:
    clone "^1.0.2"
    color-convert "^1.3.0"
    color-string "^0.3.0"

colormin@^1.0.5:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/colormin/download/colormin-1.1.2.tgz#ea2f7420a72b96881a38aae59ec124a6f7298133"
  dependencies:
    color "^0.11.0"
    css-color-names "0.0.4"
    has "^1.0.1"

colors@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/colors/download/colors-1.1.2.tgz#168a4701756b6a7f51a12ce0c97bfa28c084ed63"

combined-stream@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.6.tgz#723e7df6e801ac5613113a7e445a9b69cb632818"
  dependencies:
    delayed-stream "~1.0.0"

combined-stream@~1.0.5, combined-stream@~1.0.6:
  version "1.0.7"
  resolved "http://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.7.tgz#2d1d24317afb8abe95d6d2c0b07b57813539d828"
  dependencies:
    delayed-stream "~1.0.0"

commander@2.17.x, commander@~2.17.1:
  version "2.17.1"
  resolved "http://registry.npm.taobao.org/commander/download/commander-2.17.1.tgz#bd77ab7de6de94205ceacc72f1716d29f20a77bf"

commander@^2.13.0:
  version "2.18.0"
  resolved "http://registry.npm.taobao.org/commander/download/commander-2.18.0.tgz#2bf063ddee7c7891176981a2cc798e5754bc6970"

commander@~2.13.0:
  version "2.13.0"
  resolved "http://registry.npm.taobao.org/commander/download/commander-2.13.0.tgz#6964bca67685df7c1f1430c584f07d7597885b9c"

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"

component-emitter@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/component-emitter/download/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"

compressible@~2.0.14:
  version "2.0.15"
  resolved "http://registry.npm.taobao.org/compressible/download/compressible-2.0.15.tgz#857a9ab0a7e5a07d8d837ed43fe2defff64fe212"
  dependencies:
    mime-db ">= 1.36.0 < 2"

compression@^1.5.2:
  version "1.7.3"
  resolved "http://registry.npm.taobao.org/compression/download/compression-1.7.3.tgz#27e0e176aaf260f7f2c2813c3e440adb9f1993db"
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.14"
    debug "2.6.9"
    on-headers "~1.0.1"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "http://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.3.0:
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.5.0.tgz#b06873934bc5e344fef611a196a6faae0aee015a"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/console-browserify/download/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  dependencies:
    date-now "^0.1.4"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"

consolidate@^0.14.0:
  version "0.14.5"
  resolved "http://registry.npm.taobao.org/consolidate/download/consolidate-0.14.5.tgz#5a25047bc76f73072667c8cb52c989888f494c63"
  dependencies:
    bluebird "^3.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"

content-disposition@0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.2.tgz#0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4"

content-type@~1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"

convert-source-map@^1.5.1:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.6.0.tgz#51b537a8c43e0f04dec1993bffcdd504e758ac20"
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"

cookie@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.taobao.org/cookie/download/cookie-0.3.1.tgz#e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"

copy-webpack-plugin@^4.0.1:
  version "4.5.2"
  resolved "http://registry.npm.taobao.org/copy-webpack-plugin/download/copy-webpack-plugin-4.5.2.tgz#d53444a8fea2912d806e78937390ddd7e632ee5c"
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    globby "^7.1.1"
    is-glob "^4.0.0"
    loader-utils "^1.1.0"
    minimatch "^3.0.4"
    p-limit "^1.0.0"
    serialize-javascript "^1.4.0"

core-js@^2.4.0, core-js@^2.5.0:
  version "2.5.7"
  resolved "http://registry.npm.taobao.org/core-js/download/core-js-2.5.7.tgz#f972608ff0cead68b841a16a932d0b183791814e"

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

cosmiconfig@^2.1.0, cosmiconfig@^2.1.1:
  version "2.2.2"
  resolved "http://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-2.2.2.tgz#6173cebd56fac042c1f4390edf7af6c07c7cb892"
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.4.3"
    minimist "^1.2.0"
    object-assign "^4.1.0"
    os-homedir "^1.0.1"
    parse-json "^2.2.0"
    require-from-string "^1.1.0"

cosmiconfig@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-4.0.0.tgz#760391549580bbd2df1e562bc177b13c290972dc"
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.9.0"
    parse-json "^4.0.0"
    require-from-string "^2.0.1"

create-ecdh@^4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.3.tgz#c9111b6f33045c4697f144787f9254cdc77c45ff"
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.2:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.2, create-hmac@^1.1.4:
  version "1.1.7"
  resolved "http://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-env@^5.1.4:
  version "5.2.0"
  resolved "http://registry.npm.taobao.org/cross-env/download/cross-env-5.2.0.tgz#6ecd4c015d5773e614039ee529076669b9d126f2"
  dependencies:
    cross-spawn "^6.0.5"
    is-windows "^1.0.0"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

crypt@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/crypt/download/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "http://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-color-names@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz#808adc2e79cf84738069b646cb20ec27beb629e0"

css-loader@^0.28.0:
  version "0.28.11"
  resolved "http://registry.npm.taobao.org/css-loader/download/css-loader-0.28.11.tgz#c3f9864a700be2711bb5a2462b2389b1a392dab7"
  dependencies:
    babel-code-frame "^6.26.0"
    css-selector-tokenizer "^0.7.0"
    cssnano "^3.10.0"
    icss-utils "^2.1.0"
    loader-utils "^1.0.2"
    lodash.camelcase "^4.3.0"
    object-assign "^4.1.1"
    postcss "^5.0.6"
    postcss-modules-extract-imports "^1.2.0"
    postcss-modules-local-by-default "^1.2.0"
    postcss-modules-scope "^1.1.0"
    postcss-modules-values "^1.3.0"
    postcss-value-parser "^3.3.0"
    source-list-map "^2.0.0"

css-select@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/css-select/download/css-select-1.2.0.tgz#2b3a110539c5355f1cd8d314623e870b121ec858"
  dependencies:
    boolbase "~1.0.0"
    css-what "2.1"
    domutils "1.5.1"
    nth-check "~1.0.1"

css-selector-tokenizer@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/css-selector-tokenizer/download/css-selector-tokenizer-0.7.0.tgz#e6988474ae8c953477bf5e7efecfceccd9cf4c86"
  dependencies:
    cssesc "^0.1.0"
    fastparse "^1.1.1"
    regexpu-core "^1.0.0"

css-what@2.1:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/css-what/download/css-what-2.1.0.tgz#9467d032c38cfaefb9f2d79501253062f87fa1bd"

cssesc@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/cssesc/download/cssesc-0.1.0.tgz#c814903e45623371a0477b40109aaafbeeaddbb4"

cssnano@^3.10.0, cssnano@^3.4.0:
  version "3.10.0"
  resolved "http://registry.npm.taobao.org/cssnano/download/cssnano-3.10.0.tgz#4f38f6cea2b9b17fa01490f23f1dc68ea65c1c38"
  dependencies:
    autoprefixer "^6.3.1"
    decamelize "^1.1.2"
    defined "^1.0.0"
    has "^1.0.1"
    object-assign "^4.0.1"
    postcss "^5.0.14"
    postcss-calc "^5.2.0"
    postcss-colormin "^2.1.8"
    postcss-convert-values "^2.3.4"
    postcss-discard-comments "^2.0.4"
    postcss-discard-duplicates "^2.0.1"
    postcss-discard-empty "^2.0.1"
    postcss-discard-overridden "^0.1.1"
    postcss-discard-unused "^2.2.1"
    postcss-filter-plugins "^2.0.0"
    postcss-merge-idents "^2.1.5"
    postcss-merge-longhand "^2.0.1"
    postcss-merge-rules "^2.0.3"
    postcss-minify-font-values "^1.0.2"
    postcss-minify-gradients "^1.0.1"
    postcss-minify-params "^1.0.4"
    postcss-minify-selectors "^2.0.4"
    postcss-normalize-charset "^1.1.0"
    postcss-normalize-url "^3.0.7"
    postcss-ordered-values "^2.1.0"
    postcss-reduce-idents "^2.2.2"
    postcss-reduce-initial "^1.0.0"
    postcss-reduce-transforms "^1.0.3"
    postcss-svgo "^2.1.1"
    postcss-unique-selectors "^2.0.2"
    postcss-value-parser "^3.2.3"
    postcss-zindex "^2.0.1"

csso@~2.3.1:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/csso/download/csso-2.3.2.tgz#ddd52c587033f49e94b71fc55569f252e8ff5f85"
  dependencies:
    clap "^1.0.9"
    source-map "^0.5.3"

cuint@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/cuint/download/cuint-0.2.2.tgz#408086d409550c2631155619e9fa7bcadc3b991b"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.taobao.org/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  dependencies:
    array-find-index "^1.0.1"

cyclist@~0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/cyclist/download/cyclist-0.2.2.tgz#1b33792e11e914a2fd6d6ed6447464444e5fa640"

d@1:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/d/download/d-1.0.0.tgz#754bb5bfe55451da69a58b94d45f4c5b0462d58f"
  dependencies:
    es5-ext "^0.10.9"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/date-now/download/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"

de-indent@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"

debug@2.6.9, debug@^2.1.2, debug@^2.2.0, debug@^2.3.3, debug@^2.6.6, debug@^2.6.8, debug@^2.6.9:
  version "2.6.9"
  resolved "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  dependencies:
    ms "2.0.0"

debug@=3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  dependencies:
    ms "2.0.0"

debug@^3.1.0:
  version "3.2.5"
  resolved "http://registry.npm.taobao.org/debug/download/debug-3.2.5.tgz#c2418fbfd7a29f4d4f70ff4cea604d4b64c46407"
  dependencies:
    ms "^2.1.1"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.1.2:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"

deep-equal@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.taobao.org/deep-extend/download/deep-extend-0.6.0.tgz#c4fa7c95404a17a9c3e8ca7e1537312b736330ac"

deepmerge@^1.2.0:
  version "1.5.2"
  resolved "http://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz#10499d868844cdad4fee0842df8c7f6f0c95a753"

define-properties@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

defined@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/defined/download/defined-1.0.0.tgz#c98d9bcef75674188e110969151199e39b1fa693"

del@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/del/download/del-3.0.0.tgz#53ecf699ffcbcb39637691ab13baf160819766e5"
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"

depd@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/depd/download/depd-1.1.1.tgz#5783b4e1c459f06fa5ca27f991f3d06e7a310359"

depd@~1.1.1, depd@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"

des.js@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/des.js/download/des.js-1.0.0.tgz#c074d2e2aa6a8a9a07dbd61f9a15c2cd83ec8ecc"
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@~1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/detect-indent/download/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

detect-libc@^1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/detect-libc/download/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"

detect-node@^2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/detect-node/download/detect-node-2.0.4.tgz#014ee8f8f669c5c58023da64b8179c083a28c46c"

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "http://registry.npm.taobao.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

dir-glob@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/dir-glob/download/dir-glob-2.0.0.tgz#0b205d2b6aef98238ca286598a8204d29d0a0034"
  dependencies:
    arrify "^1.0.1"
    path-type "^3.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"

dns-packet@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/dns-packet/download/dns-packet-1.3.1.tgz#12aa426981075be500b910eedcd0b47dd7deda5a"
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/dns-txt/download/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  dependencies:
    buffer-indexof "^1.0.0"

dom-converter@~0.1:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/dom-converter/download/dom-converter-0.1.4.tgz#a45ef5727b890c9bffe6d7c876e7b19cb0e17f3b"
  dependencies:
    utila "~0.3"

dom-serializer@0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/dom-serializer/download/dom-serializer-0.1.0.tgz#073c697546ce0780ce23be4a28e293e40bc30c82"
  dependencies:
    domelementtype "~1.1.1"
    entities "~1.1.1"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"

domelementtype@1:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.0.tgz#b17aed82e8ab59e52dd9c19b1756e0fc187204c2"

domelementtype@~1.1.1:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/domelementtype/download/domelementtype-1.1.3.tgz#bd28773e2642881aec51544924299c5cd822185b"

domhandler@2.1:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/domhandler/download/domhandler-2.1.0.tgz#d2646f5e57f6c3bab11cf6cb05d3c0acf7412594"
  dependencies:
    domelementtype "1"

domutils@1.1:
  version "1.1.6"
  resolved "http://registry.npm.taobao.org/domutils/download/domutils-1.1.6.tgz#bddc3de099b9a2efacc51c623f28f416ecc57485"
  dependencies:
    domelementtype "1"

domutils@1.5.1:
  version "1.5.1"
  resolved "http://registry.npm.taobao.org/domutils/download/domutils-1.5.1.tgz#dcd8488a26f563d61079e48c9f7b7e32373682cf"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

duplexer@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/duplexer/download/duplexer-0.1.1.tgz#ace6ff808c1ce66b57d1ebf97977acb02334cfc1"

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.taobao.org/duplexify/download/duplexify-3.6.0.tgz#592903f5d80b38d037220541264d69a198fb3410"
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"

ejs@^2.5.7:
  version "2.6.1"
  resolved "http://registry.npm.taobao.org/ejs/download/ejs-2.6.1.tgz#498ec0d495655abc6f23cd61868d926464071aa0"

electron-to-chromium@^1.2.7, electron-to-chromium@^1.3.30, electron-to-chromium@^1.3.47:
  version "1.3.70"
  resolved "http://registry.npm.taobao.org/electron-to-chromium/download/electron-to-chromium-1.3.70.tgz#ded377256d92d81b4257d36c65aa890274afcfd2"

element-ui@2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/element-ui/download/element-ui-2.1.0.tgz#eec860e3d3b131e61c2974a3e2b19a689d040f14"
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    throttle-debounce "^1.0.1"

element-ui@^2.2.1:
  version "2.4.7"
  resolved "http://registry.npm.taobao.org/element-ui/download/element-ui-2.4.7.tgz#073b404222815c08ba2e68b39ec8e6ff7b7b13e0"
  dependencies:
    async-validator "~1.8.1"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    normalize-wheel "^1.0.1"
    resize-observer-polyfill "^1.5.0"
    throttle-debounce "^1.0.1"

elliptic@^6.0.0:
  version "6.4.1"
  resolved "http://registry.npm.taobao.org/elliptic/download/elliptic-6.4.1.tgz#c2d0b7776911b86722c632c3c06c60f2f819939a"
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.1.tgz#ed29634d19baba463b6ce6b80a37213eab71ec43"
  dependencies:
    once "^1.4.0"

enhanced-resolve@^3.4.0:
  version "3.4.1"
  resolved "http://registry.npm.taobao.org/enhanced-resolve/download/enhanced-resolve-3.4.1.tgz#0421e339fd71419b3da13d129b3979040230476e"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    object-assign "^4.0.1"
    tapable "^0.2.7"

entities@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/entities/download/entities-1.1.1.tgz#6e5c2d0a5621b5dadaecef80b90edfb5cd7772f0"

errno@^0.1.3, errno@~0.1.7:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/errno/download/errno-0.1.7.tgz#4684d71779ad39af177e3f007996f7c67c852618"
  dependencies:
    prr "~1.0.1"

error-ex@^1.2.0, error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.2.tgz#4ae8dbaa2bf90a8b450707b9149dcabca135520d"
  dependencies:
    stackframe "^1.0.4"

es-abstract@^1.7.0:
  version "1.12.0"
  resolved "http://registry.npm.taobao.org/es-abstract/download/es-abstract-1.12.0.tgz#9dbbdd27c6856f0001421ca18782d786bf8a6165"
  dependencies:
    es-to-primitive "^1.1.1"
    function-bind "^1.1.1"
    has "^1.0.1"
    is-callable "^1.1.3"
    is-regex "^1.0.4"

es-to-primitive@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.1.1.tgz#45355248a88979034b6792e19bb81f2b7975dd0d"
  dependencies:
    is-callable "^1.1.1"
    is-date-object "^1.0.1"
    is-symbol "^1.0.1"

es5-ext@^0.10.14, es5-ext@^0.10.35, es5-ext@^0.10.9, es5-ext@~0.10.14:
  version "0.10.46"
  resolved "http://registry.npm.taobao.org/es5-ext/download/es5-ext-0.10.46.tgz#efd99f67c5a7ec789baa3daa7f79870388f7f572"
  dependencies:
    es6-iterator "~2.0.3"
    es6-symbol "~3.1.1"
    next-tick "1"

es6-iterator@^2.0.1, es6-iterator@~2.0.1, es6-iterator@~2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/es6-map/download/es6-map-0.1.5.tgz#9136e0503dcc06a301690f0bb14ff4e364e949f0"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-set@~0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/es6-set/download/es6-set-0.1.5.tgz#d2b3ec5d4d800ced818db538d28974db0a73ccb1"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@3.1.1, es6-symbol@^3.1.1, es6-symbol@~3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.taobao.org/es6-symbol/download/es6-symbol-3.1.1.tgz#bf00ef4fdab6ba1b46ecb7b629b4c7ed5715cc77"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-weak-map@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/es6-weak-map/download/es6-weak-map-2.0.2.tgz#5e3ab32251ffd1538a1f8e5ffa1357772f92d96f"
  dependencies:
    d "1"
    es5-ext "^0.10.14"
    es6-iterator "^2.0.1"
    es6-symbol "^3.1.1"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

escope@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.taobao.org/escope/download/escope-3.6.0.tgz#e01975e812781a163a6dadfdd80398dc64c889c3"
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

esprima@^2.6.0:
  version "2.7.3"
  resolved "http://registry.npm.taobao.org/esprima/download/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/esrecurse/download/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.1.0, estraverse@^4.1.1:
  version "4.2.0"
  resolved "http://registry.npm.taobao.org/estraverse/download/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"

esutils@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/esutils/download/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

etag@~1.8.1:
  version "1.8.1"
  resolved "http://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"

event-emitter@~0.3.5:
  version "0.3.5"
  resolved "http://registry.npm.taobao.org/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter3@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/eventemitter3/download/eventemitter3-3.1.0.tgz#090b4d6cdbd645ed10bf750d4b5407942d7ba163"

events@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/events/download/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"

eventsource@0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.taobao.org/eventsource/download/eventsource-0.1.6.tgz#0acede849ed7dd1ccc32c811bb11b944d4f29232"
  dependencies:
    original ">=0.0.5"

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

execa@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/execa/download/execa-0.7.0.tgz#944becd34cc41ee32a63a9faf27ad5a65fc59777"
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/expand-brackets/download/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  dependencies:
    is-posix-bracket "^0.1.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "http://registry.npm.taobao.org/expand-range/download/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  dependencies:
    fill-range "^2.1.0"

express@^4.16.2:
  version "4.16.3"
  resolved "http://registry.npm.taobao.org/express/download/express-4.16.3.tgz#6af8a502350db3246ecc4becf6b5a34d22f7ed53"
  dependencies:
    accepts "~1.3.5"
    array-flatten "1.1.1"
    body-parser "1.18.2"
    content-disposition "0.5.2"
    content-type "~1.0.4"
    cookie "0.3.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.1.1"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.3"
    qs "6.5.1"
    range-parser "~1.2.0"
    safe-buffer "5.1.1"
    send "0.16.2"
    serve-static "1.13.2"
    setprototypeof "1.1.0"
    statuses "~1.4.0"
    type-is "~1.6.16"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.1, extend@~3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"

extglob@^0.3.1:
  version "0.3.2"
  resolved "http://registry.npm.taobao.org/extglob/download/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  dependencies:
    is-extglob "^1.0.0"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extract-text-webpack-plugin@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/extract-text-webpack-plugin/download/extract-text-webpack-plugin-3.0.2.tgz#5f043eaa02f9750a9258b78c0a6e0dc1408fb2f7"
  dependencies:
    async "^2.4.1"
    loader-utils "^1.1.0"
    schema-utils "^0.3.0"
    webpack-sources "^1.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/extsprintf/download/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"

fast-deep-equal@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-1.1.0.tgz#c053477817c86b51daa853c81e059b733d023614"

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"

fastparse@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/fastparse/download/fastparse-1.1.1.tgz#d1e2643b38a94d7583b479060e6c4affc94071f8"

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.10.0.tgz#4e492f8d04dfb6f89003507f6edbf2d501e7c6f4"
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.11.1.tgz#f0efe18c4f56e4f40afc7e06c719fd5ee6188f38"
  dependencies:
    websocket-driver ">=0.5.1"

file-loader@^1.1.4:
  version "1.1.11"
  resolved "http://registry.npm.taobao.org/file-loader/download/file-loader-1.1.11.tgz#6fe886449b0f2a936e43cabaac0cdbfb369506f8"
  dependencies:
    loader-utils "^1.0.2"
    schema-utils "^0.4.5"

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/filename-regex/download/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"

filesize@^3.5.11:
  version "3.6.1"
  resolved "http://registry.npm.taobao.org/filesize/download/filesize-3.6.1.tgz#090bb3ee01b6f801a8a8be99d31710b3422bb317"

fill-range@^2.1.0:
  version "2.2.4"
  resolved "http://registry.npm.taobao.org/fill-range/download/fill-range-2.2.4.tgz#eb1e773abb056dcd8df2bfdf6af59b8b3a936565"
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^3.0.0"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

finalhandler@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.1.tgz#eebf4ed840079c83f4249038c9d703008301b105"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    statuses "~1.4.0"
    unpipe "~1.0.0"

find-cache-dir@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-1.0.0.tgz#9288e3e9e3cc3748717d39eade17cf71fc30ee6f"
  dependencies:
    commondir "^1.0.1"
    make-dir "^1.0.0"
    pkg-dir "^2.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0, find-up@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  dependencies:
    locate-path "^2.0.0"

flatten@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/flatten/download/flatten-1.0.2.tgz#dae46a9d78fbe25292258cc1e780a41d95c03782"

flush-write-stream@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.0.3.tgz#c5d586ef38af6097650b49bc41b55fabb19f35bd"
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.4"

follow-redirects@^1.0.0, follow-redirects@^1.2.5:
  version "1.5.8"
  resolved "http://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.5.8.tgz#1dbfe13e45ad969f813e86c00e5296f525c885a1"
  dependencies:
    debug "=3.1.0"

font-awesome@^4.7.0:
  version "4.7.0"
  resolved "http://registry.npm.taobao.org/font-awesome/download/font-awesome-4.7.0.tgz#8fa8cf0411a1a31afd07b06d2902bb9fc815a133"

for-in@^0.1.3:
  version "0.1.8"
  resolved "http://registry.npm.taobao.org/for-in/download/for-in-0.1.8.tgz#d8773908e31256109952b1fdb9b3fa867d2775e1"

for-in@^1.0.1, for-in@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"

for-own@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/for-own/download/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  dependencies:
    for-in "^1.0.1"

for-own@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/for-own/download/for-own-1.0.0.tgz#c63332f415cedc4b04dbfe70cf836494c53cb44b"
  dependencies:
    for-in "^1.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~2.3.1, form-data@~2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/form-data/download/form-data-2.3.2.tgz#4970498be604c20c005d4f5c23aecd21d6b49099"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "1.0.6"
    mime-types "^2.1.12"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"

friendly-errors-webpack-plugin@^1.6.1:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/friendly-errors-webpack-plugin/download/friendly-errors-webpack-plugin-1.7.0.tgz#efc86cbb816224565861a1be7a9d84d0aafea136"
  dependencies:
    chalk "^1.1.3"
    error-stack-parser "^2.0.0"
    string-width "^2.0.0"

from2@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-minipass@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.taobao.org/fs-minipass/download/fs-minipass-1.2.5.tgz#06c277218454ec288df77ada54a03b8702aacb9d"
  dependencies:
    minipass "^2.2.1"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "http://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

fsevents@^1.2.2:
  version "1.2.4"
  resolved "http://registry.npm.taobao.org/fsevents/download/fsevents-1.2.4.tgz#f41dcb1af2582af3692da36fc55cbd8e1041c426"
  dependencies:
    nan "^2.9.2"
    node-pre-gyp "^0.10.0"

fstream@^1.0.0, fstream@^1.0.2:
  version "1.0.11"
  resolved "http://registry.npm.taobao.org/fstream/download/fstream-1.0.11.tgz#5c1fb1f117477114f0632a0eb4b71b3cb0fd3171"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"

gauge@~2.7.3:
  version "2.7.4"
  resolved "http://registry.npm.taobao.org/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^1.0.0:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/gaze/download/gaze-1.1.3.tgz#c441733e13b927ac8c0ff0b4c3b033f28812924a"
  dependencies:
    globule "^1.0.0"

get-caller-file@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.3.tgz#f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/get-stdin/download/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  dependencies:
    assert-plus "^1.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/glob-base/download/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/glob-parent/download/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.0.5, glob@^7.1.2, glob@~7.1.1:
  version "7.1.3"
  resolved "http://registry.npm.taobao.org/glob/download/glob-7.1.3.tgz#3960832d3f1574108342dafd3a67b332c0969df1"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^9.18.0:
  version "9.18.0"
  resolved "http://registry.npm.taobao.org/globals/download/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

globby@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.taobao.org/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globby@^7.1.1:
  version "7.1.1"
  resolved "http://registry.npm.taobao.org/globby/download/globby-7.1.1.tgz#fb2ccff9401f8600945dfada97440cca972b8680"
  dependencies:
    array-union "^1.0.1"
    dir-glob "^2.0.0"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globule@^1.0.0:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/globule/download/globule-1.2.1.tgz#5dffb1b191f22d20797a9369b49eab4e9839696d"
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.10"
    minimatch "~3.0.2"

graceful-fs@^4.1.11, graceful-fs@^4.1.2:
  version "4.1.11"
  resolved "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"

growly@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/growly/download/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"

gzip-size@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.taobao.org/gzip-size/download/gzip-size-4.1.0.tgz#8ae096257eabe7d69c45be2b67c448124ffb517c"
  dependencies:
    duplexer "^0.1.1"
    pify "^3.0.0"

handle-thing@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.taobao.org/handle-thing/download/handle-thing-1.2.5.tgz#fd7aad726bf1a5fd16dfc29b2f7a6601d27139c4"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"

har-validator@~5.0.3:
  version "5.0.3"
  resolved "http://registry.npm.taobao.org/har-validator/download/har-validator-5.0.3.tgz#ba402c266194f15956ef15e0fcf242993f6a7dfd"
  dependencies:
    ajv "^5.1.0"
    har-schema "^2.0.0"

har-validator@~5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.taobao.org/har-validator/download/har-validator-5.1.0.tgz#44657f5688a22cfd4b72486e81b3a3fb11742c29"
  dependencies:
    ajv "^5.3.0"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/has-flag/download/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"

has-flag@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/has-flag/download/has-flag-2.0.0.tgz#e8207af1cc7b30d446cc70b734b5e8be18f88d51"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"

has-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.0.tgz#ba1a8f1af2a0fc39650f5c850367704122063b44"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/hash-base/download/hash-base-3.0.4.tgz#5fc8686847ecd73499403319a6b0a3f3f6ae4918"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz#33b40777754c6432573c120cc3808bbd10d47f04"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.5"
  resolved "http://registry.npm.taobao.org/hash.js/download/hash.js-1.1.5.tgz#e38ab4b85dfb1e0c40fe9265c0e9b54854c23812"
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

he@1.1.x, he@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/he/download/he-1.1.1.tgz#93410fd21b009735151f8868c2f271f3427e23fd"

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/home-or-tmp/download/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hosted-git-info@^2.1.4:
  version "2.7.1"
  resolved "http://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.7.1.tgz#97f236977bd6e125408930ff6de3eec6281ec047"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "http://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-comment-regex@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/html-comment-regex/download/html-comment-regex-1.1.1.tgz#668b93776eaae55ebde8f3ad464b307a4963625e"

html-entities@^1.2.0:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/html-entities/download/html-entities-1.2.1.tgz#0df29351f0721163515dfb9e5543e5f6eed5162f"

html-minifier@^3.2.3:
  version "3.5.20"
  resolved "http://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.20.tgz#7b19fd3caa0cb79f7cde5ee5c3abdf8ecaa6bb14"
  dependencies:
    camel-case "3.0.x"
    clean-css "4.2.x"
    commander "2.17.x"
    he "1.1.x"
    param-case "2.1.x"
    relateurl "0.2.x"
    uglify-js "3.4.x"

html-webpack-plugin@^2.30.1:
  version "2.30.1"
  resolved "http://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-2.30.1.tgz#7f9c421b7ea91ec460f56527d78df484ee7537d5"
  dependencies:
    bluebird "^3.4.7"
    html-minifier "^3.2.3"
    loader-utils "^0.2.16"
    lodash "^4.17.3"
    pretty-error "^2.0.2"
    toposort "^1.0.0"

htmlparser2@~3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.3.0.tgz#cc70d05a59f6542e43f0e685c982e14c924a9efe"
  dependencies:
    domelementtype "1"
    domhandler "2.1"
    domutils "1.1"
    readable-stream "1.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "http://registry.npm.taobao.org/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"

http-errors@1.6.2:
  version "1.6.2"
  resolved "http://registry.npm.taobao.org/http-errors/download/http-errors-1.6.2.tgz#0a002cc85707192a7e7946ceedc11155f60ec736"
  dependencies:
    depd "1.1.1"
    inherits "2.0.3"
    setprototypeof "1.0.3"
    statuses ">= 1.3.1 < 2"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.4.0:
  version "0.4.13"
  resolved "http://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.4.13.tgz#3bd6d6fde6e3172c9334c3b33b6c193d80fe1137"

http-proxy-middleware@~0.17.4:
  version "0.17.4"
  resolved "http://registry.npm.taobao.org/http-proxy-middleware/download/http-proxy-middleware-0.17.4.tgz#642e8848851d66f09d4f124912846dbaeb41b833"
  dependencies:
    http-proxy "^1.16.2"
    is-glob "^3.1.0"
    lodash "^4.17.2"
    micromatch "^2.3.11"

http-proxy@^1.16.2:
  version "1.17.0"
  resolved "http://registry.npm.taobao.org/http-proxy/download/http-proxy-1.17.0.tgz#7ad38494658f84605e2f6db4436df410f4e5be9a"
  dependencies:
    eventemitter3 "^3.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"

iconv-lite@0.4.19:
  version "0.4.19"
  resolved "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.19.tgz#f7468f60135f5e5dad3399c0a81be9a1603a082b"

iconv-lite@^0.4.4:
  version "0.4.24"
  resolved "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz#06ea6f83679a7749e386cfe1fe812ae5db223ded"

icss-utils@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/icss-utils/download/icss-utils-2.1.0.tgz#83f0a0ec378bf3246178b6c2ad9136f135b1c962"
  dependencies:
    postcss "^6.0.1"

ieee754@^1.1.4:
  version "1.1.12"
  resolved "http://registry.npm.taobao.org/ieee754/download/ieee754-1.1.12.tgz#50bf24e5b9c8bb98af4964c941cdb0918da7b60b"

iferr@^0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"

ignore-walk@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/ignore-walk/download/ignore-walk-3.0.1.tgz#a83e62e7d272ac0e3b551aaa82831a19b69f82f8"
  dependencies:
    minimatch "^3.0.4"

ignore@^3.3.5:
  version "3.3.10"
  resolved "http://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"

import-cwd@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/import-cwd/download/import-cwd-2.1.0.tgz#aa6cf36e722761285cb371ec6519f53e2435b0a9"
  dependencies:
    import-from "^2.1.0"

import-from@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/import-from/download/import-from-2.1.0.tgz#335db7f2a7affd53aaa471d4b8021dee36b7f3b1"
  dependencies:
    resolve-from "^3.0.0"

import-local@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/import-local/download/import-local-1.0.0.tgz#5e4ffdc03f4fe6c009c6729beb29631c2f8227bc"
  dependencies:
    pkg-dir "^2.0.0"
    resolve-cwd "^2.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

in-publish@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/in-publish/download/in-publish-2.0.0.tgz#e20ff5e3a2afc2690320b6dc552682a9c7fadf51"

indent-string@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/indent-string/download/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  dependencies:
    repeating "^2.0.0"

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"

indexof@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/indexof/download/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.3, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inherits@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"

ini@~1.3.0:
  version "1.3.5"
  resolved "http://registry.npm.taobao.org/ini/download/ini-1.3.5.tgz#eee25f56db1c9ec6085e0c22778083f596abf927"

internal-ip@1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/internal-ip/download/internal-ip-1.2.0.tgz#ae9fbf93b984878785d50a8de1b356956058cf5c"
  dependencies:
    meow "^3.3.0"

interpret@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/interpret/download/interpret-1.1.0.tgz#7ed1b1410c6a0e0f78cf95d3b8440c63f78b8614"

invariant@^2.2.2:
  version "2.2.4"
  resolved "http://registry.npm.taobao.org/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/invert-kv/download/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"

ip@^1.1.0, ip@^1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.taobao.org/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"

ipaddr.js@1.8.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.8.0.tgz#eaa33d6ddd7ace8f7f6fe0c9ca0440e706738b1e"

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz#50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6"

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.1.5, is-buffer@~1.1.1:
  version "1.1.6"
  resolved "http://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-builtin-module/download/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  dependencies:
    builtin-modules "^1.0.0"

is-callable@^1.1.1, is-callable@^1.1.3:
  version "1.1.4"
  resolved "http://registry.npm.taobao.org/is-callable/download/is-callable-1.1.4.tgz#1e1adf219e1eeb684d691f9d6a05ff0d30a24d75"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/is-dotfile/download/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/is-equal-shallow/download/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-extglob/download/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"

is-finite@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/is-finite/download/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/is-glob/download/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/is-glob/download/is-glob-4.0.0.tgz#9521c76845cc2610a85203ddf080a958c2ffabc0"
  dependencies:
    is-extglob "^2.1.1"

is-number@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/is-number/download/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  dependencies:
    kind-of "^3.0.2"

is-number@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/is-number/download/is-number-4.0.0.tgz#0026e37f5454d73e356dfe6564699867c6a7f0ff"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz#5ac48b345ef675339bd6c7a48a912110b241cf52"
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-path-inside/download/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
  dependencies:
    path-is-inside "^1.0.1"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"

is-plain-object@^2.0.1, is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  dependencies:
    isobject "^3.0.1"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/is-posix-bracket/download/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/is-primitive/download/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"

is-regex@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/is-regex/download/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
  dependencies:
    has "^1.0.1"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-svg@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/is-svg/download/is-svg-2.1.0.tgz#cf61090da0d9efbcab8722deba6f032208dbb0e9"
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.2.tgz#a055f6ae57192caee329e7a860118b497a950f38"
  dependencies:
    has-symbols "^1.0.0"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

is-windows@^1.0.0, is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

jquery@1.11.2:
  version "1.11.2"
  resolved "http://registry.npm.taobao.org/jquery/download/jquery-1.11.2.tgz#30ab26857211c37caa83da0f6903155fe49bb72d"

js-base64@^2.1.8, js-base64@^2.1.9:
  version "2.4.9"
  resolved "http://registry.npm.taobao.org/js-base64/download/js-base64-2.4.9.tgz#748911fb04f48a60c4771b375cac45a80df11c03"

"js-tokens@^3.0.0 || ^4.0.0":
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/js-tokens/download/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"

js-yaml@^3.4.3, js-yaml@^3.9.0:
  version "3.12.0"
  resolved "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.12.0.tgz#eaed656ec8344f10f527c6bfa1b6e2244de167d1"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@~3.7.0:
  version "3.7.0"
  resolved "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.7.0.tgz#5c967ddd837a9bfdca5f2de84253abe8a1c03b80"
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/jsesc/download/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"

json-loader@^0.5.4:
  version "0.5.7"
  resolved "http://registry.npm.taobao.org/json-loader/download/json-loader-0.5.7.tgz#dca14a70235ff82f0ac9a3abeb60d337a365185d"

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "http://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.3.1.tgz#349a6d44c53a51de89b40805c5d5e59b417d3340"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"

json-schema@0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

json3@^3.3.2:
  version "3.3.2"
  resolved "http://registry.npm.taobao.org/json3/download/json3-3.3.2.tgz#3c0434743df93e2f5c42aee7b19bcb483575f4e1"

json5@^0.5.0, json5@^0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

killable@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/killable/download/killable-1.0.1.tgz#4c8ce441187a061c7474fb87ca08e2a638194892"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.2"
  resolved "http://registry.npm.taobao.org/kind-of/download/kind-of-6.0.2.tgz#01146b36a6218e64e58f3a8d66de5d7fc6f6d051"

kindeditor@^4.1.10:
  version "4.1.10"
  resolved "http://registry.npm.taobao.org/kindeditor/download/kindeditor-4.1.10.tgz#bbbbba9de38e13f8568205be765ef7339eb6d515"

last-call-webpack-plugin@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/last-call-webpack-plugin/download/last-call-webpack-plugin-2.1.2.tgz#ad80c6e310998294d2ed2180a68e9589e4768c44"
  dependencies:
    lodash "^4.17.4"
    webpack-sources "^1.0.1"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/lazy-cache/download/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/lcid/download/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  dependencies:
    invert-kv "^1.0.0"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/load-json-file/download/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/load-json-file/download/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

load-script@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/load-script/download/load-script-1.0.0.tgz#0491939e0bee5643ee494a7e3da3d2bac70c6ca4"

loader-runner@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/loader-runner/download/loader-runner-2.3.0.tgz#f482aea82d543e07921700d5a46ef26fdac6b8a2"

loader-utils@^0.2.16:
  version "0.2.17"
  resolved "http://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.1, loader-utils@^1.0.2, loader-utils@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/loader-utils/download/loader-utils-1.1.0.tgz#c98aef488bcceda2ffb5e2de646d6a754429f5cd"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/locate-path/download/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

lodash.assign@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.taobao.org/lodash.assign/download/lodash.assign-4.2.0.tgz#0d99f3ccd7a6d261d19bdaeb9245005d285808e7"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://registry.npm.taobao.org/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"

lodash.clonedeep@^4.3.2:
  version "4.5.0"
  resolved "http://registry.npm.taobao.org/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://registry.npm.taobao.org/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"

lodash.mergewith@^4.6.0:
  version "4.6.1"
  resolved "http://registry.npm.taobao.org/lodash.mergewith/download/lodash.mergewith-4.6.1.tgz#639057e726c3afbdb3e7d42741caa8d6e4335927"

lodash.tail@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/lodash.tail/download/lodash.tail-4.1.1.tgz#d2333a36d9e7717c8ad2f7cacafec7c32b444664"

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"

lodash@^4.0.0, lodash@^4.17.10, lodash@^4.17.2, lodash@^4.17.3, lodash@^4.17.4, lodash@^4.17.5, lodash@~4.17.10:
  version "4.17.11"
  resolved "http://registry.npm.taobao.org/lodash/download/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"

log-symbols@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  dependencies:
    chalk "^2.0.1"

loglevel@^1.4.1:
  version "1.6.1"
  resolved "http://registry.npm.taobao.org/loglevel/download/loglevel-1.6.1.tgz#e0fc95133b6ef276cdc8887cdaf24aa6f156f8fa"

longest@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/longest/download/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case@^1.1.1:
  version "1.1.4"
  resolved "http://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"

lru-cache@^4.0.1, lru-cache@^4.1.1:
  version "4.1.3"
  resolved "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.3.tgz#a1175cf3496dfc8436c156c334b4955992bce69c"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/make-dir/download/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  dependencies:
    pify "^3.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  dependencies:
    object-visit "^1.0.0"

math-expression-evaluator@^1.2.14:
  version "1.2.17"
  resolved "http://registry.npm.taobao.org/math-expression-evaluator/download/math-expression-evaluator-1.2.17.tgz#de819fdbcd84dccd8fae59c6aeb79615b9d266ac"

math-random@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/math-random/download/math-random-1.0.1.tgz#8b3aac588b8a66e4975e3cdea67f7bb329601fac"

md5.js@^1.3.4:
  version "1.3.4"
  resolved "http://registry.npm.taobao.org/md5.js/download/md5.js-1.3.4.tgz#e9bdbde94a20a5ac18b04340fc5764d5b09d901d"
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

md5@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/md5/download/md5-2.2.1.tgz#53ab38d5fe3c8891ba465329ea23fac0540126f9"
  dependencies:
    charenc "~0.0.1"
    crypt "~0.0.1"
    is-buffer "~1.1.1"

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"

mem@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/mem/download/mem-1.1.0.tgz#5edd52b485ca1d900fe64895505399a0dfa45f76"
  dependencies:
    mimic-fn "^1.0.0"

memory-fs@^0.4.0, memory-fs@~0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.3.0, meow@^3.7.0:
  version "3.7.0"
  resolved "http://registry.npm.taobao.org/meow/download/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"

methods@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"

micromatch@^2.3.11:
  version "2.3.11"
  resolved "http://registry.npm.taobao.org/micromatch/download/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "http://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

"mime-db@>= 1.36.0 < 2", mime-db@~1.36.0:
  version "1.36.0"
  resolved "http://registry.npm.taobao.org/mime-db/download/mime-db-1.36.0.tgz#5020478db3c7fe93aad7bbcc4dcf869c43363397"

mime-types@^2.1.12, mime-types@~2.1.17, mime-types@~2.1.18, mime-types@~2.1.19:
  version "2.1.20"
  resolved "http://registry.npm.taobao.org/mime-types/download/mime-types-2.1.20.tgz#930cb719d571e903738520f8470911548ca2cc19"
  dependencies:
    mime-db "~1.36.0"

mime@1.3.x:
  version "1.3.6"
  resolved "http://registry.npm.taobao.org/mime/download/mime-1.3.6.tgz#591d84d3653a6b0b4a3b9df8de5aa8108e72e5e0"

mime@1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/mime/download/mime-1.4.1.tgz#121f9ebc49e3766f311a76e1fa1c8003c4b03aa6"

mime@^1.4.1, mime@^1.5.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"

minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@^1.1.3, minimist@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

minipass@^2.2.1, minipass@^2.3.3:
  version "2.3.4"
  resolved "http://registry.npm.taobao.org/minipass/download/minipass-2.3.4.tgz#4768d7605ed6194d6d576169b9e12ef71e9d9957"
  dependencies:
    safe-buffer "^5.1.2"
    yallist "^3.0.0"

minizlib@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/minizlib/download/minizlib-1.1.0.tgz#11e13658ce46bc3a70a267aac58359d1e0c29ceb"
  dependencies:
    minipass "^2.2.1"

mississippi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/mississippi/download/mississippi-2.0.0.tgz#3442a508fafc28500486feea99409676e4ee5a6f"
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^2.0.1"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.1.tgz#a49e7268dce1a0d9698e45326c5626df3543d0fe"
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mixin-object@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/mixin-object/download/mixin-object-2.0.1.tgz#4fb949441dab182540f1fe035ba60e1947a5e57e"
  dependencies:
    for-in "^0.1.3"
    is-extendable "^0.1.1"

mkdirp@0.5.x, "mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@~0.5.0, mkdirp@~0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

moment@^2.19.3:
  version "2.22.2"
  resolved "http://registry.npm.taobao.org/moment/download/moment-2.22.2.tgz#3c257f9839fc0e93ff53149632239eb90783ff66"

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

ms@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "http://registry.npm.taobao.org/multicast-dns/download/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

nan@^2.10.0, nan@^2.9.2:
  version "2.11.0"
  resolved "http://registry.npm.taobao.org/nan/download/nan-2.11.0.tgz#574e360e4d954ab16966ec102c0c049fd961a099"

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

needle@^2.2.1:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/needle/download/needle-2.2.3.tgz#c1b04da378cd634d8befe2de965dc2cfb0fd65ca"
  dependencies:
    debug "^2.1.2"
    iconv-lite "^0.4.4"
    sax "^1.2.4"

negotiator@0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/negotiator/download/negotiator-0.6.1.tgz#2b327184e8992101177b28563fb5e7102acd0ca9"

neo-async@^2.5.0:
  version "2.5.2"
  resolved "http://registry.npm.taobao.org/neo-async/download/neo-async-2.5.2.tgz#489105ce7bc54e709d736b195f82135048c50fcc"

next-tick@1:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/next-tick/download/next-tick-1.0.0.tgz#ca86d1fe8828169b0120208e3dc8424b9db8342c"

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"

no-case@^2.2.0:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz#60b813396be39b3f1288a4c1ed5d1e7d28b464ac"
  dependencies:
    lower-case "^1.1.1"

node-forge@0.7.5:
  version "0.7.5"
  resolved "http://registry.npm.taobao.org/node-forge/download/node-forge-0.7.5.tgz#6c152c345ce11c52f465c2abd957e8639cd674df"

node-gyp@^3.8.0:
  version "3.8.0"
  resolved "http://registry.npm.taobao.org/node-gyp/download/node-gyp-3.8.0.tgz#540304261c330e80d0d5edce253a68cb3964218c"
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3 || 4"
    osenv "0"
    request "^2.87.0"
    rimraf "2"
    semver "~5.3.0"
    tar "^2.0.0"
    which "1"

node-libs-browser@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-2.1.0.tgz#5f94263d404f6e44767d726901fff05478d600df"
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^1.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.0"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.10.3"
    vm-browserify "0.0.4"

node-notifier@^5.1.2:
  version "5.2.1"
  resolved "http://registry.npm.taobao.org/node-notifier/download/node-notifier-5.2.1.tgz#fa313dd08f5517db0e2502e5758d664ac69f9dea"
  dependencies:
    growly "^1.3.0"
    semver "^5.4.1"
    shellwords "^0.1.1"
    which "^1.3.0"

node-pre-gyp@^0.10.0:
  version "0.10.3"
  resolved "http://registry.npm.taobao.org/node-pre-gyp/download/node-pre-gyp-0.10.3.tgz#3070040716afdc778747b61b6887bf78880b80fc"
  dependencies:
    detect-libc "^1.0.2"
    mkdirp "^0.5.1"
    needle "^2.2.1"
    nopt "^4.0.1"
    npm-packlist "^1.1.6"
    npmlog "^4.0.2"
    rc "^1.2.7"
    rimraf "^2.6.1"
    semver "^5.3.0"
    tar "^4"

node-sass@^4.8.3:
  version "4.9.3"
  resolved "http://registry.npm.taobao.org/node-sass/download/node-sass-4.9.3.tgz#f407cf3d66f78308bb1e346b24fa428703196224"
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash.assign "^4.2.0"
    lodash.clonedeep "^4.3.2"
    lodash.mergewith "^4.6.0"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.10.0"
    node-gyp "^3.8.0"
    npmlog "^4.0.0"
    request "2.87.0"
    sass-graph "^2.2.4"
    stdout-stream "^1.4.0"
    "true-case-path" "^1.0.2"

"nopt@2 || 3":
  version "3.0.6"
  resolved "http://registry.npm.taobao.org/nopt/download/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  dependencies:
    abbrev "1"

nopt@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/nopt/download/nopt-4.0.1.tgz#d0d4685afd5415193c8c7505602d0d17cd64474d"
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

normalize-package-data@^2.3.2, normalize-package-data@^2.3.4:
  version "2.4.0"
  resolved "http://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.4.0.tgz#12f95a307d58352075a04907b84ac8be98ac012f"
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.1, normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"

normalize-url@^1.4.0:
  version "1.9.1"
  resolved "http://registry.npm.taobao.org/normalize-url/download/normalize-url-1.9.1.tgz#2cc0d66b31ea23036458436e3620d85954c66c3c"
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-wheel@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/normalize-wheel/download/normalize-wheel-1.0.1.tgz#aec886affdb045070d856447df62ecf86146ec45"

normalize.css@7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.taobao.org/normalize.css/download/normalize.css-7.0.0.tgz#abfb1dd82470674e0322b53ceb1aaf412938e4bf"

npm-bundled@^1.0.1:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/npm-bundled/download/npm-bundled-1.0.5.tgz#3c1732b7ba936b3a10325aef616467c0ccbcc979"

npm-packlist@^1.1.6:
  version "1.1.11"
  resolved "http://registry.npm.taobao.org/npm-packlist/download/npm-packlist-1.1.11.tgz#84e8c683cbe7867d34b1d357d893ce29e28a02de"
  dependencies:
    ignore-walk "^3.0.1"
    npm-bundled "^1.0.1"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  dependencies:
    path-key "^2.0.0"

"npmlog@0 || 1 || 2 || 3 || 4", npmlog@^4.0.0, npmlog@^4.0.2:
  version "4.1.2"
  resolved "http://registry.npm.taobao.org/npmlog/download/npmlog-4.1.2.tgz#08a7f2a8bf734604779a9efa4ad5cc717abb954b"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

nprogress@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/nprogress/download/nprogress-0.2.0.tgz#cb8f34c53213d895723fcbab907e9422adbcafb1"

nth-check@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/nth-check/download/nth-check-1.0.1.tgz#9929acdf628fc2c41098deab82ac580cf149aae4"
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

oauth-sign@~0.8.2:
  version "0.8.2"
  resolved "http://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.8.2.tgz#46a6ab7f0aead8deae9ec0565780b7d4efeb9d43"

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-keys@^1.0.12:
  version "1.0.12"
  resolved "http://registry.npm.taobao.org/object-keys/download/object-keys-1.0.12.tgz#09c53855377575310cca62f55bb334abff7b3ed2"

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  dependencies:
    isobject "^3.0.0"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/object.omit/download/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  dependencies:
    isobject "^3.0.1"

obuf@^1.0.0, obuf@^1.1.1:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/on-headers/download/on-headers-1.0.1.tgz#928f5d0f470d49342651ea6794b0857c100693f7"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  dependencies:
    mimic-fn "^1.0.0"

opener@^1.4.3:
  version "1.5.1"
  resolved "http://registry.npm.taobao.org/opener/download/opener-1.5.1.tgz#6d2f0e77f1a0af0032aca716c2c1fbb8e7e8abed"

opn@^5.1.0:
  version "5.4.0"
  resolved "http://registry.npm.taobao.org/opn/download/opn-5.4.0.tgz#cb545e7aab78562beb11aa3bfabc7042e1761035"
  dependencies:
    is-wsl "^1.1.0"

optimize-css-assets-webpack-plugin@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/optimize-css-assets-webpack-plugin/download/optimize-css-assets-webpack-plugin-3.2.0.tgz#09a40c4cefde1dd0142444a873c56aa29eb18e6f"
  dependencies:
    cssnano "^3.4.0"
    last-call-webpack-plugin "^2.1.2"

ora@^1.2.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/ora/download/ora-1.4.0.tgz#884458215b3a5d4097592285f93321bb7a79e2e5"
  dependencies:
    chalk "^2.1.0"
    cli-cursor "^2.1.0"
    cli-spinners "^1.0.1"
    log-symbols "^2.1.0"

original@>=0.0.5:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/original/download/original-1.0.2.tgz#e442a61cffe1c5fd20a65f3261c26663b303f25f"
  dependencies:
    url-parse "^1.4.3"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/os-locale/download/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  dependencies:
    lcid "^1.0.0"

os-locale@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/os-locale/download/os-locale-2.1.0.tgz#42bc2900a6b5b8bd17376c8e882b65afccf24bf2"
  dependencies:
    execa "^0.7.0"
    lcid "^1.0.0"
    mem "^1.1.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

osenv@0, osenv@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/osenv/download/osenv-0.1.5.tgz#85cdfafaeb28e8677f416e287592b5f3f49ea410"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"

p-limit@^1.0.0, p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/p-limit/download/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  dependencies:
    p-try "^1.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/p-locate/download/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  dependencies:
    p-limit "^1.1.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/p-map/download/p-map-1.2.0.tgz#e4e94f311eabbc8633a1e79908165fca26241b6b"

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/p-try/download/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"

pako@~1.0.5:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/pako/download/pako-1.0.6.tgz#0101211baa70c4bca4a0f63f2206e97b7dfaf258"

parallel-transform@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.1.0.tgz#d410f065b05da23081fcd10f28854c29bda33b06"
  dependencies:
    cyclist "~0.2.2"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

param-case@2.1.x:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  dependencies:
    no-case "^2.2.0"

parse-asn1@^5.0.0:
  version "5.1.1"
  resolved "http://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.1.tgz#f6bf293818332bd0dab54efb16087724745e6ca8"
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/parse-glob/download/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parseurl@~1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.taobao.org/parseurl/download/parseurl-1.3.2.tgz#fc289d4ed8993119460c156253262cdc8de65bf3"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"

path-browserify@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.0.tgz#a0b870729aae214005b7d5032ec2cbbb0fb4451a"

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0, path-is-absolute@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-is-inside@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"

path-parse@^1.0.5:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"

path-type@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/path-type/download/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

path-type@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/path-type/download/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  dependencies:
    pify "^2.0.0"

path-type@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  dependencies:
    pify "^3.0.0"

pbkdf2@^3.0.3:
  version "3.0.16"
  resolved "http://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.0.16.tgz#7404208ec6b01b62d85bf83853a8064f8d9c2a5c"
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"

pify@^2.0.0, pify@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pify@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-dir@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/pkg-dir/download/pkg-dir-2.0.0.tgz#f6d5d1109e19d63edf428e0bd57e12777615334b"
  dependencies:
    find-up "^2.1.0"

portfinder@^1.0.13, portfinder@^1.0.9:
  version "1.0.17"
  resolved "http://registry.npm.taobao.org/portfinder/download/portfinder-1.0.17.tgz#a8a1691143e46c4735edefcf4fbcccedad26456a"
  dependencies:
    async "^1.5.2"
    debug "^2.2.0"
    mkdirp "0.5.x"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"

postcss-calc@^5.2.0:
  version "5.3.1"
  resolved "http://registry.npm.taobao.org/postcss-calc/download/postcss-calc-5.3.1.tgz#77bae7ca928ad85716e2fda42f261bf7c1d65b5e"
  dependencies:
    postcss "^5.0.2"
    postcss-message-helpers "^2.0.0"
    reduce-css-calc "^1.2.6"

postcss-colormin@^2.1.8:
  version "2.2.2"
  resolved "http://registry.npm.taobao.org/postcss-colormin/download/postcss-colormin-2.2.2.tgz#6631417d5f0e909a3d7ec26b24c8a8d1e4f96e4b"
  dependencies:
    colormin "^1.0.5"
    postcss "^5.0.13"
    postcss-value-parser "^3.2.3"

postcss-convert-values@^2.3.4:
  version "2.6.1"
  resolved "http://registry.npm.taobao.org/postcss-convert-values/download/postcss-convert-values-2.6.1.tgz#bbd8593c5c1fd2e3d1c322bb925dcae8dae4d62d"
  dependencies:
    postcss "^5.0.11"
    postcss-value-parser "^3.1.2"

postcss-discard-comments@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/postcss-discard-comments/download/postcss-discard-comments-2.0.4.tgz#befe89fafd5b3dace5ccce51b76b81514be00e3d"
  dependencies:
    postcss "^5.0.14"

postcss-discard-duplicates@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/postcss-discard-duplicates/download/postcss-discard-duplicates-2.1.0.tgz#b9abf27b88ac188158a5eb12abcae20263b91932"
  dependencies:
    postcss "^5.0.4"

postcss-discard-empty@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/postcss-discard-empty/download/postcss-discard-empty-2.1.0.tgz#d2b4bd9d5ced5ebd8dcade7640c7d7cd7f4f92b5"
  dependencies:
    postcss "^5.0.14"

postcss-discard-overridden@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/postcss-discard-overridden/download/postcss-discard-overridden-0.1.1.tgz#8b1eaf554f686fb288cd874c55667b0aa3668d58"
  dependencies:
    postcss "^5.0.16"

postcss-discard-unused@^2.2.1:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/postcss-discard-unused/download/postcss-discard-unused-2.2.3.tgz#bce30b2cc591ffc634322b5fb3464b6d934f4433"
  dependencies:
    postcss "^5.0.14"
    uniqs "^2.0.0"

postcss-filter-plugins@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/postcss-filter-plugins/download/postcss-filter-plugins-2.0.3.tgz#82245fdf82337041645e477114d8e593aa18b8ec"
  dependencies:
    postcss "^5.0.4"

postcss-import@^11.0.0:
  version "11.1.0"
  resolved "http://registry.npm.taobao.org/postcss-import/download/postcss-import-11.1.0.tgz#55c9362c9192994ec68865d224419df1db2981f0"
  dependencies:
    postcss "^6.0.1"
    postcss-value-parser "^3.2.3"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-load-config@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-1.2.0.tgz#539e9afc9ddc8620121ebf9d8c3673e0ce50d28a"
  dependencies:
    cosmiconfig "^2.1.0"
    object-assign "^4.1.0"
    postcss-load-options "^1.2.0"
    postcss-load-plugins "^2.3.0"

postcss-load-config@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-2.0.0.tgz#f1312ddbf5912cd747177083c5ef7a19d62ee484"
  dependencies:
    cosmiconfig "^4.0.0"
    import-cwd "^2.0.0"

postcss-load-options@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/postcss-load-options/download/postcss-load-options-1.2.0.tgz#b098b1559ddac2df04bc0bb375f99a5cfe2b6d8c"
  dependencies:
    cosmiconfig "^2.1.0"
    object-assign "^4.1.0"

postcss-load-plugins@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/postcss-load-plugins/download/postcss-load-plugins-2.3.0.tgz#745768116599aca2f009fad426b00175049d8d92"
  dependencies:
    cosmiconfig "^2.1.1"
    object-assign "^4.1.0"

postcss-loader@^2.0.8:
  version "2.1.6"
  resolved "http://registry.npm.taobao.org/postcss-loader/download/postcss-loader-2.1.6.tgz#1d7dd7b17c6ba234b9bed5af13e0bea40a42d740"
  dependencies:
    loader-utils "^1.1.0"
    postcss "^6.0.0"
    postcss-load-config "^2.0.0"
    schema-utils "^0.4.0"

postcss-merge-idents@^2.1.5:
  version "2.1.7"
  resolved "http://registry.npm.taobao.org/postcss-merge-idents/download/postcss-merge-idents-2.1.7.tgz#4c5530313c08e1d5b3bbf3d2bbc747e278eea270"
  dependencies:
    has "^1.0.1"
    postcss "^5.0.10"
    postcss-value-parser "^3.1.1"

postcss-merge-longhand@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/postcss-merge-longhand/download/postcss-merge-longhand-2.0.2.tgz#23d90cd127b0a77994915332739034a1a4f3d658"
  dependencies:
    postcss "^5.0.4"

postcss-merge-rules@^2.0.3:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/postcss-merge-rules/download/postcss-merge-rules-2.1.2.tgz#d1df5dfaa7b1acc3be553f0e9e10e87c61b5f721"
  dependencies:
    browserslist "^1.5.2"
    caniuse-api "^1.5.2"
    postcss "^5.0.4"
    postcss-selector-parser "^2.2.2"
    vendors "^1.0.0"

postcss-message-helpers@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/postcss-message-helpers/download/postcss-message-helpers-2.0.0.tgz#a4f2f4fab6e4fe002f0aed000478cdf52f9ba60e"

postcss-minify-font-values@^1.0.2:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/postcss-minify-font-values/download/postcss-minify-font-values-1.0.5.tgz#4b58edb56641eba7c8474ab3526cafd7bbdecb69"
  dependencies:
    object-assign "^4.0.1"
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-minify-gradients@^1.0.1:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/postcss-minify-gradients/download/postcss-minify-gradients-1.0.5.tgz#5dbda11373703f83cfb4a3ea3881d8d75ff5e6e1"
  dependencies:
    postcss "^5.0.12"
    postcss-value-parser "^3.3.0"

postcss-minify-params@^1.0.4:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/postcss-minify-params/download/postcss-minify-params-1.2.2.tgz#ad2ce071373b943b3d930a3fa59a358c28d6f1f3"
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.2"
    postcss-value-parser "^3.0.2"
    uniqs "^2.0.0"

postcss-minify-selectors@^2.0.4:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/postcss-minify-selectors/download/postcss-minify-selectors-2.1.1.tgz#b2c6a98c0072cf91b932d1a496508114311735bf"
  dependencies:
    alphanum-sort "^1.0.2"
    has "^1.0.1"
    postcss "^5.0.14"
    postcss-selector-parser "^2.0.0"

postcss-modules-extract-imports@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.0.tgz#66140ecece38ef06bf0d3e355d69bf59d141ea85"
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz#f7d80c398c5a393fa7964466bd19500a7d61c069"
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz#d6ea64994c79f97b62a72b426fbe6056a194bb90"
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz#ecffa9d7e192518389f42ad0e83f72aec456ea20"
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-normalize-charset@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/postcss-normalize-charset/download/postcss-normalize-charset-1.1.1.tgz#ef9ee71212d7fe759c78ed162f61ed62b5cb93f1"
  dependencies:
    postcss "^5.0.5"

postcss-normalize-url@^3.0.7:
  version "3.0.8"
  resolved "http://registry.npm.taobao.org/postcss-normalize-url/download/postcss-normalize-url-3.0.8.tgz#108f74b3f2fcdaf891a2ffa3ea4592279fc78222"
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^1.4.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"

postcss-ordered-values@^2.1.0:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/postcss-ordered-values/download/postcss-ordered-values-2.2.3.tgz#eec6c2a67b6c412a8db2042e77fe8da43f95c11d"
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.1"

postcss-reduce-idents@^2.2.2:
  version "2.4.0"
  resolved "http://registry.npm.taobao.org/postcss-reduce-idents/download/postcss-reduce-idents-2.4.0.tgz#c2c6d20cc958284f6abfbe63f7609bf409059ad3"
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-reduce-initial@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/postcss-reduce-initial/download/postcss-reduce-initial-1.0.1.tgz#68f80695f045d08263a879ad240df8dd64f644ea"
  dependencies:
    postcss "^5.0.4"

postcss-reduce-transforms@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/postcss-reduce-transforms/download/postcss-reduce-transforms-1.0.4.tgz#ff76f4d8212437b31c298a42d2e1444025771ae1"
  dependencies:
    has "^1.0.1"
    postcss "^5.0.8"
    postcss-value-parser "^3.0.1"

postcss-selector-parser@^2.0.0, postcss-selector-parser@^2.2.2:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-2.2.3.tgz#f9437788606c3c9acee16ffe8d8b16297f27bb90"
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-svgo@^2.1.1:
  version "2.1.6"
  resolved "http://registry.npm.taobao.org/postcss-svgo/download/postcss-svgo-2.1.6.tgz#b6df18aa613b666e133f08adb5219c2684ac108d"
  dependencies:
    is-svg "^2.0.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"
    svgo "^0.7.0"

postcss-unique-selectors@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/postcss-unique-selectors/download/postcss-unique-selectors-2.0.2.tgz#981d57d29ddcb33e7b1dfe1fd43b8649f933ca1d"
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss-url@^7.2.1:
  version "7.3.2"
  resolved "http://registry.npm.taobao.org/postcss-url/download/postcss-url-7.3.2.tgz#5fea273807fb84b38c461c3c9a9e8abd235f7120"
  dependencies:
    mime "^1.4.1"
    minimatch "^3.0.4"
    mkdirp "^0.5.0"
    postcss "^6.0.1"
    xxhashjs "^0.2.1"

postcss-value-parser@^3.0.1, postcss-value-parser@^3.0.2, postcss-value-parser@^3.1.1, postcss-value-parser@^3.1.2, postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.0.tgz#87f38f9f18f774a4ab4c8a232f5c5ce8872a9d15"

postcss-zindex@^2.0.1:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/postcss-zindex/download/postcss-zindex-2.2.0.tgz#d2109ddc055b91af67fc4cb3b025946639d2af22"
  dependencies:
    has "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss@^5.0.10, postcss@^5.0.11, postcss@^5.0.12, postcss@^5.0.13, postcss@^5.0.14, postcss@^5.0.16, postcss@^5.0.2, postcss@^5.0.4, postcss@^5.0.5, postcss@^5.0.6, postcss@^5.0.8, postcss@^5.2.16:
  version "5.2.18"
  resolved "http://registry.npm.taobao.org/postcss/download/postcss-5.2.18.tgz#badfa1497d46244f6390f58b319830d9107853c5"
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0.0, postcss@^6.0.1, postcss@^6.0.17, postcss@^6.0.8:
  version "6.0.23"
  resolved "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz#61c82cc328ac60e677645f979054eb98bc0e3324"
  dependencies:
    chalk "^2.4.1"
    source-map "^0.6.1"
    supports-color "^5.4.0"

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"

preserve@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/preserve/download/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"

prettier@^1.7.0:
  version "1.14.3"
  resolved "http://registry.npm.taobao.org/prettier/download/prettier-1.14.3.tgz#90238dd4c0684b7edce5f83b0fb7328e48bd0895"

pretty-error@^2.0.2:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/pretty-error/download/pretty-error-2.1.1.tgz#5f4f87c8f91e5ae3f3ba87ab4cf5e03b1a17f1a3"
  dependencies:
    renderkid "^2.0.1"
    utila "~0.4"

private@^0.1.6, private@^0.1.8:
  version "0.1.8"
  resolved "http://registry.npm.taobao.org/private/download/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"

process-nextick-args@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.0.tgz#a37d732f4271b4ab1ad070d35508e8290788ffaa"

process@^0.11.10:
  version "0.11.10"
  resolved "http://registry.npm.taobao.org/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"

proxy-addr@~2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.4.tgz#ecfc733bf22ff8c6f407fa275327b9ab67e48b93"
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.8.0"

prr@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

psl@^1.1.24:
  version "1.1.29"
  resolved "http://registry.npm.taobao.org/psl/download/psl-1.1.29.tgz#60f580d360170bb722a797cc704411e6da850c67"

public-encrypt@^4.0.0:
  version "4.0.2"
  resolved "http://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.2.tgz#46eb9107206bf73489f8b85b69d91334c6610994"
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"

pump@^2.0.0, pump@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "http://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"

punycode@^1.2.4, punycode@^1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

punycode@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"

q@^1.1.2:
  version "1.5.1"
  resolved "http://registry.npm.taobao.org/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"

qrcodejs2@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/qrcodejs2/download/qrcodejs2-0.0.2.tgz#465afe5e39f19facecb932c11f7a186109146ae1"

qs@6.5.1:
  version "6.5.1"
  resolved "http://registry.npm.taobao.org/qs/download/qs-6.5.1.tgz#349cdf6eef89ec45c12d7d5eb3fc0c870343a6d8"

qs@~6.5.1, qs@~6.5.2:
  version "6.5.2"
  resolved "http://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"

query-string@^4.1.0:
  version "4.3.4"
  resolved "http://registry.npm.taobao.org/query-string/download/query-string-4.3.4.tgz#bbb693b9ca915c232515b228b1a02b609043dbeb"
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"

querystring@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"

querystringify@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/querystringify/download/querystringify-2.0.0.tgz#fa3ed6e68eb15159457c89b37bc6472833195755"

randomatic@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/randomatic/download/randomatic-3.1.0.tgz#36f2ca708e9e567f5ed2ec01949026d50aa10116"
  dependencies:
    is-number "^4.0.0"
    kind-of "^6.0.0"
    math-random "^1.0.1"

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5:
  version "2.0.6"
  resolved "http://registry.npm.taobao.org/randombytes/download/randombytes-2.0.6.tgz#d302c522948588848a8d300c932b44c24231da80"
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@^1.0.3, range-parser@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/range-parser/download/range-parser-1.2.0.tgz#f49be6b487894ddc40dcc94a322f611092e00d5e"

raw-body@2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/raw-body/download/raw-body-2.3.2.tgz#bcd60c77d3eb93cde0050295c3f379389bc88f89"
  dependencies:
    bytes "3.0.0"
    http-errors "1.6.2"
    iconv-lite "0.4.19"
    unpipe "1.0.0"

rc@^1.2.7:
  version "1.2.8"
  resolved "http://registry.npm.taobao.org/rc/download/rc-1.2.8.tgz#cd924bf5200a075b83c188cd6b9e211b7fc0d3ed"
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/read-cache/download/read-cache-1.0.0.tgz#e664ef31161166c9751cdbe8dbcf86b5fb58f774"
  dependencies:
    pify "^2.3.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/read-pkg/download/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/read-pkg/download/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.4, readable-stream@^2.0.6, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.2.9, readable-stream@^2.3.3, readable-stream@^2.3.6:
  version "2.3.6"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@1.0:
  version "1.0.34"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@^2.0.0:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.taobao.org/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/redent/download/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

reduce-css-calc@^1.2.6:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/reduce-css-calc/download/reduce-css-calc-1.3.0.tgz#747c914e049614a4c9cfbba629871ad1d2927716"
  dependencies:
    balanced-match "^0.4.2"
    math-expression-evaluator "^1.2.14"
    reduce-function-call "^1.0.1"

reduce-function-call@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/reduce-function-call/download/reduce-function-call-1.0.2.tgz#5a200bf92e0e37751752fe45b0ab330fd4b6be99"
  dependencies:
    balanced-match "^0.4.2"

regenerate@^1.2.1:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/regenerate/download/regenerate-1.4.0.tgz#4a856ec4b56e4077c557589cae85e7a4c8869a11"

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"

regenerator-transform@^0.10.0:
  version "0.10.1"
  resolved "http://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.10.1.tgz#1e4996837231da8b7f3cf4114d71b5691a0680dd"
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.4"
  resolved "http://registry.npm.taobao.org/regex-cache/download/regex-cache-0.4.4.tgz#75bdc58a2a1496cec48a12835bc54c8d562336dd"
  dependencies:
    is-equal-shallow "^0.1.3"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexpu-core@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-1.0.0.tgz#86a763f58ee4d7c2f6b102e4764050de7ed90c6b"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/regjsgen/download/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/regjsparser/download/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "http://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"

renderkid@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/renderkid/download/renderkid-2.0.1.tgz#898cabfc8bede4b7b91135a3ffd323e58c0db319"
  dependencies:
    css-select "^1.1.0"
    dom-converter "~0.1"
    htmlparser2 "~3.3.0"
    strip-ansi "^3.0.0"
    utila "~0.3"

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"

repeat-string@^1.5.2, repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"

repeating@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/repeating/download/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

request@2.87.0:
  version "2.87.0"
  resolved "http://registry.npm.taobao.org/request/download/request-2.87.0.tgz#32f00235cd08d482b4d0d68db93a829c0ed5756e"
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.6.0"
    caseless "~0.12.0"
    combined-stream "~1.0.5"
    extend "~3.0.1"
    forever-agent "~0.6.1"
    form-data "~2.3.1"
    har-validator "~5.0.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.17"
    oauth-sign "~0.8.2"
    performance-now "^2.1.0"
    qs "~6.5.1"
    safe-buffer "^5.1.1"
    tough-cookie "~2.3.3"
    tunnel-agent "^0.6.0"
    uuid "^3.1.0"

request@^2.87.0:
  version "2.88.0"
  resolved "http://registry.npm.taobao.org/request/download/request-2.88.0.tgz#9c2fca4f7d35b592efe57c7f0a55e81052124fef"
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.0"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.4.3"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-from-string@^1.1.0:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/require-from-string/download/require-from-string-1.2.1.tgz#529c9ccef27380adfec9a2f965b649bbee636418"

require-from-string@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"

resize-observer-polyfill@^1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/resize-observer-polyfill/download/resize-observer-polyfill-1.5.0.tgz#660ff1d9712a2382baa2cad450a4716209f9ca69"

resolve-cwd@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/resolve-cwd/download/resolve-cwd-2.0.0.tgz#00a9f7387556e27038eae232caa372a6a59b665a"
  dependencies:
    resolve-from "^3.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"

resolve@^1.1.6, resolve@^1.1.7, resolve@^1.4.0:
  version "1.8.1"
  resolved "http://registry.npm.taobao.org/resolve/download/resolve-1.8.1.tgz#82f1ec19a423ac1fbd080b0bab06ba36e84a7a26"
  dependencies:
    path-parse "^1.0.5"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"

right-align@^0.1.1:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/right-align/download/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  dependencies:
    align-text "^0.1.1"

rimraf@2, rimraf@^2.2.8, rimraf@^2.5.4, rimraf@^2.6.0, rimraf@^2.6.1, rimraf@^2.6.2:
  version "2.6.2"
  resolved "http://registry.npm.taobao.org/rimraf/download/rimraf-2.6.2.tgz#2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36"
  dependencies:
    glob "^7.0.5"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  dependencies:
    aproba "^1.1.1"

safe-buffer@5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.1.tgz#893312af69b2123def71f57889001671eeb2c853"

safe-buffer@5.1.2, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"

sass-graph@^2.2.4:
  version "2.2.4"
  resolved "http://registry.npm.taobao.org/sass-graph/download/sass-graph-2.2.4.tgz#13fbd63cd1caf0908b9fd93476ad43a51d1e0b49"
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^7.0.0"

sass-loader@^6.0.6:
  version "6.0.7"
  resolved "http://registry.npm.taobao.org/sass-loader/download/sass-loader-6.0.7.tgz#dd2fdb3e7eeff4a53f35ba6ac408715488353d00"
  dependencies:
    clone-deep "^2.0.1"
    loader-utils "^1.0.1"
    lodash.tail "^4.1.1"
    neo-async "^2.5.0"
    pify "^3.0.0"

sax@^1.2.4, sax@~1.2.1:
  version "1.2.4"
  resolved "http://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"

schema-utils@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/schema-utils/download/schema-utils-0.3.0.tgz#f5877222ce3e931edae039f17eb3716e7137f8cf"
  dependencies:
    ajv "^5.0.0"

schema-utils@^0.4.0, schema-utils@^0.4.5:
  version "0.4.7"
  resolved "http://registry.npm.taobao.org/schema-utils/download/schema-utils-0.4.7.tgz#ba74f597d2be2ea880131746ee17d0a093c68187"
  dependencies:
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/scss-tokenizer/download/scss-tokenizer-0.2.3.tgz#8eb06db9a9723333824d3f5530641149847ce5d1"
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"

selfsigned@^1.9.1:
  version "1.10.3"
  resolved "http://registry.npm.taobao.org/selfsigned/download/selfsigned-1.10.3.tgz#d628ecf9e3735f84e8bafba936b3cf85bea43823"
  dependencies:
    node-forge "0.7.5"

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.4.1, semver@^5.5.0:
  version "5.5.1"
  resolved "http://registry.npm.taobao.org/semver/download/semver-5.5.1.tgz#7dfdd8814bdb7cabc7be0fb1d734cfb66c940477"

semver@~5.3.0:
  version "5.3.0"
  resolved "http://registry.npm.taobao.org/semver/download/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"

send@0.16.2:
  version "0.16.2"
  resolved "http://registry.npm.taobao.org/send/download/send-0.16.2.tgz#6ecca1e0f8c156d141597559848df64730a6bbc1"
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.6.2"
    mime "1.4.1"
    ms "2.0.0"
    on-finished "~2.3.0"
    range-parser "~1.2.0"
    statuses "~1.4.0"

serialize-javascript@^1.4.0:
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-1.5.0.tgz#1aa336162c88a890ddad5384baebc93a655161fe"

serve-index@^1.7.2:
  version "1.9.1"
  resolved "http://registry.npm.taobao.org/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.13.2:
  version "1.13.2"
  resolved "http://registry.npm.taobao.org/serve-static/download/serve-static-1.13.2.tgz#095e8472fd5b46237db50ce486a43f4b86c6cec1"
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.2"
    send "0.16.2"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

set-value@^0.4.3:
  version "0.4.3"
  resolved "http://registry.npm.taobao.org/set-value/download/set-value-0.4.3.tgz#7db08f9d3d22dc7f78e53af3c3bf4666ecdfccf1"
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.1"
    to-object-path "^0.3.0"

set-value@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/set-value/download/set-value-2.0.0.tgz#71ae4a88f0feefbbf52d1ea604f3fb315ebb6274"
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"

setprototypeof@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.0.3.tgz#66567e37043eeb4f04d91bd658c0cbefb55b8e04"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "http://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/shallow-clone/download/shallow-clone-1.0.0.tgz#4480cd06e882ef68b2ad88a3ea54832e2c48b571"
  dependencies:
    is-extendable "^0.1.1"
    kind-of "^5.0.0"
    mixin-object "^2.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"

shelljs@^0.7.6:
  version "0.7.8"
  resolved "http://registry.npm.taobao.org/shelljs/download/shelljs-0.7.8.tgz#decbcf874b0d1e5fb72e14b164a9683048e9acb3"
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shellwords@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/shellwords/download/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

slash@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs-client@1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.1.5.tgz#1bb7c0f7222c40f42adf14f4442cbd1269771a83"
  dependencies:
    debug "^2.6.6"
    eventsource "0.1.6"
    faye-websocket "~0.11.0"
    inherits "^2.0.1"
    json3 "^3.3.2"
    url-parse "^1.1.8"

sockjs@0.3.19:
  version "0.3.19"
  resolved "http://registry.npm.taobao.org/sockjs/download/sockjs-0.3.19.tgz#d976bbe800af7bd20ae08598d582393508993c0d"
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^3.0.1"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/sort-keys/download/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  dependencies:
    is-plain-obj "^1.0.0"

sortablejs@1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/sortablejs/download/sortablejs-1.7.0.tgz#80a2b2370abd568e1cec8c271131ef30a904fa28"

source-list-map@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.0.tgz#aaa47403f7b245a92fbc97ea08f250d6087ed085"

source-map-resolve@^0.5.0:
  version "0.5.2"
  resolved "http://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.2.tgz#72e2cc34095543e43b2c62b2c4c10d4a9054f259"
  dependencies:
    atob "^2.1.1"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.4.15:
  version "0.4.18"
  resolved "http://registry.npm.taobao.org/source-map-support/download/source-map-support-0.4.18.tgz#0286a6de8be42641338594e97ccea75f0a2c585f"
  dependencies:
    source-map "^0.5.6"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"

source-map@^0.4.2:
  version "0.4.4"
  resolved "http://registry.npm.taobao.org/source-map/download/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.3, source-map@^0.5.6, source-map@^0.5.7, source-map@~0.5.1:
  version "0.5.7"
  resolved "http://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"

source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"

spdx-correct@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.0.0.tgz#05a5b4d7153a195bc92c3c425b69f3b2a9524c82"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.1.0.tgz#2c7ae61056c714a5b9b9b2b2af7d311ef5c78fe9"

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-3.0.1.tgz#e2a303236cac54b04031fa7a5a79c7e701df852f"

spdy-transport@^2.0.18:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/spdy-transport/download/spdy-transport-2.1.0.tgz#4bbb15aaffed0beefdd56ad61dbdc8ba3e2cb7a1"
  dependencies:
    debug "^2.6.8"
    detect-node "^2.0.3"
    hpack.js "^2.1.6"
    obuf "^1.1.1"
    readable-stream "^2.2.9"
    safe-buffer "^5.0.1"
    wbuf "^1.7.2"

spdy@^3.4.1:
  version "3.4.7"
  resolved "http://registry.npm.taobao.org/spdy/download/spdy-3.4.7.tgz#42ff41ece5cc0f99a3a6c28aabb73f5c3b03acbc"
  dependencies:
    debug "^2.6.8"
    handle-thing "^1.2.5"
    http-deceiver "^1.2.7"
    safe-buffer "^5.0.1"
    select-hose "^2.0.0"
    spdy-transport "^2.0.18"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

sshpk@^1.7.0:
  version "1.14.2"
  resolved "http://registry.npm.taobao.org/sshpk/download/sshpk-1.14.2.tgz#c6fc61648a3d9c4e764fd3fcdf4ea105e492ba98"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    dashdash "^1.12.0"
    getpass "^0.1.1"
    safer-buffer "^2.0.2"
  optionalDependencies:
    bcrypt-pbkdf "^1.0.0"
    ecc-jsbn "~0.1.1"
    jsbn "~0.1.0"
    tweetnacl "~0.14.0"

ssri@^5.2.4:
  version "5.3.0"
  resolved "http://registry.npm.taobao.org/ssri/download/ssri-5.3.0.tgz#ba3872c9c6d33a0704a7d71ff045e5ec48999d06"
  dependencies:
    safe-buffer "^5.1.1"

stackframe@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/stackframe/download/stackframe-1.0.4.tgz#357b24a992f9427cba6b545d96a14ed2cbca187b"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.3.1 < 2", "statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"

statuses@~1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/statuses/download/statuses-1.4.0.tgz#bb73d446da2796106efcc1b601a253d6c46bd087"

stdout-stream@^1.4.0:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/stdout-stream/download/stdout-stream-1.4.1.tgz#5ac174cdd5cd726104aa0c0b2bd83815d8d535de"
  dependencies:
    readable-stream "^2.0.1"

stream-browserify@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.1.tgz#66266ee5f9bdb9940a4e4514cafb43bb71e5c9db"
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "http://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "http://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.0.tgz#d5c752825e5367e786f78e18e445ea223a155952"

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2", string-width@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string_decoder@^1.0.0, string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  dependencies:
    safe-buffer "~5.1.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "http://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/strip-indent/download/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^3.2.3:
  version "3.2.3"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  dependencies:
    has-flag "^1.0.0"

supports-color@^4.2.1:
  version "4.5.0"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-4.5.0.tgz#be7a0de484dec5c5cddf8b3d59125044912f635b"
  dependencies:
    has-flag "^2.0.0"

supports-color@^5.1.0, supports-color@^5.3.0, supports-color@^5.4.0:
  version "5.5.0"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  dependencies:
    has-flag "^3.0.0"

svgo@^0.7.0:
  version "0.7.2"
  resolved "http://registry.npm.taobao.org/svgo/download/svgo-0.7.2.tgz#9f5772413952135c6fefbf40afe6a4faa88b4bb5"
  dependencies:
    coa "~1.0.1"
    colors "~1.1.2"
    csso "~2.3.1"
    js-yaml "~3.7.0"
    mkdirp "~0.5.1"
    sax "~1.2.1"
    whet.extend "~0.9.9"

tapable@^0.2.7:
  version "0.2.8"
  resolved "http://registry.npm.taobao.org/tapable/download/tapable-0.2.8.tgz#99372a5c999bf2df160afc0d74bed4f47948cd22"

tar@^2.0.0:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/tar/download/tar-2.2.1.tgz#8e4d2a256c0e2185c6b18ad694aec968b83cb1d1"
  dependencies:
    block-stream "*"
    fstream "^1.0.2"
    inherits "2"

tar@^4:
  version "4.4.6"
  resolved "http://registry.npm.taobao.org/tar/download/tar-4.4.6.tgz#63110f09c00b4e60ac8bcfe1bf3c8660235fbc9b"
  dependencies:
    chownr "^1.0.1"
    fs-minipass "^1.2.5"
    minipass "^2.3.3"
    minizlib "^1.1.0"
    mkdirp "^0.5.0"
    safe-buffer "^5.1.2"
    yallist "^3.0.2"

throttle-debounce@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-1.1.0.tgz#51853da37be68a155cb6e827b3514a3c422e89cd"

through2@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/through2/download/through2-2.0.3.tgz#0004569b37c7c74ba39c43f3ced78d1ad94140be"
  dependencies:
    readable-stream "^2.1.5"
    xtend "~4.0.1"

thunky@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/thunky/download/thunky-1.0.2.tgz#a862e018e3fb1ea2ec3fce5d55605cf57f247371"

time-stamp@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/time-stamp/download/time-stamp-2.1.0.tgz#6c5c0b2bc835a244616abcfddf81ce13a1975c9f"

timers-browserify@^2.0.4:
  version "2.0.10"
  resolved "http://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.10.tgz#1d28e3d2aadf1d5a5996c4e9f95601cd053480ae"
  dependencies:
    setimmediate "^1.0.4"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"

to-fast-properties@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toposort@^1.0.0:
  version "1.0.7"
  resolved "http://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz#2e68442d9f64ec720b8cc89e6443ac6caa950029"

tough-cookie@~2.3.3:
  version "2.3.4"
  resolved "http://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.3.4.tgz#ec60cee38ac675063ffc97a5c18970578ee83655"
  dependencies:
    punycode "^1.4.1"

tough-cookie@~2.4.3:
  version "2.4.3"
  resolved "http://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.4.3.tgz#53f36da3f47783b0925afa06ff9f3b165280f781"
  dependencies:
    psl "^1.1.24"
    punycode "^1.4.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/trim-newlines/download/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/trim-right/download/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"

"true-case-path@^1.0.2":
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/true-case-path/download/true-case-path-1.0.3.tgz#f813b5a8c86b40da59606722b144e3225799f47d"
  dependencies:
    glob "^7.1.2"

tryer@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/tryer/download/tryer-1.0.1.tgz#f2c85406800b9b0f74c9f7465b81eaad241252f8"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-is@~1.6.15, type-is@~1.6.16:
  version "1.6.16"
  resolved "http://registry.npm.taobao.org/type-is/download/type-is-1.6.16.tgz#f89ce341541c672b25ee7ae3c73dee3b2be50194"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.18"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

uglify-es@^3.3.4:
  version "3.3.9"
  resolved "http://registry.npm.taobao.org/uglify-es/download/uglify-es-3.3.9.tgz#0c1c4f0700bed8dbc124cdb304d2592ca203e677"
  dependencies:
    commander "~2.13.0"
    source-map "~0.6.1"

uglify-js@3.4.x:
  version "3.4.9"
  resolved "http://registry.npm.taobao.org/uglify-js/download/uglify-js-3.4.9.tgz#af02f180c1207d76432e473ed24a28f4a782bae3"
  dependencies:
    commander "~2.17.1"
    source-map "~0.6.1"

uglify-js@^2.8.29:
  version "2.8.29"
  resolved "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

uglifyjs-webpack-plugin@^0.4.6:
  version "0.4.6"
  resolved "http://registry.npm.taobao.org/uglifyjs-webpack-plugin/download/uglifyjs-webpack-plugin-0.4.6.tgz#b951f4abb6bd617e66f63eb891498e391763e309"
  dependencies:
    source-map "^0.5.6"
    uglify-js "^2.8.29"
    webpack-sources "^1.0.1"

uglifyjs-webpack-plugin@^1.1.1:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/uglifyjs-webpack-plugin/download/uglifyjs-webpack-plugin-1.3.0.tgz#75f548160858163a08643e086d5fefe18a5d67de"
  dependencies:
    cacache "^10.0.4"
    find-cache-dir "^1.0.0"
    schema-utils "^0.4.5"
    serialize-javascript "^1.4.0"
    source-map "^0.6.1"
    uglify-es "^3.3.4"
    webpack-sources "^1.1.0"
    worker-farm "^1.5.2"

union-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/union-value/download/union-value-1.0.0.tgz#5c71c34cb5bad5dcebe3ea0cd08207ba5aa1aea4"
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^0.4.3"

uniq@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"

uniqs@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz#ffede4b36b25290696e6e165d4a59edb998e6b02"

unique-filename@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.1.tgz#5e9edc6d1ce8fb264db18a507ef9bd8544451ca6"
  dependencies:
    imurmurhash "^0.1.4"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.0.5:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/upath/download/upath-1.1.0.tgz#35256597e46a581db4793d0ce47fa9aebfc9fabd"

upper-case@^1.1.1:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "http://registry.npm.taobao.org/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"

url-loader@^0.5.8:
  version "0.5.9"
  resolved "http://registry.npm.taobao.org/url-loader/download/url-loader-0.5.9.tgz#cc8fea82c7b906e7777019250869e569e995c295"
  dependencies:
    loader-utils "^1.0.2"
    mime "1.3.x"

url-parse@^1.1.8, url-parse@^1.4.3:
  version "1.4.3"
  resolved "http://registry.npm.taobao.org/url-parse/download/url-parse-1.4.3.tgz#bfaee455c889023219d757e045fa6a684ec36c15"
  dependencies:
    querystringify "^2.0.0"
    requires-port "^1.0.0"

url@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.taobao.org/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.taobao.org/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

util@0.10.3:
  version "0.10.3"
  resolved "http://registry.npm.taobao.org/util/download/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  dependencies:
    inherits "2.0.1"

util@^0.10.3:
  version "0.10.4"
  resolved "http://registry.npm.taobao.org/util/download/util-0.10.4.tgz#3aa0125bfe668a4672de58857d3ace27ecb76901"
  dependencies:
    inherits "2.0.3"

utila@~0.3:
  version "0.3.3"
  resolved "http://registry.npm.taobao.org/utila/download/utila-0.3.3.tgz#d7e8e7d7e309107092b05f8d9688824d633a4226"

utila@~0.4:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"

uuid@^3.0.1, uuid@^3.1.0, uuid@^3.3.2:
  version "3.3.2"
  resolved "http://registry.npm.taobao.org/uuid/download/uuid-3.3.2.tgz#1b4af4955eb3077c501c23872fc6513811587131"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"

vendors@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/vendors/download/vendors-1.0.2.tgz#7fcb5eef9f5623b156bcea89ec37d63676f21801"

verror@1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vm-browserify@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/vm-browserify/download/vm-browserify-0.0.4.tgz#5d7ea45bbef9e4a6ff65f95438e0a87c357d5a73"
  dependencies:
    indexof "0.0.1"

vue-hot-reload-api@^2.2.0:
  version "2.3.1"
  resolved "http://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.1.tgz#b2d3d95402a811602380783ea4f566eb875569a2"

vue-im@^2.3.4:
  version "2.3.4"
  resolved "http://registry.npm.taobao.org/vue-im/download/vue-im-2.3.4.tgz#ad5e7713f91c1bc85a94e2a352ff99f90e19a724"
  dependencies:
    element-ui "^2.2.1"
    font-awesome "^4.7.0"
    lodash "^4.17.4"
    moment "^2.19.3"

vue-kindeditor@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/vue-kindeditor/download/vue-kindeditor-0.3.0.tgz#8fd5fd68eab43ca1477c8f15a198f737e776a58d"
  dependencies:
    kindeditor "^4.1.10"
    vue "^2.5.2"
    vue-router "^3.0.1"

vue-loader@^13.3.0:
  version "13.7.3"
  resolved "http://registry.npm.taobao.org/vue-loader/download/vue-loader-13.7.3.tgz#e07440f78230a639d00ada4da7b96d0e9d62037f"
  dependencies:
    consolidate "^0.14.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    lru-cache "^4.1.1"
    postcss "^6.0.8"
    postcss-load-config "^1.1.0"
    postcss-selector-parser "^2.0.0"
    prettier "^1.7.0"
    resolve "^1.4.0"
    source-map "^0.6.1"
    vue-hot-reload-api "^2.2.0"
    vue-style-loader "^3.0.0"
    vue-template-es2015-compiler "^1.6.0"

vue-router@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/vue-router/download/vue-router-3.0.1.tgz#d9b05ad9c7420ba0f626d6500d693e60092cc1e9"

vue-style-loader@^3.0.0, vue-style-loader@^3.0.1:
  version "3.1.2"
  resolved "http://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-3.1.2.tgz#6b66ad34998fc9520c2f1e4d5fa4091641c1597a"
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-compiler@^2.5.2:
  version "2.5.17"
  resolved "http://registry.npm.taobao.org/vue-template-compiler/download/vue-template-compiler-2.5.17.tgz#52a4a078c327deb937482a509ae85c06f346c3cb"
  dependencies:
    de-indent "^1.0.2"
    he "^1.1.0"

vue-template-es2015-compiler@^1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.6.0.tgz#dc42697133302ce3017524356a6c61b7b69b4a18"

vue@^2.5.2:
  version "2.5.17"
  resolved "http://registry.npm.taobao.org/vue/download/vue-2.5.17.tgz#0f8789ad718be68ca1872629832ed533589c6ada"

vuex@3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/vuex/download/vuex-3.0.1.tgz#e761352ebe0af537d4bb755a9b9dc4be3df7efd2"

watchpack@^1.4.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/watchpack/download/watchpack-1.6.0.tgz#4bc12c2ebe8aa277a71f1d3f14d685c7b446cd00"
  dependencies:
    chokidar "^2.0.2"
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"

wbuf@^1.1.0, wbuf@^1.7.2:
  version "1.7.3"
  resolved "http://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  dependencies:
    minimalistic-assert "^1.0.0"

webpack-bundle-analyzer@^2.9.0:
  version "2.13.1"
  resolved "http://registry.npm.taobao.org/webpack-bundle-analyzer/download/webpack-bundle-analyzer-2.13.1.tgz#07d2176c6e86c3cdce4c23e56fae2a7b6b4ad526"
  dependencies:
    acorn "^5.3.0"
    bfj-node4 "^5.2.0"
    chalk "^2.3.0"
    commander "^2.13.0"
    ejs "^2.5.7"
    express "^4.16.2"
    filesize "^3.5.11"
    gzip-size "^4.1.0"
    lodash "^4.17.4"
    mkdirp "^0.5.1"
    opener "^1.4.3"
    ws "^4.0.0"

webpack-dev-middleware@1.12.2:
  version "1.12.2"
  resolved "http://registry.npm.taobao.org/webpack-dev-middleware/download/webpack-dev-middleware-1.12.2.tgz#f8fc1120ce3b4fc5680ceecb43d777966b21105e"
  dependencies:
    memory-fs "~0.4.1"
    mime "^1.5.0"
    path-is-absolute "^1.0.0"
    range-parser "^1.0.3"
    time-stamp "^2.0.0"

webpack-dev-server@^2.9.1:
  version "2.11.3"
  resolved "http://registry.npm.taobao.org/webpack-dev-server/download/webpack-dev-server-2.11.3.tgz#3fd48a402164a6569d94d3d17f131432631b4873"
  dependencies:
    ansi-html "0.0.7"
    array-includes "^3.0.3"
    bonjour "^3.5.0"
    chokidar "^2.0.0"
    compression "^1.5.2"
    connect-history-api-fallback "^1.3.0"
    debug "^3.1.0"
    del "^3.0.0"
    express "^4.16.2"
    html-entities "^1.2.0"
    http-proxy-middleware "~0.17.4"
    import-local "^1.0.0"
    internal-ip "1.2.0"
    ip "^1.1.5"
    killable "^1.0.0"
    loglevel "^1.4.1"
    opn "^5.1.0"
    portfinder "^1.0.9"
    selfsigned "^1.9.1"
    serve-index "^1.7.2"
    sockjs "0.3.19"
    sockjs-client "1.1.5"
    spdy "^3.4.1"
    strip-ansi "^3.0.0"
    supports-color "^5.1.0"
    webpack-dev-middleware "1.12.2"
    yargs "6.6.0"

webpack-merge@^4.1.0:
  version "4.1.4"
  resolved "http://registry.npm.taobao.org/webpack-merge/download/webpack-merge-4.1.4.tgz#0fde38eabf2d5fd85251c24a5a8c48f8a3f4eb7b"
  dependencies:
    lodash "^4.17.5"

webpack-sources@^1.0.1, webpack-sources@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/webpack-sources/download/webpack-sources-1.3.0.tgz#2a28dcb9f1f45fe960d8f1493252b5ee6530fa85"
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^3.6.0:
  version "3.12.0"
  resolved "http://registry.npm.taobao.org/webpack/download/webpack-3.12.0.tgz#3f9e34360370602fcf639e97939db486f4ec0d74"
  dependencies:
    acorn "^5.0.0"
    acorn-dynamic-import "^2.0.0"
    ajv "^6.1.0"
    ajv-keywords "^3.1.0"
    async "^2.1.2"
    enhanced-resolve "^3.4.0"
    escope "^3.6.0"
    interpret "^1.0.0"
    json-loader "^0.5.4"
    json5 "^0.5.1"
    loader-runner "^2.3.0"
    loader-utils "^1.1.0"
    memory-fs "~0.4.1"
    mkdirp "~0.5.0"
    node-libs-browser "^2.0.0"
    source-map "^0.5.3"
    supports-color "^4.2.1"
    tapable "^0.2.7"
    uglifyjs-webpack-plugin "^0.4.6"
    watchpack "^1.4.0"
    webpack-sources "^1.0.1"
    yargs "^8.0.2"

websocket-driver@>=0.5.1:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.7.0.tgz#0caf9d2d755d93aee049d4bdd0d3fe2cca2a24eb"
  dependencies:
    http-parser-js ">=0.4.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.3.tgz#5d2ff22977003ec687a4b87073dfbbac146ccf29"

whet.extend@~0.9.9:
  version "0.9.9"
  resolved "http://registry.npm.taobao.org/whet.extend/download/whet.extend-0.9.9.tgz#f877d5bf648c97e5aa542fadc16d6a259b9c11a1"

which-module@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/which-module/download/which-module-1.0.0.tgz#bba63ca861948994ff307736089e3b96026c2a4f"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"

which@1, which@^1.2.9, which@^1.3.0:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/wide-align/download/wide-align-1.1.3.tgz#ae074e6bdc0c14a431e804e624549c633b000457"
  dependencies:
    string-width "^1.0.2 || 2"

window-size@0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/window-size/download/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"

worker-farm@^1.5.2:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/worker-farm/download/worker-farm-1.6.0.tgz#aecc405976fab5a95526180846f0dba288f3a4a0"
  dependencies:
    errno "~0.1.7"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

ws@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.taobao.org/ws/download/ws-4.1.0.tgz#a979b5d7d4da68bf54efe0408967c324869a7289"
  dependencies:
    async-limiter "~1.0.0"
    safe-buffer "~5.1.0"

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/xtend/download/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"

xxhashjs@^0.2.1:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/xxhashjs/download/xxhashjs-0.2.2.tgz#8a6251567621a1c46a5ae204da0249c7f8caa9d8"
  dependencies:
    cuint "^0.2.2"

y18n@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.taobao.org/y18n/download/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"

y18n@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/y18n/download/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"

yallist@^3.0.0, yallist@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/yallist/download/yallist-3.0.2.tgz#8452b4bb7e83c7c188d8041c1a837c773d6d8bb9"

yargs-parser@^4.2.0:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-4.2.1.tgz#29cceac0dc4f03c6c87b4a9f217dd18c9f74871c"
  dependencies:
    camelcase "^3.0.0"

yargs-parser@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-5.0.0.tgz#275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a"
  dependencies:
    camelcase "^3.0.0"

yargs-parser@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-7.0.0.tgz#8d0ac42f16ea55debd332caf4c4038b3e3f5dfd9"
  dependencies:
    camelcase "^4.1.0"

yargs@6.6.0:
  version "6.6.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-6.6.0.tgz#782ec21ef403345f830a808ca3d513af56065208"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^4.2.0"

yargs@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-7.1.0.tgz#6ba318eb16961727f5d284f8ea003e8d6154d0c8"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^5.0.0"

yargs@^8.0.2:
  version "8.0.2"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-8.0.2.tgz#6299a9055b1cefc969ff7e79c1d918dceb22c360"
  dependencies:
    camelcase "^4.1.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^2.0.0"
    read-pkg-up "^2.0.0"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^2.0.0"
    which-module "^2.0.0"
    y18n "^3.2.1"
    yargs-parser "^7.0.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #2ecc71;
            --background-color: #f5f6fa;
            --text-color: #2c3e50;
            --border-color: #dcdde1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Patient Profile Header */
        .patient-header {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .patient-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .patient-header:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .patient-header:hover::before {
            opacity: 1;
        }

        .patient-avatar {
            width: 100px;
            height: 100px;
            background: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
        }

        .patient-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .info-item {
            padding: 10px;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 500;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .dashboard-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            transform: translateY(-3px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-title i {
            font-size: 18px;
            color: var(--secondary-color);
        }

        /* Charts */
        .chart-container {
            width: 100%;
            height: 300px;
        }

        /* Fixed height cards */
        .fixed-height-card {
            height: 400px;
            overflow-y: auto;
        }

        .lab-table-container {
            height: calc(100% - 60px); /* Subtract header height */
            overflow-y: auto;
        }

        .medication-list-container {
            height: calc(100% - 60px); /* Subtract header height */
            overflow-y: auto;
        }

        /* Timeline */
        .timeline {
            grid-column: 1 / -1;
        }

        .timeline-container {
            position: relative;
            padding: 20px 0;
        }

        .timeline-item {
            display: flex;
            margin-bottom: 15px;
            position: relative;
        }

        .timeline-date {
            width: 120px;
            padding-right: 20px;
            text-align: right;
            font-size: 14px;
            color: #666;
        }

        .timeline-content {
            flex: 1;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            margin-left: 20px;
            position: relative;
        }

        .timeline-content::before {
            content: '';
            position: absolute;
            left: -26px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--secondary-color);
        }

        /* Recent Records */
        .recent-records {
            grid-column: 1 / -1;
        }

        .records-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .record-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid var(--secondary-color);
        }

        .record-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .record-value {
            font-size: 13px;
            color: #666;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            .alerts-followup-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .patient-info {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .patient-info {
                grid-template-columns: 1fr;
            }
        }

        /* New Styles */
        .patient-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .detail-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .detail-card:hover {
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            transform: translateY(-3px);
        }

        .detail-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-title i {
            font-size: 18px;
            color: var(--secondary-color);
        }

        .detail-content {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .detail-item {
            padding: 8px;
        }

        .detail-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 500;
        }

        .alert {
            color: #e74c3c;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .alert i {
            animation: pulse 1.5s infinite;
        }

        .lab-results {
            margin-top: 20px;
        }

        .lab-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .lab-table th,
        .lab-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .lab-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .normal {
            color: #2ecc71;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .abnormal {
            color: #e74c3c;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .medication-list {
            list-style: none;
            margin-top: 10px;
        }

        .medication-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .medication-item:hover {
            background: #f8f9fa;
            padding-left: 15px;
        }

        .medication-name {
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .medication-name i {
            color: var(--secondary-color);
            font-size: 14px;
        }

        .medication-details {
            color: #666;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .medication-details i {
            color: #666;
            font-size: 12px;
        }

        .timeline-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
            transition: all 0.3s ease;
        }

        .timeline-badge i {
            font-size: 12px;
        }

        .badge-visit {
            background: #3498db;
            color: white;
        }

        .badge-test {
            background: #2ecc71;
            color: white;
        }

        .badge-medication {
            background: #e67e22;
            color: white;
        }

        .badge-surgery {
            background: #e74c3c;
            color: white;
        }

        .badge-treatment {
            background: #9b59b6;
            color: white;
        }

        .badge-consultation {
            background: #34495e;
            color: white;
        }

        .progress-container {
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }

        .progress-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }

        .treatment-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .stat-card:hover {
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 24px;
            color: var(--secondary-color);
            margin-bottom: 8px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .treatment-content {
            display: grid;
            grid-template-columns: 2fr 3fr;
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .progress-stats-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px 0;
        }

        /* Alert Styles */
        .alert-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 0;
            height: 100%;
            width: 100%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .alert-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .alert-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .alert-title i {
            font-size: 18px;
            color: var(--secondary-color);
        }

        .alert-list {
            display: grid;
            gap: 15px;
        }

        .alert-item {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .alert-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .alert-severity {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            font-size: 14px;
        }

        .alert-severity.high {
            color: #e74c3c;
        }

        .alert-severity.medium {
            color: #f39c12;
        }

        .alert-severity.low {
            color: #3498db;
        }

        .alert-content {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .alert-message {
            font-size: 14px;
            color: var(--primary-color);
        }

        .alert-details {
            font-size: 13px;
            color: #666;
        }

        .alert-time {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }

        .alert-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .alert-badge.high {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .alert-badge.medium {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
        }

        .alert-badge.low {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        /* Follow-up Styles */
        .alerts-followup-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            grid-column: 1 / -1;
            width: 100%;
        }

        .alert-card, .followup-card {
            margin: 0;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        .alert-list, .followup-list {
            flex: 1;
            overflow-y: auto;
        }

        .followup-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .followup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .followup-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .followup-title i {
            font-size: 18px;
            color: var(--secondary-color);
        }

        .followup-item {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .followup-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .followup-type {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            font-size: 14px;
            color: var(--primary-color);
        }

        .followup-content {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .followup-message {
            font-size: 14px;
            color: var(--primary-color);
        }

        .followup-details {
            font-size: 13px;
            color: #666;
        }

        .followup-date {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
        }

        .followup-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .followup-badge.upcoming {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .followup-badge.today {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            animation: pulse 1.5s infinite;
        }

        .followup-badge.future {
            background: rgba(46, 204, 113, 0.1);
            color: #2ecc71;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Patient Profile Header -->
        <div class="patient-header">
            <div class="patient-avatar">
                P
            </div>
            <div class="patient-info">
                <div class="info-item">
                    <div class="info-label">姓名</div>
                    <div class="info-value">黄翠娥</div>
                </div>
                <div class="info-item">
                    <div class="info-label">ID</div>
                    <div class="info-value">1799705</div>
                </div>
                <div class="info-item">
                    <div class="info-label">性别</div>
                    <div class="info-value">女</div>
                </div>
                <div class="info-item">
                    <div class="info-label">年龄</div>
                    <div class="info-value">88岁</div>
                </div>
                <div class="info-item">
                    <div class="info-label">电话</div>
                    <div class="info-value">13601635770</div>
                </div>
                <div class="info-item">
                    <div class="info-label">医保类型</div>
                    <div class="info-value">城镇职工医保</div>
                </div>
            </div>
        </div>

        <!-- Extended Patient Details -->
        <div class="patient-details">
            <!-- Personal Information -->
            <div class="detail-card">
                <div class="detail-title">
                    <i class="fas fa-user"></i>
                    个人信息
                </div>
                <div class="detail-content">
                    <div class="detail-item">
                        <div class="detail-label">婚姻状态</div>
                        <div class="detail-value">已婚</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">民族</div>
                        <div class="detail-value">汉族</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">职业</div>
                        <div class="detail-value">城镇职工</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">工作单位</div>
                        <div class="detail-value"></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">现住址</div>
                        <div class="detail-value"></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">籍贯</div>
                        <div class="detail-value"></div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div class="detail-card">
                <div class="detail-title">
                    <i class="fas fa-phone-alt"></i>
                    紧急联系人
                </div>
                <div class="detail-content">
                    <div class="detail-item">
                        <div class="detail-label">姓名</div>
                        <div class="detail-value">赵春</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">关系</div>
                        <div class="detail-value">配偶</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">联系电话</div>
                        <div class="detail-value">159****1965</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">联系地址</div>
                        <div class="detail-value">广东省深圳市南山区</div>
                    </div>
                </div>
            </div>

            <!-- Medical History -->
            <div class="detail-card">
                <div class="detail-title">
                    <i class="fas fa-notes-medical"></i>
                    病史信息
                </div>
                <div class="detail-content">
                    <div class="detail-item">
                        <div class="detail-label">过敏史</div>
                        <div class="detail-value alert">
                            <i class="fas fa-exclamation-circle"></i>
                            青霉素、阿莫西林
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">家族史</div>
                        <div class="detail-value">父亲：直肠癌</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">既往史</div>
                        <div class="detail-value">无重大疾病史</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Visit Distribution -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        就诊分布
                    </h3>
                </div>
                <div id="visitDistribution" class="chart-container"></div>
            </div>

            <!-- Vital Signs Trends -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-heartbeat"></i>
                        生命体征趋势
                    </h3>
                </div>
                <div id="vitalSigns" class="chart-container"></div>
            </div>

            <!-- Lab Test Trends -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-flask"></i>
                        检验结果趋势
                    </h3>
                </div>
                <div id="labTests" class="chart-container"></div>
            </div>

            <!-- Diagnosis Distribution -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-stethoscope"></i>
                        诊断分布
                    </h3>
                </div>
                <div id="diagnosisDistribution" class="chart-container"></div>
            </div>

            <!-- Lab Results Section -->
            <div class="dashboard-card fixed-height-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-medical-alt"></i>
                        检验结果详情
                    </h3>
                </div>
                <div class="lab-table-container">
                    <table class="lab-table">
                        <thead>
                            <tr>
                                <th>检验项目</th>
                                <th>结果</th>
                                <th>参考范围</th>
                                <th>状态</th>
                                <th>检验日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>血红蛋白</td>
                                <td>140 g/L</td>
                                <td>130-175 g/L</td>
                                <td><span class="normal"><i class="fas fa-check-circle"></i>正常</span></td>
                                <td>2022-06-29</td>
                            </tr>
                            <tr>
                                <td>白细胞计数</td>
                                <td>11.2 x10^9/L</td>
                                <td>4.0-10.0 x10^9/L</td>
                                <td><span class="abnormal"><i class="fas fa-exclamation-circle"></i>偏高</span></td>
                                <td>2022-06-29</td>
                            </tr>
                            <tr>
                                <td>血小板计数</td>
                                <td>250 x10^9/L</td>
                                <td>100-300 x10^9/L</td>
                                <td><span class="normal"><i class="fas fa-check-circle"></i>正常</span></td>
                                <td>2022-06-29</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Medication Section -->
            <div class="dashboard-card fixed-height-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-pills"></i>
                        用药记录
                    </h3>
                </div>
                <div class="medication-list-container">
                    <ul class="medication-list">
                        <li class="medication-item">
                            <div class="medication-name">
                                <i class="fas fa-capsules"></i>
                                阿莫西林胶囊
                            </div>
                            <div class="medication-details">
                                <i class="fas fa-clock"></i>
                                0.5g 每日三次
                            </div>
                        </li>
                        <li class="medication-item">
                            <div class="medication-name">
                                <i class="fas fa-capsules"></i>
                                布洛芬缓释胶囊
                            </div>
                            <div class="medication-details">
                                <i class="fas fa-clock"></i>
                                0.3g 每日两次
                            </div>
                        </li>
                        <li class="medication-item">
                            <div class="medication-name">
                                <i class="fas fa-tablets"></i>
                                维生素C片
                            </div>
                            <div class="medication-details">
                                <i class="fas fa-clock"></i>
                                100mg 每日一次
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Enhanced Timeline -->
            <div class="dashboard-card timeline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt"></i>
                        就医时间轴
                    </h3>
                </div>
                <div class="timeline-container">
                    <div class="timeline-item">
                        <div class="timeline-date">2022-06-29</div>
                        <div class="timeline-content">
                            门诊就诊 - 鼻咽门诊
                            <span class="timeline-badge badge-visit">
                                <i class="fas fa-clinic-medical"></i>
                                门诊
                            </span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2022-06-29</div>
                        <div class="timeline-content">
                            血常规检验
                            <span class="timeline-badge badge-test">
                                <i class="fas fa-vials"></i>
                                检验
                            </span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2022-06-30</div>
                        <div class="timeline-content">
                            超声多普勒检查
                            <span class="timeline-badge badge-test">
                                <i class="fas fa-procedures"></i>
                                检查
                            </span>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-date">2022-06-30</div>
                        <div class="timeline-content">
                            开具处方药物
                            <span class="timeline-badge badge-medication">
                                <i class="fas fa-prescription"></i>
                                用药
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Diagnosis Details -->
            <div class="dashboard-card timeline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-alt"></i>
                        治疗计划
                    </h3>
                </div>
                <div class="treatment-content">
                    <div class="timeline-container">
                        <div class="timeline-item">
                            <div class="timeline-date">2022-07-05</div>
                            <div class="timeline-content">
                                放射治疗 - 第一阶段
                                <span class="timeline-badge badge-treatment">
                                    <i class="fas fa-clock"></i>
                                    待开始
                                </span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2022-07-20</div>
                            <div class="timeline-content">
                                化疗 - 第一周期
                                <span class="timeline-badge badge-treatment">
                                    <i class="fas fa-syringe"></i>
                                    待开始
                                </span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2022-08-10</div>
                            <div class="timeline-content">
                                疗效评估 - CT复查
                                <span class="timeline-badge badge-test">
                                    <i class="fas fa-flask"></i>
                                    待安排
                                </span>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2022-08-15</div>
                            <div class="timeline-content">
                                多学科会诊
                                <span class="timeline-badge badge-consultation">
                                    <i class="fas fa-user-md"></i>
                                    待安排
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="progress-stats-container">
                        <div class="progress-container">
                            <div class="progress-title">
                                <i class="fas fa-radiation"></i>
                                放疗进度
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-stats">
                                <span>0/25次</span>
                                <span>0%</span>
                            </div>
                        </div>
                        <div class="progress-container">
                            <div class="progress-title">
                                <i class="fas fa-syringe"></i>
                                化疗进度
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-stats">
                                <span>0/6周期</span>
                                <span>0%</span>
                            </div>
                        </div>
                        <div class="treatment-stats">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-yen-sign"></i>
                                </div>
                                <div class="stat-value">￥52,800</div>
                                <div class="stat-label">预估总费用</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="stat-value">￥42,240</div>
                                <div class="stat-label">医保报销</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="stat-value">3个月</div>
                                <div class="stat-label">预计疗程</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="stat-value">4次</div>
                                <div class="stat-label">复查次数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wearable Device Alerts and Follow-up Container -->
            <div class="alerts-followup-container">
                <!-- Wearable Device Alerts -->
                <div class="dashboard-card alert-card">
                    <div class="alert-header">
                        <h3 class="alert-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            可穿戴设备警示
                        </h3>
                    </div>
                    <div class="alert-list">
                        <div class="alert-item">
                            <div class="alert-severity high">
                                <i class="fas fa-exclamation-circle"></i>
                                <span class="alert-badge high">高危</span>
                            </div>
                            <div class="alert-content">
                                <div class="alert-message">血压异常升高</div>
                                <div class="alert-details">
                                    收缩压: 165mmHg (正常范围: 90-140mmHg)
                                    <br>
                                    建议: 请立即就医检查，保持情绪稳定，避免剧烈运动
                                </div>
                            </div>
                            <div class="alert-time">10分钟前</div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-severity medium">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span class="alert-badge medium">中度</span>
                            </div>
                            <div class="alert-content">
                                <div class="alert-message">心率不规则</div>
                                <div class="alert-details">
                                    心率: 95次/分 (正常范围: 60-100次/分)
                                    <br>
                                    建议: 注意休息，避免过度劳累，如症状持续请及时就医
                                </div>
                            </div>
                            <div class="alert-time">30分钟前</div>
                        </div>

                        <div class="alert-item">
                            <div class="alert-severity low">
                                <i class="fas fa-info-circle"></i>
                                <span class="alert-badge low">轻微</span>
                            </div>
                            <div class="alert-content">
                                <div class="alert-message">体温轻微升高</div>
                                <div class="alert-details">
                                    体温: 37.5°C (正常范围: 36.3-37.2°C)
                                    <br>
                                    建议: 多休息，注意观察，如有不适及时就医
                                </div>
                            </div>
                            <div class="alert-time">2小时前</div>
                        </div>
                    </div>
                </div>

                <!-- Follow-up Reminders -->
                <div class="dashboard-card followup-card">
                    <div class="followup-header">
                        <h3 class="followup-title">
                            <i class="fas fa-calendar-check"></i>
                            随访计划提醒
                        </h3>
                    </div>
                    <div class="followup-list">
                        <div class="followup-item">
                            <div class="followup-type">
                                <i class="fas fa-stethoscope"></i>
                            </div>
                            <div class="followup-content">
                                <div class="followup-message">肿瘤科随访复诊</div>
                                <div class="followup-details">
                                    地点: 门诊楼5层肿瘤科
                                    <br>
                                    需携带: 近期检查报告、病历本
                                </div>
                            </div>
                            <div class="followup-date">
                                <span class="followup-badge today">今日</span>
                                <span>14:30</span>
                            </div>
                        </div>

                        <div class="followup-item">
                            <div class="followup-type">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="followup-content">
                                <div class="followup-message">CT复查</div>
                                <div class="followup-details">
                                    地点: 医技楼3层放射科
                                    <br>
                                    需空腹，携带医保卡
                                </div>
                            </div>
                            <div class="followup-date">
                                <span class="followup-badge upcoming">3天后</span>
                                <span>09:00</span>
                            </div>
                        </div>

                        <div class="followup-item">
                            <div class="followup-type">
                                <i class="fas fa-procedures"></i>
                            </div>
                            <div class="followup-content">
                                <div class="followup-message">放疗评估</div>
                                <div class="followup-details">
                                    地点: 放疗中心2层
                                    <br>
                                    需携带: 近期检验单、CT片
                                </div>
                            </div>
                            <div class="followup-date">
                                <span class="followup-badge future">下周</span>
                                <span>10:30</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize ECharts instances
        const visitChart = echarts.init(document.getElementById('visitDistribution'));
        const vitalChart = echarts.init(document.getElementById('vitalSigns'));
        const labChart = echarts.init(document.getElementById('labTests'));
        const diagnosisChart = echarts.init(document.getElementById('diagnosisDistribution'));

        // Enhanced Visit Distribution Chart
        visitChart.setOption({
            title: {
                left: 'center',
                top: 20
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c}次 ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'horizontal',
                bottom: 10,
                left: 'center',
                data: ['门诊', '急诊', '住院', '体检']
            },
            series: [{
                name: '就诊类型',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '45%'],
                avoidLabelOverlap: true,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    formatter: '{b}: {c}次\n{d}%'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '14',
                        fontWeight: 'bold'
                    }
                },
                data: [
                    { value: 28, name: '门诊' },
                    { value: 8, name: '急诊' },
                    { value: 5, name: '住院' },
                    { value: 4, name: '体检' }
                ]
            }]
        });

        // Enhanced Vital Signs Chart
        vitalChart.setOption({
            title: {
                left: 'center',
                top: 20
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = params[0].axisValue + '<br/>';
                    params.forEach(param => {
                        let value = param.value;
                        if(param.seriesName === '体温') {
                            result += param.marker + param.seriesName + ': ' + value + '°C<br/>';
                        } else {
                            result += param.marker + param.seriesName + ': ' + value + 'mmHg<br/>';
                        }
                    });
                    return result;
                },
                backgroundColor: 'rgba(255,255,255,0.9)',
                borderColor: '#ccc',
                borderWidth: 1,
                padding: [5, 10],
                textStyle: {
                    color: '#666'
                }
            },
            legend: {
                data: ['收缩压', '舒张压', '体温'],
                bottom: 10,
                left: 'center',
                padding: [5, 10],
                textStyle: {
                    color: '#666'
                }
            },
            grid: {
                left: '12%',
                right: '12%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['6-25', '6-26', '6-27', '6-28', '6-29', '6-30', '7-1', '7-2', '7-3', '7-4'],
                axisLine: {
                    lineStyle: {
                        color: '#ccc'
                    }
                },
                axisTick: {
                    show: false
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '血压(mmHg)',
                    min: 40,
                    max: 200,
                    interval: 20,
                    axisLabel: {
                        formatter: '{value}',
                        margin: 8,
                        color: '#666'
                    },
                    nameTextStyle: {
                        padding: [0, 30, 0, 0],
                        color: '#666'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#eee'
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '体温(°C)',
                    min: 35,
                    max: 42,
                    interval: 0.5,
                    position: 'right',
                    offset: 20,
                    axisLabel: {
                        formatter: '{value}',
                        margin: 8,
                        color: '#666'
                    },
                    nameLocation: 'end',
                    nameGap: 15,
                    nameTextStyle: {
                        padding: [0, 0, 0, 20],
                        color: '#666'
                    },
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }
                    }
                }
            ],
            series: [
                {
                    name: '收缩压',
                    type: 'line',
                    smooth: true,
                    data: [125, 128, 130, 135, 132, 128, 125, 122, 120, 118],
                    label: {
                        show: false
                    },
                    lineStyle: {
                        width: 3
                    },
                    markLine: {
                        silent: true,
                        lineStyle: {
                            color: '#e74c3c',
                            type: 'dashed'
                        },
                        label: {
                            show: false
                        },
                        data: [
                            {
                                yAxis: 140
                            }
                        ]
                    }
                },
                {
                    name: '舒张压',
                    type: 'line',
                    smooth: true,
                    data: [85, 88, 90, 92, 88, 85, 82, 80, 78, 75],
                    label: {
                        show: false
                    },
                    lineStyle: {
                        width: 3
                    },
                    markLine: {
                        silent: true,
                        lineStyle: {
                            color: '#e74c3c',
                            type: 'dashed'
                        },
                        label: {
                            show: false
                        },
                        data: [
                            {
                                yAxis: 90
                            }
                        ]
                    }
                },
                {
                    name: '体温',
                    type: 'line',
                    yAxisIndex: 1,
                    smooth: true,
                    data: [36.5, 36.8, 37.2, 38.5, 38.2, 37.8, 37.2, 36.9, 36.7, 36.5],
                    label: {
                        show: false
                    },
                    lineStyle: {
                        width: 3
                    },
                    markLine: {
                        silent: true,
                        lineStyle: {
                            color: '#e74c3c',
                            type: 'dashed'
                        },
                        label: {
                            show: false
                        },
                        data: [
                            {
                                yAxis: 37.3
                            }
                        ]
                    }
                }
            ]
        });

        // Enhanced Lab Tests Chart
        labChart.setOption({
            title: {
                left: 'center',
                top: 20
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    let result = params[0].axisValue + '<br/>';
                    params.forEach(param => {
                        let unit = '';
                        if(param.seriesName === '血红蛋白') unit = 'g/L';
                        else if(param.seriesName === '白细胞') unit = '×10^9/L';
                        else if(param.seriesName === '血小板') unit = '×10^9/L';
                        result += param.marker + param.seriesName + ': ' + param.value + unit + '<br/>';
                    });
                    return result;
                }
            },
            legend: {
                data: ['血红蛋白', '白细胞', '血小板'],
                bottom: 10
            },
            grid: {
                left: '8%',
                right: '8%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['6-25', '6-26', '6-27', '6-28', '6-29', '6-30', '7-1', '7-2', '7-3', '7-4']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '血红蛋白(g/L)',
                    min: 100,
                    max: 180,
                    interval: 10
                },
                {
                    type: 'value',
                    name: '细胞计数',
                    min: 0,
                    max: 500,
                    interval: 50
                }
            ],
            series: [
                {
                    name: '血红蛋白',
                    type: 'line',
                    smooth: true,
                    data: [142, 138, 135, 132, 128, 125, 130, 135, 138, 140],
                    markArea: {
                        silent: true,
                        data: [[{
                            name: '参考范围',
                            yAxis: 130,
                            itemStyle: {
                                color: 'rgba(46, 204, 113, 0.1)'
                            }
                        }, {
                            yAxis: 175
                        }]]
                    }
                },
                {
                    name: '白细胞',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 1,
                    data: [6.8, 7.2, 8.5, 10.2, 11.5, 10.8, 9.5, 8.2, 7.5, 6.8],
                    markArea: {
                        silent: true,
                        data: [[{
                            name: '参考范围',
                            yAxis: 4.0,
                            itemStyle: {
                                color: 'rgba(46, 204, 113, 0.1)'
                            }
                        }, {
                            yAxis: 10.0
                        }]]
                    }
                },
                {
                    name: '血小板',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 1,
                    data: [245, 252, 268, 275, 282, 278, 265, 258, 250, 245],
                    markArea: {
                        silent: true,
                        data: [[{
                            name: '参考范围',
                            yAxis: 100,
                            itemStyle: {
                                color: 'rgba(46, 204, 113, 0.1)'
                            }
                        }, {
                            yAxis: 300
                        }]]
                    }
                }
            ]
        });

        // Enhanced Diagnosis Distribution Chart
        diagnosisChart.setOption({
            title: {
                left: 'center',
                top: 20
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c}例 ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'horizontal',
                bottom: 10,
                left: 'center',
                pageButtonPosition: 'end'
            },
            series: [
                {
                    name: '诊断类型',
                    type: 'pie',
                    radius: '55%',
                    center: ['50%', '45%'],
                    data: [
                        { value: 12, name: '呼吸系统疾病' },
                        { value: 8, name: '消化系统疾病' },
                        { value: 6, name: '循环系统疾病' },
                        { value: 5, name: '内分泌系统疾病' },
                        { value: 4, name: '神经系统疾病' },
                        { value: 4, name: '泌尿系统疾病' },
                        { value: 3, name: '血液系统疾病' },
                        { value: 3, name: '免疫系统疾病' },
                        { value: 2, name: '肿瘤' },
                        { value: 2, name: '精神心理疾病' }
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        formatter: '{b}: {c}例\n{d}%'
                    }
                }
            ]
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            visitChart.resize();
            vitalChart.resize();
            labChart.resize();
            diagnosisChart.resize();
        });
    </script>
</body>
</html> 
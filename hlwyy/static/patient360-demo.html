<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Patient360 嵌入示例</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Aria<PERSON>, sans-serif;
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    h1 {
      color: #333;
      border-bottom: 1px solid #eaeaea;
      padding-bottom: 10px;
    }
    
    .intro {
      margin-bottom: 20px;
      color: #666;
    }
    
    .example-container {
      margin-top: 20px;
      border: 1px solid #eaeaea;
      border-radius: 4px;
      overflow: hidden;
      height: 700px;
    }
    
    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
    
    .code-sample {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
    }
    
    pre {
      margin: 0;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <h1>Patient360 嵌入示例</h1>
  
  <div class="intro">
    <p>这个示例展示了如何在第三方应用中嵌入 Patient360 组件。下面的框中展示的是直接嵌入的 Patient360 组件，包含组件原有的所有数据。</p>
    <p><strong>注意</strong>：您正在查看的是本地开发环境的演示。</p>
    <p>要访问Patient360演示页面的正确URL是：<a href="/home/<USER>">/home/<USER>/a></p>
  </div>
  
  <div class="example-container">
    <!-- 直接嵌入当前运行的应用 -->
    <iframe src="http://localhost:2222/home/<USER>" frameborder="0"></iframe>
  </div>
  
  <div class="code-sample">
    <h3>第三方集成代码：</h3>
    <pre><code>&lt;!-- 开发环境 --&gt;
&lt;iframe src="http://localhost:2222/home/<USER>" width="100%" height="700px" frameborder="0"&gt;&lt;/iframe&gt;

&lt;!-- 生产环境 --&gt;
&lt;iframe src="https://你的域名/home/<USER>" width="100%" height="700px" frameborder="0"&gt;&lt;/iframe&gt;</code></pre>
  </div>
  
  <div class="code-sample">
    <h3>JavaScript 集成代码：</h3>
    <pre><code>// 引入脚本
import { createPatient360Iframe } from 'path/to/patient360/export.js';

// 创建并挂载 iframe
const iframe = createPatient360Iframe('#container', {
  width: '100%',
  height: '700px',
  src: 'http://localhost:2222/home/<USER>' // 指定实际运行的应用地址
});</code></pre>
  </div>
</body>
</html> 
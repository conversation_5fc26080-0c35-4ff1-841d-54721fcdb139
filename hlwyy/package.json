{"name": "cas-ihdoc-code", "version": "1.0.0", "description": "A Vue.js project", "author": "Nova <<EMAIL>>", "private": true, "scripts": {"dev:develop": "cross-env cfg=develop node ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --progress --config build/webpack.dev.conf.js", "dev:test": "cross-env cfg=test node ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --progress --config build/webpack.dev.conf.js", "dev:publish": "cross-env cfg=publish node ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --progress --config build/webpack.dev.conf.js", "dev:prepublish": "cross-env cfg=prepublish node ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build:develop": "cross-env cfg=develop node build/build.js", "build:test": "cross-env cfg=test node build/build.js", "build:publish": "cross-env cfg=publish node build/build.js", "build:prepublish": "cross-env cfg=prepublish node build/build.js"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "axios": "^0.17.1", "base64-js": "^1.5.1", "decimal.js": "^10.2.0", "echarts": "^5.6.0", "element-ui": "2.10.1", "jsbarcode": "^3.11.0", "load-script": "^1.0.0", "lodash": "^4.17.5", "md5": "^2.2.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "sortablejs": "1.7.0", "vue": "^2.5.2", "vue-im": "^2.3.4", "vue-router": "^3.0.1", "vuex": "3.0.1"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "cross-env": "^5.1.4", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jquery": "1.11.2", "less": "^2.7.2", "less-loader": "^4.0.5", "node-notifier": "^5.1.2", "node-sass": "^4.8.3", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "qrcodejs2": "0.0.2", "rimraf": "^2.6.0", "sass-loader": "^6.0.6", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-im": "^2.3.4", "vue-kindeditor": "^0.3.0", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}
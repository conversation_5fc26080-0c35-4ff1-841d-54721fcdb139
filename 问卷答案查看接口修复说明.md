# 问卷答案查看接口修复说明

**修复时间**: 2025-07-25  
**问题**: 问卷答案查看接口返回的result字段为空

## 问题分析

### 1. 数据库数据确认
根据 `questionnaire_answer.sql` 中的数据：
```sql
INSERT INTO `questionnaire_answer` VALUES (1, 1, 1, 1, '张三', 'checkbox', '疼痛,麻木,咳嗽', '2,3,5', 0.00, '2025-07-24 21:06:07', NULL, NULL, NULL, 34, 'manual', '2025-07-24 21:06:07', '2025-07-24 21:06:07');
```

数据说明：
- `id` = 1
- `questionnaire_id` = 1 (问卷ID)
- `question_id` = 1 (题目ID)
- `patient_id` = 1 (患者ID)
- `patient_name` = '张三'
- `question_type` = 'checkbox' (多选题)
- `answer_text` = '疼痛,麻木,咳嗽' (选项文本)
- `answer_value` = '2,3,5' (选项ID)
- `plan_id` = 34 (随访计划ID)
- `subplan_id` = NULL (子计划ID为空)

### 2. 用户数据确认
根据 `ylydb.sql` 中的数据：
```sql
INSERT INTO `followup_c_user` VALUES (1, '13800138000', '张三', ...);
```

确认手机号 "13800138000" 对应的用户ID是 1，与问卷答案记录匹配。

## 修复内容

### 1. 移除examId参数依赖
原接口错误地依赖 `examId` 参数，实际上应该根据计划ID查询所有相关问卷的答案。

### 2. 修复查询条件
**问题**: 原查询条件包含了 `subplan_id` 的过滤，但数据库中的记录 `subplan_id` 为 NULL。

**修复**: 移除 `subplan_id` 的查询条件，或者正确处理 NULL 值。

```java
// 修复前
if (subplanId != null) {
    wrapper.eq(QuestionnaireAnswer::getSubplanId, subplanId);
}

// 修复后 - 暂时移除此条件
// 因为数据库中的记录subplan_id为NULL
```

### 3. 修复答案格式处理
**问题**: 多选题答案需要返回数组格式，单选题返回字符串格式。

**修复**: 新增 `getQuestionAnswerResult` 方法，根据题目类型返回正确格式：

```java
private Object getQuestionAnswerResult(Long questionnaireId, Long questionId, Long patientId, Long subplanId, String questionType) {
    // 查询答案记录
    QuestionnaireAnswer answer = questionnaireAnswerMapper.selectOne(wrapper);
    
    if (answer != null) {
        String answerValue = answer.getAnswerValue(); // "2,3,5"
        
        if ("checkbox".equals(questionType)) {
            // 多选题：返回字符串数组 ["2", "3", "5"]
            if (answerValue != null && answerValue.contains(",")) {
                String[] values = answerValue.split(",");
                List<String> valueList = new ArrayList<>();
                for (String value : values) {
                    valueList.add(value.trim());
                }
                return valueList; // Jackson自动转换为JSON数组
            }
        } else {
            // 单选题和文本题：返回字符串
            return answerValue != null ? answerValue : "";
        }
    }
    
    // 默认值
    return "checkbox".equals(questionType) ? new ArrayList<String>() : "";
}
```

### 4. 添加调试信息
为了便于排查问题，添加了详细的调试日志：

```java
System.out.println("查询到用户: ID=" + cUser.getId() + ", 姓名=" + cUser.getName() + ", 手机号=" + phone);
System.out.println("查询答案参数: questionnaireId=" + questionnaireId + ", questionId=" + questionId + ", patientId=" + patientId);
System.out.println("找到答案: answerValue=" + answerValue + ", questionType=" + questionType);
```

## 预期结果

修复后，使用以下请求：
```json
{
    "phone": "13800138000",
    "planId": "34"
}
```

应该返回：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "planId": 34,
        "planName": "高血压随访计划",
        "questionnaires": [
            {
                "id": 1,
                "name": "疼痛相关不适评估",
                "questions": [
                    {
                        "id": 1,
                        "questionText": "请您选择您最近1周内是否出现以下不适症状",
                        "questionType": "checkbox",
                        "result": ["2", "3", "5"],  // 修复后应该有值
                        "options": [...]
                    }
                ]
            }
        ]
    }
}
```

## 测试步骤

### 1. 编译测试
```bash
cd hlyyyintface
mvn clean compile
```

### 2. 启动服务并测试接口
```bash
# 启动服务
mvn spring-boot:run

# 测试接口
curl -X POST http://localhost:8080/emr/followup/customer/questionnaire-answers \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","planId":"34"}'
```

### 3. 查看控制台日志
观察调试信息输出，确认：
- 用户查询成功
- 答案查询参数正确
- 找到答案记录
- 返回正确格式的result

## 注意事项

### 1. 数据一致性
确保数据库中的数据与接口期望的格式一致：
- `answer_value` 字段存储的是选项ID（如 "2,3,5"）
- `question_type` 字段正确标识题目类型

### 2. 子计划关联
当前数据库记录中 `subplan_id` 为 NULL，如果后续需要支持子计划级别的答案查询，需要：
- 更新现有数据，设置正确的 `subplan_id`
- 或者修改查询逻辑，正确处理 NULL 值

### 3. 前端适配
确保前端能正确处理：
- 多选题的数组格式 result
- 单选题的字符串格式 result
- 空值的默认处理

这次修复主要解决了查询条件和答案格式的问题，应该能正确返回问卷答案数据。
